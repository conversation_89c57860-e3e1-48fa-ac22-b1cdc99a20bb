﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using bnred.Model.WMS.Warehouse;


namespace bnred.ViewModel.Warehouse.WarehouseVMs
{
    public partial class WarehouseVM : BaseCRUDVM<bnred.Model.WMS.Warehouse.Warehouse>
    {

        public WarehouseVM()
        {
            SetInclude(x => x.Manager);
        }

        protected override void InitVM()
        {
        }

        public override void DoAdd()
        {           
            base.DoAdd();
        }

        public override void DoEdit(bool updateAllFields = false)
        {
            base.DoEdit(updateAllFields);
        }

        public override void DoDelete()
        {
            base.DoDelete();
        }
    }
}
