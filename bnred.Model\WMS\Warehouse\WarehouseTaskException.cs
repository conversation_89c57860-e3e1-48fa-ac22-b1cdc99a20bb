using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums;

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库任务异常
    /// 记录任务执行过程中的异常情况
    /// </summary>
    [Table("WMS_WarehouseTaskExceptions")]
    public class WarehouseTaskException : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TaskId { get; set; }

        /// <summary>
        /// 任务
        /// </summary>
        [Display(Name = "任务")]
        public WarehouseTask Task { get; set; }

        /// <summary>
        /// 异常类型
        /// </summary>
        [Display(Name = "异常类型")]
        [Required]
        [StringLength(50)]
        public string ExceptionType { get; set; }

        /// <summary>
        /// 异常描述
        /// </summary>
        [Display(Name = "异常描述")]
        [Required]
        [StringLength(500)]
        public string ExceptionDescription { get; set; }

        /// <summary>
        /// 发生时间
        /// </summary>
        [Display(Name = "发生时间")]
        [Required]
        public DateTime OccurrenceTime { get; set; }

        /// <summary>
        /// 报告人ID
        /// </summary>
       
        public Guid? ReporterId { get; set; }

 
        public FrameworkUser Reporter { get; set; }

        /// <summary>
        /// 处理状态
        /// </summary>
        [Display(Name = "处理状态")]
        [Required]
        public int Status { get; set; }

        /// <summary>
        /// 解决方案
        /// </summary>
        [Display(Name = "解决方案")]
        [StringLength(500)]
        public string Resolution { get; set; }

        /// <summary>
        /// 解决时间
        /// </summary>
        [Display(Name = "解决时间")]
        public DateTime? ResolutionTime { get; set; }

        /// <summary>
        /// 解决人ID
        /// </summary>
        
        public Guid? ResolverId { get; set; }

 
        public FrameworkUser Resolver { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
} 