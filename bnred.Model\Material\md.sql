-- #############################################################################
-- # SQL DDL for Material Module
-- # Target File: bnred.Model/Material/md.sql
-- #############################################################################

-- #############################################################################
-- # Table: WMS_MaterialCategories (物料类别表)
-- # 来自: MaterialCategory.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_MaterialCategories', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_MaterialCategories;
GO
CREATE TABLE WMS_MaterialCategories (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    ParentID NVARCHAR(50) NULL,                          -- 父类别ID
    Code NVARCHAR(50) NOT NULL,                          -- 类别编码
    Name NVARCHAR(100) NOT NULL,                         -- 类别名称
    Description NVARCHAR(500) NULL,                      -- 类别描述
    DisplayOrder INT NOT NULL DEFAULT 0,                 -- 排序号
    Icon NVARCHAR(100) NULL,                             -- 图标
    IsEnabled BIT NOT NULL DEFAULT 1,                    -- 是否启用
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_MaterialCategories table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料类别表，用于对物料进行分类管理。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories';
-- MS_Description for WMS_MaterialCategories columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父类别ID，用于层级结构' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'ParentID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类别编码，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'Code';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类别名称，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'Name';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类别描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'显示排序号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'DisplayOrder';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类别图标CSS类或路径' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'Icon';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标记类别是否启用' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'IsEnabled';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialCategories', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Table: WMS_Materials (物料信息表)
-- # 来自: Material.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_Materials', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_Materials;
GO
CREATE TABLE WMS_Materials (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    Code NVARCHAR(50) NOT NULL UNIQUE,                   -- 物料编码
    Name NVARCHAR(100) NOT NULL,                         -- 物料名称
    Description NVARCHAR(500) NULL,                      -- 物料描述
    CategoryId NVARCHAR(50) NULL,                        -- 物料类别ID
    Specification NVARCHAR(100) NULL,                    -- 规格型号
    Unit NVARCHAR(20) NOT NULL,                          -- 计量单位
    Barcode NVARCHAR(50) NULL,                           -- 条码 (主条码，更多条码见WMS_MaterialBarcodes)
    SupplierId NVARCHAR(50) NULL,                        -- 默认供应商ID
    StandardCost DECIMAL(18,2) NOT NULL DEFAULT 0,       -- 标准成本
    SalePrice DECIMAL(18,2) NOT NULL DEFAULT 0,          -- 销售价格
    MinStock DECIMAL(18,4) NOT NULL DEFAULT 0,           -- 最小库存量
    MaxStock DECIMAL(18,4) NOT NULL DEFAULT 0,           -- 最大库存量
    SafetyStock DECIMAL(18,4) NOT NULL DEFAULT 0,        -- 安全库存量
    BatchManagement BIT NOT NULL DEFAULT 0,              -- 是否启用批次管理
    SerialNumberManagement BIT NOT NULL DEFAULT 0,       -- 是否启用序列号管理
    ShelfLife INT NULL,                                  -- 保质期(天)
    Status INT NOT NULL DEFAULT 0,                       -- 物料状态 (关联 WMS_EnumDictionary, EnumType='bnred.Model.WMS.Enums.MaterialStatus')
    ImageUrl NVARCHAR(200) NULL,                         -- 图片路径
    AttachmentUrl NVARCHAR(200) NULL,                    -- 附件路径
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_Materials table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料信息表，用于存储物料的基本信息。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials';
-- MS_Description for WMS_Materials columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料编码，唯一，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Code';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料名称，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Name';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料详细描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料类别ID (外键关联 WMS_MaterialCategories.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'CategoryId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的规格型号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Specification';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的基本计量单位，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Unit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的主条码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Barcode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'默认供应商ID (外键关联 WMS_Suppliers.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'SupplierId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的标准成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'StandardCost';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的销售价格' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'SalePrice';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最小库存量预警值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'MinStock';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最大库存量预警值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'MaxStock';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'安全库存量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'SafetyStock';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用批次管理' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'BatchManagement';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用序列号管理' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'SerialNumberManagement';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'保质期天数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'ShelfLife';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料状态 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.WMS.Enums.MaterialStatus\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料图片URL或存储路径' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'ImageUrl';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料相关附件URL或存储路径' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'AttachmentUrl';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Materials', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Table: WMS_MaterialBarcodes (物料条码表)
-- # 来自: MaterialBarcode.cs (已在模型中指定表名 WMS_MaterialBarcodes)
-- #############################################################################
IF OBJECT_ID('dbo.WMS_MaterialBarcodes', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_MaterialBarcodes;
GO
CREATE TABLE WMS_MaterialBarcodes (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    MaterialId NVARCHAR(50) NOT NULL,                    -- 物料ID
    Barcode NVARCHAR(50) NOT NULL,                       -- 条码值
    Type INT NOT NULL,                                   -- 条码类型 (关联 WMS_EnumDictionary, EnumType='bnred.Model.WMS.Enums.BarcodeType')
    IsDefault BIT NOT NULL DEFAULT 0,                    -- 是否默认条码
    IsActive BIT NOT NULL DEFAULT 1,                     -- 是否激活
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_MaterialBarcodes table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的多条码信息表。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes';
-- MS_Description for WMS_MaterialBarcodes columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID (外键关联 WMS_Materials.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'MaterialId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'条码字符串，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'Barcode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'条码类型 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.WMS.Enums.BarcodeType\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'Type';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否为物料的默认条码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'IsDefault';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'该条码是否有效' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'IsActive';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialBarcodes', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO
CREATE UNIQUE INDEX UX_MaterialBarcodes_Material_Barcode ON WMS_MaterialBarcodes (MaterialId, Barcode) WHERE IsDeleted = 0;
GO

-- #############################################################################
-- # Table: WMS_MaterialPrices (物料价格表)
-- # 来自: MaterialPrice.cs (已在模型中指定表名 WMS_MaterialPrices)
-- #############################################################################
IF OBJECT_ID('dbo.WMS_MaterialPrices', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_MaterialPrices;
GO
CREATE TABLE WMS_MaterialPrices (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    MaterialId NVARCHAR(50) NOT NULL,                    -- 物料ID
    SupplierId NVARCHAR(50) NULL,                        -- 供应商ID (如果是采购价)
    Price DECIMAL(18,4) NOT NULL,                        -- 价格
    EffectiveDate DATETIME2 NOT NULL,                    -- 生效日期
    ExpiryDate DATETIME2 NULL,                           -- 失效日期
    IsActive BIT NOT NULL DEFAULT 1,                     -- 是否激活
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_MaterialPrices table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料价格信息表，可用于采购价、销售价等。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices';
-- MS_Description for WMS_MaterialPrices columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID (外键关联 WMS_Materials.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'MaterialId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商ID (外键关联 WMS_Suppliers.ID)，如果是采购价格则填写' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'SupplierId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'价格，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'Price';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'价格生效日期，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'EffectiveDate';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'价格失效日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'ExpiryDate';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'该价格是否有效' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'IsActive';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialPrices', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Table: WMS_MaterialLocalizations (物料本地化信息表)
-- # 来自: MaterialLocalization.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_MaterialLocalizations', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_MaterialLocalizations;
GO
CREATE TABLE WMS_MaterialLocalizations (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    MaterialId NVARCHAR(50) NOT NULL,                    -- 物料ID
    LanguageCode NVARCHAR(10) NOT NULL,                  -- 语言代码
    LocalizedName NVARCHAR(100) NOT NULL,                -- 本地化名称
    LocalizedDescription NVARCHAR(500) NULL,             -- 本地化描述
    LocalizedSpecification NVARCHAR(100) NULL,           -- 本地化规格
    LocalizedUnit NVARCHAR(20) NULL,                     -- 本地化单位
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_MaterialLocalizations table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料的多语言本地化信息表。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations';
-- MS_Description for WMS_MaterialLocalizations columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID (外键关联 WMS_Materials.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'MaterialId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'语言代码 (外键关联 WMS_Language.CultureName)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'LanguageCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料在该语言下的名称，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'LocalizedName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料在该语言下的描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'LocalizedDescription';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料在该语言下的规格型号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'LocalizedSpecification';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料在该语言下的计量单位' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'LocalizedUnit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_MaterialLocalizations', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO
-- Unique constraint based on model attribute [Index(nameof(MaterialId), nameof(LanguageCode), IsUnique = true)]
CREATE UNIQUE INDEX UX_MaterialLocalizations_Material_Language ON WMS_MaterialLocalizations (MaterialId, LanguageCode) WHERE IsDeleted = 0;
GO

-- #############################################################################
-- # Foreign Key Constraints for Material Module
-- #############################################################################

ALTER TABLE WMS_MaterialCategories
ADD CONSTRAINT FK_MaterialCategories_Parent FOREIGN KEY (ParentID) REFERENCES WMS_MaterialCategories(ID);
GO

ALTER TABLE WMS_Materials
ADD CONSTRAINT FK_Materials_Category FOREIGN KEY (CategoryId) REFERENCES WMS_MaterialCategories(ID);
GO
-- Assuming WMS_Suppliers table exists in a different module (e.g., Supplier module or a root_models.sql)
-- ALTER TABLE WMS_Materials
-- ADD CONSTRAINT FK_Materials_Supplier FOREIGN KEY (SupplierId) REFERENCES dbo.WMS_Suppliers(ID); -- Change dbo.WMS_Suppliers if needed
-- GO

ALTER TABLE WMS_MaterialBarcodes
ADD CONSTRAINT FK_MaterialBarcodes_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID);
GO

ALTER TABLE WMS_MaterialPrices
ADD CONSTRAINT FK_MaterialPrices_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID);
GO
-- ALTER TABLE WMS_MaterialPrices
-- ADD CONSTRAINT FK_MaterialPrices_Supplier FOREIGN KEY (SupplierId) REFERENCES dbo.WMS_Suppliers(ID);
-- GO

ALTER TABLE WMS_MaterialLocalizations
ADD CONSTRAINT FK_MaterialLocalizations_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID);
GO
ALTER TABLE WMS_MaterialLocalizations
ADD CONSTRAINT FK_MaterialLocalizations_Language FOREIGN KEY (LanguageCode) REFERENCES dbo.WMS_Language(CultureName); -- Assumes WMS_Language table is in dbo schema and from root_models
GO

PRINT 'SQL for Material module created in bnred.Model/Material/md.sql';
GO
