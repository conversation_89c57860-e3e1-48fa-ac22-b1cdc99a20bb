is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = browser
build_property.RootNamespace = bnred.Client
build_property.RootNamespace = bnred.Client
build_property.ProjectDir = D:\work\wms2026\bnred.Client\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\work\wms2026\bnred.Client
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 
