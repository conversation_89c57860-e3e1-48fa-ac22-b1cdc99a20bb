using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 质检状态枚举
    /// 用于标识物料或批次的质检状态
    /// </summary>
    public enum QualityStatus
    {
        /// <summary>
        /// 待检
        /// 尚未进行质量检验
        /// </summary>
        [Display(Name = "待检")]
        Pending = 0,

        /// <summary>
        /// 合格
        /// 质量检验完全符合标准
        /// </summary>
        [Display(Name = "合格")]
        Qualified = 1,

        /// <summary>
        /// 不合格
        /// 质量检验不符合标准
        /// </summary>
        [Display(Name = "不合格")]
        Unqualified = 2,

        /// <summary>
        /// 部分合格
        /// 部分符合质量标准，部分不符合
        /// </summary>
        [Display(Name = "部分合格")]
        PartiallyQualified = 3,

        /// <summary>
        /// 免检
        /// 免除质量检验环节
        /// </summary>
        [Display(Name = "免检")]
        Exempted = 4
    }
} 