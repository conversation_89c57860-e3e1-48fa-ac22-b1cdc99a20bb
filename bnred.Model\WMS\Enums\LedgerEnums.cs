using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 成本计算方法
    /// Cost Calculation Method
    /// </summary>
    public enum CostCalculationMethod
    {
        /// <summary>
        /// 移动平均法
        /// 根据每次入库的数量和金额重新计算平均成本
        /// Moving Average Method
        /// </summary>
        [Display(Name = "移动平均法")]
        MovingAverage = 0,

        /// <summary>
        /// 先进先出法
        /// 按照库存入库的时间顺序计算成本
        /// First In First Out Method
        /// </summary>
        [Display(Name = "先进先出法")]
        FIFO = 1,

        /// <summary>
        /// 加权平均法
        /// 按照期间内所有入库的总金额除以总数量计算成本
        /// Weighted Average Method
        /// </summary>
        [Display(Name = "加权平均法")]
        WeightedAverage = 2,

        /// <summary>
        /// 个别认定法
        /// 对特定批次或物料单独计算成本
        /// Specific Identification Method
        /// </summary>
        [Display(Name = "个别认定法")]
        SpecificIdentification = 3,

        /// <summary>
        /// 标准成本法
        /// 使用预先设定的标准成本进行计算
        /// Standard Cost Method
        /// </summary>
        [Display(Name = "标准成本法")]
        StandardCost = 4,

        /// <summary>
        /// 后进先出法
        /// 按照库存入库的相反时间顺序计算成本
        /// Last In First Out Method
        /// </summary>
        [Display(Name = "后进先出法")]
        LIFO = 5,

        /// <summary>
        /// 计划成本法
        /// 使用计划预算中的成本进行计算
        /// Planned Cost Method
        /// </summary>
        [Display(Name = "计划成本法")]
        PlannedCost = 6,

        /// <summary>
        /// 替代成本法
        /// 使用当前市场价格作为成本基础
        /// Replacement Cost Method
        /// </summary>
        [Display(Name = "替代成本法")]
        ReplacementCost = 7
    }

    /// <summary>
    /// 账务交易类型
    /// Ledger Transaction Type
    /// </summary>
    public enum LedgerTransactionType
    {
        /// <summary>
        /// 期初
        /// Beginning Balance
        /// </summary>
        [Display(Name = "期初")]
        BeginningBalance = 0,

        /// <summary>
        /// 采购入库
        /// Purchase Inbound
        /// </summary>
        [Display(Name = "采购入库")]
        PurchaseInbound = 1,

        /// <summary>
        /// 销售出库
        /// Sales Outbound
        /// </summary>
        [Display(Name = "销售出库")]
        SalesOutbound = 2,

        /// <summary>
        /// 生产入库
        /// Production Inbound
        /// </summary>
        [Display(Name = "生产入库")]
        ProductionInbound = 3,

        /// <summary>
        /// 生产领料
        /// Production Material Requisition
        /// </summary>
        [Display(Name = "生产领料")]
        ProductionRequisition = 4,

        /// <summary>
        /// 库存调整
        /// Inventory Adjustment
        /// </summary>
        [Display(Name = "库存调整")]
        InventoryAdjustment = 5,

        /// <summary>
        /// 库存盘点
        /// Stock Taking
        /// </summary>
        [Display(Name = "库存盘点")]
        StockTaking = 6,

        /// <summary>
        /// 库存调拨
        /// Inventory Transfer
        /// </summary>
        [Display(Name = "库存调拨")]
        InventoryTransfer = 7,

        /// <summary>
        /// 其他入库
        /// Other Inbound
        /// </summary>
        [Display(Name = "其他入库")]
        OtherInbound = 8,

        /// <summary>
        /// 其他出库
        /// Other Outbound
        /// </summary>
        [Display(Name = "其他出库")]
        OtherOutbound = 9
    }

    /// <summary>
    /// 台账类型枚举
    /// 用于标识不同的台账记录类型
    /// </summary>
    public enum LedgerType
    {
        /// <summary>
        /// 入库记录
        /// 物料入库的台账记录
        /// </summary>
        [Display(Name = "入库记录")]
        Inbound = 0,

        /// <summary>
        /// 出库记录
        /// 物料出库的台账记录
        /// </summary>
        [Display(Name = "出库记录")]
        Outbound = 1,

        /// <summary>
        /// 移库记录
        /// 物料库内移动的台账记录
        /// </summary>
        [Display(Name = "移库记录")]
        Movement = 2,

        /// <summary>
        /// 盘点记录
        /// 库存盘点的台账记录
        /// </summary>
        [Display(Name = "盘点记录")]
        StockTaking = 3,

        /// <summary>
        /// 调整记录
        /// 库存调整的台账记录
        /// </summary>
        [Display(Name = "调整记录")]
        Adjustment = 4
    }

    /// <summary>
    /// 台账方向枚举
    /// 用于标识台账记录的增减方向
    /// </summary>
    public enum LedgerDirection
    {
        /// <summary>
        /// 增加
        /// 库存增加的记录
        /// </summary>
        [Display(Name = "增加")]
        Increase = 1,

        /// <summary>
        /// 减少
        /// 库存减少的记录
        /// </summary>
        [Display(Name = "减少")]
        Decrease = -1
    }
}