/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function(){function initTabs(){for(var e=$G("tabhead").children,t=0;t<e.length;t++)domUtils.on(e[t],"click",function(e){var t=e.target||e.srcElement;setTabFocus(t.getAttribute("data-content-id"))});setTabFocus("upload")}function setTabFocus(e){if(e){var t,i,a=$G("tabhead").children;for(t=0;t<a.length;t++)i=a[t].getAttribute("data-content-id"),i==e?(domUtils.addClass(a[t],"focus"),domUtils.addClass($G(i),"focus")):(domUtils.removeClasses(a[t],"focus"),domUtils.removeClasses($G(i),"focus"));switch(e){case"upload":uploadFile=uploadFile||new UploadFile("queueList");break;case"online":onlineFile=onlineFile||new OnlineFile("fileList")}}}function initButtons(){dialog.onok=function(){for(var e,t=[],i=$G("tabhead").children,a=0;a<i.length;a++)if(domUtils.hasClass(i[a],"focus")){e=i[a].getAttribute("data-content-id");break}switch(e){case"upload":t=uploadFile.getInsertList();var s=uploadFile.getQueueCount();if(s)return $(".info","#queueList").html('<span style="color:red;">'+"还有2个未上传文件".replace(/[\d]/,s)+"</span>"),!1;break;case"online":t=onlineFile.getInsertList()}editor.execCommand("insertfile",t)}}function UploadFile(e){this.$wrap=e.constructor==String?$("#"+e):$(e),this.init()}function OnlineFile(e){this.container=utils.isString(e)?document.getElementById(e):e,this.init()}var uploadFile,onlineFile;window.onload=function(){initTabs(),initButtons()},UploadFile.prototype={init:function(){this.fileList=[],this.initContainer(),this.initUploader()},initContainer:function(){this.$queue=this.$wrap.find(".filelist")},initUploader:function(){function e(e){var t=r('<li id="'+e.id+'"><p class="title">'+e.name+'</p><p class="imgWrap"></p><p class="progress"><span></span></p></li>'),i=r('<div class="file-panel"><span class="cancel">'+lang.uploadDelete+'</span><span class="rotateRight">'+lang.uploadTurnRight+'</span><span class="rotateLeft">'+lang.uploadTurnLeft+"</span></div>").appendTo(t),a=t.find("p.progress span"),s=t.find("p.imgWrap"),l=r('<p class="error"></p>').hide().appendTo(t),o=function(e){switch(e){case"exceed_size":text=lang.errorExceedSize;break;case"interrupt":text=lang.errorInterrupt;break;case"http":text=lang.errorHttp;break;case"not_allow_type":text=lang.errorFileType;break;default:text=lang.errorUploadRetry}l.text(text).show()};"invalid"===e.getStatus()?o(e.statusText):(s.text(lang.uploadPreview),"|png|jpg|jpeg|bmp|gif|".indexOf("|"+e.ext.toLowerCase()+"|")==-1?s.empty().addClass("notimage").append('<i class="file-preview file-type-'+e.ext.toLowerCase()+'"></i><span class="file-title" title="'+e.name+'">'+e.name+"</span>"):browser.ie&&browser.version<=7?s.text(lang.uploadNoPreview):n.makeThumb(e,function(e,t){if(e||!t)s.text(lang.uploadNoPreview);else{var i=r('<img src="'+t+'">');s.empty().append(i),i.on("error",function(){s.text(lang.uploadNoPreview)})}},b,x),F[e.id]=[e.size,0],e.rotation=0,e.ext&&S.indexOf(e.ext.toLowerCase())!=-1||(o("not_allow_type"),n.removeFile(e))),e.on("statuschange",function(s,n){"progress"===n?a.hide().width(0):"queued"===n&&(t.off("mouseenter mouseleave"),i.remove()),"error"===s||"invalid"===s?(o(e.statusText),F[e.id][1]=1):"interrupt"===s?o("interrupt"):"queued"===s?F[e.id][1]=0:"progress"===s&&(l.hide(),a.css("display","block")),t.removeClass("state-"+n).addClass("state-"+s)}),t.on("mouseenter",function(){i.stop().animate({height:30})}),t.on("mouseleave",function(){i.stop().animate({height:0})}),i.on("click","span",function(){var t,i=r(this).index();switch(i){case 0:return void n.removeFile(e);case 1:e.rotation+=90;break;case 2:e.rotation-=90}U?(t="rotate("+e.rotation+"deg)",s.css({"-webkit-transform":t,"-mos-transform":t,"-o-transform":t,transform:t})):s.css("filter","progid:DXImageTransform.Microsoft.BasicImage(rotation="+~~(e.rotation/90%4+4)%4+")")}),t.insertBefore(f)}function t(e){var t=r("#"+e.id);delete F[e.id],i(),t.off().find(".file-panel").off().end().remove()}function i(){var e,t=0,i=0,a=g.children();r.each(F,function(e,a){i+=a[0],t+=a[0]*a[1]}),e=i?t/i:0,a.eq(0).text(Math.round(100*e)+"%"),a.eq(1).css("width",Math.round(100*e)+"%"),s()}function a(e,t){if(e!=w){var i=n.getStats();switch(c.removeClass("state-"+w),c.addClass("state-"+e),e){case"pedding":d.addClass("element-invisible"),u.addClass("element-invisible"),h.removeClass("element-invisible"),g.hide(),p.hide(),n.refresh();break;case"ready":h.addClass("element-invisible"),d.removeClass("element-invisible"),u.removeClass("element-invisible"),g.hide(),p.show(),c.text(lang.uploadStart),n.refresh();break;case"uploading":g.show(),p.hide(),c.text(lang.uploadPause);break;case"paused":g.show(),p.hide(),c.text(lang.uploadContinue);break;case"confirm":if(g.show(),p.hide(),c.text(lang.uploadStart),i=n.getStats(),i.successNum&&!i.uploadFailNum)return void a("finish");break;case"finish":g.hide(),p.show(),i.uploadFailNum?c.text(lang.uploadRetry):c.text(lang.uploadStart)}w=e,s()}l.getQueueCount()?c.removeClass("disabled"):c.addClass("disabled")}function s(){var e,t="";"ready"===w?t=lang.updateStatusReady.replace("_",m).replace("_KB",WebUploader.formatSize(v)):"confirm"===w?(e=n.getStats(),e.uploadFailNum&&(t=lang.updateStatusConfirm.replace("_",e.successNum).replace("_",e.successNum))):(e=n.getStats(),t=lang.updateStatusFinish.replace("_",m).replace("_KB",WebUploader.formatSize(v)).replace("_",e.successNum),e.uploadFailNum&&(t+=lang.updateStatusError.replace("_",e.uploadFailNum))),p.html(t)}var n,l=this,r=jQuery,o=l.$wrap,d=o.find(".filelist"),u=o.find(".statusBar"),p=u.find(".info"),c=o.find(".uploadBtn"),f=(o.find(".filePickerBtn"),o.find(".filePickerBlock")),h=o.find(".placeholder"),g=u.find(".progress").hide(),m=0,v=0,C=window.devicePixelRatio||1,b=113*C,x=113*C,w="",F={},U=function(){var e=document.createElement("p").style,t="transition"in e||"WebkitTransition"in e||"MozTransition"in e||"msTransition"in e||"OTransition"in e;return e=null,t}(),y=editor.getActionUrl(editor.getOpt("fileActionName")),k=editor.getOpt("fileMaxSize"),S=(editor.getOpt("fileAllowFiles")||[]).join("").replace(/\./g,",").replace(/^[,]/,"");return WebUploader.Uploader.support()?editor.getOpt("fileActionName")?(n=l.uploader=WebUploader.create({pick:{id:"#filePickerReady",label:lang.uploadSelectFile},swf:"../../third-party/webuploader/Uploader.swf",server:y,fileVal:editor.getOpt("fileFieldName"),duplicate:!0,fileSingleSizeLimit:k,compress:!1}),n.addButton({id:"#filePickerBlock"}),n.addButton({id:"#filePickerBtn",label:lang.uploadAddFile}),a("pedding"),n.on("fileQueued",function(t){m++,v+=t.size,1===m&&(h.addClass("element-invisible"),u.show()),e(t)}),n.on("fileDequeued",function(e){m--,v-=e.size,t(e),i()}),n.on("filesQueued",function(e){n.isInProgress()||"pedding"!=w&&"finish"!=w&&"confirm"!=w&&"ready"!=w||a("ready"),i()}),n.on("all",function(e,t){switch(e){case"uploadFinished":a("confirm",t);break;case"startUpload":var i=utils.serializeParam(editor.queryCommandValue("serverparam"))||"",s=utils.formatUrl(y+(y.indexOf("?")==-1?"?":"&")+"encode=utf-8&"+i);n.option("server",s),a("uploading",t);break;case"stopUpload":a("paused",t)}}),n.on("uploadBeforeSend",function(e,t,i){i.X_Requested_With="XMLHttpRequest"}),n.on("uploadProgress",function(e,t){var a=r("#"+e.id),s=a.find(".progress span");s.css("width",100*t+"%"),F[e.id][1]=t,i()}),n.on("uploadSuccess",function(e,t){var i=r("#"+e.id);try{var a=t._raw||t,s=utils.str2json(a);"200"==s.Code?(l.fileList.push(s.Data),i.append('<span class="success"></span>')):i.find(".error").text(s.Msg).show()}catch(n){i.find(".error").text(lang.errorServerUpload).show()}}),n.on("uploadError",function(e,t){}),n.on("error",function(t,i){"Q_TYPE_DENIED"!=t&&"F_EXCEED_SIZE"!=t||e(i)}),n.on("uploadComplete",function(e,t){}),c.on("click",function(){return!r(this).hasClass("disabled")&&void("ready"===w?n.upload():"paused"===w?n.upload():"uploading"===w&&n.stop())}),c.addClass("state-"+w),void i()):void r("#filePickerReady").after(r("<div>").html(lang.errorLoadConfig)).hide():void r("#filePickerReady").after(r("<div>").html(lang.errorNotSupport)).hide()},getQueueCount:function(){var e,t,i,a=0,s=this.uploader.getFiles();for(t=0;e=s[t++];)i=e.getStatus(),"queued"!=i&&"uploading"!=i&&"progress"!=i||a++;return a},getInsertList:function(){for(var e=[],t=editor.getOpt("fileUrlPrefix"),i=0;i<this.fileList.length;i++){var a=this.fileList[i];e.push({title:a.FileName,url:t+a.src})}return e}},OnlineFile.prototype={init:function(){this.initContainer(),this.initEvents(),this.initData()},initContainer:function(){this.container.innerHTML="",this.list=document.createElement("ul"),this.clearFloat=document.createElement("li"),domUtils.addClass(this.list,"list"),domUtils.addClass(this.clearFloat,"clearFloat"),this.list.appendChild(this.clearFloat),this.container.appendChild(this.list)},initEvents:function(){var e=this;domUtils.on($G("fileList"),"scroll",function(t){var i=this;i.scrollHeight-(i.offsetHeight+i.scrollTop)<10&&e.getFileData()}),domUtils.on(this.list,"click",function(e){var t=e.target||e.srcElement,i=t.parentNode;"li"==i.tagName.toLowerCase()&&(domUtils.hasClass(i,"selected")?domUtils.removeClasses(i,"selected"):domUtils.addClass(i,"selected"))})},initData:function(){this.state=0,this.listSize=editor.getOpt("fileManagerListSize"),this.listIndex=0,this.listEnd=!1,this.getFileData()},getFileData:function(){var _this=this;_this.listEnd||this.isLoadingData||(this.isLoadingData=!0,ajax.request(editor.getActionUrl(editor.getOpt("fileManagerActionName")),{timeout:1e5,data:utils.extend({start:this.listIndex,size:this.listSize},editor.queryCommandValue("serverparam")),method:"get",onsuccess:function(r){try{var json=eval("("+r.responseText+")");"SUCCESS"==json.state&&(_this.pushData(json.list),_this.listIndex=parseInt(json.start)+parseInt(json.list.length),_this.listIndex>=json.total&&(_this.listEnd=!0),_this.isLoadingData=!1)}catch(e){if(r.responseText.indexOf("ue_separate_ue")!=-1){var list=r.responseText.split(r.responseText);_this.pushData(list),_this.listIndex=parseInt(list.length),_this.listEnd=!0,_this.isLoadingData=!1}}},onerror:function(){_this.isLoadingData=!1}}))},pushData:function(e){var t,i,a,s,n,l=this,r=editor.getOpt("fileManagerUrlPrefix");for(t=0;t<e.length;t++)if(e[t]&&e[t].url){if(i=document.createElement("li"),n=document.createElement("span"),a=e[t].url.substr(e[t].url.lastIndexOf(".")+1),"png|jpg|jpeg|gif|bmp".indexOf(a)!=-1)s=document.createElement("img"),domUtils.on(s,"load",function(e){return function(){l.scale(e,e.parentNode.offsetWidth,e.parentNode.offsetHeight)}}(s)),s.width=113,s.setAttribute("src",r+e[t].url+(e[t].url.indexOf("?")==-1?"?noCache=":"&noCache=")+(+new Date).toString(36));else{var o=document.createElement("i"),d=document.createElement("span");d.innerHTML=e[t].url.substr(e[t].url.lastIndexOf("/")+1),s=document.createElement("div"),s.appendChild(o),s.appendChild(d),domUtils.addClass(s,"file-wrapper"),domUtils.addClass(d,"file-title"),domUtils.addClass(o,"file-type-"+a),domUtils.addClass(o,"file-preview")}domUtils.addClass(n,"icon"),i.setAttribute("data-url",r+e[t].url),e[t].original&&i.setAttribute("data-title",e[t].original),i.appendChild(s),i.appendChild(n),this.list.insertBefore(i,this.clearFloat)}},scale:function(e,t,i,a){var s=e.width,n=e.height;"justify"==a?s>=n?(e.width=t,e.height=i*n/s,e.style.marginLeft="-"+parseInt((e.width-t)/2)+"px"):(e.width=t*s/n,e.height=i,e.style.marginTop="-"+parseInt((e.height-i)/2)+"px"):s>=n?(e.width=t*s/n,e.height=i,e.style.marginLeft="-"+parseInt((e.width-t)/2)+"px"):(e.width=t,e.height=i*n/s,e.style.marginTop="-"+parseInt((e.height-i)/2)+"px")},getInsertList:function(){var e,t=this.list.children,i=[];for(e=0;e<t.length;e++)if(domUtils.hasClass(t[e],"selected")){var a=t[e].getAttribute("data-url"),s=t[e].getAttribute("data-title")||a.substr(a.lastIndexOf("/")+1);i.push({title:s,url:a})}return i}}}();