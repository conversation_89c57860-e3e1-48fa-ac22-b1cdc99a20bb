using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseStrategyParameters")]
    public class WarehouseStrategyParameter : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string StrategyId { get; set; }
        public WarehouseStrategy Strategy { get; set; }

        [Required]
        [StringLength(50)]
        public string ParameterCode { get; set; }

        [Required]
        [StringLength(100)]
        public string ParameterName { get; set; }

        [Required]
        [StringLength(50)]
        public string ParameterValue { get; set; }

        [Required]
        public bool IsRequired { get; set; }

        [Required]
        public int SortOrder { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 