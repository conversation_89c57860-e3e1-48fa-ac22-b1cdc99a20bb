using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_LocationExtensions")]
    public class LocationExtension : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string LocationId { get; set; }
        public virtual Location Location { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Length { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Width { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Height { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal MaxWeight { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal MaxVolume { get; set; }

        [Required]
        public bool AllowMixed { get; set; }

        [Required]
        public bool AllowNested { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 