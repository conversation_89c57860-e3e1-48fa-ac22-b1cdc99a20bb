using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 计量单位类型枚举
    /// 用于标识不同的计量单位类型
    /// </summary>
    public enum UnitType
    {
        /// <summary>
        /// 重量单位
        /// 用于重量计量的单位
        /// </summary>
        [Display(Name = "重量单位")]
        Weight = 0,

        /// <summary>
        /// 长度单位
        /// 用于长度计量的单位
        /// </summary>
        [Display(Name = "长度单位")]
        Length = 1,

        /// <summary>
        /// 面积单位
        /// 用于面积计量的单位
        /// </summary>
        [Display(Name = "面积单位")]
        Area = 2,

        /// <summary>
        /// 体积单位
        /// 用于体积计量的单位
        /// </summary>
        [Display(Name = "体积单位")]
        Volume = 3,

        /// <summary>
        /// 数量单位
        /// 用于数量计量的单位
        /// </summary>
        [Display(Name = "数量单位")]
        Quantity = 4,

        /// <summary>
        /// 包装单位
        /// 用于包装计量的单位
        /// </summary>
        [Display(Name = "包装单位")]
        Package = 5
    }

    /// <summary>
    /// 单位换算类型枚举
    /// 用于标识单位换算的类型
    /// </summary>
    public enum UnitConversionType
    {
        /// <summary>
        /// 固定换算
        /// 使用固定比例换算
        /// </summary>
        [Display(Name = "固定换算")]
        Fixed = 0,

        /// <summary>
        /// 浮动换算
        /// 使用浮动比例换算
        /// </summary>
        [Display(Name = "浮动换算")]
        Float = 1,

        /// <summary>
        /// 区间换算
        /// 使用区间范围换算
        /// </summary>
        [Display(Name = "区间换算")]
        Range = 2
    }
} 