using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Inbound
{
    [Table("WMS_InboundOrders")]
    public class InboundOrder : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        [Required]
        [StringLength(50)]
        [Display(Name = "入库单号")]
        public string OrderNo { get; set; }

        [Required]
        [Display(Name = "入库类型")]
        public InboundType Type { get; set; }

        [Required]
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse.Warehouse Warehouse { get; set; }

        [Display(Name = "计划时间")]
        public DateTime? PlanTime { get; set; }
        
        [Display(Name = "实际时间")]
        public DateTime? ActualTime { get; set; }

        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}