-- ----------------------------
-- Table structure for WMS_QualityInspection
-- ----------------------------
DROP TABLE IF EXISTS [WMS_QualityInspection];
CREATE TABLE [WMS_QualityInspection] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [InspectionNo] nvarchar(50) NOT NULL, -- 质检单号
  [Type] int NOT NULL, -- 质检类型 (需要映射到 WMS_EnumDictionary, C#类型: InspectionType)
  [Status] int NOT NULL, -- 质检状态 (需要映射到 WMS_EnumDictionary, C#类型: InspectionStatus)
  [WarehouseId] nvarchar(50) NOT NULL, -- 仓库ID
  [RelatedOrderId] nvarchar(50) NULL, -- 关联单据ID
  [RelatedOrderNo] nvarchar(50) NULL, -- 关联单据号
  [RelatedOrderType] int NULL, -- 关联单据类型 (需要映射到 WMS_EnumDictionary, C#类型: RelatedOrderType)
  [PlannedInspectionTime] datetime2(7) NULL, -- 计划检验时间
  [ActualInspectionTime] datetime2(7) NULL, -- 实际检验时间
  [InspectorId] uniqueidentifier NULL, -- 质检员ID (外键关联 FrameworkUser)
  [Result] int NULL, -- 检验结果 (需要映射到 WMS_EnumDictionary, C#类型: InspectionResult)
  [Priority] int NOT NULL, -- 优先级 (需要映射到 WMS_EnumDictionary, C#类型: TaskPriority)
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_QualityInspection
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_QualityInspectionDetail
-- ----------------------------
DROP TABLE IF EXISTS [WMS_QualityInspectionDetail];
CREATE TABLE [WMS_QualityInspectionDetail] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [QualityInspectionId] nvarchar(50) NOT NULL, -- 质检单ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [LocationId] nvarchar(50) NULL, -- 库位ID
  [PlanQuantity] decimal(18,4) NOT NULL, -- 计划检验数量
  [ActualQuantity] decimal(18,4) NULL, -- 实际检验数量
  [QualifiedQuantity] decimal(18,4) NULL, -- 合格数量
  [UnqualifiedQuantity] decimal(18,4) NULL, -- 不合格数量
  [Unit] nvarchar(20) NOT NULL, -- 单位
  [Status] int NOT NULL, -- 检验状态 (需要映射到 WMS_EnumDictionary, C#类型: InspectionDetailStatus)
  [Result] int NULL, -- 检验结果 (需要映射到 WMS_EnumDictionary, C#类型: InspectionResult)
  [InspectionTime] datetime2(7) NULL, -- 检验时间
  [InspectorId] uniqueidentifier NULL, -- 检验人ID (外键关联 FrameworkUser)
  [UnqualifiedReason] nvarchar(200) NULL, -- 不合格原因
  [DisposalMethod] int NULL, -- 处理方式 (需要映射到 WMS_EnumDictionary, C#类型: DisposalMethod)
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_QualityInspectionDetail
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_QualityInspection
-- ----------------------------
ALTER TABLE [WMS_QualityInspection] ADD CONSTRAINT [FK_WMS_QualityInspection_Warehouse] FOREIGN KEY ([WarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
-- 注意: InspectorId 外键关联 WMS_FrameworkUser (或其他用户表), 请根据实际用户表名修改。
-- ALTER TABLE [WMS_QualityInspection] ADD CONSTRAINT [FK_WMS_QualityInspection_FrameworkUser] FOREIGN KEY ([InspectorId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO

-- ----------------------------
-- Foreign Keys structure for table WMS_QualityInspectionDetail
-- ----------------------------
ALTER TABLE [WMS_QualityInspectionDetail] ADD CONSTRAINT [FK_WMS_QualityInspectionDetail_QualityInspection] FOREIGN KEY ([QualityInspectionId]) REFERENCES [WMS_QualityInspection] ([ID]) ON DELETE CASCADE ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_QualityInspectionDetail] ADD CONSTRAINT [FK_WMS_QualityInspectionDetail_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_QualityInspectionDetail] ADD CONSTRAINT [FK_WMS_QualityInspectionDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_QualityInspectionDetail] ADD CONSTRAINT [FK_WMS_QualityInspectionDetail_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
-- 注意: InspectorId 外键关联 WMS_FrameworkUser (或其他用户表), 请根据实际用户表名修改。
-- ALTER TABLE [WMS_QualityInspectionDetail] ADD CONSTRAINT [FK_WMS_QualityInspectionDetail_FrameworkUser] FOREIGN KEY ([InspectorId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO

-- ----------------------------
-- MS_Description for table WMS_QualityInspection
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质量检验单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'InspectionNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'Type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'WarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联单据ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'RelatedOrderId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联单据号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'RelatedOrderNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联单据类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'RelatedOrderType'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划检验时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'PlannedInspectionTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际检验时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'ActualInspectionTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检员ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'InspectorId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检验结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'Result'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'优先级' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'Priority'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspection', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_QualityInspectionDetail
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质量检验明细' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检单ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'QualityInspectionId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划检验数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'PlanQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际检验数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'ActualQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'合格数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'QualifiedQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'不合格数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'UnqualifiedQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单位' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'Unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检验状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检验结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'Result'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检验时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'InspectionTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检验人ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'InspectorId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'不合格原因' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'UnqualifiedReason'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'处理方式' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'DisposalMethod'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_QualityInspectionDetail', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO 