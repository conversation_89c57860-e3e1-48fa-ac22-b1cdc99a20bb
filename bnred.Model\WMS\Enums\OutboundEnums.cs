using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 出库类型枚举
    /// 用于标识不同的出库业务类型
    /// </summary>
    public enum OutboundType
    {
        /// <summary>
        /// 销售出库
        /// 销售订单相关的出库
        /// </summary>
        [Display(Name = "销售出库")]
        Sales = 0,

        /// <summary>
        /// 生产领料
        /// 生产订单领料出库
        /// </summary>
        [Display(Name = "生产领料")]
        Production = 1,

        /// <summary>
        /// 退货出库
        /// 向供应商退货的出库
        /// </summary>
        [Display(Name = "退货出库")]
        Return = 2,

        /// <summary>
        /// 调拨出库
        /// 向其他仓库调出的出库
        /// </summary>
        [Display(Name = "调拨出库")]
        Transfer = 3,

        /// <summary>
        /// 盘亏出库
        /// 盘点发现实物短缺的出库
        /// </summary>
        [Display(Name = "盘亏出库")]
        StockLoss = 4,

        /// <summary>
        /// 其他出库
        /// 其他类型的出库业务
        /// </summary>
        [Display(Name = "其他出库")]
        Other = 99
    }

    /// <summary>
    /// 出库状态枚举
    /// 用于标识出库单据的处理状态
    /// </summary>
    public enum OutboundStatus
    {
        /// <summary>
        /// 草稿
        /// 出库单创建但未确认
        /// </summary>
        [Display(Name = "草稿")]
        Draft = 0,

        /// <summary>
        /// 待分配
        /// 出库单已确认,等待分配库存
        /// </summary>
        [Display(Name = "待分配")]
        PendingAllocation = 1,

        /// <summary>
        /// 待拣货
        /// 库存已分配,等待拣货
        /// </summary>
        [Display(Name = "待拣货")]
        PendingPicking = 2,

        /// <summary>
        /// 拣货中
        /// 正在进行拣货作业
        /// </summary>
        [Display(Name = "拣货中")]
        Picking = 3,

        /// <summary>
        /// 待复核
        /// 拣货完成,等待复核
        /// </summary>
        [Display(Name = "待复核")]
        PendingCheck = 4,

        /// <summary>
        /// 待发货
        /// 复核完成,等待发货
        /// </summary>
        [Display(Name = "待发货")]
        PendingShip = 5,

        /// <summary>
        /// 已完成
        /// 出库流程全部完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 6,

        /// <summary>
        /// 已取消
        /// 出库单已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 7,

        /// <summary>
        /// 异常
        /// 出库过程中出现异常
        /// </summary>
        [Display(Name = "异常")]
        Exception = 8
    }
}