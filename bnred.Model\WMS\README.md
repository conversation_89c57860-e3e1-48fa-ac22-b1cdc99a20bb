# 仓储管理系统(WMS)模型模块

本模块包含了仓储管理系统(WMS)所需的所有数据模型定义。

## 模型结构

WMS模型采用了以下结构组织:

- **Enums/**: 包含所有WMS相关的枚举类型
- **Material/**: 物料管理相关模型
- **Quality/**: 质量检验相关模型
- **Transfer/**: 库存移转相关模型
- **Warehouse/**: 仓库管理相关模型

## 关键模型

### 物料管理
- `Material`: 物料基本信息

### 质量检验
- `QualityInspection`: 质量检验单主表
- `QualityInspectionDetail`: 质量检验明细

### 库存移转
- `TransferOrder`: 移库单主表
- `TransferOrderDetail`: 移库单明细

### 仓库管理
- `WarehouseTask`: 仓库任务主表
- `WarehouseTaskDetail`: 仓库任务明细

## 枚举类型

本模块定义了多种业务枚举类型用于支持WMS业务逻辑：

- **TransferType**: 移库类型
- **TransferStatus**: 移库状态
- **TransferDetailStatus**: 移库明细状态
- **InspectionType**: 质检类型
- **InspectionStatus**: 质检状态
- **InspectionDetailStatus**: 质检明细状态
- **InspectionResult**: 质检结果
- **DisposalMethod**: 不合格品处理方式
- **TaskType**: 任务类型
- **TaskStatus**: 任务状态
- **TaskPriority**: 任务优先级

## 基类使用说明

所有模型类均继承自`WalkingTec.Mvvm.Core.BasePoco`，它提供了以下通用属性：

- ID: 主键
- CreateTime: 创建时间
- CreateBy: 创建人
- UpdateTime: 更新时间
- UpdateBy: 更新人

用户关联使用`FrameworkUser`类型从`WalkingTec.Mvvm.Core`命名空间。

## 主键生成策略

所有模型的主键使用以下方式生成：
```csharp
Guid.CreateVersion7().ToString()
```

## 注意事项

1. 在引用用户时，统一使用`FrameworkUser`类型
2. 主键生成使用`Guid.CreateVersion7().ToString()`
3. 所有非空属性都应该添加`[Required]`特性
4. 字符串类型属性应添加`[StringLength]`特性限制长度
5. 所有属性都应该添加`[Display]`特性提供友好名称 