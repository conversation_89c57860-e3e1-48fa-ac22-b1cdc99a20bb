-- ----------------------------
-- Table structure for WMS_Batch
-- ----------------------------
DROP TABLE IF EXISTS [WMS_Batch];
CREATE TABLE [WMS_Batch] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [BatchNumber] nvarchar(50) NOT NULL, -- 批次编号
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [SupplierId] nvarchar(50) NULL, -- 供应商ID
  [ProductionDate] datetime2(7) NULL, -- 生产日期
  [ExpiryDate] datetime2(7) NULL, -- 到期日期
  [ReceiptDate] datetime2(7) NOT NULL, -- 入库日期
  [Status] int NOT NULL, -- 批次状态 (需要映射到 WMS_EnumDictionary, 枚举名: BatchStatus)
  [QualityStatus] int NOT NULL, -- 质检状态 (需要映射到 WMS_EnumDictionary, 枚举名: BatchQualityStatus)
  [QualityCheckDate] datetime2(7) NULL, -- 质检日期
  [QualityCheckUserId] uniqueidentifier NULL, -- 质检人员ID
  [QualityRemark] nvarchar(500) NULL, -- 质检备注
  [Cost] decimal(18,2) NOT NULL, -- 成本价
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_Batch
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_Batch
-- ----------------------------
ALTER TABLE [WMS_Batch] ADD CONSTRAINT [FK_WMS_Batch_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Batch] ADD CONSTRAINT [FK_WMS_Batch_Supplier] FOREIGN KEY ([SupplierId]) REFERENCES [WMS_Supplier] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
-- 注意: QualityCheckUserId 外键关联 WMS_FrameworkUser (或其他用户表), 请根据实际用户表名修改。
-- ALTER TABLE [WMS_Batch] ADD CONSTRAINT [FK_WMS_Batch_FrameworkUser] FOREIGN KEY ([QualityCheckUserId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO

-- ----------------------------
-- MS_Description for table WMS_Batch
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次编号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'BatchNumber'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'SupplierId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'生产日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'ProductionDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'到期日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'ExpiryDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'ReceiptDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'QualityStatus'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'QualityCheckDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检人员ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'QualityCheckUserId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'QualityRemark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'Cost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Batch', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO 