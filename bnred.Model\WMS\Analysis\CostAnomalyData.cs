using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Analysis
{
    /// <summary>
    /// 成本异常数据
    /// </summary>
    [Table("WMS_CostAnomalyData")]
    public class CostAnomalyData : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 日期
        /// </summary>
        [Required]
        [Display(Name = "日期")]
        public DateTime Date { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料ID")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 异常类型
        /// </summary>
        [Required]
        [Display(Name = "异常类型")]
        public CostAnomalyType AnomalyType { get; set; }

        /// <summary>
        /// 实际值
        /// </summary>
        [Required]
        [Display(Name = "实际值")]
        public decimal ActualValue { get; set; }

        /// <summary>
        /// 预期值
        /// </summary>
        [Required]
        [Display(Name = "预期值")]
        public decimal ExpectedValue { get; set; }

        /// <summary>
        /// 偏差率
        /// </summary>
        [Required]
        [Display(Name = "偏差率")]
        public decimal DeviationRate { get; set; }

        /// <summary>
        /// 异常等级
        /// </summary>
        [Required]
        [Display(Name = "异常等级")]
        public CostAnomalyLevel AnomalyLevel { get; set; }

        /// <summary>
        /// 处理状态
        /// </summary>
        [Required]
        [Display(Name = "处理状态")]
        public CostAnomalyStatus Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
} 