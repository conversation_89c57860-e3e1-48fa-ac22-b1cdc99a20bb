/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;function initChartsTypeView(){for(var e=[],t=0,a=chartsConfig.length;t<a;t++)e.push('<div class="view-box" data-chart-type="'+t+'"><img width="300" src="images/charts'+t+'.png"></div>');$("#scrollBed").html(e.join(""))}function renderTable(e){for(var t,a=[],r=0;t=e.rows[r];r++){tableData[r]=[],a[r]=[];for(var i,n=0;i=t.cells[n];n++){var l=getCellValue(i);r>0&&n>0&&(l=+l),0===r||0===n?a[r].push("<th>"+l+"</th>"):a[r].push('<td><input type="text" class="data-item" value="'+l+'"></td>'),tableData[r][n]=l}a[r]=a[r].join("")}$("#tableContainer").html('<table id="showTable" border="1"><tbody><tr>'+a.join("</tr><tr>")+"</tr></tbody></table>")}function initUserConfig(e){var t={};e&&(e=e.split(";"),$.each(e,function(e,a){a=a.split(":"),t[a[0]]=a[1]}),setUserConfig(t))}function initEvent(){var e=null,t=chartsConfig.length-1,a=$("#scrollBed .view-box");$(".charts-format").delegate(".format-ctrl","change",function(){renderCharts()}),$(".table-view").delegate(".data-item","focus",function(){e=this.value}).delegate(".data-item","blur",function(){this.value!==e&&renderCharts(),e=null}),$("#buttonContainer").delegate("a","click",function(e){e.preventDefault(),"prev"===this.getAttribute("data-title")?currentChartType>0&&(currentChartType--,updateViewType(currentChartType)):currentChartType<t&&(currentChartType++,updateViewType(currentChartType))}),$("#scrollBed").delegate(".view-box","click",function(e){var t=$(this).attr("data-chart-type");a.removeClass("selected"),$(a[t]).addClass("selected"),currentChartType=0|t,currentChartType===chartsConfig.length-1?disableNotPieConfig():enableNotPieConfig(),renderCharts()})}function renderCharts(){var e=collectData();$("#chartsContainer").highcharts($.extend({},chartsConfig[currentChartType],{credits:{enabled:!1},exporting:{enabled:!1},title:{text:e.title,x:-20},subtitle:{text:e.subTitle,x:-20},xAxis:{title:{text:e.xTitle},categories:e.categories},yAxis:{title:{text:e.yTitle},plotLines:[{value:0,width:1,color:"#808080"}]},tooltip:{enabled:!0,valueSuffix:e.suffix},legend:{layout:"vertical",align:"right",verticalAlign:"middle",borderWidth:1},series:e.series}))}function updateViewType(e){$("#scrollBed").css("marginLeft",324*-e+"px")}function collectData(){var e=document.forms["data-form"],t=null;return currentChartType!==chartsConfig.length-1?(t=getSeriesAndCategories(),$.extend(t,getUserConfig())):(t=getSeriesForPieChart(),t.title=e.title.value,t.suffix=e.unit.value),t}function getUserConfig(){var e=document.forms["data-form"],t={title:e.title.value,subTitle:e["sub-title"].value,xTitle:e["x-title"].value,yTitle:e["y-title"].value,suffix:e.unit.value,tableDataFormat:getTableDataFormat(),tip:$("#tipInput").val()};return t}function setUserConfig(e){var t=document.forms["data-form"];e.title&&(t.title.value=e.title),e.subTitle&&(t["sub-title"].value=e.subTitle),e.xTitle&&(t["x-title"].value=e.xTitle),e.yTitle&&(t["y-title"].value=e.yTitle),e.suffix&&(t.unit.value=e.suffix),"-1"==e.dataFormat&&(t["charts-format"][1].checked=!0),e.tip&&(t.tip.value=e.tip),currentChartType=e.chartType||0}function getSeriesAndCategories(){var e=(document.forms["data-form"],[]),t=[],a=[],r=getTableData();if("-1"===getTableDataFormat()){for(var i=0,n=r.length;i<n;i++)for(var l=0,o=r[i].length;l<o;l++)a[l]||(a[l]=[]),a[l][i]=r[i][l];r=a}t=r[0].slice(1);for(var u,i=1;u=r[i];i++)e.push({name:u[0],data:u.slice(1)});return{series:e,categories:t}}function getTableDataFormat(){var e=document.forms["data-form"],t=e["charts-format"];return t[0].checked?t[0].value:t[1].value}function disableNotPieConfig(){updateConfigItem("disable")}function enableNotPieConfig(){updateConfigItem("enable")}function updateConfigItem(e){for(var t,a=$("#showTable")[0],r="disable"===e,i=2;t=a.rows[i];i++)for(var n,l=1;n=t.cells[l];l++)$("input",n).attr("disabled",r);$("input.not-pie-item").attr("disabled",r),$("#tipInput").attr("disabled",!r)}function getSeriesForPieChart(){for(var e={type:"pie",name:$("#tipInput").val(),data:[]},t=getTableData(),a=1,r=t[0].length;a<r;a++){var i=t[0][a],n=t[1][a];e.data.push([i,n])}return{series:[e]}}function getTableData(){for(var e,t=document.getElementById("showTable"),a=t.rows[0].cells.length-1,r=getTableInputValue(),i=0;e=r[i];i++)tableData[Math.floor(i/a)+1][i%a+1]=r[i];return tableData}function getTableInputValue(){for(var e,t=document.getElementById("showTable"),a=t.getElementsByTagName("input"),r=[],i=0;e=a[i];i++)r.push(0|e.value);return r}function getCellValue(e){var t=utils.trim(e.innerText||e.textContent||"");return t.replace(new RegExp(UE.dom.domUtils.fillChar,"g"),"").replace(/^\s+|\s+$/g,"")}function syncTableData(){for(var e,t=getTableData(),a=1;e=editorTable.rows[a];a++)for(var r,i=1;r=e.cells[i];i++)r.innerHTML=t[a][i]}var tableData=[],editorTable=null,chartsConfig=window.typeConfig,resizeTimer=null,currentChartType=0;window.onload=function(){return(editorTable=domUtils.findParentByTagName(editor.selection.getRange().startContainer,"table",!0))?(initChartsTypeView(),renderTable(editorTable),initEvent(),initUserConfig(editorTable.getAttribute("data-chart")),$("#scrollBed .view-box:eq("+currentChartType+")").trigger("click"),updateViewType(currentChartType),void dialog.addListener("resize",function(){null!=resizeTimer&&window.clearTimeout(resizeTimer),resizeTimer=window.setTimeout(function(){resizeTimer=null,renderCharts()},500)})):void(document.body.innerHTML="<div class='edui-charts-not-data'>未找到数据</div>")},dialog.onok=function(){var e=(document.forms["data-form"],getUserConfig());e.chartType=currentChartType,syncTableData(),editor.execCommand("charts",e)};