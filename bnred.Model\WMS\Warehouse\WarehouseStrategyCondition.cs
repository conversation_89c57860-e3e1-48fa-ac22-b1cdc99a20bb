using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseStrategyConditions")]
    public class WarehouseStrategyCondition : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string StrategyId { get; set; }
        public WarehouseStrategy Strategy { get; set; }

        [Required]
        [StringLength(50)]
        public string ConditionCode { get; set; }

        [Required]
        [StringLength(100)]
        public string ConditionName { get; set; }

        [Required]
        [StringLength(50)]
        public string FieldName { get; set; }

        [Required]
        [StringLength(50)]
        public string Operator { get; set; }

        [Required]
        [StringLength(500)]
        public string FieldValue { get; set; }

        [Required]
        public int Priority { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 