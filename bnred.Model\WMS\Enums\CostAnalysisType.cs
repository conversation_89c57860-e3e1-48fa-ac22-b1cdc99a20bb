using System.ComponentModel.DataAnnotations;

namespace bnred.Model.Enums
{
    /// <summary>
    /// 成本分析类型枚举
    /// </summary>
    public enum CostAnalysisType
    {
        /// <summary>
        /// 按日分析
        /// 对每日的成本数据进行分析
        /// </summary>
        Daily = 1,

        /// <summary>
        /// 按周分析
        /// 对每周的成本数据进行分析
        /// </summary>
        Weekly = 2,

        /// <summary>
        /// 按月分析
        /// 对每月的成本数据进行分析
        /// </summary>
        Monthly = 3,

        /// <summary>
        /// 按季度分析
        /// 对每季度的成本数据进行分析
        /// </summary>
        Quarterly = 4,

        /// <summary>
        /// 按年分析
        /// 对每年的成本数据进行分析
        /// </summary>
        Yearly = 5,

        /// <summary>
        /// 按类别分析
        /// 对不同物料类别的成本进行分析
        /// </summary>
        ByCategory = 6,

        /// <summary>
        /// 按区域分析
        /// 对不同仓储区域的成本进行分析
        /// </summary>
        ByRegion = 7,

        /// <summary>
        /// 按供应商分析
        /// 对不同供应商的物料成本进行分析
        /// </summary>
        BySupplier = 8,

        /// <summary>
        /// 按成本中心分析
        /// 对不同成本中心的成本进行分析
        /// </summary>
        ByCostCenter = 9,

        /// <summary>
        /// 按批次分析
        /// 对不同批次的成本进行分析
        /// </summary>
        ByBatch = 10
    }
} 