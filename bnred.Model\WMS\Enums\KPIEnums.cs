using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// KPI指标类型枚举
    /// 用于标识不同的绩效考核指标类型
    /// </summary>
    public enum KPIType
    {
        /// <summary>
        /// 收货效率
        /// 衡量收货作业的效率指标
        /// </summary>
        [Display(Name = "收货效率")]
        ReceivingEfficiency = 0,

        /// <summary>
        /// 上架效率
        /// 衡量上架作业的效率指标
        /// </summary>
        [Display(Name = "上架效率")]
        PutAwayEfficiency = 1,

        /// <summary>
        /// 拣货效率
        /// 衡量拣货作业的效率指标
        /// </summary>
        [Display(Name = "拣货效率")]
        PickingEfficiency = 2,

        /// <summary>
        /// 库存准确率
        /// 衡量库存记录准确性的指标
        /// </summary>
        [Display(Name = "库存准确率")]
        InventoryAccuracy = 3,

        /// <summary>
        /// 订单完成率
        /// 衡量订单处理完成情况的指标
        /// </summary>
        [Display(Name = "订单完成率")]
        OrderFulfillment = 4,

        /// <summary>
        /// 库存周转率
        /// 衡量库存流转速度的指标
        /// </summary>
        [Display(Name = "库存周转率")]
        InventoryTurnover = 5,

        /// <summary>
        /// 差错率
        /// 衡量作业质量的指标
        /// </summary>
        [Display(Name = "差错率")]
        ErrorRate = 6,

        /// <summary>
        /// 空间利用率
        /// 衡量仓储空间使用效率的指标
        /// </summary>
        [Display(Name = "空间利用率")]
        SpaceUtilization = 7
    }

    /// <summary>
    /// KPI计算周期枚举
    /// 用于标识KPI指标的统计周期
    /// </summary>
    public enum KPIPeriod
    {
        /// <summary>
        /// 每日
        /// 按天统计KPI指标
        /// </summary>
        [Display(Name = "每日")]
        Daily = 0,

        /// <summary>
        /// 每周
        /// 按周统计KPI指标
        /// </summary>
        [Display(Name = "每周")]
        Weekly = 1,

        /// <summary>
        /// 每月
        /// 按月统计KPI指标
        /// </summary>
        [Display(Name = "每月")]
        Monthly = 2,

        /// <summary>
        /// 每季
        /// 按季度统计KPI指标
        /// </summary>
        [Display(Name = "每季")]
        Quarterly = 3,

        /// <summary>
        /// 每年
        /// 按年统计KPI指标
        /// </summary>
        [Display(Name = "每年")]
        Yearly = 4
    }
} 