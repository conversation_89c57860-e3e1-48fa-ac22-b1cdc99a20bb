-O0
-g
-v
-fwasm-exceptions
-s EXPORT_ES6=1 -lexports.js
-s EXPORT_EXCEPTION_HANDLING_HELPERS=1
-s LLD_REPORT_UNDEFINED
-s ERROR_ON_UNDEFINED_SYMBOLS=1
-s INITIAL_MEMORY=67436544
-s STACK_SIZE=5MB
-s WASM_BIGINT=1
--pre-js "C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\src\es6\dotnet.es6.pre.js"
--js-library "C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\src\es6\dotnet.es6.lib.js"
--extern-post-js "C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\src\es6\dotnet.es6.extpost.js"
"C:\Users\<USER>\.nuget\packages\sqlitepclraw.lib.e_sqlite3\2.1.6\buildTransitive\net8.0\..\..\runtimes\browser-wasm\nativeassets\net8.0\e_sqlite3.a"
"D:\work\wms2025\bnred.Client\obj\Debug\net9.0\wasm\for-build\pinvoke.o"
"D:\work\wms2025\bnred.Client\obj\Debug\net9.0\wasm\for-build\driver.o"
"D:\work\wms2025\bnred.Client\obj\Debug\net9.0\wasm\for-build\corebindings.o"
"D:\work\wms2025\bnred.Client\obj\Debug\net9.0\wasm\for-build\runtime.o"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libicudata.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libicui18n.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libicuuc.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-component-debugger-static.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-component-diagnostics_tracing-stub-static.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-component-hot_reload-static.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-component-marshal-ilgen-static.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-ee-interp.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-icall-table.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-profiler-aot.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-profiler-browser.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-profiler-log.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-wasm-eh-wasm.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmono-wasm-simd.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libmonosgen-2.0.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libSystem.Globalization.Native.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libSystem.IO.Compression.Native.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libSystem.Native.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\libz.a"
"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Runtime.Mono.browser-wasm\9.0.2\runtimes\browser-wasm\native\wasm-bundled-timezones.a"
-o "D:\work\wms2025\bnred.Client\obj\Debug\net9.0\wasm\for-build\dotnet.native.js"
-s DEFAULT_LIBRARY_FUNCS_TO_INCLUDE="[]"
-s EXPORTED_RUNTIME_METHODS="['FS','out','err','ccall','cwrap','setValue','getValue','UTF8ToString','UTF8ArrayToString','lengthBytesUTF8','stringToUTF8Array','FS_createPath','FS_createDataFile','removeRunDependency','addRunDependency','addFunction','safeSetTimeout','runtimeKeepalivePush','runtimeKeepalivePop','maybeExit','abort','wasmExports']"
-s EXPORTED_FUNCTIONS=_free,_htons,_malloc,_sbrk,_memalign,_memset,_ntohs,stackAlloc,stackRestore,stackSave,_emscripten_force_exit,_fmod,_atan2,_fma,_pow,_fmodf,_atan2f,_fmaf,_powf,_asin,_asinh,_acos,_acosh,_atan,_atanh,_cbrt,_cos,_cosh,_exp,_log,_log2,_log10,_sin,_sinh,_tan,_tanh,_asinf,_asinhf,_acosf,_acoshf,_atanf,_atanhf,_cbrtf,_cosf,_coshf,_expf,_logf,_log2f,_log10f,_sinf,_sinhf,_tanf,_tanhf
