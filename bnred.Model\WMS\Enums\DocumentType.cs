using System.ComponentModel.DataAnnotations;

namespace bnred.Model
{
    /// <summary>
    /// 单据类型（旧版）
    /// </summary>
    public enum LegacyDocumentType
    {
        /// <summary>
        /// 采购入库
        /// </summary>
        [Display(Name = "采购入库")]
        PurchaseIn = 1,

        /// <summary>
        /// 销售出库
        /// </summary>
        [Display(Name = "销售出库")]
        SalesOut = 2,

        /// <summary>
        /// 库存调整单
        /// </summary>
        [Display(Name = "库存调整单")]
        StockAdjustment = 3,

        /// <summary>
        /// 盘点单
        /// </summary>
        [Display(Name = "盘点单")]
        StockTaking = 4,

        /// <summary>
        /// 其他入库
        /// </summary>
        [Display(Name = "其他入库")]
        OtherIn = 5,

        /// <summary>
        /// 其他出库
        /// </summary>
        [Display(Name = "其他出库")]
        OtherOut = 6,

        /// <summary>
        /// 生产入库
        /// </summary>
        [Display(Name = "生产入库")]
        ProductionIn = 7,

        /// <summary>
        /// 生产领料
        /// </summary>
        [Display(Name = "生产领料")]
        ProductionOut = 8,

        /// <summary>
        /// 退货入库
        /// </summary>
        [Display(Name = "退货入库")]
        ReturnIn = 9,

        /// <summary>
        /// 退货出库
        /// </summary>
        [Display(Name = "退货出库")]
        ReturnOut = 10
    }
} 