using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 成本异常等级
    /// 用于标识成本分析中的异常程度
    /// </summary>
    public enum CostAnomalyLevel
    {
        /// <summary>
        /// 正常 - 成本在预期范围内
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 轻微 - 成本略有偏差但在可接受范围内
        /// </summary>
        [Display(Name = "轻微")]
        Minor = 1,

        /// <summary>
        /// 中等 - 成本偏差明显但未到达严重程度
        /// </summary>
        [Display(Name = "中等")]
        Moderate = 2,

        /// <summary>
        /// 严重 - 成本偏差显著超出预期
        /// </summary>
        [Display(Name = "严重")]
        Severe = 3,

        /// <summary>
        /// 极端 - 成本偏差极大需要立即处理
        /// </summary>
        [Display(Name = "极端")]
        Critical = 4
    }

    /// <summary>
    /// 成本分析类型
    /// 定义不同的成本分析维度
    /// </summary>
    public enum CostAnalysisType
    {
        /// <summary>
        /// 物料成本分析
        /// </summary>
        [Display(Name = "物料成本分析")]
        Material = 0,

        /// <summary>
        /// 库存成本分析
        /// </summary>
        [Display(Name = "库存成本分析")]
        Inventory = 1,

        /// <summary>
        /// 仓储成本分析
        /// </summary>
        [Display(Name = "仓储成本分析")]
        Warehousing = 2,

        /// <summary>
        /// 运营成本分析
        /// </summary>
        [Display(Name = "运营成本分析")]
        Operation = 3,

        /// <summary>
        /// 综合成本分析
        /// </summary>
        [Display(Name = "综合成本分析")]
        Comprehensive = 4
    }

    /// <summary>
    /// 成本变动趋势
    /// 描述成本变动的方向和速度
    /// </summary>
    public enum CostTrend
    {
        /// <summary>
        /// 稳定 - 成本无明显变化
        /// </summary>
        [Display(Name = "稳定")]
        Stable = 0,

        /// <summary>
        /// 缓慢上升 - 成本呈现缓慢上涨趋势
        /// </summary>
        [Display(Name = "缓慢上升")]
        SlowlyRising = 1,

        /// <summary>
        /// 迅速上升 - 成本呈现快速上涨趋势
        /// </summary>
        [Display(Name = "迅速上升")]
        RapidlyRising = 2,

        /// <summary>
        /// 缓慢下降 - 成本呈现缓慢下降趋势
        /// </summary>
        [Display(Name = "缓慢下降")]
        SlowlyDecreasing = 3,

        /// <summary>
        /// 迅速下降 - 成本呈现快速下降趋势
        /// </summary>
        [Display(Name = "迅速下降")]
        RapidlyDecreasing = 4,

        /// <summary>
        /// 波动 - 成本上下波动无明显趋势
        /// </summary>
        [Display(Name = "波动")]
        Fluctuating = 5
    }
} 