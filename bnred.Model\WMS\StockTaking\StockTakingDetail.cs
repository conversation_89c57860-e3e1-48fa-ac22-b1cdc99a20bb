using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Inventory;
using bnred.Model.WMS.Models;
using bnred.Model.WMS.Enums.Inventory;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.StockTaking
{
    /// <summary>
    /// 盘点明细
    /// </summary>
    [Table("WMS_StockTakingDetails")]
    public class StockTakingDetail : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 盘点单ID
        /// </summary>
        [Required(ErrorMessage = "盘点单不能为空")]
        [Display(Name = "盘点单ID")]
        [StringLength(50)]
        public string StockTakingId { get; set; }

        /// <summary>
        /// 盘点单
        /// </summary>
        [Display(Name = "盘点单")]
        public virtual bnred.Model.WMS.Models.StockTaking StockTaking { get; set; }

        /// <summary>
        /// 库存ID
        /// </summary>
        [Required(ErrorMessage = "库存不能为空")]
        [Display(Name = "库存ID")]
        [StringLength(50)]
        public string InventoryId { get; set; }

        /// <summary>
        /// 库存
        /// </summary>
        [Display(Name = "库存")]
        public virtual Inventory.Inventory Inventory { get; set; }

        /// <summary>
        /// 账面数量
        /// </summary>
        [Required(ErrorMessage = "账面数量不能为空")]
        [Display(Name = "账面数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal BookQuantity { get; set; }

        /// <summary>
        /// 实盘数量
        /// </summary>
        [Required(ErrorMessage = "实盘数量不能为空")]
        [Display(Name = "实盘数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 差异数量
        /// </summary>
        [Required(ErrorMessage = "差异数量不能为空")]
        [Display(Name = "差异数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal DifferenceQuantity { get; set; }

        /// <summary>
        /// 差异原因
        /// </summary>
        [StringLength(500, ErrorMessage = "差异原因长度不能超过500个字符")]
        [Display(Name = "差异原因")]
        public string DifferenceReason { get; set; }

        /// <summary>
        /// 处理方式
        /// </summary>
        [StringLength(500, ErrorMessage = "处理方式长度不能超过500个字符")]
        [Display(Name = "处理方式")]
        public string ProcessingMethod { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}