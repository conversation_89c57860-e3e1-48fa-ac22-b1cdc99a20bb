using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Batch;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Quality
{
    /// <summary>
    /// 质量检验明细
    /// 用于记录质量检验的具体项目和结果
    /// </summary>
    [Table("WMS_QualityInspectionDetails")]
    public class QualityInspectionDetail : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 质检单ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string QualityInspectionId { get; set; }

        /// <summary>
        /// 质检单
        /// </summary>
        [Display(Name = "质检单")]
        public QualityInspection QualityInspection { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [StringLength(50)]
        public string BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public bnred.Model.WMS.Batch.Batch Batch { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public Location Location { get; set; }

        /// <summary>
        /// 计划检验数量
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "计划检验数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际检验数量
        /// </summary>
        [Display(Name = "实际检验数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? ActualQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Display(Name = "合格数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [Display(Name = "不合格数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(20, ErrorMessage = "{0}最多输入{1}个字符")]
        [Display(Name = "单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 检验状态
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "检验状态")]
        public InspectionDetailStatus Status { get; set; } = InspectionDetailStatus.Pending;

        /// <summary>
        /// 检验结果
        /// </summary>
        [Display(Name = "检验结果")]
        public InspectionResult? Result { get; set; }

        /// <summary>
        /// 检验时间
        /// </summary>
        [Display(Name = "检验时间")]
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 检验人ID
        /// </summary>
        [Display(Name = "检验人ID")]
 
        public Guid? InspectorId { get; set; }

 
        public FrameworkUser Inspector { get; set; }

        /// <summary>
        /// 不合格原因
        /// </summary>
        [StringLength(200, ErrorMessage = "{0}最多输入{1}个字符")]
        [Display(Name = "不合格原因")]
        public string UnqualifiedReason { get; set; }

        /// <summary>
        /// 处理方式
        /// </summary>
        [Display(Name = "处理方式")]
        public DisposalMethod? DisposalMethod { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
} 