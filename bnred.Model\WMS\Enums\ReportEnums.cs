using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 报表类型枚举
    /// 用于标识不同的报表类型
    /// </summary>
    public enum ReportType
    {
        /// <summary>
        /// 库存报表
        /// 显示库存相关统计数据
        /// </summary>
        [Display(Name = "库存报表")]
        Inventory = 0,

        /// <summary>
        /// 入库报表
        /// 显示入库相关统计数据
        /// </summary>
        [Display(Name = "入库报表")]
        Inbound = 1,

        /// <summary>
        /// 出库报表
        /// 显示出库相关统计数据
        /// </summary>
        [Display(Name = "出库报表")]
        Outbound = 2,

        /// <summary>
        /// 移库报表
        /// 显示移库相关统计数据
        /// </summary>
        [Display(Name = "移库报表")]
        Movement = 3,

        /// <summary>
        /// 盘点报表
        /// 显示盘点相关统计数据
        /// </summary>
        [Display(Name = "盘点报表")]
        StockTaking = 4,

        /// <summary>
        /// 绩效报表
        /// 显示作业绩效统计数据
        /// </summary>
        [Display(Name = "绩效报表")]
        Performance = 5,

        /// <summary>
        /// 成本报表
        /// 显示成本相关统计数据
        /// </summary>
        [Display(Name = "成本报表")]
        Cost = 6
    }

    /// <summary>
    /// 报表周期枚举
    /// 用于标识报表的统计周期
    /// </summary>
    public enum ReportPeriod
    {
        /// <summary>
        /// 实时报表
        /// 显示当前实时数据
        /// </summary>
        [Display(Name = "实时报表")]
        RealTime = 0,

        /// <summary>
        /// 日报表
        /// 显示每日统计数据
        /// </summary>
        [Display(Name = "日报表")]
        Daily = 1,

        /// <summary>
        /// 周报表
        /// 显示每周统计数据
        /// </summary>
        [Display(Name = "周报表")]
        Weekly = 2,

        /// <summary>
        /// 月报表
        /// 显示每月统计数据
        /// </summary>
        [Display(Name = "月报表")]
        Monthly = 3,

        /// <summary>
        /// 季报表
        /// 显示每季度统计数据
        /// </summary>
        [Display(Name = "季报表")]
        Quarterly = 4,

        /// <summary>
        /// 年报表
        /// 显示每年统计数据
        /// </summary>
        [Display(Name = "年报表")]
        Yearly = 5
    }

    /// <summary>
    /// 报表格式枚举
    /// 用于标识报表的输出格式
    /// </summary>
    public enum ReportFormat
    {
        /// <summary>
        /// Excel格式
        /// 输出为Excel文件
        /// </summary>
        [Display(Name = "Excel格式")]
        Excel = 0,

        /// <summary>
        /// PDF格式
        /// 输出为PDF文件
        /// </summary>
        [Display(Name = "PDF格式")]
        PDF = 1,

        /// <summary>
        /// Word格式
        /// 输出为Word文件
        /// </summary>
        [Display(Name = "Word格式")]
        Word = 2,

        /// <summary>
        /// HTML格式
        /// 输出为HTML文件
        /// </summary>
        [Display(Name = "HTML格式")]
        HTML = 3,

        /// <summary>
        /// CSV格式
        /// 输出为CSV文件
        /// </summary>
        [Display(Name = "CSV格式")]
        CSV = 4
    }
} 