﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;


namespace bnred.ViewModel.Warehouse.WarehouseVMs
{
    public partial class WarehouseListVM : BasePagedListVM<Warehouse_View, WarehouseSearcher>
    {

        protected override IEnumerable<IGridColumn<Warehouse_View>> InitGridHeader()
        {
            return new List<GridColumn<Warehouse_View>>{
                this.MakeGridHeader(x => x.ID),
                this.MakeGridHeader(x => x.Code),
                this.MakeGridHeader(x => x.Name),
                this.MakeGridHeader(x => x.Type),
                this.MakeGridHeader(x => x.Status),
                this.MakeGridHeader(x => x.Address),
                this.MakeGridHeader(x => x.<PERSON>erson),
                this.MakeGridHeader(x => x.ContactPhone),
                this.MakeGridHeader(x => x.Area),
                this.MakeGridHeader(x => x.Volume),
                this.MakeGridHeader(x => x.Name_view),
                this.MakeGridHeader(x => x.Description),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<Warehouse_View> GetSearchQuery()
        {
            var query = DC.Set<bnred.Model.WMS.Warehouse.Warehouse>()
                .CheckContain(Searcher.ID, x=>x.ID)
                .CheckContain(Searcher.Code, x=>x.Code)
                .CheckContain(Searcher.Name, x=>x.Name)
                .CheckEqual(Searcher.Type, x=>x.Type)
                .CheckEqual(Searcher.Status, x=>x.Status)
                .CheckContain(Searcher.Address, x=>x.Address)
                .CheckContain(Searcher.ContactPerson, x=>x.ContactPerson)
                .CheckContain(Searcher.ContactPhone, x=>x.ContactPhone)
                .CheckEqual(Searcher.Area, x=>x.Area)
                .CheckEqual(Searcher.Volume, x=>x.Volume)
                .CheckEqual(Searcher.ManagerID, x=>x.ManagerID)
                .CheckContain(Searcher.Description, x=>x.Description)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new Warehouse_View
                {
				    ID = x.ID,
                    Code = x.Code,
                    Name = x.Name,
                    Type = x.Type,
                    Status = x.Status,
                    Address = x.Address,
                    ContactPerson = x.ContactPerson,
                    ContactPhone = x.ContactPhone,
                    Area = x.Area,
                    Volume = x.Volume,
                    Name_view = x.Manager.Name,
                    Description = x.Description,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class Warehouse_View : bnred.Model.WMS.Warehouse.Warehouse
    {
        [Display(Name = "_Admin.Name")]
        public String Name_view { get; set; }

    }
}
