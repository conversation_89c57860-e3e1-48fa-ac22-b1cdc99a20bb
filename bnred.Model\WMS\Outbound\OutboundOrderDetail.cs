using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Outbound
{
    /// <summary>
    /// 出库单明细
    /// </summary>
    [Table("WMS_OutboundOrderDetails")]
    public class OutboundOrderDetail : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 出库单ID
        /// </summary>
        [Required]
        [Display(Name = "出库单ID")]
        public Guid OutboundOrderId { get; set; }

        /// <summary>
        /// 出库单
        /// </summary>
        [Display(Name = "出库单")]
        public virtual OutboundOrder OutboundOrder { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料ID")]
        public Guid MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public virtual Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次ID")]
        public Guid? BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public virtual Batch.Batch Batch { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [Display(Name = "库位ID")]
        public Guid LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public virtual Warehouse.Location Location { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        [Required]
        [Display(Name = "计划数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        [Display(Name = "实际数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? ActualQuantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}