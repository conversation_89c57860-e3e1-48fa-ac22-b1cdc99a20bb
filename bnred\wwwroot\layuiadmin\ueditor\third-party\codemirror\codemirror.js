/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;var CodeMirror=function(){function t(o,u){function v(t){return t>=0&&t<$e.size}function k(t){return h($e,t)}function L(t,e){sn=!0;for(var n=e-t.height,r=t;r;r=r.parent)r.height+=n}function A(t){var e={line:0,ch:0};lt(e,{line:$e.size-1,ch:k($e.size-1).text.length},X(t),e,e),en=!0}function P(t){var e=[];return $e.iter(0,$e.size,function(t){e.push(t.text)}),e.join("\n")}function U(t){function e(t){var n=me(t,!0);if(n&&!E(n,l)){_e||it(),l=n,Wt(r,n),en=!1;var i=bt();(n.line>=i.to||n.line<i.from)&&(o=setTimeout(Le(function(){e(t)}),150))}}Et(t.shiftKey);for(var n=w(t);n!=We;n=n.parentNode)if(n.parentNode==Ie&&n!=Oe)return;for(var n=w(t);n!=We;n=n.parentNode)if(n.parentNode==Pe)return Ae.onGutterClick&&Ae.onGutterClick(wn,I(Pe.childNodes,n)+mn,t),y(t);var r=me(t);switch(M(t)){case 3:return void(j&&!R&&ge(t));case 2:return void(r&&Ht(r.line,r.ch,!0))}if(!r)return void(w(t)==He&&y(t));_e||it();var i=+new Date;if(Qe&&Qe.time>i-400&&E(Qe.pos,r))return y(t),setTimeout(kt,20),Kt(r.line);if(Je&&Je.time>i-400&&E(Je.pos,r))return Qe={time:i,pos:r},y(t),Vt(r);Je={time:i,pos:r};var o,l=r;if(F&&!E(hn.from,hn.to)&&!W(r,hn.from)&&!W(hn.to,r)){Y&&(Ue.draggable=!0);var a=b(Ee,"mouseup",Le(function(e){Y&&(Ue.draggable=!1),tn=!1,a(),Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)<10&&(y(e),Ht(r.line,r.ch,!0),kt())}),!0);return void(tn=!0)}y(t),Ht(r.line,r.ch,!0);var s=b(Ee,"mousemove",Le(function(t){clearTimeout(o),y(t),e(t)}),!0),a=b(Ee,"mouseup",Le(function(t){clearTimeout(o);var e=me(t);e&&Wt(r,e),y(t),kt(),en=!0,s(),a()}),!0)}function _(t){for(var e=w(t);e!=We;e=e.parentNode)if(e.parentNode==Pe)return y(t);var n=me(t);n&&(Qe={time:+new Date,pos:n},y(t),Vt(n))}function J(t){function e(t,e){var r=new FileReader;r.onload=function(){o[e]=r.result,++l==i&&(n=Ot(n),Le(function(){var t=ct(o.join(""),n,n);Wt(n,t)})())},r.readAsText(t)}t.preventDefault();var n=me(t,!0),r=t.dataTransfer.files;if(n&&!Ae.readOnly)if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),l=0,a=0;a<i;++a)e(r[a],a);else try{var o=t.dataTransfer.getData("Text");if(o){var s=ct(o,n,n),u=hn.from,f=hn.to;Wt(n,s),tn&&ct("",u,f),kt()}}catch(t){}}function Q(t){var e=mt();D(e),t.dataTransfer.setDragImage(q,0,0),t.dataTransfer.setData("Text",e)}function tt(t){var r,i,o=Z[t.keyCode],l=K[Ae.keyMap].auto;if(null==o||t.altGraphKey)return l&&(Ae.keyMap=l),null;if(t.altKey&&(o="Alt-"+o),t.ctrlKey&&(o="Ctrl-"+o),t.metaKey&&(o="Cmd-"+o),t.shiftKey&&(r=e("Shift-"+o,Ae.extraKeys,Ae.keyMap))?i=!0:r=e(o,Ae.extraKeys,Ae.keyMap),"string"==typeof r&&(r=V.propertyIsEnumerable(r)?V[r]:null),!l||!r&&n(t)||(Ae.keyMap=l),!r)return!1;if(i){var a=Ze;Ze=null,r(wn),Ze=a}else r(wn);return y(t),!0}function et(t){_e||it();var e=t.keyCode;if(G&&27==e&&(t.returnValue=!1),Et(16==e||t.shiftKey),!Ae.onKeyEvent||!Ae.onKeyEvent(wn,x(t))){var n=tt(t);window.opera&&(Mn=n?t.keyCode:null,!n&&(R?t.metaKey:t.ctrlKey)&&88==t.keyCode&&ht(""))}}function nt(t){if(window.opera&&t.keyCode==Mn)return Mn=null,void y(t);if((!Ae.onKeyEvent||!Ae.onKeyEvent(wn,x(t)))&&(!window.opera||t.which||!tt(t))){if(Ae.electricChars&&Ge.electricChars){var e=String.fromCharCode(null==t.charCode?t.keyCode:t.charCode);Ge.electricChars.indexOf(e)>-1&&setTimeout(Le(function(){jt(hn.to.line,"smart")}),75)}vt()}}function rt(t){Ae.onKeyEvent&&Ae.onKeyEvent(wn,x(t))||16==t.keyCode&&(Ze=null)}function it(){Ae.readOnly||(_e||(Ae.onFocus&&Ae.onFocus(wn),_e=!0,We.className.search(/\bCodeMirror-focused\b/)==-1&&(We.className+=" CodeMirror-focused"),an||yt(!0)),gt(),ve())}function ot(){_e&&(Ae.onBlur&&Ae.onBlur(wn),_e=!1,We.className=We.className.replace(" CodeMirror-focused","")),clearInterval(je),setTimeout(function(){_e||(Ze=null)},150)}function lt(t,e,n,r,i){if(kn){var o=[];for($e.iter(t.line,e.line+1,function(t){o.push(t.text)}),kn.addChange(t.line,n.length,o);kn.done.length>Ae.undoDepth;)kn.done.shift()}ft(t,e,n,r,i)}function at(t,e){var n=t.pop();if(n){var r=[],i=n.start+n.added;$e.iter(n.start,i,function(t){r.push(t.text)}),e.push({start:n.start,added:n.old.length,old:r});var o=Ot({line:n.start+n.old.length-1,ch:H(r[r.length-1],n.old[n.old.length-1])});ft({line:n.start,ch:0},{line:i-1,ch:k(i-1).text.length},n.old,o,o),en=!0}}function st(){at(kn.done,kn.undone)}function ut(){at(kn.undone,kn.done)}function ft(t,e,n,r,i){function o(t){return t<=Math.min(e.line,e.line+x)?t:t+x}var l=!1,a=xn.length;Ae.lineWrapping||$e.iter(t.line,e.line,function(t){if(t.text.length==a)return l=!0,!0}),(t.line!=e.line||n.length>1)&&(sn=!0);var u=e.line-t.line,f=k(t.line),c=k(e.line);if(0==t.ch&&0==e.ch&&""==n[n.length-1]){var h=[],d=null;t.line?(d=k(t.line-1),d.fixMarkEnds(c)):c.fixMarkStarts();for(var p=0,m=n.length-1;p<m;++p)h.push(s.inheritMarks(n[p],d));u&&$e.remove(t.line,u,un),h.length&&$e.insert(t.line,h)}else if(f==c)if(1==n.length)f.replace(t.ch,e.ch,n[0]);else{c=f.split(e.ch,n[n.length-1]),f.replace(t.ch,null,n[0]),f.fixMarkEnds(c);for(var h=[],p=1,m=n.length-1;p<m;++p)h.push(s.inheritMarks(n[p],f));h.push(c),$e.insert(t.line+1,h)}else if(1==n.length)f.replace(t.ch,null,n[0]),c.replace(null,e.ch,""),f.append(c),$e.remove(t.line+1,u,un);else{var h=[];f.replace(t.ch,null,n[0]),c.replace(null,e.ch,n[n.length-1]),f.fixMarkEnds(c);for(var p=1,m=n.length-1;p<m;++p)h.push(s.inheritMarks(n[p],f));u>1&&$e.remove(t.line+1,u-1,un),$e.insert(t.line+1,h)}if(Ae.lineWrapping){var g=He.clientWidth/he()-3;$e.iter(t.line,t.line+n.length,function(t){if(!t.hidden){var e=Math.ceil(t.text.length/g)||1;e!=t.height&&L(t,e)}})}else $e.iter(t.line,p+n.length,function(t){var e=t.text;e.length>a&&(xn=e,a=e.length,cn=null,l=!1)}),l&&(a=0,xn="",cn=null,$e.iter(0,$e.size,function(t){var e=t.text;e.length>a&&(a=e.length,xn=e)}));for(var v=[],x=n.length-u-1,p=0,y=Ye.length;p<y;++p){var C=Ye[p];C<t.line?v.push(C):C>e.line&&v.push(C+x)}var w=t.line+Math.min(n.length,500);Ce(t.line,w),v.push(w),Ye=v,Me(100),rn.push({from:t.line,to:e.line+1,diff:x});var M={from:t,to:e,text:n};if(on){for(var b=on;b.next;b=b.next);b.next=M}else on=M;Nt(r,i,o(hn.from.line),o(hn.to.line)),Ie.style.height=$e.height*ce()+2*de()+"px"}function ct(t,e,n){function r(r){if(W(r,e))return r;if(!W(n,r))return i;var o=r.line+t.length-(n.line-e.line)-1,l=r.ch;return r.line==n.line&&(l+=t[t.length-1].length-(n.ch-(n.line==e.line?e.ch:0))),{line:o,ch:l}}e=Ot(e),n=n?Ot(n):e,t=X(t);var i;return dt(t,e,n,function(t){return i=t,{from:r(hn.from),to:r(hn.to)}}),i}function ht(t,e){dt(X(t),hn.from,hn.to,function(t){return"end"==e?{from:t,to:t}:"start"==e?{from:hn.from,to:hn.from}:{from:hn.from,to:t}})}function dt(t,e,n,r){var i=1==t.length?t[0].length+e.ch:t[t.length-1].length,o=r({line:e.line+t.length-1,ch:i});lt(e,n,t,o.from,o.to)}function pt(t,e){var n=t.line,r=e.line;if(n==r)return k(n).text.slice(t.ch,e.ch);var i=[k(n).text.slice(t.ch)];return $e.iter(n+1,r,function(t){i.push(t.text)}),i.push(k(r).text.slice(0,e.ch)),i.join("\n")}function mt(){return pt(hn.from,hn.to)}function gt(){bn||qe.set(Ae.pollInterval,function(){be(),xt(),_e&&gt(),Se()})}function vt(){function t(){be();var n=xt();n||e?(bn=!1,gt()):(e=!0,qe.set(60,t)),Se()}var e=!1;bn=!0,qe.set(20,t)}function xt(){if(an||!_e||$(De))return!1;var t=De.value;if(t==Sn)return!1;Ze=null;for(var e=0,n=Math.min(Sn.length,t.length);e<n&&Sn[e]==t[e];)++e;return e<Sn.length?hn.from={line:hn.from.line,ch:hn.from.ch-(Sn.length-e)}:dn&&E(hn.from,hn.to)&&(hn.to={line:hn.to.line,ch:Math.min(k(hn.to.line).text.length,hn.to.ch+(t.length-e))}),ht(t.slice(e),"end"),Sn=t,!0}function yt(t){E(hn.from,hn.to)?t&&(Sn=De.value=""):(Sn="",De.value=mt(),De.select())}function kt(){Ae.readOnly||De.focus()}function Ct(){if(Ve.getBoundingClientRect){var t=Ve.getBoundingClientRect();if(!G||t.top!=t.bottom){var e=window.innerHeight||Math.max(document.body.offsetHeight,document.documentElement.offsetHeight);(t.top<0||t.bottom>e)&&Ve.scrollIntoView()}}}function wt(){var t=se(hn.inverted?hn.from:hn.to),e=Ae.lineWrapping?Math.min(t.x,Ue.offsetWidth):t.x;return Mt(e,t.y,e,t.yBot)}function Mt(t,e,n,r){var i=pe(),o=de(),l=ce();e+=o,r+=o,t+=i,n+=i;var a=He.clientHeight,s=He.scrollTop,u=!1,f=!0;e<s?(He.scrollTop=Math.max(0,e-2*l),u=!0):r>s+a&&(He.scrollTop=r+l-a,u=!0);var c=He.clientWidth,h=He.scrollLeft,d=Ae.fixedGutter?Re.clientWidth:0;return t<h+d?(t<50&&(t=0),He.scrollLeft=Math.max(0,t-10-d),u=!0):n>c+h-3&&(He.scrollLeft=n+10-c,u=!0,n>Ie.clientWidth&&(f=!1)),u&&Ae.onScroll&&Ae.onScroll(wn),f}function bt(){var t=ce(),e=He.scrollTop-de(),n=Math.max(0,Math.floor(e/t)),r=Math.ceil((e+He.clientHeight)/t);return{from:p($e,n),to:p($e,r)}}function St(t,e){if(!He.clientWidth)return void(mn=gn=pn=0);var n=bt();if(!(t!==!0&&0==t.length&&n.from>=mn&&n.to<=gn)){var r=Math.max(n.from-100,0),i=Math.min($e.size,n.to+100);mn<r&&r-mn<20&&(r=mn),gn>i&&gn-i<20&&(i=Math.min($e.size,gn));for(var o=t===!0?[]:Lt([{from:mn,to:gn,domStart:0}],t),l=0,a=0;a<o.length;++a){var s=o[a];s.from<r&&(s.domStart+=r-s.from,s.from=r),s.to>i&&(s.to=i),s.from>=s.to?o.splice(a--,1):l+=s.to-s.from}if(l!=i-r){o.sort(function(t,e){return t.domStart-e.domStart});var u=ce(),f=Re.style.display;Ke.style.display=Re.style.display="none",At(r,i,o),Ke.style.display="";var c=r!=mn||i!=gn||vn!=He.clientHeight+u;if(c&&(vn=He.clientHeight+u),mn=r,gn=i,pn=m($e,r),Oe.style.top=pn*u+"px",Ie.style.height=$e.height*u+2*de()+"px",Ke.childNodes.length!=gn-mn)throw new Error("BAD PATCH! "+JSON.stringify(o)+" size="+(gn-mn)+" nodes="+Ke.childNodes.length);if(Ae.lineWrapping){cn=He.clientWidth;var h=Ke.firstChild;$e.iter(mn,gn,function(t){if(!t.hidden){var e=Math.round(h.offsetHeight/u)||1;t.height!=e&&(L(t,e),sn=!0)}h=h.nextSibling})}else null==cn&&(cn=le(xn)),cn>He.clientWidth?(Ue.style.width=cn+"px",Ie.style.width="",Ie.style.width=He.scrollWidth+"px"):Ue.style.width=Ie.style.width="";return Re.style.display=f,(c||sn)&&zt(),Tt(),!e&&Ae.onUpdate&&Ae.onUpdate(wn),!0}}}function Lt(t,e){for(var n=0,r=e.length||0;n<r;++n){for(var i=e[n],o=[],l=i.diff||0,a=0,s=t.length;a<s;++a){var u=t[a];i.to<=u.from&&i.diff?o.push({from:u.from+l,to:u.to+l,domStart:u.domStart}):i.to<=u.from||i.from>=u.to?o.push(u):(i.from>u.from&&o.push({from:u.from,to:i.from,domStart:u.domStart}),i.to<u.to&&o.push({from:i.to+l,to:u.to+l,domStart:u.domStart+(i.to-u.from)}))}t=o}return t}function At(t,e,n){function r(t){var e=t.nextSibling;return t.parentNode.removeChild(t),e}if(n.length){for(var i=0,o=Ke.firstChild,l=0;l<n.length;++l){for(var a=n[l];a.domStart>i;)o=r(o),i++;for(var s=0,u=a.to-a.from;s<u;++s)o=o.nextSibling,i++}for(;o;)o=r(o)}else Ke.innerHTML="";var f=n.shift(),o=Ke.firstChild,s=t,c=hn.from.line,h=hn.to.line,d=c<t&&h>=t,p=Ee.createElement("div");$e.iter(t,e,function(t){var e=null,r=null;d?(e=0,h==s&&(d=!1,r=hn.to.ch)):c==s&&(h==s?(e=hn.from.ch,r=hn.to.ch):(d=!0,e=hn.from.ch)),f&&f.to==s&&(f=n.shift()),!f||f.from>s?(t.hidden?p.innerHTML="<pre></pre>":p.innerHTML=t.getHTML(e,r,!0,yn),Ke.insertBefore(p.firstChild,o)):o=o.nextSibling,++s})}function zt(){if(Ae.gutter||Ae.lineNumbers){var t=Oe.offsetHeight,e=He.clientHeight;Re.style.height=(t-e<2?e:t)+"px";var n=[],r=mn;$e.iter(mn,Math.max(gn,mn+1),function(t){if(t.hidden)n.push("<pre></pre>");else{var e=t.gutterMarker,i=Ae.lineNumbers?r+Ae.firstLineNumber:null;e&&e.text?i=e.text.replace("%N%",null!=i?i:""):null==i&&(i=" "),n.push(e&&e.style?'<pre class="'+e.style+'">':"<pre>",i);for(var o=1;o<t.height;++o)n.push("<br/>&#160;");n.push("</pre>")}++r}),Re.style.display="none",Pe.innerHTML=n.join("");for(var i=String($e.size).length,o=Pe.firstChild,l=T(o),a="";l.length+a.length<i;)a+=" ";a&&o.insertBefore(Ee.createTextNode(a),o.firstChild),Re.style.display="",Ue.style.marginLeft=Re.offsetWidth+"px",sn=!1}}function Tt(){var t=hn.inverted?hn.from:hn.to,e=(ce(),se(t,!0)),n=z(We),r=z(Ke);Ne.style.top=e.y+r.top-n.top+"px",Ne.style.left=e.x+r.left-n.left+"px",E(hn.from,hn.to)?(Ve.style.top=e.y+"px",Ve.style.left=(Ae.lineWrapping?Math.min(e.x,Ue.offsetWidth):e.x)+"px",Ve.style.display=""):Ve.style.display="none"}function Et(t){Ze=t?Ze||(hn.inverted?hn.to:hn.from):null}function Wt(t,e){var n=Ze&&Ot(Ze);n&&(W(n,t)?t=n:W(e,n)&&(e=n)),Nt(t,e),nn=!0}function Nt(t,e,n,r){if(Ln=null,null==n&&(n=hn.from.line,r=hn.to.line),!E(hn.from,t)||!E(hn.to,e)){if(W(e,t)){var i=e;e=t,t=i}t.line!=n&&(t=Dt(t,n,hn.from.ch)),e.line!=r&&(e=Dt(e,r,hn.to.ch)),E(t,e)?hn.inverted=!1:E(t,hn.to)?hn.inverted=!1:E(e,hn.from)&&(hn.inverted=!0),E(t,e)?E(hn.from,hn.to)||rn.push({from:n,to:r+1}):E(hn.from,hn.to)?rn.push({from:t.line,to:e.line+1}):(E(t,hn.from)||(t.line<n?rn.push({from:t.line,to:Math.min(e.line,n)+1}):rn.push({from:n,to:Math.min(r,t.line)+1})),E(e,hn.to)||(e.line<r?rn.push({from:Math.max(n,t.line),to:r+1}):rn.push({from:Math.max(t.line,r),to:e.line+1}))),hn.from=t,hn.to=e,ln=!0}}function Dt(t,e,n){function r(e){for(var r=t.line+e,i=1==e?$e.size:-1;r!=i;){var o=k(r);if(!o.hidden){var l=t.ch;return(l>n||l>o.text.length)&&(l=o.text.length),{line:r,ch:l}}r+=e}}var i=k(t.line);return i.hidden?t.line>=e?r(1)||r(-1):r(-1)||r(1):t}function Ht(t,e,n){var r=Ot({line:t,ch:e||0});(n?Wt:Nt)(r,r)}function It(t){return Math.max(0,Math.min(t,$e.size-1))}function Ot(t){if(t.line<0)return{line:0,ch:0};if(t.line>=$e.size)return{line:$e.size-1,ch:k($e.size-1).text.length};var e=t.ch,n=k(t.line).text.length;return null==e||e>n?{line:t.line,ch:n}:e<0?{line:t.line,ch:0}:t}function Rt(t,e){function n(){for(var e=o+t,n=t<0?-1:$e.size;e!=n;e+=t){var r=k(e);if(!r.hidden)return o=e,a=r,!0}}function r(e){if(l==(t<0?0:a.text.length)){if(e||!n())return!1;l=t<0?a.text.length:0}else l+=t;return!0}var i=hn.inverted?hn.from:hn.to,o=i.line,l=i.ch,a=k(o);if("char"==e)r();else if("column"==e)r(!0);else if("word"==e)for(var s=!1;!(t<0)||r();){if(O(a.text.charAt(l)))s=!0;else if(s){t<0&&(t=1,r());break}if(t>0&&!r())break}return{line:o,ch:l}}function Pt(t,e){var n=t<0?hn.from:hn.to;(Ze||E(hn.from,hn.to))&&(n=Rt(t,e)),Ht(n.line,n.ch,!0)}function Ut(t,e){E(hn.from,hn.to)?t<0?ct("",Rt(t,e),hn.to):ct("",hn.from,Rt(t,e)):ct("",hn.from,hn.to),nn=!0}function Bt(t,e){var n=0,r=se(hn.inverted?hn.from:hn.to,!0);null!=Ln&&(r.x=Ln),"page"==e?n=He.clientHeight:"line"==e&&(n=ce());var i=ue(r.x,r.y+n*t+2);Ht(i.line,i.ch,!0),Ln=r.x}function Vt(t){for(var e=k(t.line).text,n=t.ch,r=t.ch;n>0&&O(e.charAt(n-1));)--n;for(;r<e.length&&O(e.charAt(r));)++r;Wt({line:t.line,ch:n},{line:t.line,ch:r})}function Kt(t){Wt({line:t,ch:0},{line:t,ch:k(t).text.length})}function Ft(t){if(E(hn.from,hn.to))return jt(hn.from.line,t);for(var e=hn.to.line-(hn.to.ch?0:1),n=hn.from.line;n<=e;++n)jt(n,t)}function jt(t,e){if(e||(e="add"),"smart"==e)if(Ge.indent)var n=ke(t);else e="prev";var r,i=k(t),o=i.indentation(Ae.tabSize),l=i.text.match(/^\s*/)[0];"prev"==e?r=t?k(t-1).indentation(Ae.tabSize):0:"smart"==e?r=Ge.indent(n,i.text.slice(l.length),i.text):"add"==e?r=o+Ae.indentUnit:"subtract"==e&&(r=o-Ae.indentUnit),r=Math.max(0,r);var a=r-o;if(a){var s="",u=0;if(Ae.indentWithTabs)for(var f=Math.floor(r/Ae.tabSize);f;--f)u+=Ae.tabSize,s+="\t";for(;u<r;)++u,s+=" "}else{if(hn.from.line!=t&&hn.to.line!=t)return;var s=l}ct(s,{line:t,ch:0},{line:t,ch:l.length})}function Gt(){Ge=t.getMode(Ae,Ae.mode),$e.iter(0,$e.size,function(t){t.stateAfter=null}),Ye=[0],Me()}function Yt(){var t=Ae.gutter||Ae.lineNumbers;Re.style.display=t?"":"none",t?sn=!0:Ke.parentNode.style.marginLeft=0}function _t(t,e){if(Ae.lineWrapping){We.className+=" CodeMirror-wrap";var n=He.clientWidth/he()-3;$e.iter(0,$e.size,function(t){if(!t.hidden){var e=Math.ceil(t.text.length/n)||1;1!=e&&L(t,e)}}),Ue.style.width=Ie.style.width=""}else We.className=We.className.replace(" CodeMirror-wrap",""),cn=null,xn="",$e.iter(0,$e.size,function(t){1==t.height||t.hidden||L(t,1),t.text.length>xn.length&&(xn=t.text)});rn.push({from:0,to:$e.size})}function qt(){for(var t='<span class="cm-tab">',e=0;e<Ae.tabSize;++e)t+=" ";return t+"</span>"}function Xt(){yn=qt(),St(!0)}function $t(){He.className=He.className.replace(/\s*cm-s-\w+/g,"")+Ae.theme.replace(/(^|\s)\s*/g," cm-s-")}function Zt(){this.set=[]}function Jt(t,e,n){function r(t,e,n,r){k(t).addMark(new l(e,n,r,i.set))}t=Ot(t),e=Ot(e);var i=new Zt;if(t.line==e.line)r(t.line,t.ch,e.ch,n);else{r(t.line,t.ch,null,n);for(var o=t.line+1,a=e.line;o<a;++o)r(o,null,null,n);r(e.line,null,e.ch,n)}return rn.push({from:t.line,to:e.line+1}),i}function Qt(t){t=Ot(t);var e=new a(t.ch);return k(t.line).addMark(e),e}function te(t,e,n){return"number"==typeof t&&(t=k(It(t))),t.gutterMarker={text:e,style:n},sn=!0,t}function ee(t){"number"==typeof t&&(t=k(It(t))),t.gutterMarker=null,sn=!0}function ne(t,e){var n=t,r=t;return"number"==typeof t?r=k(It(t)):n=d(t),null==n?null:e(r,n)?(rn.push({from:n,to:n+1}),r):null}function re(t,e){return ne(t,function(t){if(t.className!=e)return t.className=e,!0})}function ie(t,e){return ne(t,function(t,n){if(t.hidden!=e)return t.hidden=e,L(t,e?0:1),!e||hn.from.line!=n&&hn.to.line!=n||Nt(Dt(hn.from,hn.from.line,hn.from.ch),Dt(hn.to,hn.to.line,hn.to.ch)),sn=!0})}function oe(t){if("number"==typeof t){if(!v(t))return null;var e=t;if(t=k(t),!t)return null}else{var e=d(t);if(null==e)return null}var n=t.gutterMarker;return{line:e,handle:t,text:t.text,markerText:n&&n.text,markerClass:n&&n.style,lineClass:t.className}}function le(t){return Be.innerHTML="<pre><span>x</span></pre>",Be.firstChild.firstChild.firstChild.nodeValue=t,Be.firstChild.firstChild.offsetWidth||10}function ae(t,e){var n="";if(Ae.lineWrapping){var r=t.text.indexOf(" ",e+2);n=D(t.text.slice(e+1,r<0?t.text.length:r+(G?5:0)))}Be.innerHTML="<pre>"+t.getHTML(null,null,!1,yn,e)+'<span id="CodeMirror-temp-'+Wn+'">'+D(t.text.charAt(e)||" ")+"</span>"+n+"</pre>";var i=document.getElementById("CodeMirror-temp-"+Wn),o=i.offsetTop,l=i.offsetLeft;if(G&&e&&0==o&&0==l){var a=document.createElement("span");a.innerHTML="x",i.parentNode.insertBefore(a,i.nextSibling),o=a.offsetTop}return{top:o,left:l}}function se(t,e){var n,r=ce(),i=r*(m($e,t.line)-(e?pn:0));if(0==t.ch)n=0;else{var o=ae(k(t.line),t.ch);n=o.left,Ae.lineWrapping&&(i+=Math.max(0,o.top))}return{x:n,y:i,yBot:i+r}}function ue(t,e){function n(t){var e=ae(a,t);if(u){var n=Math.round(e.top/r);return Math.max(0,e.left+(n-f)*He.clientWidth)}return e.left}e<0&&(e=0);var r=ce(),i=he(),o=pn+Math.floor(e/r),l=p($e,o);if(l>=$e.size)return{line:$e.size-1,ch:k($e.size-1).text.length};var a=k(l),s=a.text,u=Ae.lineWrapping,f=u?o-m($e,l):0;if(t<=0&&0==f)return{line:l,ch:0};for(var c,h=0,d=0,g=s.length,v=Math.min(g,Math.ceil((t+f*He.clientWidth*.9)/i));;){var x=n(v);if(!(x<=t&&v<g)){c=x,g=v;break}v=Math.min(g,Math.ceil(1.2*v))}if(t>c)return{line:l,ch:g};for(v=Math.floor(.8*g),x=n(v),x<t&&(h=v,d=x);;){if(g-h<=1)return{line:l,ch:c-t>t-d?h:g};var y=Math.ceil((h+g)/2),C=n(y);C>t?(g=y,c=C):(h=y,d=C)}}function fe(t){var e=se(t,!0),n=z(Ue);return{x:n.left+e.x,y:n.top+e.y,yBot:n.top+e.yBot}}function ce(){if(null==Tn){Tn="<pre>";for(var t=0;t<49;++t)Tn+="x<br/>";Tn+="x</pre>"}var e=Ke.clientHeight;return e==zn?An:(zn=e,Be.innerHTML=Tn,An=Be.firstChild.offsetHeight/50||1,Be.innerHTML="",An)}function he(){return He.clientWidth==Nn?En:(Nn=He.clientWidth,En=le("x"))}function de(){return Ue.offsetTop}function pe(){return Ue.offsetLeft}function me(t,e){var n,r,i=z(He,!0);try{n=t.clientX,r=t.clientY}catch(t){return null}if(!e&&(n-i.left>He.clientWidth||r-i.top>He.clientHeight))return null;var o=z(Ue,!0);return ue(n-o.left,r-o.top)}function ge(t){function e(){var t=X(De.value).join("\n");t!=i&&Le(ht)(t,"end"),Ne.style.position="relative",De.style.cssText=r,an=!1,yt(!0),gt()}var n=me(t);if(n&&!window.opera){(E(hn.from,hn.to)||W(n,hn.from)||!W(n,hn.to))&&Le(Ht)(n.line,n.ch);var r=De.style.cssText;Ne.style.position="absolute",De.style.cssText="position: fixed; width: 30px; height: 30px; top: "+(t.clientY-5)+"px; left: "+(t.clientX-5)+"px; z-index: 1000; background: white; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",an=!0;var i=De.value=mt();if(kt(),De.select(),j){C(t);var o=b(window,"mouseup",function(){o(),setTimeout(e,20)},!0)}else setTimeout(e,50)}}function ve(){clearInterval(je);var t=!0;Ve.style.visibility="",je=setInterval(function(){Ve.style.visibility=(t=!t)?"":"hidden"},650)}function xe(t){function e(t,e,n){if(t.text)for(var r,i=t.styles,o=l?0:t.text.length-1,s=l?0:i.length-2,u=l?i.length:-2;s!=u;s+=2*a){var f=i[s];if(null==i[s+1]||i[s+1]==h){for(var c=l?0:f.length-1,m=l?f.length:-1;c!=m;c+=a,o+=a)if(o>=e&&o<n&&p.test(r=f.charAt(c))){var g=Dn[r];if(">"==g.charAt(1)==l)d.push(r);else{if(d.pop()!=g.charAt(0))return{pos:o,match:!1};if(!d.length)return{pos:o,match:!0}}}}else o+=a*f.length}}var n=hn.inverted?hn.from:hn.to,r=k(n.line),i=n.ch-1,o=i>=0&&Dn[r.text.charAt(i)]||Dn[r.text.charAt(++i)];if(o){for(var l=(o.charAt(0),">"==o.charAt(1)),a=l?1:-1,s=r.styles,u=i+1,f=0,c=s.length;f<c;f+=2)if((u-=s[f].length)<=0){var h=s[f+1];break}for(var d=[r.text.charAt(i)],p=/[(){}[\]]/,f=n.line,c=l?Math.min(f+100,$e.size):Math.max(-1,f-100);f!=c;f+=a){var r=k(f),m=f==n.line,g=e(r,m&&l?i+1:0,m&&!l?i:r.text.length);if(g)break}g||(g={pos:null,match:!1});var h=g.match?"CodeMirror-matchingbracket":"CodeMirror-nonmatchingbracket",v=Jt({line:n.line,ch:i},{line:n.line,ch:i+1},h),x=null!=g.pos&&Jt({line:f,ch:g.pos},{line:f,ch:g.pos+1},h),y=Le(function(){v.clear(),x&&x.clear()});t?setTimeout(y,800):fn=y}}function ye(t){for(var e,n,r=t,i=t-40;r>i;--r){if(0==r)return 0;var o=k(r-1);if(o.stateAfter)return r;var l=o.indentation(Ae.tabSize);(null==n||e>l)&&(n=r-1,e=l)}return n}function ke(t){var e=ye(t),n=e&&k(e-1).stateAfter;return n=n?r(Ge,n):i(Ge),$e.iter(e,t,function(t){t.highlight(Ge,n,Ae.tabSize),t.stateAfter=r(Ge,n)}),e<t&&rn.push({from:e,to:t}),t<$e.size&&!k(t).stateAfter&&Ye.push(t),n}function Ce(t,e){var n=ke(t);$e.iter(t,e,function(t){t.highlight(Ge,n,Ae.tabSize),t.stateAfter=r(Ge,n)})}function we(){for(var t=+new Date+Ae.workTime,e=Ye.length;Ye.length;){if(k(mn).stateAfter)var n=Ye.pop();else var n=mn;if(!(n>=$e.size)){var o=ye(n),l=o&&k(o-1).stateAfter;l=l?r(Ge,l):i(Ge);var a=0,s=Ge.compareStates,u=!1,f=o,c=!1;if($e.iter(f,$e.size,function(e){var i=e.stateAfter;if(+new Date>t)return Ye.push(f),Me(Ae.workDelay),u&&rn.push({from:n,to:f+1}),c=!0;var o=e.highlight(Ge,l,Ae.tabSize);if(o&&(u=!0),e.stateAfter=r(Ge,l),s){if(i&&s(i,l))return!0}else if(o===!1&&i){if(++a>3&&(!Ge.indent||Ge.indent(i,"")==Ge.indent(l,"")))return!0}else a=0;++f}),c)return;u&&rn.push({from:n,to:f+1})}}e&&Ae.onHighlightComplete&&Ae.onHighlightComplete(wn)}function Me(t){Ye.length&&Xe.set(t,Le(we))}function be(){en=nn=on=null,rn=[],ln=!1,un=[]}function Se(){var t,e=!1;ln&&(e=!wt()),rn.length?t=St(rn,!0):(ln&&Tt(),sn&&zt()),e&&wt(),ln&&(Ct(),ve()),_e&&!an&&(en===!0||en!==!1&&ln)&&yt(nn),ln&&Ae.matchBrackets&&setTimeout(Le(function(){fn&&(fn(),fn=null),E(hn.from,hn.to)&&xe(!1)}),20);var n=on,r=un;ln&&Ae.onCursorActivity&&Ae.onCursorActivity(wn),n&&Ae.onChange&&wn&&Ae.onChange(wn,n);for(var i=0;i<r.length;++i)r[i](wn);t&&Ae.onUpdate&&Ae.onUpdate(wn)}function Le(t){return function(){Hn++||be();try{var e=t.apply(this,arguments)}finally{--Hn||Se()}return e}}var Ae={},ze=t.defaults;for(var Te in ze)ze.hasOwnProperty(Te)&&(Ae[Te]=(u&&u.hasOwnProperty(Te)?u:ze)[Te]);var Ee=Ae.document,We=Ee.createElement("div");We.className="CodeMirror"+(Ae.lineWrapping?" CodeMirror-wrap":""),We.innerHTML='<div style="overflow: hidden; position: relative; width: 3px; height: 0px;"><textarea style="position: absolute; padding: 0; width: 1px;" wrap="off" autocorrect="off" autocapitalize="off"></textarea></div><div class="CodeMirror-scroll" tabindex="-1"><div style="position: relative"><div style="position: relative"><div class="CodeMirror-gutter"><div class="CodeMirror-gutter-text"></div></div><div class="CodeMirror-lines"><div style="position: relative"><div style="position: absolute; width: 100%; height: 0; overflow: hidden; visibility: hidden"></div><pre class="CodeMirror-cursor">&#160;</pre><div></div></div></div></div></div></div>',o.appendChild?o.appendChild(We):o(We);var Ne=We.firstChild,De=Ne.firstChild,He=We.lastChild,Ie=He.firstChild,Oe=Ie.firstChild,Re=Oe.firstChild,Pe=Re.firstChild,Ue=Re.nextSibling.firstChild,Be=Ue.firstChild,Ve=Be.nextSibling,Ke=Ve.nextSibling;$t(),/AppleWebKit/.test(navigator.userAgent)&&/Mobile\/\w+/.test(navigator.userAgent)&&(De.style.width="0px"),Y||(Ue.draggable=!0),null!=Ae.tabindex&&(De.tabIndex=Ae.tabindex),Ae.gutter||Ae.lineNumbers||(Re.style.display="none");try{le("x")}catch(Fe){throw Fe.message.match(/runtime/i)&&(Fe=new Error("A CodeMirror inside a P-style element does not work in Internet Explorer. (innerHTML bug)")),Fe}var je,Ge,Ye,_e,qe=new S,Xe=new S,$e=new c([new f([new s("")])]);Gt();var Ze,Je,Qe,tn,en,nn,rn,on,ln,an,sn,un,fn,cn,hn={from:{line:0,ch:0},to:{line:0,ch:0},inverted:!1},dn=!1,pn=0,mn=0,gn=0,vn=0,xn="",yn=qt();Le(function(){A(Ae.value||""),en=!1})();var kn=new g;b(He,"mousedown",Le(U)),b(He,"dblclick",Le(_)),b(Ue,"dragstart",Q),b(Ue,"selectstart",y),j||b(He,"contextmenu",ge),b(He,"scroll",function(){St([]),Ae.fixedGutter&&(Re.style.left=He.scrollLeft+"px"),Ae.onScroll&&Ae.onScroll(wn)}),b(window,"resize",function(){St(!0)}),b(De,"keyup",Le(rt)),b(De,"input",vt),b(De,"keydown",Le(et)),b(De,"keypress",Le(nt)),b(De,"focus",it),b(De,"blur",ot),b(He,"dragenter",C),b(He,"dragover",C),b(He,"drop",Le(J)),b(He,"paste",function(){kt(),vt()}),b(De,"paste",vt),b(De,"cut",Le(function(){ht("")}));var Cn;try{Cn=Ee.activeElement==De}catch(Fe){}Cn?setTimeout(it,20):ot();var wn=We.CodeMirror={getValue:P,setValue:Le(A),getSelection:mt,replaceSelection:Le(ht),focus:function(){kt(),it(),vt()},setOption:function(t,e){var n=Ae[t];Ae[t]=e,"mode"==t||"indentUnit"==t?Gt():"readOnly"==t&&e?(ot(),De.blur()):"theme"==t?$t():"lineWrapping"==t&&n!=e?Le(_t)():"tabSize"==t&&Le(Xt)(),"lineNumbers"!=t&&"gutter"!=t&&"firstLineNumber"!=t&&"theme"!=t||Le(Yt)()},getOption:function(t){return Ae[t]},undo:Le(st),redo:Le(ut),indentLine:Le(function(t,e){v(t)&&jt(t,null==e?"smart":e?"add":"subtract")}),indentSelection:Le(Ft),historySize:function(){return{undo:kn.done.length,redo:kn.undone.length}},clearHistory:function(){kn=new g},matchBrackets:Le(function(){xe(!0)}),getTokenAt:Le(function(t){return t=Ot(t),k(t.line).getTokenAt(Ge,ke(t.line),t.ch)}),getStateAfter:function(t){return t=It(null==t?$e.size-1:t),ke(t+1)},cursorCoords:function(t){return null==t&&(t=hn.inverted),fe(t?hn.from:hn.to)},charCoords:function(t){return fe(Ot(t))},coordsChar:function(t){var e=z(Ue);return ue(t.x-e.left,t.y-e.top)},markText:Le(Jt),setBookmark:Qt,setMarker:Le(te),clearMarker:Le(ee),setLineClass:Le(re),hideLine:Le(function(t){return ie(t,!0)}),showLine:Le(function(t){return ie(t,!1)}),onDeleteLine:function(t,e){if("number"==typeof t){if(!v(t))return null;t=k(t)}return(t.handlers||(t.handlers=[])).push(e),t},lineInfo:oe,addWidget:function(t,e,n,r,i){t=se(Ot(t));var o=t.yBot,l=t.x;if(e.style.position="absolute",Ie.appendChild(e),"over"==r)o=t.y;else if("near"==r){var a=Math.max(He.offsetHeight,$e.height*ce()),s=Math.max(Ie.clientWidth,Ue.clientWidth)-pe();t.yBot+e.offsetHeight>a&&t.y>e.offsetHeight&&(o=t.y-e.offsetHeight),l+e.offsetWidth>s&&(l=s-e.offsetWidth)}e.style.top=o+de()+"px",e.style.left=e.style.right="","right"==i?(l=Ie.clientWidth-e.offsetWidth,e.style.right="0px"):("left"==i?l=0:"middle"==i&&(l=(Ie.clientWidth-e.offsetWidth)/2),e.style.left=l+pe()+"px"),n&&Mt(l,o,l+e.offsetWidth,o+e.offsetHeight)},lineCount:function(){return $e.size},clipPos:Ot,getCursor:function(t){return null==t&&(t=hn.inverted),N(t?hn.from:hn.to)},somethingSelected:function(){return!E(hn.from,hn.to)},setCursor:Le(function(t,e,n){null==e&&"number"==typeof t.line?Ht(t.line,t.ch,n):Ht(t,e,n)}),setSelection:Le(function(t,e,n){(n?Wt:Nt)(Ot(t),Ot(e||t))}),getLine:function(t){if(v(t))return k(t).text},getLineHandle:function(t){if(v(t))return k(t)},setLine:Le(function(t,e){v(t)&&ct(e,{line:t,ch:0},{line:t,ch:k(t).text.length})}),removeLine:Le(function(t){v(t)&&ct("",{line:t,ch:0},Ot({line:t+1,ch:0}))}),replaceRange:Le(ct),getRange:function(t,e){return pt(Ot(t),Ot(e))},execCommand:function(t){return V[t](wn)},moveH:Le(Pt),deleteH:Le(Ut),moveV:Le(Bt),toggleOverwrite:function(){dn=!dn},posFromIndex:function(t){var e,n=0;return $e.iter(0,$e.size,function(r){var i=r.text.length+1;return i>t?(e=t,!0):(t-=i,void++n)}),Ot({line:n,ch:e})},indexFromPos:function(t){if(t.line<0||t.ch<0)return 0;var e=t.ch;return $e.iter(0,t.line,function(t){e+=t.text.length+1}),e},operation:function(t){return Le(t)()},refresh:function(){St(!0)},getInputField:function(){return De},getWrapperElement:function(){return We},getScrollerElement:function(){return He},getGutterElement:function(){return Re}},Mn=null,bn=!1,Sn="",Ln=null;Zt.prototype.clear=Le(function(){for(var t=1/0,e=-(1/0),n=0,r=this.set.length;n<r;++n){var i=this.set[n],o=i.marked;if(o&&i.parent){var l=d(i);t=Math.min(t,l),e=Math.max(e,l);for(var a=0;a<o.length;++a)o[a].set==this.set&&o.splice(a--,1)}}t!=1/0&&rn.push({from:t,to:e+1})}),Zt.prototype.find=function(){for(var t,e,n=0,r=this.set.length;n<r;++n)for(var i=this.set[n],o=i.marked,l=0;l<o.length;++l){var a=o[l];if(a.set==this.set&&(null!=a.from||null!=a.to)){var s=d(i);null!=s&&(null!=a.from&&(t={line:s,ch:a.from}),null!=a.to&&(e={line:s,ch:a.to}))}}return{from:t,to:e}};var An,zn,Tn,En,Wn=Math.floor(16777215*Math.random()).toString(16),Nn=0,Dn={"(":")>",")":"(<","[":"]>","]":"[<","{":"}>","}":"{<"},Hn=0;for(var In in B)B.propertyIsEnumerable(In)&&!wn.propertyIsEnumerable(In)&&(wn[In]=B[In]);return wn}function e(t,e,n){function r(t,e,n){var i=e[t];if(null!=i)return i;if(null==n&&(n=e.fallthrough),null==n)return e.catchall;if("string"==typeof n)return r(t,K[n]);for(var o=0,l=n.length;o<l;++o)if(i=r(t,K[n[o]]),null!=i)return i;return null}return e?r(t,e,n):r(t,K[n])}function n(t){var e=Z[t.keyCode];return"Ctrl"==e||"Alt"==e||"Shift"==e||"Mod"==e}function r(t,e){if(e===!0)return e;if(t.copyState)return t.copyState(e);var n={};for(var r in e){var i=e[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function i(t,e,n){return!t.startState||t.startState(e,n)}function o(t,e){this.pos=this.start=0,this.string=t,this.tabSize=e||8}function l(t,e,n,r){this.from=t,this.to=e,this.style=n,this.set=r}function a(t){this.from=t,this.to=t,this.line=null}function s(t,e){this.styles=e||[t,null],this.text=t,this.height=1,this.marked=this.gutterMarker=this.className=this.handlers=null,this.stateAfter=this.parent=this.hidden=null}function u(t,e,n,r){for(var i=0,o=0,l=0;o<e;i+=2){var a=n[i],s=o+a.length;0==l?(s>t&&r.push(a.slice(t-o,Math.min(a.length,e-o)),n[i+1]),s>=t&&(l=1)):1==l&&(s>e?r.push(a.slice(0,e-o),n[i+1]):r.push(a,n[i+1])),o=s}}function f(t){this.lines=t,this.parent=null;for(var e=0,n=t.length,r=0;e<n;++e)t[e].parent=this,r+=t[e].height;this.height=r}function c(t){this.children=t;for(var e=0,n=0,r=0,i=t.length;r<i;++r){var o=t[r];e+=o.chunkSize(),n+=o.height,o.parent=this}this.size=e,this.height=n,this.parent=null}function h(t,e){for(;!t.lines;)for(var n=0;;++n){var r=t.children[n],i=r.chunkSize();if(e<i){t=r;break}e-=i}return t.lines[e]}function d(t){if(null==t.parent)return null;for(var e=t.parent,n=I(e.lines,t),r=e.parent;r;e=r,r=r.parent){var i=0;for(r.children.length;r.children[i]!=e;++i)n+=r.children[i].chunkSize()}return n}function p(t,e){var n=0;t:do{for(var r=0,i=t.children.length;r<i;++r){var o=t.children[r],l=o.height;if(e<l){t=o;continue t}e-=l,n+=o.chunkSize()}return n}while(!t.lines);for(var r=0,i=t.lines.length;r<i;++r){var a=t.lines[r],s=a.height;if(e<s)break;e-=s}return n+r}function m(t,e){var n=0;t:do{for(var r=0,i=t.children.length;r<i;++r){var o=t.children[r],l=o.chunkSize();if(e<l){t=o;continue t}e-=l,n+=o.height}return n}while(!t.lines);for(var r=0;r<e;++r)n+=t.lines[r].height;return n}function g(){this.time=0,this.done=[],this.undone=[]}function v(){C(this)}function x(t){return t.stop||(t.stop=v),t}function y(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function k(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}function C(t){y(t),k(t)}function w(t){return t.target||t.srcElement}function M(t){return t.which?t.which:1&t.button?1:2&t.button?3:4&t.button?2:void 0}function b(t,e,n,r){
if("function"==typeof t.addEventListener){if(t.addEventListener(e,n,!1),r)return function(){t.removeEventListener(e,n,!1)}}else{var i=function(t){n(t||window.event)};if(t.attachEvent("on"+e,i),r)return function(){t.detachEvent("on"+e,i)}}}function S(){this.id=null}function L(t,e,n){null==e&&(e=t.search(/[^\s\u00a0]/),e==-1&&(e=t.length));for(var r=0,i=0;r<e;++r)"\t"==t.charAt(r)?i+=n-i%n:++i;return i}function A(t){return t.currentStyle?t.currentStyle:window.getComputedStyle(t,null)}function z(t,e){for(var n=t.ownerDocument.body,r=0,i=0,o=!1,l=t;l;l=l.offsetParent){var a=l.offsetLeft,s=l.offsetTop;l==n?(r+=Math.abs(a),i+=Math.abs(s)):(r+=a,i+=s),e&&"fixed"==A(l).position&&(o=!0)}for(var u=e&&!o?null:n,l=t.parentNode;l!=u;l=l.parentNode)null!=l.scrollLeft&&(r-=l.scrollLeft,i-=l.scrollTop);return{left:r,top:i}}function T(t){return t.textContent||t.innerText||t.nodeValue||""}function E(t,e){return t.line==e.line&&t.ch==e.ch}function W(t,e){return t.line<e.line||t.line==e.line&&t.ch<e.ch}function N(t){return{line:t.line,ch:t.ch}}function D(t){return q.textContent=t,q.innerHTML}function H(t,e){if(!e)return t?t.length:0;if(!t)return e.length;for(var n=t.length,r=e.length;n>=0&&r>=0&&t.charAt(n)==e.charAt(r);--n,--r);return r+1}function I(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;++n)if(t[n]==e)return n;return-1}function O(t){return/\w/.test(t)||t.toUpperCase()!=t.toLowerCase()}t.defaults={value:"",mode:null,theme:"default",indentUnit:2,indentWithTabs:!1,tabSize:4,keyMap:"default",extraKeys:null,electricChars:!0,onKeyEvent:null,lineWrapping:!1,lineNumbers:!1,gutter:!1,fixedGutter:!1,firstLineNumber:1,readOnly:!1,onChange:null,onCursorActivity:null,onGutterClick:null,onHighlightComplete:null,onUpdate:null,onFocus:null,onBlur:null,onScroll:null,matchBrackets:!1,workTime:100,workDelay:200,pollInterval:100,undoDepth:40,tabindex:null,document:window.document};var R=/Mac/.test(navigator.platform),P=(/Win/.test(navigator.platform),{}),U={};t.defineMode=function(e,n){t.defaults.mode||"null"==e||(t.defaults.mode=e),P[e]=n},t.defineMIME=function(t,e){U[t]=e},t.getMode=function(e,n){if("string"==typeof n&&U.hasOwnProperty(n)&&(n=U[n]),"string"==typeof n)var r=n,i={};else if(null!=n)var r=n.name,i=n;var o=P[r];return o?o(e,i||{}):(window.console&&console.warn("No mode "+r+" found, falling back to plain text."),t.getMode(e,"text/plain"))},t.listModes=function(){var t=[];for(var e in P)P.propertyIsEnumerable(e)&&t.push(e);return t},t.listMIMEs=function(){var t=[];for(var e in U)U.propertyIsEnumerable(e)&&t.push({mime:e,mode:U[e]});return t};var B=t.extensions={};t.defineExtension=function(t,e){B[t]=e};var V=t.commands={selectAll:function(t){t.setSelection({line:0,ch:0},{line:t.lineCount()-1})},killLine:function(t){var e=t.getCursor(!0),n=t.getCursor(!1),r=!E(e,n);r||t.getLine(e.line).length!=e.ch?t.replaceRange("",e,r?n:{line:e.line}):t.replaceRange("",e,{line:e.line+1,ch:0})},deleteLine:function(t){var e=t.getCursor().line;t.replaceRange("",{line:e,ch:0},{line:e})},undo:function(t){t.undo()},redo:function(t){t.redo()},goDocStart:function(t){t.setCursor(0,0,!0)},goDocEnd:function(t){t.setSelection({line:t.lineCount()-1},null,!0)},goLineStart:function(t){t.setCursor(t.getCursor().line,0,!0)},goLineStartSmart:function(t){var e=t.getCursor(),n=t.getLine(e.line),r=Math.max(0,n.search(/\S/));t.setCursor(e.line,e.ch<=r&&e.ch?0:r,!0)},goLineEnd:function(t){t.setSelection({line:t.getCursor().line},null,!0)},goLineUp:function(t){t.moveV(-1,"line")},goLineDown:function(t){t.moveV(1,"line")},goPageUp:function(t){t.moveV(-1,"page")},goPageDown:function(t){t.moveV(1,"page")},goCharLeft:function(t){t.moveH(-1,"char")},goCharRight:function(t){t.moveH(1,"char")},goColumnLeft:function(t){t.moveH(-1,"column")},goColumnRight:function(t){t.moveH(1,"column")},goWordLeft:function(t){t.moveH(-1,"word")},goWordRight:function(t){t.moveH(1,"word")},delCharLeft:function(t){t.deleteH(-1,"char")},delCharRight:function(t){t.deleteH(1,"char")},delWordLeft:function(t){t.deleteH(-1,"word")},delWordRight:function(t){t.deleteH(1,"word")},indentAuto:function(t){t.indentSelection("smart")},indentMore:function(t){t.indentSelection("add")},indentLess:function(t){t.indentSelection("subtract")},insertTab:function(t){t.replaceSelection("\t","end")},transposeChars:function(t){var e=t.getCursor(),n=t.getLine(e.line);e.ch>0&&e.ch<n.length-1&&t.replaceRange(n.charAt(e.ch)+n.charAt(e.ch-1),{line:e.line,ch:e.ch-1},{line:e.line,ch:e.ch+1})},newlineAndIndent:function(t){t.replaceSelection("\n","end"),t.indentLine(t.getCursor().line)},toggleOverwrite:function(t){t.toggleOverwrite()}},K=t.keyMap={};K.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharRight",Backspace:"delCharLeft",Tab:"indentMore","Shift-Tab":"indentLess",Enter:"newlineAndIndent",Insert:"toggleOverwrite"},K.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Alt-Up":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Down":"goDocEnd","Ctrl-Left":"goWordLeft","Ctrl-Right":"goWordRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delWordLeft","Ctrl-Delete":"delWordRight","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll",fallthrough:"basic"},K.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goWordLeft","Alt-Right":"goWordRight","Cmd-Left":"goLineStart","Cmd-Right":"goLineEnd","Alt-Backspace":"delWordLeft","Ctrl-Alt-Backspace":"delWordRight","Alt-Delete":"delWordRight","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll",fallthrough:["basic","emacsy"]},K["default"]=R?K.macDefault:K.pcDefault,K.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageUp","Shift-Ctrl-V":"goPageDown","Ctrl-D":"delCharRight","Ctrl-H":"delCharLeft","Alt-D":"delWordRight","Alt-Backspace":"delWordLeft","Ctrl-K":"killLine","Ctrl-T":"transposeChars"},t.fromTextArea=function(e,n){function r(){e.value=a.getValue()}function i(){r(),e.form.submit=l,e.form.submit(),e.form.submit=i}if(n||(n={}),n.value=e.value,!n.tabindex&&e.tabindex&&(n.tabindex=e.tabindex),e.form){var o=b(e.form,"submit",r,!0);if("function"==typeof e.form.submit){var l=e.form.submit;e.form.submit=i}}e.style.display="none";var a=t(function(t){e.parentNode.insertBefore(t,e.nextSibling)},n);return a.save=r,a.getTextArea=function(){return e},a.toTextArea=function(){r(),e.parentNode.removeChild(a.getWrapperElement()),e.style.display="",e.form&&(o(),"function"==typeof e.form.submit&&(e.form.submit=l))},a},t.copyState=r,t.startState=i,o.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return 0==this.pos},peek:function(){return this.string.charAt(this.pos)},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(t){var e=this.string.charAt(this.pos);if("string"==typeof t)var n=e==t;else var n=e&&(t.test?t.test(e):t(e));if(n)return++this.pos,e},eatWhile:function(t){for(var e=this.pos;this.eat(t););return this.pos>e},eatSpace:function(){for(var t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t},skipToEnd:function(){this.pos=this.string.length},skipTo:function(t){var e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0},backUp:function(t){this.pos-=t},column:function(){return L(this.string,this.start,this.tabSize)},indentation:function(){return L(this.string,null,this.tabSize)},match:function(t,e,n){function r(t){return n?t.toLowerCase():t}if("string"!=typeof t){var i=this.string.slice(this.pos).match(t);return i&&e!==!1&&(this.pos+=i[0].length),i}if(r(this.string).indexOf(r(t),this.pos)==this.pos)return e!==!1&&(this.pos+=t.length),!0},current:function(){return this.string.slice(this.start,this.pos)}},t.StringStream=o,l.prototype={attach:function(t){this.set.push(t)},detach:function(t){var e=I(this.set,t);e>-1&&this.set.splice(e,1)},split:function(t,e){if(this.to<=t&&null!=this.to)return null;var n=this.from<t||null==this.from?null:this.from-t+e,r=null==this.to?null:this.to-t+e;return new l(n,r,this.style,this.set)},dup:function(){return new l(null,null,this.style,this.set)},clipTo:function(t,e,n,r,i){null!=this.from&&this.from>=e&&(this.from=Math.max(r,this.from)+i),null!=this.to&&this.to>e&&(this.to=r<this.to?this.to+i:e),t&&r>this.from&&(r<this.to||null==this.to)&&(this.from=null),n&&(e<this.to||null==this.to)&&(e>this.from||null==this.from)&&(this.to=null)},isDead:function(){return null!=this.from&&null!=this.to&&this.from>=this.to},sameSet:function(t){return this.set==t.set}},a.prototype={attach:function(t){this.line=t},detach:function(t){this.line==t&&(this.line=null)},split:function(t,e){if(t<this.from)return this.from=this.to=this.from-t+e,this},isDead:function(){return this.from>this.to},clipTo:function(t,e,n,r,i){(t||e<this.from)&&(n||r>this.to)?(this.from=0,this.to=-1):this.from>e&&(this.from=this.to=Math.max(r,this.from)+i)},sameSet:function(t){return!1},find:function(){return this.line&&this.line.parent?{line:d(this.line),ch:this.from}:null},clear:function(){if(this.line){var t=I(this.line.marked,this);t!=-1&&this.line.marked.splice(t,1),this.line=null}}},s.inheritMarks=function(t,e){var n=new s(t),r=e&&e.marked;if(r)for(var i=0;i<r.length;++i)if(null==r[i].to&&r[i].style){var o=n.marked||(n.marked=[]),l=r[i],a=l.dup();o.push(a),a.attach(n)}return n},s.prototype={replace:function(t,e,n){var r=[],i=this.marked,o=null==e?this.text.length:e;if(u(0,t,this.styles,r),n&&r.push(n,null),u(o,this.text.length,this.styles,r),this.styles=r,this.text=this.text.slice(0,t)+n+this.text.slice(o),this.stateAfter=null,i)for(var l=n.length-(o-t),a=0,s=i[a];a<i.length;++a)s.clipTo(null==t,t||0,null==e,o,l),s.isDead()&&(s.detach(this),i.splice(a--,1))},split:function(t,e){var n=[e,null],r=this.marked;u(t,this.text.length,this.styles,n);var i=new s(e+this.text.slice(t),n);if(r)for(var o=0;o<r.length;++o){var l=r[o],a=l.split(t,e.length);a&&(i.marked||(i.marked=[]),i.marked.push(a),a.attach(i))}return i},append:function(t){var e=this.text.length,n=t.marked,r=this.marked;if(this.text+=t.text,u(0,t.text.length,t.styles,this.styles),r)for(var i=0;i<r.length;++i)null==r[i].to&&(r[i].to=e);if(n&&n.length){r||(this.marked=r=[]);t:for(var i=0;i<n.length;++i){var o=n[i];if(!o.from)for(var l=0;l<r.length;++l){var a=r[l];if(a.to==e&&a.sameSet(o)){a.to=null==o.to?null:o.to+e,a.isDead()&&(a.detach(this),n.splice(i--,1));continue t}}r.push(o),o.attach(this),o.from+=e,null!=o.to&&(o.to+=e)}}},fixMarkEnds:function(t){var e=this.marked,n=t.marked;if(e)for(var r=0;r<e.length;++r){var i=e[r],o=null==i.to;if(o&&n)for(var l=0;l<n.length;++l)if(n[l].sameSet(i)){o=!1;break}o&&(i.to=this.text.length)}},fixMarkStarts:function(){var t=this.marked;if(t)for(var e=0;e<t.length;++e)null==t[e].from&&(t[e].from=0)},addMark:function(t){t.attach(this),null==this.marked&&(this.marked=[]),this.marked.push(t),this.marked.sort(function(t,e){return(t.from||0)-(e.from||0)})},highlight:function(t,e,n){var r,i=new o(this.text,n),l=this.styles,a=0,s=!1,u=l[0];for(""==this.text&&t.blankLine&&t.blankLine(e);!i.eol();){var f=t.token(i,e),c=this.text.slice(i.start,i.pos);if(i.start=i.pos,a&&l[a-1]==f?l[a-2]+=c:c&&(!s&&(l[a+1]!=f||a&&l[a-2]!=r)&&(s=!0),l[a++]=c,l[a++]=f,r=u,u=l[a]),i.pos>5e3){l[a++]=this.text.slice(i.pos),l[a++]=null;break}}return l.length!=a&&(l.length=a,s=!0),a&&l[a-2]!=r&&(s=!0),s||l.length<5&&this.text.length<10&&null},getTokenAt:function(t,e,n){for(var r=this.text,i=new o(r);i.pos<n&&!i.eol();){i.start=i.pos;var l=t.token(i,e)}return{start:i.start,end:i.pos,string:i.current(),className:l||null,state:e}},indentation:function(t){return L(this.text,null,t)},getHTML:function(t,e,n,r,i){function o(t,e){t&&(s&&G&&" "==t.charAt(0)&&(t=" "+t.slice(1)),s=!1,e?a.push('<span class="',e,'">',D(t).replace(/\t/g,r),"</span>"):a.push(D(t).replace(/\t/g,r)))}function l(){c&&(v+=1,x=v<c.length?c[v]:null)}var a=[],s=!0;n&&a.push(this.className?'<pre class="'+this.className+'">':"<pre>");var u=this.styles,f=this.text,c=this.marked;t==e&&(t=null);var h=f.length;if(null!=i&&(h=Math.min(i,h)),f||null!=i)if(c||null!=t){var d,p=0,m=0,g="",v=-1,x=null;for(l();p<h;){var y=h,k="";for(null!=t&&(t>p?y=t:(null==e||e>p)&&(k=" CodeMirror-selected",null!=e&&(y=Math.min(y,e))));x&&null!=x.to&&x.to<=p;)l();for(x&&(x.from>p?y=Math.min(y,x.from):(k+=" "+x.style,null!=x.to&&(y=Math.min(y,x.to))));;){var C=p+g.length,w=d;if(k&&(w=d?d+k:k),o(C>y?g.slice(0,y-p):g,w),C>=y){g=g.slice(y-p),p=y;break}p=C,g=u[m++],d="cm-"+u[m++]}}null!=t&&null==e&&o(" ","CodeMirror-selected")}else for(var m=0,M=0;M<h;m+=2){var b=u[m],d=u[m+1],S=b.length;M+S>h&&(b=b.slice(0,h-M)),M+=S,o(b,d&&"cm-"+d)}else o(" ",null!=t&&null==e?"CodeMirror-selected":null);return n&&a.push("</pre>"),a.join("")},cleanUp:function(){if(this.parent=null,this.marked)for(var t=0,e=this.marked.length;t<e;++t)this.marked[t].detach(this)}},f.prototype={chunkSize:function(){return this.lines.length},remove:function(t,e,n){for(var r=t,i=t+e;r<i;++r){var o=this.lines[r];if(this.height-=o.height,o.cleanUp(),o.handlers)for(var l=0;l<o.handlers.length;++l)n.push(o.handlers[l])}this.lines.splice(t,e)},collapse:function(t){t.splice.apply(t,[t.length,0].concat(this.lines))},insertHeight:function(t,e,n){this.height+=n,this.lines.splice.apply(this.lines,[t,0].concat(e));for(var r=0,i=e.length;r<i;++r)e[r].parent=this},iterN:function(t,e,n){for(var r=t+e;t<r;++t)if(n(this.lines[t]))return!0}},c.prototype={chunkSize:function(){return this.size},remove:function(t,e,n){this.size-=e;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(t<o){var l=Math.min(e,o-t),a=i.height;if(i.remove(t,l,n),this.height-=a-i.height,o==l&&(this.children.splice(r--,1),i.parent=null),0==(e-=l))break;t=0}else t-=o}if(this.size-e<25){var s=[];this.collapse(s),this.children=[new f(s)]}},collapse:function(t){for(var e=0,n=this.children.length;e<n;++e)this.children[e].collapse(t)},insert:function(t,e){for(var n=0,r=0,i=e.length;r<i;++r)n+=e[r].height;this.insertHeight(t,e,n)},insertHeight:function(t,e,n){this.size+=e.length,this.height+=n;for(var r=0,i=this.children.length;r<i;++r){var o=this.children[r],l=o.chunkSize();if(t<=l){if(o.insertHeight(t,e,n),o.lines&&o.lines.length>50){for(;o.lines.length>50;){var a=o.lines.splice(o.lines.length-25,25),s=new f(a);o.height-=s.height,this.children.splice(r+1,0,s),s.parent=this}this.maybeSpill()}break}t-=l}},maybeSpill:function(){if(!(this.children.length<=10)){var t=this;do{var e=t.children.splice(t.children.length-5,5),n=new c(e);if(t.parent){t.size-=n.size,t.height-=n.height;var r=I(t.parent.children,t);t.parent.children.splice(r+1,0,n)}else{var i=new c(t.children);i.parent=t,t.children=[i,n],t=i}n.parent=t.parent}while(t.children.length>10);t.parent.maybeSpill()}},iter:function(t,e,n){this.iterN(t,e-t,n)},iterN:function(t,e,n){for(var r=0,i=this.children.length;r<i;++r){var o=this.children[r],l=o.chunkSize();if(t<l){var a=Math.min(e,l-t);if(o.iterN(t,a,n))return!0;if(0==(e-=a))break;t=0}else t-=l}}},g.prototype={addChange:function(t,e,n){this.undone.length=0;var r=+new Date,i=this.done[this.done.length-1];if(r-this.time>400||!i||i.start>t+e||i.start+i.added<t-i.added+i.old.length)this.done.push({start:t,added:e,old:n});else{var o=0;if(t<i.start){for(var l=i.start-t-1;l>=0;--l)i.old.unshift(n[l]);i.added+=i.start-t,i.start=t}else i.start<t&&(o=t-i.start,e+=o);for(var l=i.added-o,a=n.length;l<a;++l)i.old.push(n[l]);i.added<e&&(i.added=e)}this.time=r}},t.e_stop=C,t.e_preventDefault=y,t.e_stopPropagation=k,t.connect=b,S.prototype={set:function(t,e){clearTimeout(this.id),this.id=setTimeout(e,t)}};var F=function(){if(/MSIE [1-8]\b/.test(navigator.userAgent))return!1;var t=document.createElement("div");return"draggable"in t}(),j=/gecko\/\d{7}/i.test(navigator.userAgent),G=/MSIE \d/.test(navigator.userAgent),Y=/WebKit\//.test(navigator.userAgent),_="\n";!function(){var t=document.createElement("textarea");t.value="foo\nbar",t.value.indexOf("\r")>-1&&(_="\r\n")}(),null!=document.documentElement.getBoundingClientRect&&(z=function(t,e){try{var n=t.getBoundingClientRect();n={top:n.top,left:n.left}}catch(r){n={top:0,left:0}}if(!e)if(null==window.pageYOffset){var i=document.documentElement||document.body.parentNode;null==i.scrollTop&&(i=document.body),n.top+=i.scrollTop,n.left+=i.scrollLeft}else n.top+=window.pageYOffset,n.left+=window.pageXOffset;return n});var q=document.createElement("pre");"\na"==D("a")?D=function(t){return q.textContent=t,q.innerHTML.slice(1)}:"\t"!=D("\t")&&(D=function(t){return q.innerHTML="",q.appendChild(document.createTextNode(t)),q.innerHTML}),t.htmlEscape=D;var X=3!="\n\nb".split(/\n/).length?function(t){for(var e,n=0,r=[];(e=t.indexOf("\n",n))>-1;)r.push(t.slice(n,"\r"==t.charAt(e-1)?e-1:e)),n=e+1;return r.push(t.slice(n)),r}:function(t){return t.split(/\r?\n/)};t.splitLines=X;var $=window.getSelection?function(t){try{return t.selectionStart!=t.selectionEnd}catch(e){return!1}}:function(t){try{var e=t.ownerDocument.selection.createRange()}catch(n){}return!(!e||e.parentElement()!=t)&&0!=e.compareEndPoints("StartToEnd",e)};t.defineMode("null",function(){return{token:function(t){t.skipToEnd()}}}),t.defineMIME("text/plain","null");var Z={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",91:"Mod",92:"Mod",93:"Mod",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63276:"PageUp",63277:"PageDown",63275:"End",63273:"Home",63234:"Left",63232:"Up",63235:"Right",63233:"Down",63302:"Insert",63272:"Delete"};return t.keyNames=Z,function(){for(var t=0;t<10;t++)Z[t+48]=String(t);for(var t=65;t<=90;t++)Z[t]=String.fromCharCode(t);for(var t=1;t<=12;t++)Z[t+111]=Z[t+63235]="F"+t}(),t}();CodeMirror.defineMode("xml",function(t,e){function n(t,e){function n(n){return e.tokenize=n,n(t,e)}var i=t.next();if("<"==i){if(t.eat("!"))return t.eat("[")?t.match("CDATA[")?n(o("atom","]]>")):null:t.match("--")?n(o("comment","-->")):t.match("DOCTYPE",!0,!0)?(t.eatWhile(/[\w\._\-]/),n(l(1))):null;if(t.eat("?"))return t.eatWhile(/[\w\._\-]/),e.tokenize=o("meta","?>"),"meta";x=t.eat("/")?"closeTag":"openTag",t.eatSpace(),v="";for(var a;a=t.eat(/[^\s\u00a0=<>\"\'\/?]/);)v+=a;return e.tokenize=r,"tag"}return"&"==i?(t.eatWhile(/[^;]/),t.eat(";"),"atom"):(t.eatWhile(/[^&<]/),null)}function r(t,e){var r=t.next();return">"==r||"/"==r&&t.eat(">")?(e.tokenize=n,x=">"==r?"endTag":"selfcloseTag","tag"):"="==r?(x="equals",null):/[\'\"]/.test(r)?(e.tokenize=i(r),e.tokenize(t,e)):(t.eatWhile(/[^\s\u00a0=<>\"\'\/?]/),"word")}function i(t){return function(e,n){for(;!e.eol();)if(e.next()==t){n.tokenize=r;break}return"string"}}function o(t,e){return function(r,i){for(;!r.eol();){if(r.match(e)){i.tokenize=n;break}r.next()}return t}}function l(t){return function(e,r){for(var i;null!=(i=e.next());){if("<"==i)return r.tokenize=l(t+1),r.tokenize(e,r);if(">"==i){if(1==t){r.tokenize=n;break}return r.tokenize=l(t-1),r.tokenize(e,r)}}return"meta"}}function a(){for(var t=arguments.length-1;t>=0;t--)y.cc.push(arguments[t])}function s(){return a.apply(null,arguments),!0}function u(t,e){var n=w.doNotIndent.hasOwnProperty(t)||y.context&&y.context.noIndent;y.context={prev:y.context,tagName:t,indent:y.indented,startOfLine:e,noIndent:n}}function f(){y.context&&(y.context=y.context.prev)}function c(t){if("openTag"==t)return y.tagName=v,s(p,h(y.startOfLine));if("closeTag"==t){var e=!1;return e=!y.context||y.context.tagName!=v,e&&(k="error"),s(d(e))}return s()}function h(t){return function(e){return"selfcloseTag"==e||"endTag"==e&&w.autoSelfClosers.hasOwnProperty(y.tagName.toLowerCase())?s():"endTag"==e?(u(y.tagName,t),s()):s()}}function d(t){return function(e){return t&&(k="error"),"endTag"==e?(f(),s()):(k="error",s(arguments.callee))}}function p(t){return"word"==t?(k="attribute",s(p)):"equals"==t?s(m,p):"string"==t?(k="error",s(p)):a()}function m(t){return"word"==t&&w.allowUnquoted?(k="string",s()):"string"==t?s(g):a()}function g(t){return"string"==t?s(g):a()}var v,x,y,k,C=t.indentUnit,w=e.htmlMode?{autoSelfClosers:{br:!0,img:!0,hr:!0,link:!0,input:!0,meta:!0,col:!0,frame:!0,base:!0,area:!0},doNotIndent:{pre:!0},allowUnquoted:!0}:{autoSelfClosers:{},doNotIndent:{},allowUnquoted:!1},M=e.alignCDATA;return{startState:function(){return{tokenize:n,cc:[],indented:0,startOfLine:!0,tagName:null,context:null}},token:function(t,e){if(t.sol()&&(e.startOfLine=!0,e.indented=t.indentation()),t.eatSpace())return null;k=x=v=null;var n=e.tokenize(t,e);if(e.type=x,(n||x)&&"comment"!=n)for(y=e;;){var r=e.cc.pop()||c;if(r(x||n))break}return e.startOfLine=!1,k||n},indent:function(t,e,i){var o=t.context;if(t.tokenize!=r&&t.tokenize!=n||o&&o.noIndent)return i?i.match(/^(\s*)/)[0].length:0;if(M&&/<!\[CDATA\[/.test(e))return 0;for(o&&/^<\//.test(e)&&(o=o.prev);o&&!o.startOfLine;)o=o.prev;return o?o.indent+C:0},compareStates:function(t,e){if(t.indented!=e.indented||t.tokenize!=e.tokenize)return!1;for(var n=t.context,r=e.context;;n=n.prev,r=r.prev){if(!n||!r)return n==r;if(n.tagName!=r.tagName)return!1}},electricChars:"/"}}),CodeMirror.defineMIME("application/xml","xml"),CodeMirror.defineMIME("text/html",{name:"xml",htmlMode:!0}),CodeMirror.defineMode("javascript",function(t,e){function n(t,e,n){return e.tokenize=n,n(t,e)}function r(t,e){for(var n,r=!1;null!=(n=t.next());){if(n==e&&!r)return!1;r=!r&&"\\"==n}return r}function i(t,e,n){return O=t,R=n,e}function o(t,e){var o=t.next();if('"'==o||"'"==o)return n(t,e,l(o));if(/[\[\]{}\(\),;\:\.]/.test(o))return i(o);if("0"==o&&t.eat(/x/i))return t.eatWhile(/[\da-f]/i),i("number","number");if(/\d/.test(o))return t.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),i("number","number");if("/"==o)return t.eat("*")?n(t,e,a):t.eat("/")?(t.skipToEnd(),i("comment","comment")):e.reAllowed?(r(t,"/"),t.eatWhile(/[gimy]/),i("regexp","string")):(t.eatWhile(V),i("operator",null,t.current()));if("#"==o)return t.skipToEnd(),i("error","error");if(V.test(o))return t.eatWhile(V),i("operator",null,t.current());t.eatWhile(/[\w\$_]/);var s=t.current(),u=B.propertyIsEnumerable(s)&&B[s];return u&&e.kwAllowed?i(u.type,u.style,s):i("variable","variable",s)}function l(t){return function(e,n){return r(e,t)||(n.tokenize=o),i("string","string")}}function a(t,e){for(var n,r=!1;n=t.next();){if("/"==n&&r){e.tokenize=o;break}r="*"==n}return i("comment","comment")}function s(t,e,n,r,i,o){this.indented=t,this.column=e,this.type=n,this.prev=i,this.info=o,null!=r&&(this.align=r)}function u(t,e){for(var n=t.localVars;n;n=n.next)if(n.name==e)return!0}function f(t,e,n,r,i){var o=t.cc;for(F.state=t,F.stream=i,F.marked=null,F.cc=o,t.lexical.hasOwnProperty("align")||(t.lexical.align=!0);;){var l=o.length?o.pop():U?k:y;if(l(n,r)){for(;o.length&&o[o.length-1].lex;)o.pop()();return F.marked?F.marked:"variable"==n&&u(t,r)?"variable-2":e}}}function c(){for(var t=arguments.length-1;t>=0;t--)F.cc.push(arguments[t])}function h(){return c.apply(null,arguments),!0}function d(t){var e=F.state;if(e.context){F.marked="def";for(var n=e.localVars;n;n=n.next)if(n.name==t)return;e.localVars={name:t,next:e.localVars}}}function p(){F.state.context||(F.state.localVars=j),F.state.context={prev:F.state.context,vars:F.state.localVars}}function m(){F.state.localVars=F.state.context.vars,F.state.context=F.state.context.prev}function g(t,e){var n=function(){var n=F.state;n.lexical=new s(n.indented,F.stream.column(),t,null,n.lexical,e)};return n.lex=!0,n}function v(){var t=F.state;t.lexical.prev&&(")"==t.lexical.type&&(t.indented=t.lexical.indented),t.lexical=t.lexical.prev)}function x(t){return function(e){return e==t?h():";"==t?c():h(arguments.callee)}}function y(t){return"var"==t?h(g("vardef"),z,x(";"),v):"keyword a"==t?h(g("form"),k,y,v):"keyword b"==t?h(g("form"),y,v):"{"==t?h(g("}"),A,v):";"==t?h():"function"==t?h(H):"for"==t?h(g("form"),x("("),g(")"),E,x(")"),v,y,v):"variable"==t?h(g("stat"),M):"switch"==t?h(g("form"),k,g("}","switch"),x("{"),A,v,v):"case"==t?h(k,x(":")):"default"==t?h(x(":")):"catch"==t?h(g("form"),p,x("("),I,x(")"),y,v,m):c(g("stat"),k,x(";"),v)}function k(t){return K.hasOwnProperty(t)?h(w):"function"==t?h(H):"keyword c"==t?h(C):"("==t?h(g(")"),k,x(")"),v,w):"operator"==t?h(k):"["==t?h(g("]"),L(k,"]"),v,w):"{"==t?h(g("}"),L(S,"}"),v,w):h()}function C(t){return t.match(/[;\}\)\],]/)?c():c(k)}function w(t,e){if("operator"==t&&/\+\+|--/.test(e))return h(w);if("operator"==t)return h(k);if(";"!=t)return"("==t?h(g(")"),L(k,")"),v,w):"."==t?h(b,w):"["==t?h(g("]"),k,x("]"),v,w):void 0}function M(t){return":"==t?h(v,y):c(w,x(";"),v)}function b(t){if("variable"==t)return F.marked="property",h()}function S(t){if("variable"==t&&(F.marked="property"),K.hasOwnProperty(t))return h(x(":"),k)}function L(t,e){function n(r){return","==r?h(t,n):r==e?h():h(x(e))}return function(r){return r==e?h():c(t,n)}}function A(t){return"}"==t?h():c(y,A)}function z(t,e){return"variable"==t?(d(e),h(T)):h()}function T(t,e){return"="==e?h(k,T):","==t?h(z):void 0}function E(t){return"var"==t?h(z,N):";"==t?c(N):"variable"==t?h(W):c(N)}function W(t,e){return"in"==e?h(k):h(w,N)}function N(t,e){return";"==t?h(D):"in"==e?h(k):h(k,x(";"),D)}function D(t){")"!=t&&h(k)}function H(t,e){return"variable"==t?(d(e),h(H)):"("==t?h(g(")"),p,L(I,")"),v,y,m):void 0}function I(t,e){if("variable"==t)return d(e),h()}var O,R,P=t.indentUnit,U=e.json,B=function(){function t(t){return{type:t,style:"keyword"}}var e=t("keyword a"),n=t("keyword b"),r=t("keyword c"),i=t("operator"),o={type:"atom",style:"atom"};return{"if":e,"while":e,"with":e,"else":n,"do":n,"try":n,"finally":n,"return":r,"break":r,"continue":r,"new":r,"delete":r,"throw":r,"var":t("var"),"const":t("var"),"let":t("var"),"function":t("function"),"catch":t("catch"),"for":t("for"),"switch":t("switch"),"case":t("case"),"default":t("default"),"in":i,"typeof":i,"instanceof":i,"true":o,"false":o,"null":o,undefined:o,NaN:o,Infinity:o}}(),V=/[+\-*&%=<>!?|]/,K={atom:!0,number:!0,variable:!0,string:!0,regexp:!0},F={state:null,column:null,marked:null,cc:null},j={name:"this",next:{name:"arguments"}};return v.lex=!0,{startState:function(t){return{tokenize:o,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new s((t||0)-P,0,"block",(!1)),localVars:null,context:null,indented:0}},token:function(t,e){if(t.sol()&&(e.lexical.hasOwnProperty("align")||(e.lexical.align=!1),e.indented=t.indentation()),t.eatSpace())return null;var n=e.tokenize(t,e);return"comment"==O?n:(e.reAllowed="operator"==O||"keyword c"==O||O.match(/^[\[{}\(,;:]$/),e.kwAllowed="."!=O,f(e,n,O,R,t))},indent:function(t,e){if(t.tokenize!=o)return 0;var n=e&&e.charAt(0),r=t.lexical,i=r.type,l=n==i;return"vardef"==i?r.indented+4:"form"==i&&"{"==n?r.indented:"stat"==i||"form"==i?r.indented+P:"switch"!=r.info||l?r.align?r.column+(l?0:1):r.indented+(l?0:P):r.indented+(/^(?:case|default)\b/.test(e)?P:2*P)},electricChars:":{}"}}),CodeMirror.defineMIME("text/javascript","javascript"),CodeMirror.defineMIME("application/json",{name:"javascript",json:!0}),CodeMirror.defineMode("css",function(t){function e(t,e){return l=e,t}function n(t,n){var l=t.next();return"@"==l?(t.eatWhile(/[\w\\\-]/),e("meta",t.current())):"/"==l&&t.eat("*")?(n.tokenize=r,r(t,n)):"<"==l&&t.eat("!")?(n.tokenize=i,i(t,n)):"="!=l?"~"!=l&&"|"!=l||!t.eat("=")?'"'==l||"'"==l?(n.tokenize=o(l),n.tokenize(t,n)):"#"==l?(t.eatWhile(/[\w\\\-]/),e("atom","hash")):"!"==l?(t.match(/^\s*\w*/),e("keyword","important")):/\d/.test(l)?(t.eatWhile(/[\w.%]/),e("number","unit")):/[,.+>*\/]/.test(l)?e(null,"select-op"):/[;{}:\[\]]/.test(l)?e(null,l):(t.eatWhile(/[\w\\\-]/),e("variable","variable")):e(null,"compare"):void e(null,"compare")}function r(t,r){for(var i,o=!1;null!=(i=t.next());){if(o&&"/"==i){r.tokenize=n;break}o="*"==i}return e("comment","comment")}function i(t,r){for(var i,o=0;null!=(i=t.next());){if(o>=2&&">"==i){r.tokenize=n;break}o="-"==i?o+1:0}return e("comment","comment")}function o(t){return function(r,i){for(var o,l=!1;null!=(o=r.next())&&(o!=t||l);)l=!l&&"\\"==o;return l||(i.tokenize=n),e("string","string")}}var l,a=t.indentUnit;return{startState:function(t){return{tokenize:n,baseIndent:t||0,stack:[]}},token:function(t,e){if(t.eatSpace())return null;var n=e.tokenize(t,e),r=e.stack[e.stack.length-1];return"hash"==l&&"rule"==r?n="atom":"variable"==n&&("rule"==r?n="number":r&&"@media{"!=r||(n="tag")),"rule"==r&&/^[\{\};]$/.test(l)&&e.stack.pop(),"{"==l?"@media"==r?e.stack[e.stack.length-1]="@media{":e.stack.push("{"):"}"==l?e.stack.pop():"@media"==l?e.stack.push("@media"):"{"==r&&"comment"!=l&&e.stack.push("rule"),n},indent:function(t,e){var n=t.stack.length;return/^\}/.test(e)&&(n-="rule"==t.stack[t.stack.length-1]?2:1),t.baseIndent+n*a},electricChars:"}"}}),CodeMirror.defineMIME("text/css","css"),CodeMirror.defineMode("htmlmixed",function(t,e){function n(t,e){var n=l.token(t,e.htmlState);return"tag"==n&&">"==t.current()&&e.htmlState.context&&(/^script$/i.test(e.htmlState.context.tagName)?(e.token=i,e.localState=a.startState(l.indent(e.htmlState,"")),e.mode="javascript"):/^style$/i.test(e.htmlState.context.tagName)&&(e.token=o,e.localState=s.startState(l.indent(e.htmlState,"")),e.mode="css")),n}function r(t,e,n){var r=t.current(),i=r.search(e);return i>-1&&t.backUp(r.length-i),n}function i(t,e){return t.match(/^<\/\s*script\s*>/i,!1)?(e.token=n,e.curState=null,e.mode="html",n(t,e)):r(t,/<\/\s*script\s*>/,a.token(t,e.localState))}function o(t,e){return t.match(/^<\/\s*style\s*>/i,!1)?(e.token=n,e.localState=null,e.mode="html",n(t,e)):r(t,/<\/\s*style\s*>/,s.token(t,e.localState))}var l=CodeMirror.getMode(t,{name:"xml",htmlMode:!0}),a=CodeMirror.getMode(t,"javascript"),s=CodeMirror.getMode(t,"css");return{startState:function(){var t=l.startState();return{token:n,localState:null,mode:"html",htmlState:t}},copyState:function(t){if(t.localState)var e=CodeMirror.copyState(t.token==o?s:a,t.localState);return{token:t.token,localState:e,mode:t.mode,htmlState:CodeMirror.copyState(l,t.htmlState)}},token:function(t,e){return e.token(t,e)},indent:function(t,e){return t.token==n||/^\s*<\//.test(e)?l.indent(t.htmlState,e):t.token==i?a.indent(t.localState,e):s.indent(t.localState,e)},compareStates:function(t,e){return l.compareStates(t.htmlState,e.htmlState)},electricChars:"/{}:"}}),CodeMirror.defineMIME("text/html","htmlmixed");