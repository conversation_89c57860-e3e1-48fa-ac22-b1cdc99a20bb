using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using System;

namespace bnred.Model
{
    public class Localization : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        [Required]
        [StringLength(50)]
        public string EntityType { get; set; }

        [Required]
        public int EntityId { get; set; }

        [Required]
        public int lanID { get; set; }
        public Language lan { get; set; }

        [Required]
        public string Value { get; set; }

      
    }
}