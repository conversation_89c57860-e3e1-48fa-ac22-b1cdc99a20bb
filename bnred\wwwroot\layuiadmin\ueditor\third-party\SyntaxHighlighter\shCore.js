/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;var XRegExp;if(XRegExp)throw Error("can't load XRegExp twice in the same frame");if(function(e){function t(e,t){if(!XRegExp.isRegExp(e))throw TypeError("type RegExp expected");var i=e._xregexp;return e=XRegExp(e.source,r(e)+(t||"")),i&&(e._xregexp={source:i.source,captureNames:i.captureNames?i.captureNames.slice(0):null}),e}function r(e){return(e.global?"g":"")+(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.extended?"x":"")+(e.sticky?"y":"")}function i(e,t,r,i){var n,s,a,o=c.length;l=!0;try{for(;o--;)if(a=c[o],r&a.scope&&(!a.trigger||a.trigger.call(i))&&(a.pattern.lastIndex=t,s=a.pattern.exec(e),s&&s.index===t)){n={output:a.handler.call(i,s,r),match:s};break}}catch(g){throw g}finally{l=!1}return n}function n(e,t,r){if(Array.prototype.indexOf)return e.indexOf(t,r);for(var i=r||0;i<e.length;i++)if(e[i]===t)return i;return-1}XRegExp=function(r,n){var s,o,c,u,d,h=[],f=XRegExp.OUTSIDE_CLASS,m=0;if(XRegExp.isRegExp(r)){if(n!==e)throw TypeError("can't supply flags when constructing one RegExp from another");return t(r)}if(l)throw Error("can't call the XRegExp constructor within token definition functions");for(n=n||"",s={hasNamedCapture:!1,captureNames:[],hasFlag:function(e){return n.indexOf(e)>-1},setFlag:function(e){n+=e}};m<r.length;)o=i(r,m,f,s),o?(h.push(o.output),m+=o.match[0].length||1):(c=g.exec.call(p[f],r.slice(m)))?(h.push(c[0]),m+=c[0].length):(u=r.charAt(m),"["===u?f=XRegExp.INSIDE_CLASS:"]"===u&&(f=XRegExp.OUTSIDE_CLASS),h.push(u),m++);return d=RegExp(h.join(""),g.replace.call(n,a,"")),d._xregexp={source:r,captureNames:s.hasNamedCapture?s.captureNames:null},d},XRegExp.version="1.5.1",XRegExp.INSIDE_CLASS=1,XRegExp.OUTSIDE_CLASS=2;var s=/\$(?:(\d\d?|[$&`'])|{([$\w]+)})/g,a=/[^gimy]+|([\s\S])(?=[\s\S]*\1)/g,o=/^(?:[?*+]|{\d+(?:,\d*)?})\??/,l=!1,c=[],g={exec:RegExp.prototype.exec,test:RegExp.prototype.test,match:String.prototype.match,replace:String.prototype.replace,split:String.prototype.split},u=g.exec.call(/()??/,"")[1]===e,d=function(){var e=/^/g;return g.test.call(e,""),!e.lastIndex}(),h=RegExp.prototype.sticky!==e,p={};p[XRegExp.INSIDE_CLASS]=/^(?:\\(?:[0-3][0-7]{0,2}|[4-7][0-7]?|x[\dA-Fa-f]{2}|u[\dA-Fa-f]{4}|c[A-Za-z]|[\s\S]))/,p[XRegExp.OUTSIDE_CLASS]=/^(?:\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9]\d*|x[\dA-Fa-f]{2}|u[\dA-Fa-f]{4}|c[A-Za-z]|[\s\S])|\(\?[:=!]|[?*+]\?|{\d+(?:,\d*)?}\??)/,XRegExp.addToken=function(e,r,i,n){c.push({pattern:t(e,"g"+(h?"y":"")),handler:r,scope:i||XRegExp.OUTSIDE_CLASS,trigger:n||null})},XRegExp.cache=function(e,t){var r=e+"/"+(t||"");return XRegExp.cache[r]||(XRegExp.cache[r]=XRegExp(e,t))},XRegExp.copyAsGlobal=function(e){return t(e,"g")},XRegExp.escape=function(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")},XRegExp.execAt=function(e,r,i,n){var s,a=t(r,"g"+(n&&h?"y":""));return a.lastIndex=i=i||0,s=a.exec(e),n&&s&&s.index!==i&&(s=null),r.global&&(r.lastIndex=s?a.lastIndex:0),s},XRegExp.freezeTokens=function(){XRegExp.addToken=function(){throw Error("can't run addToken after freezeTokens")}},XRegExp.isRegExp=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},XRegExp.iterate=function(e,r,i,n){for(var s,a=t(r,"g"),o=-1;s=a.exec(e);)r.global&&(r.lastIndex=a.lastIndex),i.call(n,s,++o,e,r),a.lastIndex===s.index&&a.lastIndex++;r.global&&(r.lastIndex=0)},XRegExp.matchChain=function(e,r){return function i(e,n){var s,a=r[n].regex?r[n]:{regex:r[n]},o=t(a.regex,"g"),l=[];for(s=0;s<e.length;s++)XRegExp.iterate(e[s],o,function(e){l.push(a.backref?e[a.backref]||"":e[0])});return n!==r.length-1&&l.length?i(l,n+1):l}([e],0)},RegExp.prototype.apply=function(e,t){return this.exec(t[0])},RegExp.prototype.call=function(e,t){return this.exec(t)},RegExp.prototype.exec=function(t){var i,s,a,o;if(this.global||(o=this.lastIndex),i=g.exec.apply(this,arguments)){if(!u&&i.length>1&&n(i,"")>-1&&(a=RegExp(this.source,g.replace.call(r(this),"g","")),g.replace.call((t+"").slice(i.index),a,function(){for(var t=1;t<arguments.length-2;t++)arguments[t]===e&&(i[t]=e)})),this._xregexp&&this._xregexp.captureNames)for(var l=1;l<i.length;l++)s=this._xregexp.captureNames[l-1],s&&(i[s]=i[l]);!d&&this.global&&!i[0].length&&this.lastIndex>i.index&&this.lastIndex--}return this.global||(this.lastIndex=o),i},RegExp.prototype.test=function(e){var t,r;return this.global||(r=this.lastIndex),t=g.exec.call(this,e),t&&!d&&this.global&&!t[0].length&&this.lastIndex>t.index&&this.lastIndex--,this.global||(this.lastIndex=r),!!t},String.prototype.match=function(e){if(XRegExp.isRegExp(e)||(e=RegExp(e)),e.global){var t=g.match.apply(this,arguments);return e.lastIndex=0,t}return e.exec(this)},String.prototype.replace=function(e,t){var r,i,a,o,l=XRegExp.isRegExp(e);return l?(e._xregexp&&(r=e._xregexp.captureNames),e.global||(o=e.lastIndex)):e+="","[object Function]"===Object.prototype.toString.call(t)?i=g.replace.call(this+"",e,function(){if(r){arguments[0]=new String(arguments[0]);for(var i=0;i<r.length;i++)r[i]&&(arguments[0][r[i]]=arguments[i+1])}return l&&e.global&&(e.lastIndex=arguments[arguments.length-2]+arguments[0].length),t.apply(null,arguments)}):(a=this+"",i=g.replace.call(a,e,function(){var e=arguments;return g.replace.call(t+"",s,function(t,i,s){if(!i){var a=+s;return a<=e.length-3?e[a]:(a=r?n(r,s):-1,a>-1?e[a+1]:t)}switch(i){case"$":return"$";case"&":return e[0];case"`":return e[e.length-1].slice(0,e[e.length-2]);case"'":return e[e.length-1].slice(e[e.length-2]+e[0].length);default:var o="";if(i=+i,!i)return t;for(;i>e.length-3;)o=String.prototype.slice.call(i,-1)+o,i=Math.floor(i/10);return(i?e[i]||"":"$")+o}})})),l&&(e.global?e.lastIndex=0:e.lastIndex=o),i},String.prototype.split=function(t,r){if(!XRegExp.isRegExp(t))return g.split.apply(this,arguments);var i,n,s=this+"",a=[],o=0;if(r===e||+r<0)r=1/0;else if(r=Math.floor(+r),!r)return[];for(t=XRegExp.copyAsGlobal(t);(i=t.exec(s))&&!(t.lastIndex>o&&(a.push(s.slice(o,i.index)),i.length>1&&i.index<s.length&&Array.prototype.push.apply(a,i.slice(1)),n=i[0].length,o=t.lastIndex,a.length>=r));)t.lastIndex===i.index&&t.lastIndex++;return o===s.length?g.test.call(t,"")&&!n||a.push(""):a.push(s.slice(o)),a.length>r?a.slice(0,r):a},XRegExp.addToken(/\(\?#[^)]*\)/,function(e){return g.test.call(o,e.input.slice(e.index+e[0].length))?"":"(?:)"}),XRegExp.addToken(/\((?!\?)/,function(){return this.captureNames.push(null),"("}),XRegExp.addToken(/\(\?<([$\w]+)>/,function(e){return this.captureNames.push(e[1]),this.hasNamedCapture=!0,"("}),XRegExp.addToken(/\\k<([\w$]+)>/,function(e){var t=n(this.captureNames,e[1]);return t>-1?"\\"+(t+1)+(isNaN(e.input.charAt(e.index+e[0].length))?"":"(?:)"):e[0]}),XRegExp.addToken(/\[\^?]/,function(e){return"[]"===e[0]?"\\b\\B":"[\\s\\S]"}),XRegExp.addToken(/^\(\?([imsx]+)\)/,function(e){return this.setFlag(e[1]),""}),XRegExp.addToken(/(?:\s+|#.*)+/,function(e){return g.test.call(o,e.input.slice(e.index+e[0].length))?"":"(?:)"},XRegExp.OUTSIDE_CLASS,function(){return this.hasFlag("x")}),XRegExp.addToken(/\./,function(){return"[\\s\\S]"},XRegExp.OUTSIDE_CLASS,function(){return this.hasFlag("s")})}(),"undefined"==typeof SyntaxHighlighter)var SyntaxHighlighter=function(){function e(e,t){return e.className.indexOf(t)!=-1}function t(t,r){e(t,r)||(t.className+=" "+r)}function r(e,t){e.className=e.className.replace(t,"")}function i(e){for(var t=[],r=0;r<e.length;r++)t.push(e[r]);return t}function n(e){return e.split(/\r?\n/)}function s(e){var t="highlighter_";return 0==e.indexOf(t)?e:t+e}function a(e){return D.vars.highlighters[s(e)]}function o(e){return document.getElementById(s(e))}function l(e){D.vars.highlighters[s(e.id)]=e}function c(e,t,r){if(null==e)return null;var i,n,s=1!=r?e.childNodes:[e.parentNode],a={"#":"id",".":"className"}[t.substr(0,1)]||"nodeName";if(i="nodeName"!=a?t.substr(1):t.toUpperCase(),(e[a]||"").indexOf(i)!=-1)return e;for(var o=0;s&&o<s.length&&null==n;o++)n=c(s[o],t,r);return n}function g(e,t){return c(e,t,!0)}function u(e,t,r){r=Math.max(r||0,0);for(var i=r;i<e.length;i++)if(e[i]==t)return i;return-1}function d(e){return(e||"")+Math.round(1e6*Math.random()).toString()}function h(e,t){var r,i={};for(r in e)i[r]=e[r];for(r in t)i[r]=t[r];return i}function p(e){var t={"true":!0,"false":!1}[e];return null==t?e:t}function f(e,t,r,i,n){var s=(screen.width-r)/2,a=(screen.height-i)/2;n+=", left="+s+", top="+a+", width="+r+", height="+i,n=n.replace(/^,/,"");var o=window.open(e,t,n);return o.focus(),o}function m(e,t,r,i){function n(e){e=e||window.event,e.target||(e.target=e.srcElement,e.preventDefault=function(){this.returnValue=!1}),r.call(i||window,e)}e.attachEvent?e.attachEvent("on"+t,n):e.addEventListener(t,n,!1)}function x(e){window.alert(D.config.strings.alert+e)}function y(e,t){var r=D.vars.discoveredBrushes,i=null;if(null==r){r={};for(var n in D.brushes){var s=D.brushes[n],a=s.aliases;if(null!=a){s.brushName=n.toLowerCase();for(var o=0;o<a.length;o++)r[a[o]]=n}}D.vars.discoveredBrushes=r}return i=D.brushes[r[e]],null==i&&t&&x(D.config.strings.noBrush+e),i}function b(e,t){for(var r=n(e),i=0;i<r.length;i++)r[i]=t(r[i],i);return r.join("\r\n")}function w(e){return e.replace(/^[ ]*[\n]+|[\n]*[ ]*$/g,"")}function S(e){for(var t,r={},i=new XRegExp("^\\[(?<values>(.*?))\\]$"),n=new XRegExp("(?<name>[\\w-]+)\\s*:\\s*(?<value>[\\w-%#]+|\\[.*?\\]|\".*?\"|'.*?')\\s*;?","g");null!=(t=n.exec(e));){var s=t.value.replace(/^['"]|['"]$/g,"");if(null!=s&&i.test(s)){var a=i.exec(s);s=a.values.length>0?a.values.split(/\s*,\s*/):[]}r[t.name]=s}return r}function v(e,t){return null==e||0==e.length||"\n"==e?e:(e=e.replace(/</g,"&lt;"),e=e.replace(/ {2,}/g,function(e){for(var t="",r=0;r<e.length-1;r++)t+=D.config.space;return t+" "}),null!=t&&(e=b(e,function(e){if(0==e.length)return"";var r="";return e=e.replace(/^(&nbsp;| )+/,function(e){return r=e,""}),0==e.length?r:r+'<code class="'+t+'">'+e+"</code>"})),e)}function H(e,t){for(var r=e.toString();r.length<t;)r="0"+r;return r}function L(e,t){for(var r="",i=0;i<t;i++)r+=" ";return e.replace(/\t/g,r)}function _(e,t){function r(e,t,r){return e.substr(0,t)+s.substr(0,r)+e.substr(t+1,e.length)}for(var i=(n(e),"\t"),s="",a=0;a<50;a++)s+="                    ";return e=b(e,function(e){if(e.indexOf(i)==-1)return e;for(var n=0;(n=e.indexOf(i))!=-1;){var s=t-n%t;e=r(e,n,s)}return e})}function R(e){var t=/<br\s*\/?>|&lt;br\s*\/?&gt;/gi;return 1==D.config.bloggerMode&&(e=e.replace(t,"\n")),1==D.config.stripBrs&&(e=e.replace(t,"")),e}function E(e){return e.replace(/^\s+|\s+$/g,"")}function C(e){for(var t=n(R(e)),r=(new Array,/^\s*/),i=1e3,s=0;s<t.length&&i>0;s++){var a=t[s];if(0!=E(a).length){var o=r.exec(a);if(null==o)return e;i=Math.min(o[0].length,i)}}if(i>0)for(var s=0;s<t.length;s++)t[s]=t[s].substr(i);return t.join("\n")}function k(e,t){return e.index<t.index?-1:e.index>t.index?1:e.length<t.length?-1:e.length>t.length?1:0}function I(e,t){function r(e,t){return e[0]}for(var i=null,n=[],s=t.func?t.func:r;null!=(i=t.regex.exec(e));){var a=s(i,t);"string"==typeof a&&(a=[new D.Match(a,i.index,t.css)]),n=n.concat(a)}return n}function T(e){var t=/(.*)((&gt;|&lt;).*)/;return e.replace(D.regexLib.url,function(e){var r="",i=null;return(i=t.exec(e))&&(e=i[1],r=i[2]),'<a href="'+e+'">'+e+"</a>"+r})}function N(){for(var e=document.getElementsByTagName("script"),t=[],r=0;r<e.length;r++)"syntaxhighlighter"==e[r].type&&t.push(e[r]);return t}function P(e){var t="<![CDATA[",r="]]>",i=E(e),n=!1,s=t.length,a=r.length;0==i.indexOf(t)&&(i=i.substring(s),n=!0);var o=i.length;return i.indexOf(r)==o-a&&(i=i.substring(0,o-a),n=!0),n?i:e}function A(e){var i,n=e.target,s=g(n,".syntaxhighlighter"),o=g(n,".container"),l=document.createElement("textarea");if(o&&s&&!c(o,"textarea")){i=a(s.id),t(s,"source");for(var u=o.childNodes,d=[],h=0;h<u.length;h++)d.push(u[h].innerText||u[h].textContent);d=d.join("\r"),d=d.replace(/\u00a0/g," "),l.appendChild(document.createTextNode(d)),o.appendChild(l),l.focus(),l.select(),m(l,"blur",function(e){l.parentNode.removeChild(l),r(s,"source")})}}"undefined"!=typeof require&&"undefined"==typeof XRegExp&&(XRegExp=require("XRegExp").XRegExp);var D={defaults:{"class-name":"","first-line":1,"pad-line-numbers":!1,highlight:!1,title:null,"smart-tabs":!0,"tab-size":4,gutter:!0,toolbar:!0,"quick-code":!0,collapse:!1,"auto-links":!1,light:!1,unindent:!0,"html-script":!1},config:{space:"&nbsp;",useScriptTags:!0,bloggerMode:!1,stripBrs:!1,tagName:"pre",strings:{expandSource:"expand source",help:"?",alert:"SyntaxHighlighter\n\n",noBrush:"Can't find brush for: ",brushNotHtmlScript:"Brush wasn't configured for html-script option: ",aboutDialog:"@ABOUT@"}},vars:{discoveredBrushes:null,highlighters:{}},brushes:{},regexLib:{multiLineCComments:/\/\*[\s\S]*?\*\//gm,singleLineCComments:/\/\/.*$/gm,singleLinePerlComments:/#.*$/gm,doubleQuotedString:/"([^\\"\n]|\\.)*"/g,singleQuotedString:/'([^\\'\n]|\\.)*'/g,multiLineDoubleQuotedString:new XRegExp('"([^\\\\"]|\\\\.)*"',"gs"),multiLineSingleQuotedString:new XRegExp("'([^\\\\']|\\\\.)*'","gs"),xmlComments:/(&lt;|<)!--[\s\S]*?--(&gt;|>)/gm,url:/\w+:\/\/[\w-.\/?%&=:@;#]*/g,phpScriptTags:{left:/(&lt;|<)\?(?:=|php)?/g,right:/\?(&gt;|>)/g,eof:!0},aspScriptTags:{left:/(&lt;|<)%=?/g,right:/%(&gt;|>)/g},scriptScriptTags:{left:/(&lt;|<)\s*script.*?(&gt;|>)/gi,right:/(&lt;|<)\/\s*script\s*(&gt;|>)/gi}},toolbar:{getHtml:function(e){function t(e,t){return D.toolbar.getButtonHtml(e,t,D.config.strings[t])}for(var r='<div class="toolbar">',i=D.toolbar.items,n=i.list,s=0;s<n.length;s++)r+=(i[n[s]].getHtml||t)(e,n[s]);return r+="</div>"},getButtonHtml:function(e,t,r){return'<span><a href="#" class="toolbar_item command_'+t+" "+t+'">'+r+"</a></span>"},handler:function(e){function t(e){var t=new RegExp(e+"_(\\w+)"),r=t.exec(i);return r?r[1]:null}var r=e.target,i=r.className||"",n=a(g(r,".syntaxhighlighter").id),s=t("command");n&&s&&D.toolbar.items[s].execute(n),e.preventDefault()},items:{list:["expandSource","help"],expandSource:{getHtml:function(e){if(1!=e.getParam("collapse"))return"";var t=e.getParam("title");return D.toolbar.getButtonHtml(e,"expandSource",t?t:D.config.strings.expandSource)},execute:function(e){var t=o(e.id);r(t,"collapsed")}},help:{execute:function(e){var t=f("","_blank",500,250,"scrollbars=0"),r=t.document;r.write(D.config.strings.aboutDialog),r.close(),t.focus()}}}},findElements:function(e,t){var r=t?[t]:i(document.getElementsByTagName(D.config.tagName)),n=D.config,s=[];if(n.useScriptTags&&(r=r.concat(N())),0===r.length)return s;for(var a=0;a<r.length;a++){var o={target:r[a],params:h(e,S(r[a].className))};null!=o.params.brush&&s.push(o)}return s},highlight:function(e,t){var r=this.findElements(e,t),i="innerHTML",n=null,s=D.config;if(0!==r.length)for(var a=0;a<r.length;a++){var o,t=r[a],l=t.target,c=t.params,g=c.brush;if(null!=g){if("true"==c["html-script"]||1==D.defaults["html-script"])n=new D.HtmlScript(g),g="htmlscript";else{var u=y(g);if(!u)continue;n=new u}o=l[i],s.useScriptTags&&(o=P(o)),""!=(l.title||"")&&(c.title=l.title),c.brush=g,n.init(c),t=n.getDiv(o),""!=(l.id||"")&&(t.id=l.id);var d=t.firstChild.firstChild;d.className=t.firstChild.className,l.parentNode.replaceChild(d,l)}}},all:function(e){m(window,"load",function(){D.highlight(e)})}};return D.Match=function(e,t,r){this.value=e,this.index=t,this.length=e.length,this.css=r,this.brushName=null},D.Match.prototype.toString=function(){return this.value},D.HtmlScript=function(e){function t(e,t){for(var r=0;r<e.length;r++)e[r].index+=t}function r(e,r){for(var s,a=e.code,o=[],l=i.regexList,c=e.index+e.left.length,g=i.htmlScript,u=0;u<l.length;u++)s=I(a,l[u]),t(s,c),o=o.concat(s);null!=g.left&&null!=e.left&&(s=I(e.left,g.left),t(s,e.index),o=o.concat(s)),null!=g.right&&null!=e.right&&(s=I(e.right,g.right),t(s,e.index+e[0].lastIndexOf(e.right)),o=o.concat(s));for(var d=0;d<o.length;d++)o[d].brushName=n.brushName;return o}var i,n=y(e),s=new D.brushes.Xml,a=this,o="getDiv getHtml init".split(" ");if(null!=n){i=new n;for(var l=0;l<o.length;l++)(function(){var e=o[l];a[e]=function(){return s[e].apply(s,arguments)}})();return null==i.htmlScript?void x(D.config.strings.brushNotHtmlScript+e):void s.regexList.push({regex:i.htmlScript.code,func:r})}},D.Highlighter=function(){},D.Highlighter.prototype={getParam:function(e,t){var r=this.params[e];return p(null==r?t:r)},create:function(e){return document.createElement(e)},findMatches:function(e,t){var r=[];if(null!=e)for(var i=0;i<e.length;i++)"object"==typeof e[i]&&(r=r.concat(I(t,e[i])));return this.removeNestedMatches(r.sort(k))},removeNestedMatches:function(e){for(var t=0;t<e.length;t++)if(null!==e[t])for(var r=e[t],i=r.index+r.length,n=t+1;n<e.length&&null!==e[t];n++){var s=e[n];if(null!==s){if(s.index>i)break;s.index==r.index&&s.length>r.length?e[t]=null:s.index>=r.index&&s.index<i&&(e[n]=null)}}return e},figureOutLineNumbers:function(e){var t=[],r=parseInt(this.getParam("first-line"));return b(e,function(e,i){t.push(i+r)}),t},isLineHighlighted:function(e){var t=this.getParam("highlight",[]);return"object"!=typeof t&&null==t.push&&(t=[t]),u(t,e.toString())!=-1},getLineHtml:function(e,t,r){var i=["line","number"+t,"index"+e,"alt"+(t%2==0?1:2).toString()];return this.isLineHighlighted(t)&&i.push("highlighted"),0==t&&i.push("break"),'<div class="'+i.join(" ")+'">'+r+"</div>"},getLineNumbersHtml:function(e,t){var r="",i=n(e).length,s=parseInt(this.getParam("first-line")),a=this.getParam("pad-line-numbers");1==a?a=(s+i-1).toString().length:1==isNaN(a)&&(a=0);for(var o=0;o<i;o++){var l=t?t[o]:s+o,e=0==l?D.config.space:H(l,a);r+=this.getLineHtml(o,l,e)}return r},getCodeLinesHtml:function(e,t){e=E(e);for(var r=n(e),i=(this.getParam("pad-line-numbers"),parseInt(this.getParam("first-line"))),e="",s=this.getParam("brush"),a=0;a<r.length;a++){var o=r[a],l=/^(&nbsp;|\s)+/.exec(o),c=null,g=t?t[a]:i+a;null!=l&&(c=l[0].toString(),o=o.substr(c.length),c=c.replace(" ",D.config.space)),o=E(o),0==o.length&&(o=D.config.space),e+=this.getLineHtml(a,g,(null!=c?'<code class="'+s+' spaces">'+c+"</code>":"")+o)}return e},getTitleHtml:function(e){return e?"<caption>"+e+"</caption>":""},getMatchesHtml:function(e,t){function r(e){var t=e?e.brushName||s:s;return t?t+" ":""}for(var i=0,n="",s=this.getParam("brush",""),a=0;a<t.length;a++){var o,l=t[a];null!==l&&0!==l.length&&(o=r(l),n+=v(e.substr(i,l.index-i),o+"plain")+v(l.value,o+l.css),i=l.index+l.length+(l.offset||0))}return n+=v(e.substr(i),r()+"plain")},getHtml:function(e){var t,r,i,n="",a=["syntaxhighlighter"];return 1==this.getParam("light")&&(this.params.toolbar=this.params.gutter=!1),className="syntaxhighlighter",1==this.getParam("collapse")&&a.push("collapsed"),0==(gutter=this.getParam("gutter"))&&a.push("nogutter"),a.push(this.getParam("class-name")),a.push(this.getParam("brush")),e=w(e).replace(/\r/g," "),t=this.getParam("tab-size"),e=1==this.getParam("smart-tabs")?_(e,t):L(e,t),this.getParam("unindent")&&(e=C(e)),gutter&&(i=this.figureOutLineNumbers(e)),r=this.findMatches(this.regexList,e),n=this.getMatchesHtml(e,r),n=this.getCodeLinesHtml(n,i),this.getParam("auto-links")&&(n=T(n)),"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.match(/MSIE/)&&a.push("ie"),n='<div id="'+s(this.id)+'" class="'+a.join(" ")+'">'+(this.getParam("toolbar")?D.toolbar.getHtml(this):"")+'<table border="0" cellpadding="0" cellspacing="0">'+this.getTitleHtml(this.getParam("title"))+"<tbody><tr>"+(gutter?'<td class="gutter">'+this.getLineNumbersHtml(e)+"</td>":"")+'<td class="code"><div class="container">'+n+"</div></td></tr></tbody></table></div>"},getDiv:function(e){null===e&&(e=""),this.code=e;var t=this.create("div");return t.innerHTML=this.getHtml(e),this.getParam("toolbar")&&m(c(t,".toolbar"),"click",D.toolbar.handler),this.getParam("quick-code")&&m(c(t,".code"),"dblclick",A),t},init:function(e){this.id=d(),l(this),this.params=h(D.defaults,e||{}),1==this.getParam("light")&&(this.params.toolbar=this.params.gutter=!1)},getKeywords:function(e){return e=e.replace(/^\s+|\s+$/g,"").replace(/\s+/g,"|"),"\\b(?:"+e+")\\b"},forHtmlScript:function(e){var t={end:e.right.source};e.eof&&(t.end="(?:(?:"+t.end+")|$)"),this.htmlScript={left:{regex:e.left,css:"script"},right:{regex:e.right,css:"script"},code:new XRegExp("(?<left>"+e.left.source+")(?<code>.*?)(?<right>"+t.end+")","sgi")}}},D}();"undefined"!=typeof exports?exports.SyntaxHighlighter=SyntaxHighlighter:null,function(){function e(){var e="class interface function package",t="-Infinity ...rest Array as AS3 Boolean break case catch const continue Date decodeURI decodeURIComponent default delete do dynamic each else encodeURI encodeURIComponent escape extends false final finally flash_proxy for get if implements import in include Infinity instanceof int internal is isFinite isNaN isXMLName label namespace NaN native new null Null Number Object object_proxy override parseFloat parseInt private protected public return set static String super switch this throw true try typeof uint undefined unescape use void while with";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b([\d]+(\.[\d]+)?|0x[a-f0-9]+)\b/gi,css:"value"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"color3"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword"},{regex:new RegExp("var","gm"),css:"variable"},{regex:new RegExp("trace","gm"),css:"color1"}],this.forHtmlScript(SyntaxHighlighter.regexLib.scriptScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["actionscript3","as3"],SyntaxHighlighter.brushes.AS3=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="after before beginning continue copy each end every from return get global in local named of set some that the then times to where whose with without",t="first second third fourth fifth sixth seventh eighth ninth tenth last front back middle",r="activate add alias AppleScript ask attachment boolean class constant delete duplicate empty exists false id integer list make message modal modified new no paragraph pi properties quit real record remove rest result reveal reverse run running save string true word yes";this.regexList=[{regex:/(--|#).*$/gm,css:"comments"},{regex:/\(\*(?:[\s\S]*?\(\*[\s\S]*?\*\))*[\s\S]*?\*\)/gm,css:"comments"},{regex:/"[\s\S]*?"/gm,css:"string"},{regex:/(?:,|:|¬|'s\b|\(|\)|\{|\}|«|\b\w*»)/g,css:"color1"},{regex:/(-)?(\d)+(\.(\d)?)?(E\+(\d)+)?/g,css:"color1"},{regex:/(?:&(amp;|gt;|lt;)?|=|� |>|<|≥|>=|≤|<=|\*|\+|-|\/|÷|\^)/g,css:"color2"},{regex:/\b(?:and|as|div|mod|not|or|return(?!\s&)(ing)?|equals|(is(n't| not)? )?equal( to)?|does(n't| not) equal|(is(n't| not)? )?(greater|less) than( or equal( to)?)?|(comes|does(n't| not) come) (after|before)|is(n't| not)?( in)? (back|front) of|is(n't| not)? behind|is(n't| not)?( (in|contained by))?|does(n't| not) contain|contain(s)?|(start|begin|end)(s)? with|((but|end) )?(consider|ignor)ing|prop(erty)?|(a )?ref(erence)?( to)?|repeat (until|while|with)|((end|exit) )?repeat|((else|end) )?if|else|(end )?(script|tell|try)|(on )?error|(put )?into|(of )?(it|me)|its|my|with (timeout( of)?|transaction)|end (timeout|transaction))\b/g,css:"keyword"},{regex:/\b\d+(st|nd|rd|th)\b/g,css:"keyword"},{regex:/\b(?:about|above|against|around|at|below|beneath|beside|between|by|(apart|aside) from|(instead|out) of|into|on(to)?|over|since|thr(ough|u)|under)\b/g,css:"color3"},{regex:/\b(?:adding folder items to|after receiving|choose( ((remote )?application|color|folder|from list|URL))?|clipboard info|set the clipboard to|(the )?clipboard|entire contents|display(ing| (alert|dialog|mode))?|document( (edited|file|nib name))?|file( (name|type))?|(info )?for|giving up after|(name )?extension|quoted form|return(ed)?|second(?! item)(s)?|list (disks|folder)|text item(s| delimiters)?|(Unicode )?text|(disk )?item(s)?|((current|list) )?view|((container|key) )?window|with (data|icon( (caution|note|stop))?|parameter(s)?|prompt|properties|seed|title)|case|diacriticals|hyphens|numeric strings|punctuation|white space|folder creation|application(s( folder)?| (processes|scripts position|support))?|((desktop )?(pictures )?|(documents|downloads|favorites|home|keychain|library|movies|music|public|scripts|sites|system|users|utilities|workflows) )folder|desktop|Folder Action scripts|font(s| panel)?|help|internet plugins|modem scripts|(system )?preferences|printer descriptions|scripting (additions|components)|shared (documents|libraries)|startup (disk|items)|temporary items|trash|on server|in AppleTalk zone|((as|long|short) )?user name|user (ID|locale)|(with )?password|in (bundle( with identifier)?|directory)|(close|open for) access|read|write( permission)?|(g|s)et eof|using( delimiters)?|starting at|default (answer|button|color|country code|entr(y|ies)|identifiers|items|name|location|script editor)|hidden( answer)?|open(ed| (location|untitled))?|error (handling|reporting)|(do( shell)?|load|run|store) script|administrator privileges|altering line endings|get volume settings|(alert|boot|input|mount|output|set) volume|output muted|(fax|random )?number|round(ing)?|up|down|toward zero|to nearest|as taught in school|system (attribute|info)|((AppleScript( Studio)?|system) )?version|(home )?directory|(IPv4|primary Ethernet) address|CPU (type|speed)|physical memory|time (stamp|to GMT)|replacing|ASCII (character|number)|localized string|from table|offset|summarize|beep|delay|say|(empty|multiple) selections allowed|(of|preferred) type|invisibles|showing( package contents)?|editable URL|(File|FTP|News|Media|Web) [Ss]ervers|Telnet hosts|Directory services|Remote applications|waiting until completion|saving( (in|to))?|path (for|to( (((current|frontmost) )?application|resource))?)|POSIX (file|path)|(background|RGB) color|(OK|cancel) button name|cancel button|button(s)?|cubic ((centi)?met(re|er)s|yards|feet|inches)|square ((kilo)?met(re|er)s|miles|yards|feet)|(centi|kilo)?met(re|er)s|miles|yards|feet|inches|lit(re|er)s|gallons|quarts|(kilo)?grams|ounces|pounds|degrees (Celsius|Fahrenheit|Kelvin)|print( (dialog|settings))?|clos(e(able)?|ing)|(de)?miniaturized|miniaturizable|zoom(ed|able)|attribute run|action (method|property|title)|phone|email|((start|end)ing|home) page|((birth|creation|current|custom|modification) )?date|((((phonetic )?(first|last|middle))|computer|host|maiden|related) |nick)?name|aim|icq|jabber|msn|yahoo|address(es)?|save addressbook|should enable action|city|country( code)?|formatte(r|d address)|(palette )?label|state|street|zip|AIM [Hh]andle(s)?|my card|select(ion| all)?|unsaved|(alpha )?value|entr(y|ies)|group|(ICQ|Jabber|MSN) handle|person|people|company|department|icon image|job title|note|organization|suffix|vcard|url|copies|collating|pages (across|down)|request print time|target( printer)?|((GUI Scripting|Script menu) )?enabled|show Computer scripts|(de)?activated|awake from nib|became (key|main)|call method|of (class|object)|center|clicked toolbar item|closed|for document|exposed|(can )?hide|idle|keyboard (down|up)|event( (number|type))?|launch(ed)?|load (image|movie|nib|sound)|owner|log|mouse (down|dragged|entered|exited|moved|up)|move|column|localization|resource|script|register|drag (info|types)|resigned (active|key|main)|resiz(e(d)?|able)|right mouse (down|dragged|up)|scroll wheel|(at )?index|should (close|open( untitled)?|quit( after last window closed)?|zoom)|((proposed|screen) )?bounds|show(n)?|behind|in front of|size (mode|to fit)|update(d| toolbar item)?|was (hidden|miniaturized)|will (become active|close|finish launching|hide|miniaturize|move|open|quit|(resign )?active|((maximum|minimum|proposed) )?size|show|zoom)|bundle|data source|movie|pasteboard|sound|tool(bar| tip)|(color|open|save) panel|coordinate system|frontmost|main( (bundle|menu|window))?|((services|(excluded from )?windows) )?menu|((executable|frameworks|resource|scripts|shared (frameworks|support)) )?path|(selected item )?identifier|data|content(s| view)?|character(s)?|click count|(command|control|option|shift) key down|context|delta (x|y|z)|key( code)?|location|pressure|unmodified characters|types|(first )?responder|playing|(allowed|selectable) identifiers|allows customization|(auto saves )?configuration|visible|image( name)?|menu form representation|tag|user(-| )defaults|associated file name|(auto|needs) display|current field editor|floating|has (resize indicator|shadow)|hides when deactivated|level|minimized (image|title)|opaque|position|release when closed|sheet|title(d)?)\b/g,css:"color3"},{regex:new RegExp(this.getKeywords(r),"gm"),css:"color3"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["applescript"],SyntaxHighlighter.brushes.AppleScript=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="if fi then elif else for do done until while break continue case esac function return in eq ne ge le",t="alias apropos awk basename bash bc bg builtin bzip2 cal cat cd cfdisk chgrp chmod chown chrootcksum clear cmp comm command cp cron crontab csplit cut date dc dd ddrescue declare df diff diff3 dig dir dircolors dirname dirs du echo egrep eject enable env ethtool eval exec exit expand export expr false fdformat fdisk fg fgrep file find fmt fold format free fsck ftp gawk getopts grep groups gzip hash head history hostname id ifconfig import install join kill less let ln local locate logname logout look lpc lpr lprint lprintd lprintq lprm ls lsof make man mkdir mkfifo mkisofs mknod more mount mtools mv netstat nice nl nohup nslookup open op passwd paste pathchk ping popd pr printcap printenv printf ps pushd pwd quota quotacheck quotactl ram rcp read readonly renice remsync rm rmdir rsync screen scp sdiff sed select seq set sftp shift shopt shutdown sleep sort source split ssh strace su sudo sum symlink sync tail tar tee test time times touch top traceroute trap tr true tsort tty type ulimit umask umount unalias uname unexpand uniq units unset unshar useradd usermod users uuencode uudecode v vdir vi watch wc whereis which who whoami Wget xargs yes";this.regexList=[{regex:/^#!.*$/gm,css:"preprocessor bold"},{regex:/\/[\w-\/]+/gm,css:"plain"},{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"functions"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["bash","shell","sh"],SyntaxHighlighter.brushes.Bash=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="Abs ACos AddSOAPRequestHeader AddSOAPResponseHeader AjaxLink AjaxOnLoad ArrayAppend ArrayAvg ArrayClear ArrayDeleteAt ArrayInsertAt ArrayIsDefined ArrayIsEmpty ArrayLen ArrayMax ArrayMin ArraySet ArraySort ArraySum ArraySwap ArrayToList Asc ASin Atn BinaryDecode BinaryEncode BitAnd BitMaskClear BitMaskRead BitMaskSet BitNot BitOr BitSHLN BitSHRN BitXor Ceiling CharsetDecode CharsetEncode Chr CJustify Compare CompareNoCase Cos CreateDate CreateDateTime CreateObject CreateODBCDate CreateODBCDateTime CreateODBCTime CreateTime CreateTimeSpan CreateUUID DateAdd DateCompare DateConvert DateDiff DateFormat DatePart Day DayOfWeek DayOfWeekAsString DayOfYear DaysInMonth DaysInYear DE DecimalFormat DecrementValue Decrypt DecryptBinary DeleteClientVariable DeserializeJSON DirectoryExists DollarFormat DotNetToCFType Duplicate Encrypt EncryptBinary Evaluate Exp ExpandPath FileClose FileCopy FileDelete FileExists FileIsEOF FileMove FileOpen FileRead FileReadBinary FileReadLine FileSetAccessMode FileSetAttribute FileSetLastModified FileWrite Find FindNoCase FindOneOf FirstDayOfMonth Fix FormatBaseN GenerateSecretKey GetAuthUser GetBaseTagData GetBaseTagList GetBaseTemplatePath GetClientVariablesList GetComponentMetaData GetContextRoot GetCurrentTemplatePath GetDirectoryFromPath GetEncoding GetException GetFileFromPath GetFileInfo GetFunctionList GetGatewayHelper GetHttpRequestData GetHttpTimeString GetK2ServerDocCount GetK2ServerDocCountLimit GetLocale GetLocaleDisplayName GetLocalHostIP GetMetaData GetMetricData GetPageContext GetPrinterInfo GetProfileSections GetProfileString GetReadableImageFormats GetSOAPRequest GetSOAPRequestHeader GetSOAPResponse GetSOAPResponseHeader GetTempDirectory GetTempFile GetTemplatePath GetTickCount GetTimeZoneInfo GetToken GetUserRoles GetWriteableImageFormats Hash Hour HTMLCodeFormat HTMLEditFormat IIf ImageAddBorder ImageBlur ImageClearRect ImageCopy ImageCrop ImageDrawArc ImageDrawBeveledRect ImageDrawCubicCurve ImageDrawLine ImageDrawLines ImageDrawOval ImageDrawPoint ImageDrawQuadraticCurve ImageDrawRect ImageDrawRoundRect ImageDrawText ImageFlip ImageGetBlob ImageGetBufferedImage ImageGetEXIFTag ImageGetHeight ImageGetIPTCTag ImageGetWidth ImageGrayscale ImageInfo ImageNegative ImageNew ImageOverlay ImagePaste ImageRead ImageReadBase64 ImageResize ImageRotate ImageRotateDrawingAxis ImageScaleToFit ImageSetAntialiasing ImageSetBackgroundColor ImageSetDrawingColor ImageSetDrawingStroke ImageSetDrawingTransparency ImageSharpen ImageShear ImageShearDrawingAxis ImageTranslate ImageTranslateDrawingAxis ImageWrite ImageWriteBase64 ImageXORDrawingMode IncrementValue InputBaseN Insert Int IsArray IsBinary IsBoolean IsCustomFunction IsDate IsDDX IsDebugMode IsDefined IsImage IsImageFile IsInstanceOf IsJSON IsLeapYear IsLocalHost IsNumeric IsNumericDate IsObject IsPDFFile IsPDFObject IsQuery IsSimpleValue IsSOAPRequest IsStruct IsUserInAnyRole IsUserInRole IsUserLoggedIn IsValid IsWDDX IsXML IsXmlAttribute IsXmlDoc IsXmlElem IsXmlNode IsXmlRoot JavaCast JSStringFormat LCase Left Len ListAppend ListChangeDelims ListContains ListContainsNoCase ListDeleteAt ListFind ListFindNoCase ListFirst ListGetAt ListInsertAt ListLast ListLen ListPrepend ListQualify ListRest ListSetAt ListSort ListToArray ListValueCount ListValueCountNoCase LJustify Log Log10 LSCurrencyFormat LSDateFormat LSEuroCurrencyFormat LSIsCurrency LSIsDate LSIsNumeric LSNumberFormat LSParseCurrency LSParseDateTime LSParseEuroCurrency LSParseNumber LSTimeFormat LTrim Max Mid Min Minute Month MonthAsString Now NumberFormat ParagraphFormat ParseDateTime Pi PrecisionEvaluate PreserveSingleQuotes Quarter QueryAddColumn QueryAddRow QueryConvertForGrid QueryNew QuerySetCell QuotedValueList Rand Randomize RandRange REFind REFindNoCase ReleaseComObject REMatch REMatchNoCase RemoveChars RepeatString Replace ReplaceList ReplaceNoCase REReplace REReplaceNoCase Reverse Right RJustify Round RTrim Second SendGatewayMessage SerializeJSON SetEncoding SetLocale SetProfileString SetVariable Sgn Sin Sleep SpanExcluding SpanIncluding Sqr StripCR StructAppend StructClear StructCopy StructCount StructDelete StructFind StructFindKey StructFindValue StructGet StructInsert StructIsEmpty StructKeyArray StructKeyExists StructKeyList StructKeyList StructNew StructSort StructUpdate Tan TimeFormat ToBase64 ToBinary ToScript ToString Trim UCase URLDecode URLEncodedFormat URLSessionFormat Val ValueList VerifyClient Week Wrap Wrap WriteOutput XmlChildPos XmlElemNew XmlFormat XmlGetNodeType XmlNew XmlParse XmlSearch XmlTransform XmlValidate Year YesNoFormat",t="cfabort cfajaximport cfajaxproxy cfapplet cfapplication cfargument cfassociate cfbreak cfcache cfcalendar cfcase cfcatch cfchart cfchartdata cfchartseries cfcol cfcollection cfcomponent cfcontent cfcookie cfdbinfo cfdefaultcase cfdirectory cfdiv cfdocument cfdocumentitem cfdocumentsection cfdump cfelse cfelseif cferror cfexchangecalendar cfexchangeconnection cfexchangecontact cfexchangefilter cfexchangemail cfexchangetask cfexecute cfexit cffeed cffile cfflush cfform cfformgroup cfformitem cfftp cffunction cfgrid cfgridcolumn cfgridrow cfgridupdate cfheader cfhtmlhead cfhttp cfhttpparam cfif cfimage cfimport cfinclude cfindex cfinput cfinsert cfinterface cfinvoke cfinvokeargument cflayout cflayoutarea cfldap cflocation cflock cflog cflogin cfloginuser cflogout cfloop cfmail cfmailparam cfmailpart cfmenu cfmenuitem cfmodule cfNTauthenticate cfobject cfobjectcache cfoutput cfparam cfpdf cfpdfform cfpdfformparam cfpdfparam cfpdfsubform cfpod cfpop cfpresentation cfpresentationslide cfpresenter cfprint cfprocessingdirective cfprocparam cfprocresult cfproperty cfquery cfqueryparam cfregistry cfreport cfreportparam cfrethrow cfreturn cfsavecontent cfschedule cfscript cfsearch cfselect cfset cfsetting cfsilent cfslider cfsprydataset cfstoredproc cfswitch cftable cftextarea cfthread cfthrow cftimer cftooltip cftrace cftransaction cftree cftreeitem cftry cfupdate cfwddx cfwindow cfxml cfzip cfzipparam",r="all and any between cross in join like not null or outer some";
this.regexList=[{regex:new RegExp("--(.*)$","gm"),css:"comments"},{regex:SyntaxHighlighter.regexLib.xmlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(e),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(r),"gmi"),css:"color1"},{regex:new RegExp(this.getKeywords(t),"gmi"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["coldfusion","cf"],SyntaxHighlighter.brushes.ColdFusion=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="ATOM BOOL BOOLEAN BYTE CHAR COLORREF DWORD DWORDLONG DWORD_PTR DWORD32 DWORD64 FLOAT HACCEL HALF_PTR HANDLE HBITMAP HBRUSH HCOLORSPACE HCONV HCONVLIST HCURSOR HDC HDDEDATA HDESK HDROP HDWP HENHMETAFILE HFILE HFONT HGDIOBJ HGLOBAL HHOOK HICON HINSTANCE HKEY HKL HLOCAL HMENU HMETAFILE HMODULE HMONITOR HPALETTE HPEN HRESULT HRGN HRSRC HSZ HWINSTA HWND INT INT_PTR INT32 INT64 LANGID LCID LCTYPE LGRPID LONG LONGLONG LONG_PTR LONG32 LONG64 LPARAM LPBOOL LPBYTE LPCOLORREF LPCSTR LPCTSTR LPCVOID LPCWSTR LPDWORD LPHANDLE LPINT LPLONG LPSTR LPTSTR LPVOID LPWORD LPWSTR LRESULT PBOOL PBOOLEAN PBYTE PCHAR PCSTR PCTSTR PCWSTR PDWORDLONG PDWORD_PTR PDWORD32 PDWORD64 PFLOAT PHALF_PTR PHANDLE PHKEY PINT PINT_PTR PINT32 PINT64 PLCID PLONG PLONGLONG PLONG_PTR PLONG32 PLONG64 POINTER_32 POINTER_64 PSHORT PSIZE_T PSSIZE_T PSTR PTBYTE PTCHAR PTSTR PUCHAR PUHALF_PTR PUINT PUINT_PTR PUINT32 PUINT64 PULONG PULONGLONG PULONG_PTR PULONG32 PULONG64 PUSHORT PVOID PWCHAR PWORD PWSTR SC_HANDLE SC_LOCK SERVICE_STATUS_HANDLE SHORT SIZE_T SSIZE_T TBYTE TCHAR UCHAR UHALF_PTR UINT UINT_PTR UINT32 UINT64 ULONG ULONGLONG ULONG_PTR ULONG32 ULONG64 USHORT USN VOID WCHAR WORD WPARAM WPARAM WPARAM char bool short int __int32 __int64 __int8 __int16 long float double __wchar_t clock_t _complex _dev_t _diskfree_t div_t ldiv_t _exception _EXCEPTION_POINTERS FILE _finddata_t _finddatai64_t _wfinddata_t _wfinddatai64_t __finddata64_t __wfinddata64_t _FPIEEE_RECORD fpos_t _HEAPINFO _HFILE lconv intptr_t jmp_buf mbstate_t _off_t _onexit_t _PNH ptrdiff_t _purecall_handler sig_atomic_t size_t _stat __stat64 _stati64 terminate_function time_t __time64_t _timeb __timeb64 tm uintptr_t _utimbuf va_list wchar_t wctrans_t wctype_t wint_t signed",t="auto break case catch class const decltype __finally __exception __try const_cast continue private public protected __declspec default delete deprecated dllexport dllimport do dynamic_cast else enum explicit extern if for friend goto inline mutable naked namespace new noinline noreturn nothrow register reinterpret_cast return selectany sizeof static static_cast struct switch template this thread throw true false try typedef typeid typename union using uuid virtual void volatile whcar_t while",r="assert isalnum isalpha iscntrl isdigit isgraph islower isprintispunct isspace isupper isxdigit tolower toupper errno localeconv setlocale acos asin atan atan2 ceil cos cosh exp fabs floor fmod frexp ldexp log log10 modf pow sin sinh sqrt tan tanh jmp_buf longjmp setjmp raise signal sig_atomic_t va_arg va_end va_start clearerr fclose feof ferror fflush fgetc fgetpos fgets fopen fprintf fputc fputs fread freopen fscanf fseek fsetpos ftell fwrite getc getchar gets perror printf putc putchar puts remove rename rewind scanf setbuf setvbuf sprintf sscanf tmpfile tmpnam ungetc vfprintf vprintf vsprintf abort abs atexit atof atoi atol bsearch calloc div exit free getenv labs ldiv malloc mblen mbstowcs mbtowc qsort rand realloc srand strtod strtol strtoul system wcstombs wctomb memchr memcmp memcpy memmove memset strcat strchr strcmp strcoll strcpy strcspn strerror strlen strncat strncmp strncpy strpbrk strrchr strspn strstr strtok strxfrm asctime clock ctime difftime gmtime localtime mktime strftime time";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/^ *#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"color1 bold"},{regex:new RegExp(this.getKeywords(r),"gm"),css:"functions bold"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword bold"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["cpp","c"],SyntaxHighlighter.brushes.Cpp=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){function e(e,t){var r=0==e[0].indexOf("///")?"color1":"comments";return[new SyntaxHighlighter.Match(e[0],e.index,r)]}var t="abstract as base bool break byte case catch char checked class const continue decimal default delegate do double else enum event explicit volatile extern false finally fixed float for foreach get goto if implicit in int interface internal is lock long namespace new null object operator out override params private protected public readonly ref return sbyte sealed set short sizeof stackalloc static string struct switch this throw true try typeof uint ulong unchecked unsafe ushort using virtual void while var from group by into select let where orderby join on equals ascending descending";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,func:e},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:/@"(?:[^"]|"")*"/g,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/^\s*#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword"},{regex:/\bpartial(?=\s+(?:class|interface|struct)\b)/g,css:"keyword"},{regex:/\byield(?=\s+(?:return|break)\b)/g,css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["c#","c-sharp","csharp"],SyntaxHighlighter.brushes.CSharp=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){function e(e){return"\\b([a-z_]|)"+e.replace(/ /g,"(?=:)\\b|\\b([a-z_\\*]|\\*|)")+"(?=:)\\b"}function t(e){return"\\b"+e.replace(/ /g,"(?!-)(?!:)\\b|\\b()")+":\\b"}var r="ascent azimuth background-attachment background-color background-image background-position background-repeat background baseline bbox border-collapse border-color border-spacing border-style border-top border-right border-bottom border-left border-top-color border-right-color border-bottom-color border-left-color border-top-style border-right-style border-bottom-style border-left-style border-top-width border-right-width border-bottom-width border-left-width border-width border bottom cap-height caption-side centerline clear clip color content counter-increment counter-reset cue-after cue-before cue cursor definition-src descent direction display elevation empty-cells float font-size-adjust font-family font-size font-stretch font-style font-variant font-weight font height left letter-spacing line-height list-style-image list-style-position list-style-type list-style margin-top margin-right margin-bottom margin-left margin marker-offset marks mathline max-height max-width min-height min-width orphans outline-color outline-style outline-width outline overflow padding-top padding-right padding-bottom padding-left padding page page-break-after page-break-before page-break-inside pause pause-after pause-before pitch pitch-range play-during position quotes right richness size slope src speak-header speak-numeral speak-punctuation speak speech-rate stemh stemv stress table-layout text-align top text-decoration text-indent text-shadow text-transform unicode-bidi unicode-range units-per-em vertical-align visibility voice-family volume white-space widows width widths word-spacing x-height z-index",i="above absolute all always aqua armenian attr aural auto avoid baseline behind below bidi-override black blink block blue bold bolder both bottom braille capitalize caption center center-left center-right circle close-quote code collapse compact condensed continuous counter counters crop cross crosshair cursive dashed decimal decimal-leading-zero default digits disc dotted double embed embossed e-resize expanded extra-condensed extra-expanded fantasy far-left far-right fast faster fixed format fuchsia gray green groove handheld hebrew help hidden hide high higher icon inline-table inline inset inside invert italic justify landscape large larger left-side left leftwards level lighter lime line-through list-item local loud lower-alpha lowercase lower-greek lower-latin lower-roman lower low ltr marker maroon medium message-box middle mix move narrower navy ne-resize no-close-quote none no-open-quote no-repeat normal nowrap n-resize nw-resize oblique olive once open-quote outset outside overline pointer portrait pre print projection purple red relative repeat repeat-x repeat-y rgb ridge right right-side rightwards rtl run-in screen scroll semi-condensed semi-expanded separate se-resize show silent silver slower slow small small-caps small-caption smaller soft solid speech spell-out square s-resize static status-bar sub super sw-resize table-caption table-cell table-column table-column-group table-footer-group table-header-group table-row table-row-group teal text-bottom text-top thick thin top transparent tty tv ultra-condensed ultra-expanded underline upper-alpha uppercase upper-latin upper-roman url visible wait white wider w-resize x-fast x-high x-large x-loud x-low x-slow x-small x-soft xx-large xx-small yellow",n="[mM]onospace [tT]ahoma [vV]erdana [aA]rial [hH]elvetica [sS]ans-serif [sS]erif [cC]ourier mono sans serif";this.regexList=[{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\#[a-fA-F0-9]{3,6}/g,css:"value"},{regex:/(-?\d+)(\.\d+)?(px|em|pt|\:|\%|)/g,css:"value"},{regex:/!important/g,css:"color3"},{regex:new RegExp(e(r),"gm"),css:"keyword"},{regex:new RegExp(t(i),"g"),css:"value"},{regex:new RegExp(this.getKeywords(n),"g"),css:"color1"}],this.forHtmlScript({left:/(&lt;|<)\s*style.*?(&gt;|>)/gi,right:/(&lt;|<)\/\s*style\s*(&gt;|>)/gi})}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["css"],SyntaxHighlighter.brushes.CSS=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="abs addr and ansichar ansistring array as asm begin boolean byte cardinal case char class comp const constructor currency destructor div do double downto else end except exports extended false file finalization finally for function goto if implementation in inherited int64 initialization integer interface is label library longint longword mod nil not object of on or packed pansichar pansistring pchar pcurrency pdatetime pextended pint64 pointer private procedure program property pshortstring pstring pvariant pwidechar pwidestring protected public published raise real real48 record repeat set shl shortint shortstring shr single smallint string then threadvar to true try type unit until uses val var varirnt while widechar widestring with word write writeln xor";this.regexList=[{regex:/\(\*[\s\S]*?\*\)/gm,css:"comments"},{regex:/{(?!\$)[\s\S]*?}/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\{\$[a-zA-Z]+ .+\}/g,css:"color1"},{regex:/\b[\d\.]+\b/g,css:"value"},{regex:/\$[a-zA-Z0-9]+\b/g,css:"value"},{regex:new RegExp(this.getKeywords(e),"gmi"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["delphi","pascal","pas"],SyntaxHighlighter.brushes.Delphi=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){this.regexList=[{regex:/^\+\+\+ .*$/gm,css:"color2"},{regex:/^\-\-\- .*$/gm,css:"color2"},{regex:/^\s.*$/gm,css:"color1"},{regex:/^@@.*@@.*$/gm,css:"variable"},{regex:/^\+.*$/gm,css:"string"},{regex:/^\-.*$/gm,css:"color3"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["diff","patch"],SyntaxHighlighter.brushes.Diff=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="after and andalso band begin bnot bor bsl bsr bxor case catch cond div end fun if let not of or orelse query receive rem try when xor module export import define";this.regexList=[{regex:new RegExp("[A-Z][A-Za-z0-9_]+","g"),css:"constants"},{regex:new RegExp("\\%.+","gm"),css:"comments"},{regex:new RegExp("\\?[A-Za-z0-9_]+","g"),css:"preprocessor"},{regex:new RegExp("[a-z0-9_]+:[a-z0-9_]+","g"),css:"functions"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["erl","erlang"],SyntaxHighlighter.brushes.Erland=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="as assert break case catch class continue def default do else extends finally if in implements import instanceof interface new package property return switch throw throws try while public protected private static",t="void boolean byte char short int long float double",r="null",i="allProperties count get size collect each eachProperty eachPropertyName eachWithIndex find findAll findIndexOf grep inject max min reverseEach sort asImmutable asSynchronized flatten intersect join pop reverse subMap toList padRight padLeft contains eachMatch toCharacter toLong toUrl tokenize eachFile eachFileRecurse eachB yte eachLine readBytes readLine getText splitEachLine withReader append encodeBase64 decodeBase64 filterLine transformChar transformLine withOutputStream withPrintWriter withStream withStreams withWriter withWriterAppend write writeLine dump inspect invokeMethod print println step times upto use waitForOrKill getText";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/""".*"""/g,css:"string"},{regex:new RegExp("\\b([\\d]+(\\.[\\d]+)?|0x[a-f0-9]+)\\b","gi"),css:"value"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"color1"},{regex:new RegExp(this.getKeywords(r),"gm"),css:"constants"},{regex:new RegExp(this.getKeywords(i),"gm"),css:"functions"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["groovy"],SyntaxHighlighter.brushes.Groovy=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="abstract assert boolean break byte case catch char class const continue default do double else enum extends false final finally float for goto if implements import instanceof int interface long native new null package private protected public return short static strictfp super switch synchronized this throw throws true transient try void volatile while";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:/\/\*([^\*][\s\S]*)?\*\//gm,css:"comments"},{regex:/\/\*(?!\*\/)\*[\s\S]*?\*\//gm,css:"preprocessor"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b([\d]+(\.[\d]+)?|0x[a-f0-9]+)\b/gi,css:"value"},{regex:/(?!\@interface\b)\@[\$\w]+\b/g,css:"color1"},{regex:/\@interface\b/g,css:"color2"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"}],this.forHtmlScript({left:/(&lt;|<)%[@!=]?/g,right:/%(&gt;|>)/g})}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["java"],SyntaxHighlighter.brushes.Java=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="Boolean Byte Character Double Duration Float Integer Long Number Short String Void",t="abstract after and as assert at before bind bound break catch class continue def delete else exclusive extends false finally first for from function if import in indexof init insert instanceof into inverse last lazy mixin mod nativearray new not null on or override package postinit protected public public-init public-read replace return reverse sizeof step super then this throw true try tween typeof var where while with attribute let private readonly static trigger";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:/(-?\.?)(\b(\d*\.?\d+|\d+\.?\d*)(e[+-]?\d+)?|0x[a-f\d]+)\b\.?/gi,css:"color2"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"variable"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["jfx","javafx"],SyntaxHighlighter.brushes.JavaFX=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="break case catch continue default delete do else false  for function if in instanceof new null return super switch this throw true try typeof var while with",t=SyntaxHighlighter.regexLib;this.regexList=[{regex:t.multiLineDoubleQuotedString,css:"string"},{regex:t.multiLineSingleQuotedString,css:"string"},{regex:t.singleLineCComments,css:"comments"},{regex:t.multiLineCComments,css:"comments"},{regex:/\s*#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"}],this.forHtmlScript(t.scriptScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["js","jscript","javascript"],SyntaxHighlighter.brushes.JScript=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="abs accept alarm atan2 bind binmode chdir chmod chomp chop chown chr chroot close closedir connect cos crypt defined delete each endgrent endhostent endnetent endprotoent endpwent endservent eof exec exists exp fcntl fileno flock fork format formline getc getgrent getgrgid getgrnam gethostbyaddr gethostbyname gethostent getlogin getnetbyaddr getnetbyname getnetent getpeername getpgrp getppid getpriority getprotobyname getprotobynumber getprotoent getpwent getpwnam getpwuid getservbyname getservbyport getservent getsockname getsockopt glob gmtime grep hex index int ioctl join keys kill lc lcfirst length link listen localtime lock log lstat map mkdir msgctl msgget msgrcv msgsnd oct open opendir ord pack pipe pop pos print printf prototype push quotemeta rand read readdir readline readlink readpipe recv rename reset reverse rewinddir rindex rmdir scalar seek seekdir select semctl semget semop send setgrent sethostent setnetent setpgrp setpriority setprotoent setpwent setservent setsockopt shift shmctl shmget shmread shmwrite shutdown sin sleep socket socketpair sort splice split sprintf sqrt srand stat study substr symlink syscall sysopen sysread sysseek system syswrite tell telldir time times tr truncate uc ucfirst umask undef unlink unpack unshift utime values vec wait waitpid warn write say",t="bless caller continue dbmclose dbmopen die do dump else elsif eval exit for foreach goto if import last local my next no our package redo ref require return sub tie tied unless untie until use wantarray while given when default try catch finally has extends with before after around override augment";this.regexList=[{regex:/(<<|&lt;&lt;)((\w+)|(['"])(.+?)\4)[\s\S]+?\n\3\5\n/g,css:"string"},{regex:/#.*$/gm,css:"comments"},{regex:/^#!.*\n/g,css:"preprocessor"},{regex:/-?\w+(?=\s*=(>|&gt;))/g,css:"string"},{regex:/\bq[qwxr]?\([\s\S]*?\)/g,css:"string"},{regex:/\bq[qwxr]?\{[\s\S]*?\}/g,css:"string"},{regex:/\bq[qwxr]?\[[\s\S]*?\]/g,css:"string"},{regex:/\bq[qwxr]?(<|&lt;)[\s\S]*?(>|&gt;)/g,css:"string"},{regex:/\bq[qwxr]?([^\w({<[])[\s\S]*?\1/g,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/(?:&amp;|[$@%*]|\$#)[a-zA-Z_](\w+|::)*/g,css:"variable"},{regex:/\b__(?:END|DATA)__\b[\s\S]*$/g,css:"comments"},{regex:/(^|\n)=\w[\s\S]*?(\n=cut\s*\n|$)/g,css:"comments"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"functions"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.phpScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["perl","Perl","pl"],SyntaxHighlighter.brushes.Perl=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="abs acos acosh addcslashes addslashes array_change_key_case array_chunk array_combine array_count_values array_diff array_diff_assoc array_diff_key array_diff_uassoc array_diff_ukey array_fill array_filter array_flip array_intersect array_intersect_assoc array_intersect_key array_intersect_uassoc array_intersect_ukey array_key_exists array_keys array_map array_merge array_merge_recursive array_multisort array_pad array_pop array_product array_push array_rand array_reduce array_reverse array_search array_shift array_slice array_splice array_sum array_udiff array_udiff_assoc array_udiff_uassoc array_uintersect array_uintersect_assoc array_uintersect_uassoc array_unique array_unshift array_values array_walk array_walk_recursive atan atan2 atanh base64_decode base64_encode base_convert basename bcadd bccomp bcdiv bcmod bcmul bindec bindtextdomain bzclose bzcompress bzdecompress bzerrno bzerror bzerrstr bzflush bzopen bzread bzwrite ceil chdir checkdate checkdnsrr chgrp chmod chop chown chr chroot chunk_split class_exists closedir closelog copy cos cosh count count_chars date decbin dechex decoct deg2rad delete ebcdic2ascii echo empty end ereg ereg_replace eregi eregi_replace error_log error_reporting escapeshellarg escapeshellcmd eval exec exit exp explode extension_loaded feof fflush fgetc fgetcsv fgets fgetss file_exists file_get_contents file_put_contents fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype floatval flock floor flush fmod fnmatch fopen fpassthru fprintf fputcsv fputs fread fscanf fseek fsockopen fstat ftell ftok getallheaders getcwd getdate getenv gethostbyaddr gethostbyname gethostbynamel getimagesize getlastmod getmxrr getmygid getmyinode getmypid getmyuid getopt getprotobyname getprotobynumber getrandmax getrusage getservbyname getservbyport gettext gettimeofday gettype glob gmdate gmmktime ini_alter ini_get ini_get_all ini_restore ini_set interface_exists intval ip2long is_a is_array is_bool is_callable is_dir is_double is_executable is_file is_finite is_float is_infinite is_int is_integer is_link is_long is_nan is_null is_numeric is_object is_readable is_real is_resource is_scalar is_soap_fault is_string is_subclass_of is_uploaded_file is_writable is_writeable mkdir mktime nl2br parse_ini_file parse_str parse_url passthru pathinfo print readlink realpath rewind rewinddir rmdir round str_ireplace str_pad str_repeat str_replace str_rot13 str_shuffle str_split str_word_count strcasecmp strchr strcmp strcoll strcspn strftime strip_tags stripcslashes stripos stripslashes stristr strlen strnatcasecmp strnatcmp strncasecmp strncmp strpbrk strpos strptime strrchr strrev strripos strrpos strspn strstr strtok strtolower strtotime strtoupper strtr strval substr substr_compare",t="abstract and array as break case catch cfunction class clone const continue declare default die do else elseif enddeclare endfor endforeach endif endswitch endwhile extends final for foreach function global goto if implements include include_once interface instanceof insteadof namespace new old_function or private protected public return require require_once static switch trait throw try use var while xor ",r="__FILE__ __LINE__ __METHOD__ __FUNCTION__ __CLASS__";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\$\w+/g,css:"variable"},{regex:new RegExp(this.getKeywords(e),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(r),"gmi"),css:"constants"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.phpScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["php"],SyntaxHighlighter.brushes.Php=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["text","plain"],SyntaxHighlighter.brushes.Plain=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="while validateset validaterange validatepattern validatelength validatecount until trap switch return ref process param parameter in if global: function foreach for finally filter end elseif else dynamicparam do default continue cmdletbinding break begin alias \\? % #script #private #local #global mandatory parametersetname position valuefrompipeline valuefrompipelinebypropertyname valuefromremainingarguments helpmessage ",t=" and as band bnot bor bxor casesensitive ccontains ceq cge cgt cle clike clt cmatch cne cnotcontains cnotlike cnotmatch contains creplace eq exact f file ge gt icontains ieq ige igt ile ilike ilt imatch ine inotcontains inotlike inotmatch ireplace is isnot le like lt match ne not notcontains notlike notmatch or regex replace wildcard",r="write where wait use update unregister undo trace test tee take suspend stop start split sort skip show set send select scroll resume restore restart resolve resize reset rename remove register receive read push pop ping out new move measure limit join invoke import group get format foreach export expand exit enter enable disconnect disable debug cxnew copy convertto convertfrom convert connect complete compare clear checkpoint aggregate add",i=" component description example externalhelp forwardhelpcategory forwardhelptargetname forwardhelptargetname functionality inputs link notes outputs parameter remotehelprunspace role synopsis";this.regexList=[{regex:new RegExp("^\\s*#[#\\s]*\\.("+this.getKeywords(i)+").*$","gim"),css:"preprocessor help bold"},{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:/(&lt;|<)#[\s\S]*?#(&gt;|>)/gm,css:"comments here"},{regex:new RegExp('@"\\n[\\s\\S]*?\\n"@',"gm"),css:"script string here"},{regex:new RegExp("@'\\n[\\s\\S]*?\\n'@","gm"),css:"script string single here"},{regex:new RegExp('"(?:\\$\\([^\\)]*\\)|[^"]|`"|"")*[^`]"',"g"),css:"string"},{regex:new RegExp("'(?:[^']|'')*'","g"),css:"string single"},{regex:new RegExp("[\\$|@|@@](?:(?:global|script|private|env):)?[A-Z0-9_]+","gi"),css:"variable"},{regex:new RegExp("(?:\\b"+r.replace(/ /g,"\\b|\\b")+")-[a-zA-Z_][a-zA-Z0-9_]*","gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(e),"gmi"),css:"keyword"},{regex:new RegExp("-"+this.getKeywords(t),"gmi"),css:"operator value"},{regex:new RegExp("\\[[A-Z_\\[][A-Z0-9_. `,\\[\\]]*\\]","gi"),css:"constants"},{regex:new RegExp("\\s+-(?!"+this.getKeywords(t)+")[a-zA-Z_][a-zA-Z0-9_]*","gmi"),css:"color1"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["powershell","ps","posh"],SyntaxHighlighter.brushes.PowerShell=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="and assert break class continue def del elif else except exec finally for from global if import in is lambda not or pass print raise return try yield while",t="__import__ abs all any apply basestring bin bool buffer callable chr classmethod cmp coerce compile complex delattr dict dir divmod enumerate eval execfile file filter float format frozenset getattr globals hasattr hash help hex id input int intern isinstance issubclass iter len list locals long map max min next object oct open ord pow print property range raw_input reduce reload repr reversed round set setattr slice sorted staticmethod str sum super tuple type type unichr unicode vars xrange zip",r="None True False self cls class_";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:/^\s*@\w+/gm,css:"decorator"},{regex:/(['\"]{3})([^\1])*?\1/gm,css:"comments"},{regex:/"(?!")(?:\.|\\\"|[^\""\n])*"/gm,css:"string"},{regex:/'(?!')(?:\.|(\\\')|[^\''\n])*'/gm,css:"string"},{regex:/\+|\-|\*|\/|\%|=|==/gm,css:"keyword"},{regex:/\b\d+\.?\w*/g,css:"value"},{regex:new RegExp(this.getKeywords(t),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(r),"gm"),css:"color1"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["py","python"],SyntaxHighlighter.brushes.Python=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="alias and BEGIN begin break case class def define_method defined do each else elsif END end ensure false for if in module new next nil not or raise redo rescue retry return self super then throw true undef unless until when while yield",t="Array Bignum Binding Class Continuation Dir Exception FalseClass File::Stat File Fixnum Fload Hash Integer IO MatchData Method Module NilClass Numeric Object Proc Range Regexp String Struct::TMS Symbol ThreadGroup Thread Time TrueClass";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b[A-Z0-9_]+\b/g,css:"constants"},{regex:/:[a-z][A-Za-z0-9_]*/g,css:"color2"},{regex:/(\$|@@|@)\w+/g,css:"variable bold"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(t),"gm"),css:"color1"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),
e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["ruby","rails","ror","rb"],SyntaxHighlighter.brushes.Ruby=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){function e(e){return"\\b([a-z_]|)"+e.replace(/ /g,"(?=:)\\b|\\b([a-z_\\*]|\\*|)")+"(?=:)\\b"}function t(e){return"\\b"+e.replace(/ /g,"(?!-)(?!:)\\b|\\b()")+":\\b"}var r="ascent azimuth background-attachment background-color background-image background-position background-repeat background baseline bbox border-collapse border-color border-spacing border-style border-top border-right border-bottom border-left border-top-color border-right-color border-bottom-color border-left-color border-top-style border-right-style border-bottom-style border-left-style border-top-width border-right-width border-bottom-width border-left-width border-width border bottom cap-height caption-side centerline clear clip color content counter-increment counter-reset cue-after cue-before cue cursor definition-src descent direction display elevation empty-cells float font-size-adjust font-family font-size font-stretch font-style font-variant font-weight font height left letter-spacing line-height list-style-image list-style-position list-style-type list-style margin-top margin-right margin-bottom margin-left margin marker-offset marks mathline max-height max-width min-height min-width orphans outline-color outline-style outline-width outline overflow padding-top padding-right padding-bottom padding-left padding page page-break-after page-break-before page-break-inside pause pause-after pause-before pitch pitch-range play-during position quotes right richness size slope src speak-header speak-numeral speak-punctuation speak speech-rate stemh stemv stress table-layout text-align top text-decoration text-indent text-shadow text-transform unicode-bidi unicode-range units-per-em vertical-align visibility voice-family volume white-space widows width widths word-spacing x-height z-index",i="above absolute all always aqua armenian attr aural auto avoid baseline behind below bidi-override black blink block blue bold bolder both bottom braille capitalize caption center center-left center-right circle close-quote code collapse compact condensed continuous counter counters crop cross crosshair cursive dashed decimal decimal-leading-zero digits disc dotted double embed embossed e-resize expanded extra-condensed extra-expanded fantasy far-left far-right fast faster fixed format fuchsia gray green groove handheld hebrew help hidden hide high higher icon inline-table inline inset inside invert italic justify landscape large larger left-side left leftwards level lighter lime line-through list-item local loud lower-alpha lowercase lower-greek lower-latin lower-roman lower low ltr marker maroon medium message-box middle mix move narrower navy ne-resize no-close-quote none no-open-quote no-repeat normal nowrap n-resize nw-resize oblique olive once open-quote outset outside overline pointer portrait pre print projection purple red relative repeat repeat-x repeat-y rgb ridge right right-side rightwards rtl run-in screen scroll semi-condensed semi-expanded separate se-resize show silent silver slower slow small small-caps small-caption smaller soft solid speech spell-out square s-resize static status-bar sub super sw-resize table-caption table-cell table-column table-column-group table-footer-group table-header-group table-row table-row-group teal text-bottom text-top thick thin top transparent tty tv ultra-condensed ultra-expanded underline upper-alpha uppercase upper-latin upper-roman url visible wait white wider w-resize x-fast x-high x-large x-loud x-low x-slow x-small x-soft xx-large xx-small yellow",n="[mM]onospace [tT]ahoma [vV]erdana [aA]rial [hH]elvetica [sS]ans-serif [sS]erif [cC]ourier mono sans serif",s="!important !default",a="@import @extend @debug @warn @if @for @while @mixin @include",o=SyntaxHighlighter.regexLib;this.regexList=[{regex:o.multiLineCComments,css:"comments"},{regex:o.singleLineCComments,css:"comments"},{regex:o.doubleQuotedString,css:"string"},{regex:o.singleQuotedString,css:"string"},{regex:/\#[a-fA-F0-9]{3,6}/g,css:"value"},{regex:/\b(-?\d+)(\.\d+)?(px|em|pt|\:|\%|)\b/g,css:"value"},{regex:/\$\w+/g,css:"variable"},{regex:new RegExp(this.getKeywords(s),"g"),css:"color3"},{regex:new RegExp(this.getKeywords(a),"g"),css:"preprocessor"},{regex:new RegExp(e(r),"gm"),css:"keyword"},{regex:new RegExp(t(i),"g"),css:"value"},{regex:new RegExp(this.getKeywords(n),"g"),css:"color1"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["sass","scss"],SyntaxHighlighter.brushes.Sass=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="val sealed case def true trait implicit forSome import match object null finally super override try lazy for var catch throw type extends class while with new final yield abstract else do if return protected private this package false",t="[_:=><%#@]+";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineSingleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.multiLineDoubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/0x[a-f0-9]+|\d+(\.\d+)?/gi,css:"value"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"},{regex:new RegExp(t,"gm"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["scala"],SyntaxHighlighter.brushes.Scala=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="abs avg case cast coalesce convert count current_timestamp current_user day isnull left lower month nullif replace right session_user space substring sum system_user upper user year",t="absolute action add after alter as asc at authorization begin bigint binary bit by cascade char character check checkpoint close collate column commit committed connect connection constraint contains continue create cube current current_date current_time cursor database date deallocate dec decimal declare default delete desc distinct double drop dynamic else end end-exec escape except exec execute false fetch first float for force foreign forward free from full function global goto grant group grouping having hour ignore index inner insensitive insert instead int integer intersect into is isolation key last level load local max min minute modify move name national nchar next no numeric of off on only open option order out output partial password precision prepare primary prior privileges procedure public read real references relative repeatable restrict return returns revoke rollback rollup rows rule schema scroll second section select sequence serializable set size smallint static statistics table temp temporary then time timestamp to top transaction translation trigger true truncate uncommitted union unique update values varchar varying view when where with work",r="all and any between cross in join like not null or outer some";this.regexList=[{regex:/--(.*)$/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineDoubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.multiLineSingleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(e),"gmi"),css:"color2"},{regex:new RegExp(this.getKeywords(r),"gmi"),css:"color1"},{regex:new RegExp(this.getKeywords(t),"gmi"),css:"keyword"}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["sql"],SyntaxHighlighter.brushes.Sql=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){var e="AddHandler AddressOf AndAlso Alias And Ansi As Assembly Auto Boolean ByRef Byte ByVal Call Case Catch CBool CByte CChar CDate CDec CDbl Char CInt Class CLng CObj Const CShort CSng CStr CType Date Decimal Declare Default Delegate Dim DirectCast Do Double Each Else ElseIf End Enum Erase Error Event Exit False Finally For Friend Function Get GetType GoSub GoTo Handles If Implements Imports In Inherits Integer Interface Is Let Lib Like Long Loop Me Mod Module MustInherit MustOverride MyBase MyClass Namespace New Next Not Nothing NotInheritable NotOverridable Object On Option Optional Or OrElse Overloads Overridable Overrides ParamArray Preserve Private Property Protected Public RaiseEvent ReadOnly ReDim REM RemoveHandler Resume Return Select Set Shadows Shared Short Single Static Step Stop String Structure Sub SyncLock Then Throw To True Try TypeOf Unicode Until Variant When While With WithEvents WriteOnly Xor";this.regexList=[{regex:/'.*$/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:/^\s*#.*$/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(e),"gm"),css:"keyword"}],this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["vb","vbnet"],SyntaxHighlighter.brushes.Vb=e,"undefined"!=typeof exports?exports.Brush=e:null}(),function(){function e(){function e(e,t){var r=SyntaxHighlighter.Match,i=e[0],n=new XRegExp("(&lt;|<)[\\s\\/\\?]*(?<name>[:\\w-\\.]+)","xg").exec(i),s=[];if(null!=e.attributes)for(var a,o=new XRegExp("(?<name> [\\w:\\-\\.]+)\\s*=\\s*(?<value> \".*?\"|'.*?'|\\w+)","xg");null!=(a=o.exec(i));)s.push(new r(a.name,e.index+a.index,"color1")),s.push(new r(a.value,e.index+a.index+a[0].indexOf(a.value),"string"));return null!=n&&s.push(new r(n.name,e.index+n[0].indexOf(n.name),"keyword")),s}this.regexList=[{regex:new XRegExp("(\\&lt;|<)\\!\\[[\\w\\s]*?\\[(.|\\s)*?\\]\\](\\&gt;|>)","gm"),css:"color2"},{regex:SyntaxHighlighter.regexLib.xmlComments,css:"comments"},{regex:new XRegExp("(&lt;|<)[\\s\\/\\?]*(\\w+)(?<attributes>.*?)[\\s\\/\\?]*(&gt;|>)","sg"),func:e}]}SyntaxHighlighter=SyntaxHighlighter||("undefined"!=typeof require?require("shCore").SyntaxHighlighter:null),e.prototype=new SyntaxHighlighter.Highlighter,e.aliases=["xml","xhtml","xslt","html"],SyntaxHighlighter.brushes.Xml=e,"undefined"!=typeof exports?exports.Brush=e:null}();