using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 任务类型枚举
    /// 用于标识不同的仓库作业任务类型
    /// </summary>
    public enum TaskType
    {
        /// <summary>
        /// 收货任务
        /// 接收供应商送货或退货的任务
        /// </summary>
        [Display(Name = "收货任务")]
        Receiving = 0,

        /// <summary>
        /// 上架任务
        /// 将收货区物料转移到存储区的任务
        /// </summary>
        [Display(Name = "上架任务")]
        PutAway = 1,

        /// <summary>
        /// 拣货任务
        /// 按订单要求拣选物料的任务
        /// </summary>
        [Display(Name = "拣货任务")]
        Picking = 2,

        /// <summary>
        /// 补货任务
        /// 将存储区物料补充到拣货区的任务
        /// </summary>
        [Display(Name = "补货任务")]
        Replenishment = 3,

        /// <summary>
        /// 移库任务
        /// 在仓库内部移动物料的任务
        /// </summary>
        [Display(Name = "移库任务")]
        Moving = 4,

        /// <summary>
        /// 盘点任务
        /// 清点核对库存的任务
        /// </summary>
        [Display(Name = "盘点任务")]
        StockTaking = 5,

        /// <summary>
        /// 复核任务
        /// 对拣货或收货进行复查的任务
        /// </summary>
        [Display(Name = "复核任务")]
        Checking = 6
    }

    /// <summary>
    /// 任务状态枚举
    /// 用于标识任务的执行状态
    /// </summary>
    public enum TaskStatus
    {
        /// <summary>
        /// 待分配
        /// 任务创建但未分配执行人
        /// </summary>
        [Display(Name = "待分配")]
        PendingAssignment = 0,

        /// <summary>
        /// 已分配
        /// 任务已分配但未开始执行
        /// </summary>
        [Display(Name = "已分配")]
        Assigned = 1,

        /// <summary>
        /// 进行中
        /// 任务正在执行
        /// </summary>
        [Display(Name = "进行中")]
        Processing = 2,

        /// <summary>
        /// 已暂停
        /// 任务暂时停止执行
        /// </summary>
        [Display(Name = "已暂停")]
        Paused = 3,

        /// <summary>
        /// 已完成
        /// 任务执行完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 4,

        /// <summary>
        /// 已取消
        /// 任务被取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 5,

        /// <summary>
        /// 异常
        /// 任务执行出现异常
        /// </summary>
        [Display(Name = "异常")]
        Exception = 6
    }

    /// <summary>
    /// 任务优先级枚举
    /// 用于标识任务的紧急程度
    /// </summary>
    public enum TaskPriority
    {
        /// <summary>
        /// 低优先级
        /// 可延后处理的任务
        /// </summary>
        [Display(Name = "低优先级")]
        Low = 0,

        /// <summary>
        /// 普通优先级
        /// 正常处理的任务
        /// </summary>
        [Display(Name = "普通优先级")]
        Normal = 1,

        /// <summary>
        /// 高优先级
        /// 优先处理的任务
        /// </summary>
        [Display(Name = "高优先级")]
        High = 2,

        /// <summary>
        /// 紧急优先级
        /// 需要立即处理的任务
        /// </summary>
        [Display(Name = "紧急优先级")]
        Urgent = 3
    }
} 