{"openapi": "3.0.1", "info": {"title": "My API", "version": "v1"}, "paths": {"/api/_account/login": {"post": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"account": {"type": "string"}, "password": {"type": "string"}, "tenant": {"type": "string"}, "rememberLogin": {"type": "boolean", "default": false}}}, "encoding": {"account": {"style": "form"}, "password": {"style": "form"}, "tenant": {"style": "form"}, "rememberLogin": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_account/loginjwt": {"post": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleLogin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SimpleLogin"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SimpleLogin"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_account/loginremote": {"get": {"tags": ["Account"], "parameters": [{"name": "_remotetoken", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/settenant": {"get": {"tags": ["Account"], "parameters": [{"name": "tenant", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/reg": {"post": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleReg"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SimpleReg"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SimpleReg"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_account/refreshtoken": {"post": {"tags": ["Account"], "parameters": [{"name": "refreshToken", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Token"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Token"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}}}}, "/api/_account/checkuserinfo": {"get": {"tags": ["Account"], "parameters": [{"name": "IsApi", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/changepassword": {"post": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_account/logout": {"get": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/getframeworkroles": {"get": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/getframeworkgroups": {"get": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/getframeworkgroupstree": {"get": {"tags": ["Account"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/getuserbyid": {"get": {"tags": ["Account"], "parameters": [{"name": "keywords", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/getuserbygroup": {"get": {"tags": ["Account"], "parameters": [{"name": "keywords", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_account/getuserbyrole": {"get": {"tags": ["Account"], "parameters": [{"name": "keywords", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_actionlog/search": {"post": {"tags": ["ActionLog"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionLogSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActionLogSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ActionLogSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_actionlog/{id}": {"get": {"tags": ["ActionLog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ActionLogVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActionLogVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActionLogVM"}}}}}}}, "/api/_actionlog/batchdelete": {"post": {"tags": ["ActionLog"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_actionlog/exportexcel": {"post": {"tags": ["ActionLog"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActionLogSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActionLogSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ActionLogSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_actionlog/exportexcelbyids": {"post": {"tags": ["ActionLog"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-definitions/backup": {"get": {"tags": ["Backup"], "parameters": [{"name": "ids", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "query", "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"application/zip": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-instances/bulk/cancel": {"post": {"tags": ["BulkCancel"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCancelWorkflowsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCancelWorkflowsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkCancelWorkflowsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-instances/bulk": {"delete": {"tags": ["BulkDelete"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteWorkflowsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteWorkflowsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkDeleteWorkflowsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-instances/bulk/retry": {"post": {"tags": ["BulkRetry"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkRetryWorkflowsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkRetryWorkflowsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkRetryWorkflowsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-instances/{id}/cancel": {"post": {"tags": ["Cancel"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/_dataprivilege/search": {"post": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/get": {"get": {"tags": ["DataPrivilege"], "parameters": [{"name": "TableName", "in": "query", "schema": {"type": "string"}}, {"name": "TargetId", "in": "query", "schema": {"type": "string"}}, {"name": "DpType", "in": "query", "schema": {"$ref": "#/components/schemas/DpTypeEnum"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}}}}}}, "/api/_dataprivilege/add": {"post": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/edit": {"put": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DataPrivilegeVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/delete": {"post": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleDpModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SimpleDpModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SimpleDpModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/getprivilegebytablename": {"get": {"tags": ["DataPrivilege"], "parameters": [{"name": "table", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/getprivileges": {"get": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/getusergroups": {"get": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_dataprivilege/getusergroupstree": {"get": {"tags": ["DataPrivilege"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-instances/{id}": {"delete": {"tags": ["Delete"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "get": {"tags": ["Get"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstance"}}}}}}}, "/v1/workflow-definitions/{definitionId}": {"delete": {"tags": ["DeleteByDefinition"], "parameters": [{"name": "definitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted"}}}}, "/v1/workflow-definitions/{definitionId}/{versionOptions}": {"delete": {"tags": ["DeleteByDefinitionAndVersion"], "parameters": [{"name": "definitionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionOptions", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"202": {"description": "Accepted"}}}}, "/v1/workflows/{workflowDefinitionId}/dispatch": {"post": {"tags": ["Dispatch"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowDefinitionRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowDefinitionRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowDefinitionRequestModel"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowDefinitionResponseModel"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-instances/{workflowInstanceId}/dispatch": {"post": {"tags": ["Dispatch"], "parameters": [{"name": "workflowInstanceId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowInstanceRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowInstanceRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowInstanceRequestModel"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchWorkflowInstanceResponseModel"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/signals/{signalName}/dispatch": {"post": {"tags": ["Dispatch"], "parameters": [{"name": "signalName", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchSignalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DispatchSignalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DispatchSignalRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchSignalResponse"}}}}}}}, "/signals/dispatch/{token}": {"get": {"tags": ["DispatchEndpoint"], "parameters": [{"name": "token", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["DispatchEndpoint"], "parameters": [{"name": "token", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/v1/workflows/{workflowDefinitionId}/execute": {"post": {"tags": ["Execute"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowDefinitionRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowDefinitionRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowDefinitionRequestModel"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowDefinitionResponseModel"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-instances/{workflowInstanceId}/execute": {"post": {"tags": ["Execute"], "parameters": [{"name": "workflowInstanceId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowInstanceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowInstanceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowInstanceRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteWorkflowInstanceResponseModel"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/signals/{signalName}/execute": {"post": {"tags": ["Execute"], "parameters": [{"name": "signalName", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteSignalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExecuteSignalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExecuteSignalRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteSignalResponse"}}}}}}}, "/v1/workflow-definitions/{workflowDefinitionId}/{versionOptions}/export": {"post": {"tags": ["Export"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionOptions", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/_file/upload": {"post": {"tags": ["FileApi"], "parameters": [{"name": "sm", "in": "query", "schema": {"type": "string"}}, {"name": "groupName", "in": "query", "schema": {"type": "string"}}, {"name": "subdir", "in": "query", "schema": {"type": "string"}}, {"name": "extra", "in": "query", "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/uploadimage": {"post": {"tags": ["FileApi"], "parameters": [{"name": "width", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "height", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sm", "in": "query", "schema": {"type": "string"}}, {"name": "groupName", "in": "query", "schema": {"type": "string"}}, {"name": "subdir", "in": "query", "schema": {"type": "string"}}, {"name": "extra", "in": "query", "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/getfilename/{id}": {"get": {"tags": ["FileApi"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/getfile/{id}": {"get": {"tags": ["FileApi"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "width", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "height", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/getfileinfo/{id}": {"get": {"tags": ["FileApi"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/getuserphoto/{id}": {"get": {"tags": ["FileApi"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "width", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "height", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/downloadfile/{id}": {"get": {"tags": ["FileApi"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_file/deletedfile/{id}": {"get": {"tags": ["FileApi"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "csName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/search": {"post": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/{id}": {"get": {"tags": ["FrameworkGroup"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}}}}}}, "/api/_frameworkgroup/add": {"post": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/edit": {"put": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/batchdelete": {"post": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/exportexcel": {"post": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/exportexcelbyids": {"post": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/getexceltemplate": {"get": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/import": {"post": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupImportVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupImportVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkGroupImportVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/getparents": {"get": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkgroup/getparentstree": {"get": {"tags": ["FrameworkGroup"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/search": {"post": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/{id}": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}}}}}}, "/api/_frameworkmenu/add": {"post": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/edit": {"put": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkMenuVM2"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/batchdelete": {"post": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/exportexcel": {"post": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/exportexcelbyids": {"post": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/unsetpages": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/_frameworkmenu/refreshmenu": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/getactionsbymodel": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "ModelName", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/getfolders": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkmenu/geticons": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}}}}}}}}, "/api/_frameworkmenu/geticonitems": {"get": {"tags": ["FrameworkMenu"], "parameters": [{"name": "key", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}}}}}}}, "/api/_frameworkrole/search": {"post": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/{id}": {"get": {"tags": ["FrameworkRole"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}}}}}}, "/api/_frameworkrole/getpageactions/{id}": {"get": {"tags": ["FrameworkRole"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FrameworkRoleMDVM2"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleMDVM2"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleMDVM2"}}}}}}}, "/api/_frameworkrole/editprivilege": {"put": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleMDVM2"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleMDVM2"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleMDVM2"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/add": {"post": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/edit": {"put": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/batchdelete": {"post": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/exportexcel": {"post": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/exportexcelbyids": {"post": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/getexceltemplate": {"get": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkrole/import": {"post": {"tags": ["FrameworkRole"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleImportVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleImportVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkRoleImportVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/search": {"post": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/{id}": {"get": {"tags": ["FrameworkTenant"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}}}}}}, "/api/_frameworktenant/add": {"post": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/edit": {"put": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/batchdelete": {"post": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/exportexcel": {"post": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/exportexcelbyids": {"post": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/getexceltemplate": {"get": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/import": {"post": {"tags": ["FrameworkTenant"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantImportVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantImportVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkTenantImportVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworktenant/getframeworktenants": {"get": {"tags": ["FrameworkTenant"], "parameters": [{"name": "parent", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/search": {"post": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkUserSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/{id}": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}}}}}}, "/api/_frameworkuser/add": {"post": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/edit": {"put": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/batchdelete": {"post": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/exportexcel": {"post": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkUserSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/exportexcelbyids": {"post": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getexceltemplate": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/import": {"post": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserImportVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserImportVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkUserImportVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getframeworkroles": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getframeworkgroups": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getframeworkgroupstree": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getuserbyid": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "keywords", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getuserbygroup": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "keywords", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/getuserbyrole": {"get": {"tags": ["FrameworkUser"], "parameters": [{"name": "keywords", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/_frameworkuser/password": {"put": {"tags": ["FrameworkUser"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FrameworkUserVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-registry/{id}/{versionOptions}": {"get": {"tags": ["Get"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionOptions", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowBlueprintModelPagedList"}}}}}}}, "/v1/workflow-instances/{id}/execution-log": {"get": {"tags": ["Get"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowExecutionLogRecordPagedList"}}}}}}}, "/v1/version": {"get": {"tags": ["Get"], "responses": {"200": {"description": "Success"}}}}, "/v1/scripting/javascript/type-definitions/{workflowDefinitionId}": {"post": {"tags": ["Get"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "query", "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IntellisenseContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntellisenseContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IntellisenseContext"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/v1/designer/runtime-select-list": {"post": {"tags": ["Get"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RuntimeSelectListContextHolder"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RuntimeSelectListContextHolder"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RuntimeSelectListContextHolder"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OkObjectResult"}}}}}}}, "/v1/workflow-instances/{workflowInstanceId}/activity-stats/{activityId}": {"get": {"tags": ["Get"], "parameters": [{"name": "workflowInstanceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "activityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityStats"}}}}}}}, "/v1/workflow-definitions/{workflowDefinitionId}/{versionOptions}": {"get": {"tags": ["GetByDefinitionAndVersion"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionOptions", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinition"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-definitions/{versionId}": {"get": {"tags": ["GetByVersionId"], "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinition"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-definitions/{definitionId}/history": {"get": {"tags": ["History"], "parameters": [{"name": "definitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowDefinitionVersionModel"}}}}}}}}, "/v1/workflow-definitions/{workflowDefinitionId}/import": {"post": {"tags": ["Import"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ContentType": {"type": "string"}, "ContentDisposition": {"type": "string"}, "Headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "FileName": {"type": "string"}}}, "encoding": {"ContentType": {"style": "form"}, "ContentDisposition": {"style": "form"}, "Headers": {"style": "form"}, "Length": {"style": "form"}, "Name": {"style": "form"}, "FileName": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/v1/workflow-storage-providers": {"get": {"tags": ["List"], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowStorageDescriptor"}}}}}}}}, "/v1/workflow-providers": {"get": {"tags": ["List"], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowProviderDescriptor"}}}}}}}}, "/v1/workflow-instances": {"get": {"tags": ["List"], "parameters": [{"name": "workflow", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/WorkflowStatus"}}, {"name": "correlationId", "in": "query", "schema": {"type": "string"}}, {"name": "orderBy", "in": "query", "schema": {"$ref": "#/components/schemas/WorkflowInstanceOrderBy"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": "0"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": "25"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowInstanceSummaryModelPagedList"}}}}}}}, "/v1/workflow-definitions": {"get": {"tags": ["List"], "parameters": [{"name": "ids", "in": "query", "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "orderBy", "in": "query", "schema": {"$ref": "#/components/schemas/WorkflowDefinitionOrderBy"}}, {"name": "sortBy", "in": "query", "schema": {"$ref": "#/components/schemas/SortBy"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "query", "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinitionSummaryModelPagedList"}}}}}}, "post": {"tags": ["Save"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveWorkflowDefinitionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveWorkflowDefinitionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveWorkflowDefinitionRequest"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinition"}}}}}}}, "/v1/workflow-channels": {"get": {"tags": ["List"], "responses": {"200": {"description": "Success"}}}}, "/v1/features": {"get": {"tags": ["List"], "responses": {"200": {"description": "Success"}}}}, "/v1/activities": {"get": {"tags": ["List"], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityDescriptor"}}}}}}}}, "/v1/workflow-registry": {"get": {"tags": ["ListAll"], "parameters": [{"name": "version", "in": "query", "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowBlueprintSummaryModel"}}}}}}}}, "/v1/workflow-registry/by-definition-version-ids": {"get": {"tags": ["ListByDefinitionVersionIds"], "parameters": [{"name": "ids", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowBlueprintSummaryModelPagedList"}}}}}}}, "/v1/workflow-registry/by-provider/{providerName}": {"get": {"tags": ["ListByProvider"], "parameters": [{"name": "providerName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "query", "schema": {"$ref": "#/components/schemas/VersionOptions"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowBlueprintSummaryModelPagedList"}}}}}}}, "/v1/workflow-definitions/{workflowDefinitionId}/publish": {"post": {"tags": ["Publish"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinition"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-definitions/restore": {"post": {"tags": ["Rest<PERSON>"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ContentType": {"type": "string"}, "ContentDisposition": {"type": "string"}, "Headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "FileName": {"type": "string"}}}, "encoding": {"ContentType": {"style": "form"}, "ContentDisposition": {"style": "form"}, "Headers": {"style": "form"}, "Length": {"style": "form"}, "Name": {"style": "form"}, "FileName": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-definitions/{workflowDefinitionId}/retract": {"post": {"tags": ["Retract"], "parameters": [{"name": "workflowDefinitionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinition"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-instances/{id}/retry": {"post": {"tags": ["Retry"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetryWorkflowRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetryWorkflowRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RetryWorkflowRequest"}}}}, "responses": {"200": {"description": "Success"}, "202": {"description": "Accepted"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflow-definitions/{definitionId}/revert/{version}": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "definitionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowDefinition"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/v1/workflows/trigger": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerWorkflowsRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TriggerWorkflowsRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TriggerWorkflowsRequestModel"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerWorkflowsRequestModel"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/signals/trigger/{token}": {"get": {"tags": ["TriggerEndpoint"], "parameters": [{"name": "token", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["TriggerEndpoint"], "parameters": [{"name": "token", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/search": {"post": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/{id}": {"get": {"tags": ["Warehouse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}}}}}}, "/api/warehouse/add": {"post": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/edit": {"put": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/batchdelete": {"post": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/exportexcel": {"post": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/exportexcelbyids": {"post": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/getexceltemplate": {"get": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/import": {"post": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseImportVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseImportVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseImportVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehouse/getframeworkusers": {"get": {"tags": ["Warehouse"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/search": {"post": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/{id}": {"get": {"tags": ["WarehouseArea"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}}}}}}, "/api/warehousearea/add": {"post": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/edit": {"put": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/batchdelete": {"post": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/exportexcel": {"post": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaSearcher"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaSearcher"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaSearcher"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/exportexcelbyids": {"post": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/getexceltemplate": {"get": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/import": {"post": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaImportVM"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaImportVM"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseAreaImportVM"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/getwarehouses": {"get": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/warehousearea/getframeworkusers": {"get": {"tags": ["WarehouseArea"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getworkflowusers": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "itcode", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getworkflowgroups": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "ids", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getworkflowgroupmanagers": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "ids", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getworkflowmygroupmanagers": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "itcode", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getworkflowroles": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "ids", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/gettimeline": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "entitytype", "in": "query", "schema": {"type": "string"}}, {"name": "entityid", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getworkflow": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "entitytype", "in": "query", "schema": {"type": "string"}}, {"name": "entityid", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/_workflowapi/getmyapprove": {"get": {"tags": ["WorkflowApi"], "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "entitytype", "in": "query", "schema": {"type": "string"}}, {"name": "entityid", "in": "query", "schema": {"type": "string"}}, {"name": "tag", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": "1"}}, {"name": "take", "in": "query", "schema": {"type": "integer", "format": "int32", "default": "20"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"ActionLog": {"type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ModuleName": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "ActionName": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "ITCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ActionUrl": {"maxLength": 250, "minLength": 0, "type": "string", "nullable": true}, "ActionTime": {"type": "string", "format": "date-time"}, "Duration": {"type": "number", "format": "double"}, "Remark": {"type": "string", "nullable": true}, "IP": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "LogType": {"$ref": "#/components/schemas/ActionLogTypesEnum"}, "TenantCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "ActionLogSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "ITCode": {"type": "string", "nullable": true}, "ActionUrl": {"type": "string", "nullable": true}, "LogType": {"type": "array", "items": {"$ref": "#/components/schemas/ActionLogTypesEnum"}, "nullable": true}, "ActionTime": {"$ref": "#/components/schemas/DateRange"}, "IP": {"type": "string", "nullable": true}, "Duration": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "ActionLogTypesEnum": {"enum": ["Normal", "Exception", "Debug", "Job"], "type": "string"}, "ActionLogVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/ActionLog"}}, "additionalProperties": false}, "ActivityBlueprintModel": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "ParentId": {"type": "string", "nullable": true}, "PersistWorkflow": {"type": "boolean"}, "LoadWorkflowContext": {"type": "boolean"}, "SaveWorkflowContext": {"type": "boolean"}, "InputProperties": {"$ref": "#/components/schemas/Variables"}, "OutputProperties": {"$ref": "#/components/schemas/Variables"}, "X": {"type": "integer", "format": "int32", "nullable": true}, "Y": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ActivityDefinition": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "X": {"type": "integer", "format": "int32", "nullable": true}, "Y": {"type": "integer", "format": "int32", "nullable": true}, "PersistWorkflow": {"type": "boolean"}, "LoadWorkflowContext": {"type": "boolean"}, "SaveWorkflowContext": {"type": "boolean"}, "Properties": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityDefinitionProperty"}, "nullable": true}, "PropertyStorageProviders": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ActivityDefinitionProperty": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Syntax": {"type": "string", "nullable": true}, "Expressions": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ActivityDescriptor": {"type": "object", "properties": {"Type": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Category": {"type": "string", "nullable": true}, "Traits": {"$ref": "#/components/schemas/ActivityTraits"}, "Outcomes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Properties": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityInputDescriptor"}, "nullable": true, "deprecated": true}, "InputProperties": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityInputDescriptor"}, "nullable": true}, "OutputProperties": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityOutputDescriptor"}, "nullable": true}, "CustomAttributes": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ActivityEventCount": {"type": "object", "properties": {"EventName": {"type": "string", "nullable": true}, "Count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ActivityFault": {"type": "object", "properties": {"Message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ActivityInputDescriptor": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Type": {"$ref": "#/components/schemas/Type"}, "UIHint": {"type": "string", "nullable": true}, "Label": {"type": "string", "nullable": true}, "Hint": {"type": "string", "nullable": true}, "Options": {"nullable": true}, "Category": {"type": "string", "nullable": true}, "Order": {"type": "number", "format": "float"}, "DefaultValue": {"nullable": true}, "DefaultSyntax": {"type": "string", "nullable": true}, "SupportedSyntaxes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "IsReadOnly": {"type": "boolean", "nullable": true}, "IsBrowsable": {"type": "boolean", "nullable": true}, "IsDesignerCritical": {"type": "boolean"}, "DefaultWorkflowStorageProvider": {"type": "string", "nullable": true}, "DisableWorkflowProviderSelection": {"type": "boolean"}, "ConsiderValuesAsOutcomes": {"type": "boolean"}}, "additionalProperties": false}, "ActivityOutputDescriptor": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Type": {"$ref": "#/components/schemas/Type"}, "Hint": {"type": "string", "nullable": true}, "IsBrowsable": {"type": "boolean", "nullable": true}, "DefaultWorkflowStorageProvider": {"type": "string", "nullable": true}, "DisableWorkflowProviderSelection": {"type": "boolean"}}, "additionalProperties": false}, "ActivityScope": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "Variables": {"$ref": "#/components/schemas/Variables"}}, "additionalProperties": false}, "ActivityStats": {"type": "object", "properties": {"EventCounts": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityEventCount"}, "nullable": true}, "Fault": {"$ref": "#/components/schemas/ActivityFault"}, "AverageExecutionTime": {"$ref": "#/components/schemas/Duration"}, "FastestExecutionTime": {"$ref": "#/components/schemas/Duration"}, "SlowestExecutionTime": {"$ref": "#/components/schemas/Duration"}, "LastExecutedAt": {"$ref": "#/components/schemas/Instant"}}, "additionalProperties": false}, "ActivityTraits": {"enum": ["Action", "<PERSON><PERSON>", "Job"], "type": "string"}, "AreaStatus": {"enum": ["Normal", "Maintenance", "Full", "Locked", "Disabled"], "type": "string"}, "AreaType": {"enum": ["Storage", "Receiving", "Shipping", "Picking", "Staging", "Return", "QualityControl", "Packaging", "Other"], "type": "string"}, "Assembly": {"type": "object", "properties": {"DefinedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "ExportedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "CodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "EntryPoint": {"$ref": "#/components/schemas/MethodInfo"}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "ImageRuntimeVersion": {"type": "string", "nullable": true, "readOnly": true}, "IsDynamic": {"type": "boolean", "readOnly": true}, "Location": {"type": "string", "nullable": true, "readOnly": true}, "ReflectionOnly": {"type": "boolean", "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "IsFullyTrusted": {"type": "boolean", "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "EscapedCodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "ManifestModule": {"$ref": "#/components/schemas/Module"}, "Modules": {"type": "array", "items": {"$ref": "#/components/schemas/Module"}, "nullable": true, "readOnly": true}, "GlobalAssemblyCache": {"type": "boolean", "readOnly": true, "deprecated": true}, "HostContext": {"type": "integer", "format": "int64", "readOnly": true}, "SecurityRuleSet": {"$ref": "#/components/schemas/SecurityRuleSet"}}, "additionalProperties": false}, "BaseSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}}, "additionalProperties": false}, "BlockingActivity": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "ActivityType": {"type": "string", "nullable": true}, "Tag": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkCancelWorkflowsRequest": {"type": "object", "properties": {"WorkflowInstanceIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BulkDeleteWorkflowsRequest": {"type": "object", "properties": {"WorkflowInstanceIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BulkRetryWorkflowsRequest": {"type": "object", "properties": {"WorkflowInstanceIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CallingConventions": {"enum": ["Standard", "<PERSON>ar<PERSON><PERSON><PERSON>", "Any", "<PERSON><PERSON><PERSON>", "ExplicitThis"], "type": "string"}, "ChangePasswordVM": {"required": ["NewPassword", "NewPasswordComfirm", "OldPassword"], "type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "ITCode": {"type": "string", "nullable": true}, "OldPassword": {"maxLength": 50, "minLength": 0, "type": "string"}, "NewPassword": {"maxLength": 50, "minLength": 0, "type": "string"}, "NewPasswordComfirm": {"maxLength": 50, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "CollectedWorkflow": {"type": "object", "properties": {"WorkflowInstanceId": {"type": "string", "nullable": true}, "WorkflowInstance": {"$ref": "#/components/schemas/WorkflowInstance"}, "ActivityId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ComboSelectListItem": {"type": "object", "properties": {"Text": {"type": "string", "nullable": true}, "Value": {"type": "string", "nullable": true}, "Selected": {"type": "boolean"}, "Disabled": {"type": "boolean"}, "ParentId": {"type": "string", "nullable": true}, "Icon": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ConnectionDefinition": {"type": "object", "properties": {"SourceActivityId": {"type": "string", "nullable": true}, "TargetActivityId": {"type": "string", "nullable": true}, "Outcome": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ConnectionModel": {"type": "object", "properties": {"SourceActivityId": {"type": "string", "nullable": true}, "TargetActivityId": {"type": "string", "nullable": true}, "Outcome": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ConstructorInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "Attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "MethodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "CallingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsConstructor": {"type": "boolean", "readOnly": true}, "IsFinal": {"type": "boolean", "readOnly": true}, "IsHideBySig": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsVirtual": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "MethodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}}, "additionalProperties": false}, "CustomAttributeData": {"type": "object", "properties": {"AttributeType": {"$ref": "#/components/schemas/Type"}, "Constructor": {"$ref": "#/components/schemas/ConstructorInfo"}, "ConstructorArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "nullable": true, "readOnly": true}, "NamedArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeNamedArgument"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CustomAttributeNamedArgument": {"type": "object", "properties": {"MemberInfo": {"$ref": "#/components/schemas/MemberInfo"}, "TypedValue": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "MemberName": {"type": "string", "nullable": true, "readOnly": true}, "IsField": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CustomAttributeTypedArgument": {"type": "object", "properties": {"ArgumentType": {"$ref": "#/components/schemas/Type"}, "Value": {"nullable": true}}, "additionalProperties": false}, "DBTypeEnum": {"enum": ["SqlServer", "MySql", "PgSql", "Memory", "SQLite", "Oracle", "<PERSON><PERSON><PERSON>"], "type": "string"}, "DataPrivilege": {"required": ["TableName"], "type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UserCode": {"type": "string", "nullable": true}, "GroupCode": {"type": "string", "nullable": true}, "TableName": {"maxLength": 50, "minLength": 0, "type": "string"}, "RelateId": {"type": "string", "nullable": true}, "Domain": {"type": "string", "nullable": true}, "TenantCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "DataPrivilegeSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "Name": {"type": "string", "nullable": true}, "TableName": {"type": "string", "nullable": true}, "TableNames": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}, "nullable": true}, "DpType": {"$ref": "#/components/schemas/DpTypeEnum"}, "DomainID": {"type": "string", "format": "uuid", "nullable": true}, "AllDomains": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}, "nullable": true}}, "additionalProperties": false}, "DataPrivilegeVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/DataPrivilege"}, "TableNames": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}, "nullable": true}, "AllItems": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}, "nullable": true}, "SelectedItemsID": {"type": "array", "items": {"type": "string"}, "nullable": true}, "DpType": {"$ref": "#/components/schemas/DpTypeEnum"}, "DpList": {"$ref": "#/components/schemas/DpListVM"}, "IsAll": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "DateRange": {"type": "object", "properties": {"Value": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "DispatchSignalRequest": {"type": "object", "properties": {"WorkflowInstanceId": {"type": "string", "nullable": true}, "CorrelationId": {"type": "string", "nullable": true}, "Input": {"nullable": true}}, "additionalProperties": false}, "DispatchSignalResponse": {"type": "object", "properties": {"StartedWorkflows": {"type": "array", "items": {"$ref": "#/components/schemas/CollectedWorkflow"}, "nullable": true}}, "additionalProperties": false}, "DispatchWorkflowDefinitionRequestModel": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "CorrelationId": {"type": "string", "nullable": true}, "ContextId": {"type": "string", "nullable": true}, "Input": {"nullable": true}}, "additionalProperties": false}, "DispatchWorkflowDefinitionResponseModel": {"type": "object", "properties": {"WorkflowInstanceId": {"type": "string", "nullable": true}, "ActivityId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DispatchWorkflowInstanceRequestModel": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "Input": {"$ref": "#/components/schemas/WorkflowInput"}}, "additionalProperties": false}, "DispatchWorkflowInstanceResponseModel": {"type": "object", "additionalProperties": false}, "DpListVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Ids": {"type": "array", "items": {"type": "string"}, "nullable": true}, "SelectorValueField": {"type": "string", "nullable": true}, "DetailGridPrix": {"type": "string", "nullable": true}, "ModelType": {"$ref": "#/components/schemas/Type"}}, "additionalProperties": false}, "DpTypeEnum": {"enum": ["UserGroup", "User"], "type": "string"}, "Duration": {"type": "object", "properties": {"Days": {"type": "integer", "format": "int32", "readOnly": true}, "NanosecondOfDay": {"type": "integer", "format": "int64", "readOnly": true}, "Hours": {"type": "integer", "format": "int32", "readOnly": true}, "Minutes": {"type": "integer", "format": "int32", "readOnly": true}, "Seconds": {"type": "integer", "format": "int32", "readOnly": true}, "Milliseconds": {"type": "integer", "format": "int32", "readOnly": true}, "SubsecondTicks": {"type": "integer", "format": "int32", "readOnly": true}, "SubsecondNanoseconds": {"type": "integer", "format": "int32", "readOnly": true}, "BclCompatibleTicks": {"type": "integer", "format": "int64", "readOnly": true}, "TotalDays": {"type": "number", "format": "double", "readOnly": true}, "TotalHours": {"type": "number", "format": "double", "readOnly": true}, "TotalMinutes": {"type": "number", "format": "double", "readOnly": true}, "TotalSeconds": {"type": "number", "format": "double", "readOnly": true}, "TotalMilliseconds": {"type": "number", "format": "double", "readOnly": true}, "TotalTicks": {"type": "number", "format": "double", "readOnly": true}, "TotalNanoseconds": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "EventAttributes": {"enum": ["None", "SpecialName", "RTSpecialName"], "type": "string"}, "EventInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Attributes": {"$ref": "#/components/schemas/EventAttributes"}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "AddMethod": {"$ref": "#/components/schemas/MethodInfo"}, "RemoveMethod": {"$ref": "#/components/schemas/MethodInfo"}, "RaiseMethod": {"$ref": "#/components/schemas/MethodInfo"}, "IsMulticast": {"type": "boolean", "readOnly": true}, "EventHandlerType": {"$ref": "#/components/schemas/Type"}}, "additionalProperties": false}, "ExecuteSignalRequest": {"type": "object", "properties": {"WorkflowInstanceId": {"type": "string", "nullable": true}, "CorrelationId": {"type": "string", "nullable": true}, "Input": {"nullable": true}}, "additionalProperties": false}, "ExecuteSignalResponse": {"type": "object", "properties": {"StartedWorkflows": {"type": "array", "items": {"$ref": "#/components/schemas/CollectedWorkflow"}, "nullable": true}}, "additionalProperties": false}, "ExecuteWorkflowDefinitionRequestModel": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "CorrelationId": {"type": "string", "nullable": true}, "ContextId": {"type": "string", "nullable": true}, "Input": {"nullable": true}}, "additionalProperties": false}, "ExecuteWorkflowDefinitionResponseModel": {"type": "object", "properties": {"Executed": {"type": "boolean"}, "ActivityId": {"type": "string", "nullable": true}, "WorkflowInstance": {"$ref": "#/components/schemas/WorkflowInstance"}}, "additionalProperties": false}, "ExecuteWorkflowInstanceRequest": {"type": "object", "properties": {"WorkflowInstanceId": {"type": "string", "nullable": true}, "ActivityId": {"type": "string", "nullable": true}, "Input": {"$ref": "#/components/schemas/WorkflowInput"}}, "additionalProperties": false}, "ExecuteWorkflowInstanceResponseModel": {"type": "object", "properties": {"Executed": {"type": "boolean"}, "ActivityId": {"type": "string", "nullable": true}, "WorkflowInstance": {"$ref": "#/components/schemas/WorkflowInstance"}}, "additionalProperties": false}, "FieldAttributes": {"enum": ["PrivateScope", "Private", "FamANDAssem", "Assembly", "Family", "FamORAssem", "Public", "FieldAccessMask", "Static", "InitOnly", "Literal", "NotSerialized", "HasFieldRVA", "SpecialName", "RTSpecialName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PinvokeImpl", "<PERSON><PERSON><PERSON><PERSON>", "ReservedMask"], "type": "string"}, "FieldInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Attributes": {"$ref": "#/components/schemas/FieldAttributes"}, "FieldType": {"$ref": "#/components/schemas/Type"}, "IsInitOnly": {"type": "boolean", "readOnly": true}, "IsLiteral": {"type": "boolean", "readOnly": true}, "IsNotSerialized": {"type": "boolean", "readOnly": true, "deprecated": true}, "IsPinvokeImpl": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "FieldHandle": {"$ref": "#/components/schemas/RuntimeFieldHandle"}}, "additionalProperties": false}, "FrameworkGroup": {"required": ["GroupCode", "GroupName"], "type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "ParentId": {"type": "string", "format": "uuid", "nullable": true}, "Children": {"type": "array", "items": {"$ref": "#/components/schemas/FrameworkGroup"}, "nullable": true}, "HasChildren": {"type": "boolean", "readOnly": true}, "GroupCode": {"maxLength": 50, "minLength": 0, "pattern": "^[0-9]*$", "type": "string"}, "GroupName": {"maxLength": 50, "minLength": 0, "type": "string"}, "GroupRemark": {"type": "string", "nullable": true}, "Manager": {"type": "string", "nullable": true}, "UsersCount": {"type": "integer", "format": "int32"}, "TenantCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "FrameworkGroupImportVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "UploadFileId": {"type": "string", "nullable": true}, "UseBulkSave": {"type": "boolean"}, "IsOverWriteExistData": {"type": "boolean"}}, "additionalProperties": false}, "FrameworkGroupSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "GroupCode": {"type": "string", "nullable": true}, "GroupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FrameworkGroupVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/FrameworkGroup"}}, "additionalProperties": false}, "FrameworkMenu": {"required": ["DisplayOrder", "FolderOnly", "IsInherit", "IsInside", "IsPublic", "PageName", "ShowOnMenu"], "type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "ParentId": {"type": "string", "format": "uuid", "nullable": true}, "Children": {"type": "array", "items": {"$ref": "#/components/schemas/FrameworkMenu"}, "nullable": true}, "HasChildren": {"type": "boolean", "readOnly": true}, "PageName": {"maxLength": 100, "minLength": 0, "type": "string"}, "ActionName": {"type": "string", "nullable": true}, "ModuleName": {"type": "string", "nullable": true}, "FolderOnly": {"type": "boolean"}, "IsInherit": {"type": "boolean"}, "ClassName": {"type": "string", "nullable": true}, "MethodName": {"type": "string", "nullable": true}, "Domain": {"type": "string", "nullable": true}, "ShowOnMenu": {"type": "boolean"}, "IsPublic": {"type": "boolean"}, "DisplayOrder": {"type": "integer", "format": "int32"}, "IsInside": {"type": "boolean"}, "TenantAllowed": {"type": "boolean", "nullable": true}, "Url": {"type": "string", "nullable": true}, "Icon": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "FrameworkMenuVM2": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/FrameworkMenu"}, "SelectedActionIDs": {"type": "array", "items": {"type": "string"}, "nullable": true}, "SelectedModule": {"type": "string", "nullable": true}, "SelectedRolesCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "FrameworkRole": {"required": ["RoleCode", "RoleName"], "type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "RoleCode": {"maxLength": 50, "minLength": 0, "pattern": "^[0-9]*$", "type": "string"}, "RoleName": {"maxLength": 50, "minLength": 0, "type": "string"}, "RoleRemark": {"type": "string", "nullable": true}, "TenantCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UsersCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "FrameworkRoleImportVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "UploadFileId": {"type": "string", "nullable": true}, "UseBulkSave": {"type": "boolean"}, "IsOverWriteExistData": {"type": "boolean"}}, "additionalProperties": false}, "FrameworkRoleMDVM2": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/FrameworkRole"}, "Pages": {"type": "array", "items": {"$ref": "#/components/schemas/Page_View"}, "nullable": true}}, "additionalProperties": false}, "FrameworkRoleSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "RoleCode": {"type": "string", "nullable": true}, "RoleName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FrameworkRoleVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/FrameworkRole"}}, "additionalProperties": false}, "FrameworkTenant": {"required": ["TCode", "TName"], "type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "TCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "TName": {"maxLength": 50, "minLength": 0, "type": "string"}, "TDb": {"type": "string", "nullable": true}, "TDbType": {"$ref": "#/components/schemas/DBTypeEnum"}, "DbContext": {"type": "string", "nullable": true}, "TDomain": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "TenantCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "EnableSub": {"type": "boolean"}, "Enabled": {"type": "boolean"}, "IsUsingDB": {"type": "boolean", "readOnly": true}, "Attributes": {"type": "object", "additionalProperties": {"nullable": true}, "nullable": true}}, "additionalProperties": false}, "FrameworkTenantImportVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "UploadFileId": {"type": "string", "nullable": true}, "UseBulkSave": {"type": "boolean"}, "IsOverWriteExistData": {"type": "boolean"}}, "additionalProperties": false}, "FrameworkTenantSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "TCode": {"type": "string", "nullable": true}, "TName": {"type": "string", "nullable": true}, "TDomain": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FrameworkTenantVM": {"required": ["AdminRoleCode"], "type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/FrameworkTenant"}, "AdminRoleCode": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "FrameworkUser": {"required": ["ITCode", "Name", "Password"], "type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ITCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "Password": {"maxLength": 32, "minLength": 0, "type": "string"}, "Name": {"maxLength": 50, "minLength": 0, "type": "string"}, "IsValid": {"type": "boolean"}, "PhotoId": {"type": "string", "format": "uuid", "nullable": true}, "TenantCode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "Email": {"maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$", "type": "string", "nullable": true}, "Gender": {"$ref": "#/components/schemas/GenderEnum"}, "CellPhone": {"pattern": "^[1][3-9]\\d{9}$", "type": "string", "nullable": true}, "HomePhone": {"maxLength": 30, "minLength": 0, "pattern": "^[-0-9\\s]{8,30}$", "type": "string", "nullable": true}, "Address": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "ZipCode": {"pattern": "^[0-9]{6,6}$", "type": "string", "nullable": true}}, "additionalProperties": false}, "FrameworkUserImportVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "UploadFileId": {"type": "string", "nullable": true}, "UseBulkSave": {"type": "boolean"}, "IsOverWriteExistData": {"type": "boolean"}}, "additionalProperties": false}, "FrameworkUserSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "ITCode": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "IsValid": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "FrameworkUserVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/FrameworkUser"}, "SelectedRolesCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "SelectedGroupCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GenderEnum": {"enum": ["Male", "Female"], "type": "string"}, "GenericParameterAttributes": {"enum": ["None", "Covariant", "Contravariant", "VarianceMask", "ReferenceTypeConstraint", "NotNullableValueTypeConstraint", "DefaultConstructorConstraint", "SpecialConstraintMask", "AllowByRefLike"], "type": "string"}, "IBookmark": {"type": "object", "additionalProperties": false}, "ICustomAttributeProvider": {"type": "object", "additionalProperties": false}, "IOutputFormatter": {"type": "object", "additionalProperties": false}, "Instant": {"type": "object", "additionalProperties": false}, "IntPtr": {"type": "object", "additionalProperties": false}, "IntellisenseContext": {"type": "object", "properties": {"ActivityTypeName": {"type": "string", "nullable": true}, "PropertyName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "JToken": {"type": "array", "items": {"$ref": "#/components/schemas/JToken"}}, "LayoutKind": {"enum": ["Sequential", "Explicit", "Auto"], "type": "string"}, "Location": {"required": ["AreaId", "Code"], "type": "object", "properties": {"ID": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "AreaId": {"maxLength": 50, "minLength": 0, "type": "string"}, "Area": {"$ref": "#/components/schemas/WarehouseArea"}, "Type": {"$ref": "#/components/schemas/LocationType"}, "Status": {"$ref": "#/components/schemas/LocationStatus"}, "Row": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "Column": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "Level": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "MaxWeight": {"type": "number", "format": "double", "nullable": true}, "MaxVolume": {"type": "number", "format": "double", "nullable": true}, "Length": {"type": "number", "format": "double", "nullable": true}, "Width": {"type": "number", "format": "double", "nullable": true}, "Height": {"type": "number", "format": "double", "nullable": true}, "Barcode": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "AllowMixed": {"type": "boolean"}, "IsEnabled": {"type": "boolean"}, "Remark": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "LocationStatus": {"enum": ["Empty", "Occupied", "Locked", "Disabled"], "type": "string"}, "LocationType": {"enum": ["Storage", "Picking", "Receiving", "Shipping", "Return", "QC", "Scrap", "Other"], "type": "string"}, "MemberInfo": {"type": "object", "properties": {"MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "MemberTypes": {"enum": ["<PERSON><PERSON><PERSON><PERSON>", "Event", "Field", "Method", "Property", "TypeInfo", "Custom", "NestedType", "All"], "type": "string"}, "MenuItem": {"type": "object", "properties": {"Text": {"type": "string", "nullable": true}, "Value": {"type": "string", "nullable": true}, "Icon": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MethodAttributes": {"enum": ["PrivateScope", "Private", "FamANDAssem", "Assembly", "Family", "FamORAssem", "Public", "MemberAccessMask", "UnmanagedExport", "Static", "Final", "Virtual", "HideBySig", "NewSlot", "CheckAccessOnOverride", "Abstract", "SpecialName", "RTSpecialName", "PinvokeImpl", "HasSecurity", "RequireSecObject", "ReservedMask"], "type": "string"}, "MethodBase": {"type": "object", "properties": {"MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "Attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "MethodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "CallingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsConstructor": {"type": "boolean", "readOnly": true}, "IsFinal": {"type": "boolean", "readOnly": true}, "IsHideBySig": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsVirtual": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "MethodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "MethodImplAttributes": {"enum": ["IL", "Native", "OPTIL", "CodeTypeMask", "ManagedMask", "NoInlining", "ForwardRef", "Synchronized", "NoOptimization", "PreserveSig", "AggressiveInlining", "AggressiveOptimization", "InternalCall", "MaxMethodImplVal"], "type": "string"}, "MethodInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "Attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "MethodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "CallingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsConstructor": {"type": "boolean", "readOnly": true}, "IsFinal": {"type": "boolean", "readOnly": true}, "IsHideBySig": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsVirtual": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "MethodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "ReturnParameter": {"$ref": "#/components/schemas/ParameterInfo"}, "ReturnType": {"$ref": "#/components/schemas/Type"}, "ReturnTypeCustomAttributes": {"$ref": "#/components/schemas/ICustomAttributeProvider"}}, "additionalProperties": false}, "Module": {"type": "object", "properties": {"Assembly": {"$ref": "#/components/schemas/Assembly"}, "FullyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "MDStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}, "ModuleVersionId": {"type": "string", "format": "uuid", "readOnly": true}, "ScopeName": {"type": "string", "nullable": true, "readOnly": true}, "ModuleHandle": {"$ref": "#/components/schemas/ModuleHandle"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ModuleHandle": {"type": "object", "properties": {"MDStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "OkObjectResult": {"type": "object", "properties": {"Value": {"nullable": true}, "Formatters": {"type": "array", "items": {"$ref": "#/components/schemas/IOutputFormatter"}, "nullable": true}, "ContentTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "DeclaredType": {"$ref": "#/components/schemas/Type"}, "StatusCode": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "Page_View": {"type": "object", "properties": {"ID": {"type": "string", "format": "uuid"}, "Name": {"type": "string", "nullable": true}, "Actions": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "AllActions": {"type": "array", "items": {"$ref": "#/components/schemas/ComboSelectListItem"}, "nullable": true}, "Children": {"type": "array", "items": {"$ref": "#/components/schemas/Page_View"}, "nullable": true}, "IsFolder": {"type": "boolean"}, "ParentID": {"type": "string", "format": "uuid", "nullable": true}, "Level": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ParameterAttributes": {"enum": ["None", "In", "Out", "Lcid", "<PERSON><PERSON><PERSON>", "Optional", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reserved3", "Reserved4", "ReservedMask"], "type": "string"}, "ParameterInfo": {"type": "object", "properties": {"Attributes": {"$ref": "#/components/schemas/ParameterAttributes"}, "Member": {"$ref": "#/components/schemas/MemberInfo"}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "ParameterType": {"$ref": "#/components/schemas/Type"}, "Position": {"type": "integer", "format": "int32", "readOnly": true}, "IsIn": {"type": "boolean", "readOnly": true}, "IsLcid": {"type": "boolean", "readOnly": true}, "IsOptional": {"type": "boolean", "readOnly": true}, "IsOut": {"type": "boolean", "readOnly": true}, "IsRetval": {"type": "boolean", "readOnly": true}, "DefaultValue": {"nullable": true, "readOnly": true}, "RawDefaultValue": {"nullable": true, "readOnly": true}, "HasDefaultValue": {"type": "boolean", "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "PropertyAttributes": {"enum": ["None", "SpecialName", "RTSpecialName", "<PERSON><PERSON><PERSON><PERSON>", "Reserved2", "Reserved3", "Reserved4", "ReservedMask"], "type": "string"}, "PropertyInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "PropertyType": {"$ref": "#/components/schemas/Type"}, "Attributes": {"$ref": "#/components/schemas/PropertyAttributes"}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "CanRead": {"type": "boolean", "readOnly": true}, "CanWrite": {"type": "boolean", "readOnly": true}, "GetMethod": {"$ref": "#/components/schemas/MethodInfo"}, "SetMethod": {"$ref": "#/components/schemas/MethodInfo"}}, "additionalProperties": false}, "RetryWorkflowRequest": {"type": "object", "properties": {"RunImmediately": {"type": "boolean"}}, "additionalProperties": false}, "RuntimeFieldHandle": {"type": "object", "properties": {"Value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeMethodHandle": {"type": "object", "properties": {"Value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeSelectListContextHolder": {"type": "object", "properties": {"ProviderTypeName": {"type": "string", "nullable": true}, "Context": {"nullable": true}}, "additionalProperties": false}, "RuntimeTypeHandle": {"type": "object", "properties": {"Value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "SaveWorkflowDefinitionRequest": {"type": "object", "properties": {"WorkflowDefinitionId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Tag": {"type": "string", "nullable": true}, "Channel": {"type": "string", "nullable": true}, "Variables": {"type": "string", "nullable": true}, "ContextOptions": {"$ref": "#/components/schemas/WorkflowContextOptions"}, "IsSingleton": {"type": "boolean"}, "PersistenceBehavior": {"$ref": "#/components/schemas/WorkflowPersistenceBehavior"}, "DeleteCompletedInstances": {"type": "boolean"}, "Publish": {"type": "boolean"}, "Activities": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityDefinition"}, "nullable": true}, "Connections": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionDefinition"}, "nullable": true}, "CustomAttributes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ScheduledActivity": {"type": "object", "properties": {"ActivityId": {"type": "string", "nullable": true}, "Input": {"nullable": true}}, "additionalProperties": false}, "SecurityRuleSet": {"enum": ["None", "Level1", "Level2"], "type": "string"}, "SimpleDpModel": {"type": "object", "properties": {"ModelName": {"type": "string", "nullable": true}, "Id": {"type": "string", "nullable": true}, "Type": {"$ref": "#/components/schemas/DpTypeEnum"}}, "additionalProperties": false}, "SimpleException": {"type": "object", "properties": {"Type": {"$ref": "#/components/schemas/Type"}, "Message": {"type": "string", "nullable": true}, "StackTrace": {"type": "string", "nullable": true}, "InnerException": {"$ref": "#/components/schemas/SimpleException"}, "Data": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "SimpleLogin": {"type": "object", "properties": {"Account": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "Tenant": {"type": "string", "nullable": true}, "RemoteToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SimpleReg": {"required": ["ITCode", "Name", "Password"], "type": "object", "properties": {"ITCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "Name": {"maxLength": 50, "minLength": 0, "type": "string"}, "Password": {"maxLength": 50, "minLength": 0, "type": "string"}, "PhotoId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "SortBy": {"enum": ["Ascending", "Descending"], "type": "string"}, "SortDir": {"enum": ["Asc", "Desc"], "type": "string"}, "SortInfo": {"type": "object", "properties": {"Property": {"type": "string", "nullable": true}, "Direction": {"$ref": "#/components/schemas/SortDir"}}, "additionalProperties": false}, "StructLayoutAttribute": {"type": "object", "properties": {"TypeId": {"nullable": true, "readOnly": true}, "Value": {"$ref": "#/components/schemas/LayoutKind"}}, "additionalProperties": false}, "Token": {"type": "object", "properties": {"access_token": {"type": "string", "nullable": true}, "expires_in": {"type": "integer", "format": "int32"}, "token_type": {"type": "string", "nullable": true}, "refresh_token": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TriggerWorkflowsRequestModel": {"type": "object", "properties": {"ActivityType": {"type": "string", "nullable": true}, "Bookmark": {"$ref": "#/components/schemas/IBookmark"}, "CorrelationId": {"type": "string", "nullable": true}, "WorkflowInstanceId": {"type": "string", "nullable": true}, "ContextId": {"type": "string", "nullable": true}, "Input": {"nullable": true}, "Dispatch": {"type": "boolean"}}, "additionalProperties": false}, "Type": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Namespace": {"type": "string", "nullable": true, "readOnly": true}, "AssemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "Assembly": {"$ref": "#/components/schemas/Assembly"}, "Module": {"$ref": "#/components/schemas/Module"}, "IsInterface": {"type": "boolean", "readOnly": true}, "IsNested": {"type": "boolean", "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "DeclaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "UnderlyingSystemType": {"$ref": "#/components/schemas/Type"}, "IsTypeDefinition": {"type": "boolean", "readOnly": true}, "IsArray": {"type": "boolean", "readOnly": true}, "IsByRef": {"type": "boolean", "readOnly": true}, "IsPointer": {"type": "boolean", "readOnly": true}, "IsConstructedGenericType": {"type": "boolean", "readOnly": true}, "IsGenericParameter": {"type": "boolean", "readOnly": true}, "IsGenericTypeParameter": {"type": "boolean", "readOnly": true}, "IsGenericMethodParameter": {"type": "boolean", "readOnly": true}, "IsGenericType": {"type": "boolean", "readOnly": true}, "IsGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "IsSZArray": {"type": "boolean", "readOnly": true}, "IsVariableBoundArray": {"type": "boolean", "readOnly": true}, "IsByRefLike": {"type": "boolean", "readOnly": true}, "IsFunctionPointer": {"type": "boolean", "readOnly": true}, "IsUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "HasElementType": {"type": "boolean", "readOnly": true}, "GenericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "GenericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "GenericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "Attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsImport": {"type": "boolean", "readOnly": true}, "IsSealed": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsClass": {"type": "boolean", "readOnly": true}, "IsNestedAssembly": {"type": "boolean", "readOnly": true}, "IsNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "IsNestedFamily": {"type": "boolean", "readOnly": true}, "IsNestedFamORAssem": {"type": "boolean", "readOnly": true}, "IsNestedPrivate": {"type": "boolean", "readOnly": true}, "IsNestedPublic": {"type": "boolean", "readOnly": true}, "IsNotPublic": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsAutoLayout": {"type": "boolean", "readOnly": true}, "IsExplicitLayout": {"type": "boolean", "readOnly": true}, "IsLayoutSequential": {"type": "boolean", "readOnly": true}, "IsAnsiClass": {"type": "boolean", "readOnly": true}, "IsAutoClass": {"type": "boolean", "readOnly": true}, "IsUnicodeClass": {"type": "boolean", "readOnly": true}, "IsCOMObject": {"type": "boolean", "readOnly": true}, "IsContextful": {"type": "boolean", "readOnly": true}, "IsEnum": {"type": "boolean", "readOnly": true}, "IsMarshalByRef": {"type": "boolean", "readOnly": true}, "IsPrimitive": {"type": "boolean", "readOnly": true}, "IsValueType": {"type": "boolean", "readOnly": true}, "IsSignatureType": {"type": "boolean", "readOnly": true}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "StructLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "TypeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "TypeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "GUID": {"type": "string", "format": "uuid", "readOnly": true}, "BaseType": {"$ref": "#/components/schemas/Type"}, "IsSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "IsVisible": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TypeAttributes": {"enum": ["NotPublic", "Public", "NestedPublic", "NestedPrivate", "NestedFamily", "NestedAssembly", "NestedFamANDAssem", "VisibilityMask", "SequentialLayout", "ExplicitLayout", "LayoutMask", "Interface", "Abstract", "Sealed", "SpecialName", "RTSpecialName", "Import", "Serializable", "WindowsRuntime", "UnicodeClass", "AutoClass", "StringFormatMask", "HasSecurity", "ReservedMask", "BeforeFieldInit", "CustomFormatMask"], "type": "string"}, "TypeInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Namespace": {"type": "string", "nullable": true, "readOnly": true}, "AssemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "Assembly": {"$ref": "#/components/schemas/Assembly"}, "Module": {"$ref": "#/components/schemas/Module"}, "IsInterface": {"type": "boolean", "readOnly": true}, "IsNested": {"type": "boolean", "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "DeclaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "UnderlyingSystemType": {"$ref": "#/components/schemas/Type"}, "IsTypeDefinition": {"type": "boolean", "readOnly": true}, "IsArray": {"type": "boolean", "readOnly": true}, "IsByRef": {"type": "boolean", "readOnly": true}, "IsPointer": {"type": "boolean", "readOnly": true}, "IsConstructedGenericType": {"type": "boolean", "readOnly": true}, "IsGenericParameter": {"type": "boolean", "readOnly": true}, "IsGenericTypeParameter": {"type": "boolean", "readOnly": true}, "IsGenericMethodParameter": {"type": "boolean", "readOnly": true}, "IsGenericType": {"type": "boolean", "readOnly": true}, "IsGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "IsSZArray": {"type": "boolean", "readOnly": true}, "IsVariableBoundArray": {"type": "boolean", "readOnly": true}, "IsByRefLike": {"type": "boolean", "readOnly": true}, "IsFunctionPointer": {"type": "boolean", "readOnly": true}, "IsUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "HasElementType": {"type": "boolean", "readOnly": true}, "GenericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "GenericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "GenericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "Attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsImport": {"type": "boolean", "readOnly": true}, "IsSealed": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsClass": {"type": "boolean", "readOnly": true}, "IsNestedAssembly": {"type": "boolean", "readOnly": true}, "IsNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "IsNestedFamily": {"type": "boolean", "readOnly": true}, "IsNestedFamORAssem": {"type": "boolean", "readOnly": true}, "IsNestedPrivate": {"type": "boolean", "readOnly": true}, "IsNestedPublic": {"type": "boolean", "readOnly": true}, "IsNotPublic": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsAutoLayout": {"type": "boolean", "readOnly": true}, "IsExplicitLayout": {"type": "boolean", "readOnly": true}, "IsLayoutSequential": {"type": "boolean", "readOnly": true}, "IsAnsiClass": {"type": "boolean", "readOnly": true}, "IsAutoClass": {"type": "boolean", "readOnly": true}, "IsUnicodeClass": {"type": "boolean", "readOnly": true}, "IsCOMObject": {"type": "boolean", "readOnly": true}, "IsContextful": {"type": "boolean", "readOnly": true}, "IsEnum": {"type": "boolean", "readOnly": true}, "IsMarshalByRef": {"type": "boolean", "readOnly": true}, "IsPrimitive": {"type": "boolean", "readOnly": true}, "IsValueType": {"type": "boolean", "readOnly": true}, "IsSignatureType": {"type": "boolean", "readOnly": true}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "StructLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "TypeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "TypeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "GUID": {"type": "string", "format": "uuid", "readOnly": true}, "BaseType": {"$ref": "#/components/schemas/Type"}, "IsSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "IsVisible": {"type": "boolean", "readOnly": true}, "GenericTypeParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "DeclaredConstructors": {"type": "array", "items": {"$ref": "#/components/schemas/ConstructorInfo"}, "nullable": true, "readOnly": true}, "DeclaredEvents": {"type": "array", "items": {"$ref": "#/components/schemas/EventInfo"}, "nullable": true, "readOnly": true}, "DeclaredFields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}, "nullable": true, "readOnly": true}, "DeclaredMembers": {"type": "array", "items": {"$ref": "#/components/schemas/MemberInfo"}, "nullable": true, "readOnly": true}, "DeclaredMethods": {"type": "array", "items": {"$ref": "#/components/schemas/MethodInfo"}, "nullable": true, "readOnly": true}, "DeclaredNestedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "DeclaredProperties": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyInfo"}, "nullable": true, "readOnly": true}, "ImplementedInterfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Variables": {"type": "object", "properties": {"Data": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "VersionOptions": {"type": "object", "properties": {"IsLatest": {"type": "boolean", "readOnly": true}, "IsLatestOrPublished": {"type": "boolean", "readOnly": true}, "IsPublished": {"type": "boolean", "readOnly": true}, "IsDraft": {"type": "boolean", "readOnly": true}, "AllVersions": {"type": "boolean", "readOnly": true}, "Version": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "Warehouse": {"required": ["Code", "Name"], "type": "object", "properties": {"ID": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "Type": {"$ref": "#/components/schemas/WarehouseType"}, "Status": {"$ref": "#/components/schemas/WarehouseStatus"}, "Address": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "ContactPerson": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ContactPhone": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "Area": {"type": "number", "format": "double", "nullable": true}, "Volume": {"type": "number", "format": "double", "nullable": true}, "ManagerID": {"type": "string", "format": "uuid", "nullable": true}, "Manager": {"$ref": "#/components/schemas/FrameworkUser"}, "Description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "Remark": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "Areas": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseArea"}, "nullable": true}}, "additionalProperties": false}, "WarehouseArea": {"required": ["Code", "Name", "WarehouseId"], "type": "object", "properties": {"ID": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "CreateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "UpdateTime": {"type": "string", "format": "date-time", "nullable": true}, "UpdateBy": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "Code": {"maxLength": 50, "minLength": 0, "type": "string"}, "Name": {"maxLength": 100, "minLength": 0, "type": "string"}, "WarehouseId": {"maxLength": 50, "minLength": 0, "type": "string"}, "Warehouse": {"$ref": "#/components/schemas/Warehouse"}, "Type": {"$ref": "#/components/schemas/AreaType"}, "Status": {"$ref": "#/components/schemas/AreaStatus"}, "Area": {"type": "number", "format": "double", "nullable": true}, "Volume": {"type": "number", "format": "double", "nullable": true}, "MaxCapacity": {"type": "integer", "format": "int32", "nullable": true}, "CurrentCapacity": {"type": "integer", "format": "int32", "nullable": true}, "ManagerId": {"type": "string", "format": "uuid", "nullable": true}, "Manager": {"$ref": "#/components/schemas/FrameworkUser"}, "Description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "Remark": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "Locations": {"type": "array", "items": {"$ref": "#/components/schemas/Location"}, "nullable": true}}, "additionalProperties": false}, "WarehouseAreaImportVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "UploadFileId": {"type": "string", "nullable": true}, "UseBulkSave": {"type": "boolean"}, "IsOverWriteExistData": {"type": "boolean"}}, "additionalProperties": false}, "WarehouseAreaSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "ID": {"type": "string", "nullable": true}, "Code": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "WarehouseId": {"type": "string", "nullable": true}, "Type": {"$ref": "#/components/schemas/AreaType"}, "Status": {"$ref": "#/components/schemas/AreaStatus"}, "Area": {"type": "number", "format": "double", "nullable": true}, "Volume": {"type": "number", "format": "double", "nullable": true}, "MaxCapacity": {"type": "integer", "format": "int32", "nullable": true}, "CurrentCapacity": {"type": "integer", "format": "int32", "nullable": true}, "ManagerId": {"type": "string", "format": "uuid", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WarehouseAreaVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/WarehouseArea"}}, "additionalProperties": false}, "WarehouseImportVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "UploadFileId": {"type": "string", "nullable": true}, "UseBulkSave": {"type": "boolean"}, "IsOverWriteExistData": {"type": "boolean"}}, "additionalProperties": false}, "WarehouseSearcher": {"type": "object", "properties": {"Page": {"type": "integer", "format": "int32"}, "Limit": {"type": "integer", "format": "int32"}, "IsPlainText": {"type": "boolean", "nullable": true}, "IsEnumToString": {"type": "boolean", "nullable": true}, "SortInfo": {"$ref": "#/components/schemas/SortInfo"}, "ID": {"type": "string", "nullable": true}, "Code": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "Type": {"$ref": "#/components/schemas/WarehouseType"}, "Status": {"$ref": "#/components/schemas/WarehouseStatus"}, "Address": {"type": "string", "nullable": true}, "ContactPerson": {"type": "string", "nullable": true}, "ContactPhone": {"type": "string", "nullable": true}, "Area": {"type": "number", "format": "double", "nullable": true}, "Volume": {"type": "number", "format": "double", "nullable": true}, "ManagerID": {"type": "string", "format": "uuid", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WarehouseStatus": {"enum": ["Operating", "Suspended", "Maintenance", "Closed"], "type": "string"}, "WarehouseType": {"enum": ["RawMaterial", "FinishedProduct", "SemiFinished", "Transit", "Return", "Defective", "Virtual", "General"], "type": "string"}, "WarehouseVM": {"type": "object", "properties": {"DeletedFileIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "Entity": {"$ref": "#/components/schemas/Warehouse"}}, "additionalProperties": false}, "WorkflowBlueprintModel": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "ParentId": {"type": "string", "nullable": true}, "PersistWorkflow": {"type": "boolean"}, "LoadWorkflowContext": {"type": "boolean"}, "SaveWorkflowContext": {"type": "boolean"}, "InputProperties": {"$ref": "#/components/schemas/Variables"}, "OutputProperties": {"$ref": "#/components/schemas/Variables"}, "X": {"type": "integer", "format": "int32", "nullable": true}, "Y": {"type": "integer", "format": "int32", "nullable": true}, "Activities": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityBlueprintModel"}, "nullable": true}, "Connections": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionModel"}, "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "TenantId": {"type": "string", "nullable": true}, "IsSingleton": {"type": "boolean"}, "IsEnabled": {"type": "boolean"}, "IsPublished": {"type": "boolean"}, "IsLatest": {"type": "boolean"}, "IsDisabled": {"type": "boolean"}, "Variables": {"$ref": "#/components/schemas/Variables"}, "ContextOptions": {"$ref": "#/components/schemas/WorkflowContextOptions"}, "PersistenceBehavior": {"$ref": "#/components/schemas/WorkflowPersistenceBehavior"}, "DeleteCompletedInstances": {"type": "boolean"}, "CustomAttributes": {"$ref": "#/components/schemas/Variables"}}, "additionalProperties": false}, "WorkflowBlueprintModelPagedList": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowBlueprintModel"}, "nullable": true}, "Page": {"type": "integer", "format": "int32", "nullable": true}, "PageSize": {"type": "integer", "format": "int32", "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkflowBlueprintSummaryModel": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "VersionId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "TenantId": {"type": "string", "nullable": true}, "IsSingleton": {"type": "boolean"}, "IsPublished": {"type": "boolean"}, "IsLatest": {"type": "boolean"}, "IsDisabled": {"type": "boolean"}}, "additionalProperties": false}, "WorkflowBlueprintSummaryModelPagedList": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowBlueprintSummaryModel"}, "nullable": true}, "Page": {"type": "integer", "format": "int32", "nullable": true}, "PageSize": {"type": "integer", "format": "int32", "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkflowContextFidelity": {"enum": ["<PERSON><PERSON><PERSON>", "Activity"], "type": "string"}, "WorkflowContextOptions": {"type": "object", "properties": {"ContextType": {"$ref": "#/components/schemas/Type"}, "ContextFidelity": {"$ref": "#/components/schemas/WorkflowContextFidelity"}}, "additionalProperties": false}, "WorkflowDefinition": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "DefinitionId": {"type": "string", "nullable": true}, "VersionId": {"type": "string", "nullable": true, "readOnly": true}, "TenantId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Channel": {"type": "string", "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "Variables": {"$ref": "#/components/schemas/Variables"}, "CustomAttributes": {"$ref": "#/components/schemas/Variables"}, "ContextOptions": {"$ref": "#/components/schemas/WorkflowContextOptions"}, "IsSingleton": {"type": "boolean"}, "PersistenceBehavior": {"$ref": "#/components/schemas/WorkflowPersistenceBehavior"}, "DeleteCompletedInstances": {"type": "boolean"}, "IsPublished": {"type": "boolean"}, "IsLatest": {"type": "boolean"}, "Tag": {"type": "string", "nullable": true}, "CreatedAt": {"$ref": "#/components/schemas/Instant"}, "Activities": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityDefinition"}, "nullable": true}, "Connections": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionDefinition"}, "nullable": true}}, "additionalProperties": false}, "WorkflowDefinitionOrderBy": {"enum": ["Name", "Description", "CreatedAt"], "type": "string"}, "WorkflowDefinitionSummaryModel": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "DefinitionId": {"type": "string", "nullable": true}, "TenantId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "IsSingleton": {"type": "boolean"}, "PersistenceBehavior": {"$ref": "#/components/schemas/WorkflowPersistenceBehavior"}, "IsPublished": {"type": "boolean"}, "IsLatest": {"type": "boolean"}, "CustomAttributes": {"$ref": "#/components/schemas/Variables"}, "CreatedAt": {"$ref": "#/components/schemas/Instant"}}, "additionalProperties": false}, "WorkflowDefinitionSummaryModelPagedList": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowDefinitionSummaryModel"}, "nullable": true}, "Page": {"type": "integer", "format": "int32", "nullable": true}, "PageSize": {"type": "integer", "format": "int32", "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkflowDefinitionVersionModel": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "DefinitionId": {"type": "string", "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "IsLatest": {"type": "boolean"}, "IsPublished": {"type": "boolean"}, "CreatedAt": {"$ref": "#/components/schemas/Instant"}}, "additionalProperties": false}, "WorkflowExecutionLogRecord": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "TenantId": {"type": "string", "nullable": true, "readOnly": true}, "WorkflowInstanceId": {"type": "string", "nullable": true}, "ActivityId": {"type": "string", "nullable": true}, "ActivityType": {"type": "string", "nullable": true}, "Timestamp": {"$ref": "#/components/schemas/Instant"}, "EventName": {"type": "string", "nullable": true}, "Message": {"type": "string", "nullable": true}, "Source": {"type": "string", "nullable": true}, "Data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/JToken"}, "nullable": true}}, "additionalProperties": false}, "WorkflowExecutionLogRecordPagedList": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecutionLogRecord"}, "nullable": true}, "Page": {"type": "integer", "format": "int32", "nullable": true}, "PageSize": {"type": "integer", "format": "int32", "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkflowFault": {"type": "object", "properties": {"Exception": {"$ref": "#/components/schemas/SimpleException"}, "Message": {"type": "string", "nullable": true}, "FaultedActivityId": {"type": "string", "nullable": true}, "ActivityInput": {"nullable": true}, "Resuming": {"type": "boolean"}}, "additionalProperties": false}, "WorkflowInput": {"type": "object", "properties": {"Input": {"nullable": true}, "StorageProviderName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowInputReference": {"type": "object", "properties": {"ProviderName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowInstance": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "DefinitionId": {"type": "string", "nullable": true}, "DefinitionVersionId": {"type": "string", "nullable": true}, "TenantId": {"type": "string", "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "WorkflowStatus": {"$ref": "#/components/schemas/WorkflowStatus"}, "CorrelationId": {"type": "string", "nullable": true}, "ContextType": {"type": "string", "nullable": true}, "ContextId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "CreatedAt": {"$ref": "#/components/schemas/Instant"}, "LastExecutedAt": {"$ref": "#/components/schemas/Instant"}, "FinishedAt": {"$ref": "#/components/schemas/Instant"}, "CancelledAt": {"$ref": "#/components/schemas/Instant"}, "FaultedAt": {"$ref": "#/components/schemas/Instant"}, "Variables": {"$ref": "#/components/schemas/Variables"}, "Input": {"$ref": "#/components/schemas/WorkflowInputReference"}, "Output": {"$ref": "#/components/schemas/WorkflowOutputReference"}, "ActivityData": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {}}, "nullable": true}, "Metadata": {"type": "object", "additionalProperties": {}, "nullable": true}, "BlockingActivities": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/BlockingActivity"}, "nullable": true}, "Fault": {"$ref": "#/components/schemas/WorkflowFault"}, "Faults": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowFault"}, "nullable": true}, "ScheduledActivities": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduledActivity"}, "nullable": true}, "Scopes": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityScope"}, "nullable": true}, "CurrentActivity": {"$ref": "#/components/schemas/ScheduledActivity"}, "LastExecutedActivityId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowInstanceOrderBy": {"enum": ["Started", "LastExecuted", "Finished"], "type": "string"}, "WorkflowInstanceSummaryModel": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "DefinitionId": {"type": "string", "nullable": true}, "DefinitionVersionId": {"type": "string", "nullable": true}, "TenantId": {"type": "string", "nullable": true}, "Version": {"type": "integer", "format": "int32"}, "WorkflowStatus": {"$ref": "#/components/schemas/WorkflowStatus"}, "CorrelationId": {"type": "string", "nullable": true}, "ContextType": {"type": "string", "nullable": true}, "ContextId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "CreatedAt": {"$ref": "#/components/schemas/Instant"}, "LastExecutedAt": {"$ref": "#/components/schemas/Instant"}, "FinishedAt": {"$ref": "#/components/schemas/Instant"}, "CancelledAt": {"$ref": "#/components/schemas/Instant"}, "FaultedAt": {"$ref": "#/components/schemas/Instant"}, "Metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "WorkflowInstanceSummaryModelPagedList": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowInstanceSummaryModel"}, "nullable": true}, "Page": {"type": "integer", "format": "int32", "nullable": true}, "PageSize": {"type": "integer", "format": "int32", "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkflowOutputReference": {"type": "object", "properties": {"ProviderName": {"type": "string", "nullable": true}, "ActivityId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowPersistenceBehavior": {"enum": ["Suspended", "WorkflowBurst", "WorkflowPassCompleted", "ActivityExecuted"], "type": "string"}, "WorkflowProviderDescriptor": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkflowStatus": {"enum": ["Idle", "Running", "Finished", "Suspended", "Faulted", "Cancelled"], "type": "string"}, "WorkflowStorageDescriptor": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}