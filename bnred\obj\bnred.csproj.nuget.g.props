﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files\DevExpress 24.2\Components\Offline Packages;d:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files\DevExpress 24.2\Components\Offline Packages\" />
    <SourceRoot Include="d:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)walkingtec.mvvm.mvc\8.1.12\contentFiles\any\net8.0\fonts\Candara.ttf" Condition="Exists('$(NuGetPackageRoot)walkingtec.mvvm.mvc\8.1.12\contentFiles\any\net8.0\fonts\Candara.ttf')">
      <NuGetPackageId>WalkingTec.Mvvm.Mvc</NuGetPackageId>
      <NuGetPackageVersion>8.1.12</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>fonts\Candara.ttf</Link>
    </Content>
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.3\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.3\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.props')" />
    <Import Project="$(NuGetPackageRoot)elsa.designer.components.web\2.14.1\buildTransitive\Elsa.Designer.Components.Web.props" Condition="Exists('$(NuGetPackageRoot)elsa.designer.components.web\2.14.1\buildTransitive\Elsa.Designer.Components.Web.props')" />
    <Import Project="$(NuGetPackageRoot)bootstrapblazor\9.2.0\buildTransitive\BootstrapBlazor.props" Condition="Exists('$(NuGetPackageRoot)bootstrapblazor\9.2.0\buildTransitive\BootstrapBlazor.props')" />
    <Import Project="$(NuGetPackageRoot)bootstrapblazor.summernote\8.0.0\buildTransitive\BootstrapBlazor.SummerNote.props" Condition="Exists('$(NuGetPackageRoot)bootstrapblazor.summernote\8.0.0\buildTransitive\BootstrapBlazor.SummerNote.props')" />
    <Import Project="$(NuGetPackageRoot)bootstrapblazor.markdown\8.0.0\buildTransitive\BootstrapBlazor.Markdown.props" Condition="Exists('$(NuGetPackageRoot)bootstrapblazor.markdown\8.0.0\buildTransitive\BootstrapBlazor.Markdown.props')" />
    <Import Project="$(NuGetPackageRoot)bootstrapblazor.chart\8.1.5\buildTransitive\BootstrapBlazor.Chart.props" Condition="Exists('$(NuGetPackageRoot)bootstrapblazor.chart\8.1.5\buildTransitive\BootstrapBlazor.Chart.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\3.0.0</PkgMicrosoft_Extensions_ApiDescription_Server>
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.2</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgMicrosoft_AspNetCore_Components_WebAssembly_Server Condition=" '$(PkgMicrosoft_AspNetCore_Components_WebAssembly_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webassembly.server\8.0.1</PkgMicrosoft_AspNetCore_Components_WebAssembly_Server>
  </PropertyGroup>
</Project>