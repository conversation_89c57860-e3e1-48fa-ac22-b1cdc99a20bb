﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;


namespace bnred.ViewModel.Warehouse.WarehouseAreaVMs
{
    public partial class WarehouseAreaListVM : BasePagedListVM<WarehouseArea_View, WarehouseAreaSearcher>
    {

        protected override IEnumerable<IGridColumn<WarehouseArea_View>> InitGridHeader()
        {
            return new List<GridColumn<WarehouseArea_View>>{
                this.MakeGridHeader(x => x.ID),
                this.MakeGridHeader(x => x.Code),
                this.MakeGridHeader(x => x.Name),
                this.MakeGridHeader(x => x.Name_view),
                this.MakeGridHeader(x => x.Type),
                this.MakeGridHeader(x => x.Status),
                this.MakeGridHeader(x => x.Area),
                this.MakeGridHeader(x => x.Volume),
                this.MakeGridHeader(x => x.Max<PERSON>apacity),
                this.MakeGridHeader(x => x.CurrentCapacity),
                this.MakeGridHeader(x => x.Name_view2),
                this.MakeGridHeader(x => x.Description),
                this.MakeGridHeader(x => x.Remark),
                this.MakeGridHeaderAction(width: 200)
            };
        }

        public override IOrderedQueryable<WarehouseArea_View> GetSearchQuery()
        {
            var query = DC.Set<WarehouseArea>()
                .CheckContain(Searcher.ID, x=>x.ID)
                .CheckContain(Searcher.Code, x=>x.Code)
                .CheckContain(Searcher.Name, x=>x.Name)
                .CheckEqual(Searcher.WarehouseId, x=>x.WarehouseId)
                .CheckEqual(Searcher.Type, x=>x.Type)
                .CheckEqual(Searcher.Status, x=>x.Status)
                .CheckEqual(Searcher.Area, x=>x.Area)
                .CheckEqual(Searcher.Volume, x=>x.Volume)
                .CheckEqual(Searcher.MaxCapacity, x=>x.MaxCapacity)
                .CheckEqual(Searcher.CurrentCapacity, x=>x.CurrentCapacity)
                .CheckEqual(Searcher.ManagerId, x=>x.ManagerId)
                .CheckContain(Searcher.Description, x=>x.Description)
                .CheckContain(Searcher.Remark, x=>x.Remark)
                .Select(x => new WarehouseArea_View
                {
				    ID = x.ID,
                    Code = x.Code,
                    Name = x.Name,
                    Name_view = x.Warehouse.Name,
                    Type = x.Type,
                    Status = x.Status,
                    Area = x.Area,
                    Volume = x.Volume,
                    MaxCapacity = x.MaxCapacity,
                    CurrentCapacity = x.CurrentCapacity,
                    Name_view2 = x.Manager.Name,
                    Description = x.Description,
                    Remark = x.Remark,
                })
                .OrderBy(x => x.ID);
            return query;
        }

    }

    public class WarehouseArea_View : WarehouseArea{
        [Display(Name = "仓库名称")]
        public String Name_view { get; set; }
        [Display(Name = "_Admin.Name")]
        public String Name_view2 { get; set; }

    }
}
