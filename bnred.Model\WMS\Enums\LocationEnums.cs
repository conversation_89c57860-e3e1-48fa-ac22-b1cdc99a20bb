using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 库位类型
    /// </summary>
    public enum LocationType
    {
        /// <summary>
        /// 存储位
        /// </summary>
        [Display(Name = "存储位")]
        Storage = 0,

        /// <summary>
        /// 拣货位
        /// </summary>
        [Display(Name = "拣货位")]
        Picking = 1,

        /// <summary>
        /// 收货位
        /// </summary>
        [Display(Name = "收货位")]
        Receiving = 2,

        /// <summary>
        /// 发货位
        /// </summary>
        [Display(Name = "发货位")]
        Shipping = 3,

        /// <summary>
        /// 退货位
        /// </summary>
        [Display(Name = "退货位")]
        Return = 4,

        /// <summary>
        /// 待检位
        /// </summary>
        [Display(Name = "待检位")]
        QC = 5,

        /// <summary>
        /// 报废位
        /// </summary>
        [Display(Name = "报废位")]
        Scrap = 6,

        /// <summary>
        /// 其他
        /// </summary>
        [Display(Name = "其他")]
        Other = 99
    }

    /// <summary>
    /// 库位状态
    /// </summary>
    public enum LocationStatus
    {
        /// <summary>
        /// 空闲
        /// </summary>
        [Display(Name = "空闲")]
        Empty = 0,

        /// <summary>
        /// 占用
        /// </summary>
        [Display(Name = "占用")]
        Occupied = 1,

        /// <summary>
        /// 锁定
        /// </summary>
        [Display(Name = "锁定")]
        Locked = 2,

        /// <summary>
        /// 禁用
        /// </summary>
        [Display(Name = "禁用")]
        Disabled = 3
    }
}