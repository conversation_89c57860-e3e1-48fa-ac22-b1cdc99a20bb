using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 策略类型
    /// </summary>
    public enum StrategyType
    {
        [Display(Name = "上架策略")]
        PutAway = 0,

        [Display(Name = "拣货策略")]
        Picking = 1,

        [Display(Name = "补货策略")]
        Replenishment = 2,

        [Display(Name = "库位分配策略")]
        LocationAssignment = 3,

        [Display(Name = "库存调整策略")]
        StockAdjustment = 4,

        [Display(Name = "盘点策略")]
        StockTaking = 5,

        [Display(Name = "波次策略")]
        Wave = 6,

        [Display(Name = "其他策略")]
        Other = 99
    }
} 