using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 仓库类型
    /// </summary>
    public enum WarehouseType
    {
        /// <summary>
        /// 原材料仓库
        /// </summary>
        [Display(Name = "原材料仓库")]
        RawMaterial = 0,

        /// <summary>
        /// 成品仓库
        /// </summary>
        [Display(Name = "成品仓库")]
        FinishedProduct = 1,

        /// <summary>
        /// 半成品仓库
        /// </summary>
        [Display(Name = "半成品仓库")]
        SemiFinished = 2,

        /// <summary>
        /// 周转仓库
        /// </summary>
        [Display(Name = "周转仓库")]
        Transit = 3,

        /// <summary>
        /// 退货仓库
        /// </summary>
        [Display(Name = "退货仓库")]
        Return = 4,

        /// <summary>
        /// 不良品仓库
        /// </summary>
        [Display(Name = "不良品仓库")]
        Defective = 5,

        /// <summary>
        /// 虚拟仓库
        /// </summary>
        [Display(Name = "虚拟仓库")]
        Virtual = 6,

        /// <summary>
        /// 一般仓库
        /// </summary>
        [Display(Name = "一般仓库")]
        General = 7
    }

    /// <summary>
    /// 仓库状态枚举
    /// 用于标识仓库的运营状态
    /// </summary>
    public enum WarehouseStatus
    {
        /// <summary>
        /// 正常运营
        /// 仓库正常运作状态
        /// </summary>
        [Display(Name = "正常运营")]
        Operating = 0,

        /// <summary>
        /// 暂停使用
        /// 仓库暂时停止使用状态
        /// </summary>
        [Display(Name = "暂停使用")]
        Suspended = 1,

        /// <summary>
        /// 维护中
        /// 仓库正在进行维护
        /// </summary>
        [Display(Name = "维护中")]
        Maintenance = 2,

        /// <summary>
        /// 已关闭
        /// 仓库已停止运营
        /// </summary>
        [Display(Name = "已关闭")]
        Closed = 3
    }

    /// <summary>
    /// 库区类型
    /// </summary>
    public enum AreaType
    {
        /// <summary>
        /// 存储区
        /// </summary>
        [Display(Name = "存储区")]
        Storage = 0,

        /// <summary>
        /// 收货区
        /// </summary>
        [Display(Name = "收货区")]
        Receiving = 1,

        /// <summary>
        /// 发货区
        /// </summary>
        [Display(Name = "发货区")]
        Shipping = 2,

        /// <summary>
        /// 拣选区
        /// </summary>
        [Display(Name = "拣选区")]
        Picking = 3,

        /// <summary>
        /// 暂存区
        /// </summary>
        [Display(Name = "暂存区")]
        Staging = 4,

        /// <summary>
        /// 退货区
        /// </summary>
        [Display(Name = "退货区")]
        Return = 5,

        /// <summary>
        /// 质检区
        /// </summary>
        [Display(Name = "质检区")]
        QualityControl = 6,

        /// <summary>
        /// 包装区
        /// </summary>
        [Display(Name = "包装区")]
        Packaging = 7,

        /// <summary>
        /// 其他区域
        /// </summary>
        [Display(Name = "其他区域")]
        Other = 99
    }

    /// <summary>
    /// 库区状态
    /// </summary>
    public enum AreaStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 维护中
        /// </summary>
        [Display(Name = "维护中")]
        Maintenance = 1,

        /// <summary>
        /// 已满
        /// </summary>
        [Display(Name = "已满")]
        Full = 2,

        /// <summary>
        /// 已锁定
        /// </summary>
        [Display(Name = "已锁定")]
        Locked = 3,

        /// <summary>
        /// 已禁用
        /// </summary>
        [Display(Name = "已禁用")]
        Disabled = 4
    }
}
