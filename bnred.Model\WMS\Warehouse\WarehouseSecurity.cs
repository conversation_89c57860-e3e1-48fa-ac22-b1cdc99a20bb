using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseSecurities")]
    public class WarehouseSecurity : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Required]
        public bool HasFireSystem { get; set; }

        [Required]
        public bool HasSecuritySystem { get; set; }

        [Required]
        public bool HasMonitoringSystem { get; set; }

        [Required]
        public bool HasEmergencyPower { get; set; }

        public DateTime? LastFireDrillDate { get; set; }

        public DateTime? NextFireDrillDate { get; set; }

        [StringLength(1000)]
        public string EmergencyProcedure { get; set; }

        [StringLength(500)]
        public string EmergencyContact { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 