﻿@page "/Warehouse/WarehouseArea/Create"
@using bnred.ViewModel.Warehouse.WarehouseAreaVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.ID"  />
            <BootstrapInput @bind-Value="@Model.Entity.Code"  />
            <BootstrapInput @bind-Value="@Model.Entity.Name"  />
            <Select @bind-Value="@Model.Entity.WarehouseId" Items="@AllWarehouses" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.Type"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.Status"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInputNumber @bind-Value="@Model.Entity.Area"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Volume"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.MaxCapacity"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.CurrentCapacity"  />
            <Select @bind-Value="@Model.Entity.ManagerId" Items="@AllFrameworkUsers" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Description"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Secondary" Icon="fa fa-close" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private WarehouseAreaVM Model = new WarehouseAreaVM();
    private ValidateForm vform { get; set; }

    private List<SelectedItem> AllWarehouses = new List<SelectedItem>();

    private List<SelectedItem> AllFrameworkUsers = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllWarehouses = await WtmBlazor.Api.CallItemsApi("/api/WarehouseArea/GetWarehouses", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        AllFrameworkUsers = await WtmBlazor.Api.CallItemsApi("/api/WarehouseArea/GetFrameworkUsers", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        await base.OnInitializedAsync();
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/WarehouseArea/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
