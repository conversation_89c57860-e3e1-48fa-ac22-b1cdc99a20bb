/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function(){function initTabs(){for(var t=$G("tabHeads").children,e=0;e<t.length;e++)domUtils.on(t[e],"click",function(e){for(var i=e.target||e.srcElement,o=0;o<t.length;o++)if(t[o]==i){t[o].className="focus";var n=t[o].getAttribute("data-content-id");$G(n).style.display="block","imgManager"==n&&initImagePanel()}else t[o].className="",$G(t[o].getAttribute("data-content-id")).style.display="none"})}function initColorSelector(){var t=editor.queryCommandValue("background");if(t){var e=t["background-color"],i=t["background-repeat"]||"repeat",o=t["background-image"]||"",n=t["background-position"]||"center center",a=n.split(" "),r=parseInt(a[0])||0,s=parseInt(a[1])||0;"no-repeat"==i&&(r||s)&&(i="self"),o=o.match(/url[\s]*\(([^\)]*)\)/),o=o?o[1]:"",updateFormState("colored",e,o,i,r,s)}else updateFormState();var l=function(){updateFormState(),updateBackground()};domUtils.on($G("nocolorRadio"),"click",updateBackground),domUtils.on($G("coloredRadio"),"click",l),domUtils.on($G("url"),"keyup",function(){$G("url").value&&"none"==$G("alignment").style.display&&utils.each($G("repeatType").children,function(t){t.selected="repeat"==t.getAttribute("value")&&"selected"}),l()}),domUtils.on($G("repeatType"),"change",l),domUtils.on($G("x"),"keyup",updateBackground),domUtils.on($G("y"),"keyup",updateBackground),initColorPicker()}function initColorPicker(){var t=editor,e=$G("colorPicker"),i=new UE.ui.Popup({content:new UE.ui.ColorPicker({noColorText:t.getLang("clearColor"),editor:t,onpickcolor:function(t,e){updateFormState("colored",e),updateBackground(),UE.ui.Popup.postHide()},onpicknocolor:function(t,e){updateFormState("colored","transparent"),updateBackground(),UE.ui.Popup.postHide()}}),editor:t,onhide:function(){}});domUtils.on(e,"click",function(){i.showAnchor(this)}),domUtils.on(document,"mousedown",function(t){var e=t.target||t.srcElement;UE.ui.Popup.postHide(e)}),domUtils.on(window,"scroll",function(){UE.ui.Popup.postHide()})}function initImagePanel(){onlineImage=onlineImage||new OnlineImage("imageList")}function updateFormState(t,e,i,o,n,a){var r=$G("nocolorRadio"),s=$G("coloredRadio");if(t&&(r.checked="colored"!=t&&"checked",s.checked="colored"==t&&"checked"),e&&domUtils.setStyle($G("colorPicker"),"background-color",e),i&&/^\//.test(i)){var l=document.createElement("a");l.href=i,browser.ie&&(l.href=l.href),i=browser.ie?l.href:l.protocol+"//"+l.host+l.pathname+l.search+l.hash}(i||""===i)&&($G("url").value=i),o&&utils.each($G("repeatType").children,function(t){t.selected=o==t.getAttribute("value")&&"selected"}),(n||a)&&($G("x").value=parseInt(n)||0,$G("y").value=parseInt(a)||0),$G("alignment").style.display=s.checked&&$G("url").value?"":"none",$G("custom").style.display=s.checked&&$G("url").value&&"self"==$G("repeatType").value?"":"none"}function updateBackground(){if($G("coloredRadio").checked){var t=domUtils.getStyle($G("colorPicker"),"background-color"),e=$G("url").value,i=$G("repeatType").value,o={"background-repeat":"no-repeat","background-position":"center center"};t&&(o["background-color"]=t),e&&(o["background-image"]="url("+e+")"),"self"==i?o["background-position"]=$G("x").value+"px "+$G("y").value+"px":"repeat-x"!=i&&"repeat-y"!=i&&"repeat"!=i||(o["background-repeat"]=i),editor.execCommand("background",o)}else editor.execCommand("background",null)}function OnlineImage(t){this.container=utils.isString(t)?document.getElementById(t):t,this.init()}var onlineImage,backupStyle=editor.queryCommandValue("background");window.onload=function(){initTabs(),initColorSelector()},OnlineImage.prototype={init:function(){this.reset(),this.initEvents()},initContainer:function(){this.container.innerHTML="",this.list=document.createElement("ul"),this.clearFloat=document.createElement("li"),domUtils.addClass(this.list,"list"),domUtils.addClass(this.clearFloat,"clearFloat"),this.list.id="imageListUl",this.list.appendChild(this.clearFloat),this.container.appendChild(this.list)},initEvents:function(){var t=this;domUtils.on($G("imageList"),"scroll",function(e){var i=this;i.scrollHeight-(i.offsetHeight+i.scrollTop)<10&&t.getImageData()}),domUtils.on(this.container,"click",function(t){var e=t.target||t.srcElement,i=e.parentNode,o=$G("imageListUl").childNodes;if("li"==i.tagName.toLowerCase()){updateFormState("nocolor",null,"");for(var n,a=0;n=o[a++];)n!=i||domUtils.hasClass(n,"selected")?domUtils.removeClasses(n,"selected"):(domUtils.addClass(n,"selected"),updateFormState("colored",null,i.firstChild.getAttribute("_src"),"repeat"));updateBackground()}})},initData:function(){this.state=0,this.listSize=editor.getOpt("imageManagerListSize"),this.listIndex=0,this.listEnd=!1,this.getImageData()},reset:function(){this.initContainer(),this.initData()},getImageData:function(){var _this=this;if(!_this.listEnd&&!this.isLoadingData){this.isLoadingData=!0;var url=editor.getActionUrl(editor.getOpt("imageManagerActionName")),isJsonp=utils.isCrossDomainUrl(url);ajax.request(url,{timeout:1e5,dataType:isJsonp?"jsonp":"",data:utils.extend({start:this.listIndex,size:this.listSize},editor.queryCommandValue("serverparam")),method:"get",onsuccess:function(r){try{var json=isJsonp?r:eval("("+r.responseText+")");"SUCCESS"==json.state&&(_this.pushData(json.list),_this.listIndex=parseInt(json.start)+parseInt(json.list.length),_this.listIndex>=json.total&&(_this.listEnd=!0),_this.isLoadingData=!1)}catch(e){if(r.responseText.indexOf("ue_separate_ue")!=-1){var list=r.responseText.split(r.responseText);_this.pushData(list),_this.listIndex=parseInt(list.length),_this.listEnd=!0,_this.isLoadingData=!1}}},onerror:function(){_this.isLoadingData=!1}})}},pushData:function(t){var e,i,o,n,a=this,r=editor.getOpt("imageManagerUrlPrefix");for(e=0;e<t.length;e++)t[e]&&t[e].url&&(i=document.createElement("li"),o=document.createElement("img"),n=document.createElement("span"),domUtils.on(o,"load",function(t){return function(){a.scale(t,t.parentNode.offsetWidth,t.parentNode.offsetHeight)}}(o)),o.width=113,o.setAttribute("src",r+t[e].url+(t[e].url.indexOf("?")==-1?"?noCache=":"&noCache=")+(+new Date).toString(36)),o.setAttribute("_src",r+t[e].url),domUtils.addClass(n,"icon"),i.appendChild(o),i.appendChild(n),this.list.insertBefore(i,this.clearFloat))},scale:function(t,e,i,o){var n=t.width,a=t.height;"justify"==o?n>=a?(t.width=e,t.height=i*a/n,t.style.marginLeft="-"+parseInt((t.width-e)/2)+"px"):(t.width=e*n/a,t.height=i,t.style.marginTop="-"+parseInt((t.height-i)/2)+"px"):n>=a?(t.width=e*n/a,t.height=i,t.style.marginLeft="-"+parseInt((t.width-e)/2)+"px"):(t.width=e,t.height=i*a/n,t.style.marginTop="-"+parseInt((t.height-i)/2)+"px")},getInsertList:function(){var t,e=this.list.children,i=[],o=getAlign();for(t=0;t<e.length;t++)if(domUtils.hasClass(e[t],"selected")){var n=e[t].firstChild,a=n.getAttribute("_src");i.push({src:a,_src:a,floatStyle:o})}return i}},dialog.onok=function(){updateBackground(),editor.fireEvent("saveScene")},dialog.oncancel=function(){editor.execCommand("background",backupStyle)}}();