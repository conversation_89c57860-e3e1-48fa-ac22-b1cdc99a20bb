using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Inventory;
using bnred.Model.WMS.Warehouse;

namespace bnred.Model.Analytics
{
    /// <summary>
    /// 库区利用率分析结果
    /// </summary>
    [Table("WarehouseAreaUtilizations")]
    public class WarehouseAreaUtilization : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 库区ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AreaId { get; set; }

        /// <summary>
        /// 库区
        /// </summary>
        public virtual bnred.Model.WMS.Warehouse.WarehouseArea Area { get; set; }

        /// <summary>
        /// 库区编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AreaCode { get; set; }

        /// <summary>
        /// 库区名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AreaName { get; set; }

        /// <summary>
        /// 总库位数
        /// </summary>
        public int TotalLocations { get; set; }

        /// <summary>
        /// 已用库位数
        /// </summary>
        public int UsedLocations { get; set; }

        /// <summary>
        /// 总容量（立方米）
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalVolume { get; set; }

        /// <summary>
        /// 已用容量（立方米）
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal UsedVolume { get; set; }

        /// <summary>
        /// 容量使用率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal VolumeUtilizationRate { get; set; }

        /// <summary>
        /// 库位使用率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal LocationUtilizationRate { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }

    /// <summary>
    /// 库存周转分析结果
    /// </summary>
    [Table("InventoryTurnoverAnalyses")]
    public class InventoryTurnoverAnalysis : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        public virtual bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialName { get; set; }

        /// <summary>
        /// 期初库存
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal BeginningInventory { get; set; }

        /// <summary>
        /// 期末库存
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal EndingInventory { get; set; }

        /// <summary>
        /// 平均库存
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AverageInventory { get; set; }

        /// <summary>
        /// 出库数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal OutboundQuantity { get; set; }

        /// <summary>
        /// 周转率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TurnoverRate { get; set; }

        /// <summary>
        /// 周转天数
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TurnoverDays { get; set; }

        /// <summary>
        /// 统计开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 统计结束日期
        /// </summary>
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// 库存预警分析结果
    /// </summary>
    [Table("InventoryAlertAnalyses")]
    public class InventoryAlertAnalysis : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 库存ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InventoryId { get; set; }

        /// <summary>
        /// 库存
        /// </summary>
        public virtual bnred.Model.WMS.Inventory.Inventory Inventory { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        public virtual bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialName { get; set; }

        /// <summary>
        /// 当前数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentQuantity { get; set; }

        /// <summary>
        /// 预警等级
        /// </summary>
        public InventoryAlertLevel AlertLevel { get; set; }

        /// <summary>
        /// 预警原因
        /// </summary>
        [StringLength(500)]
        public string AlertReason { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }

    /// <summary>
    /// 库存预测分析结果
    /// </summary>
    [Table("InventoryForecastAnalyses")]
    public class InventoryForecastAnalysis : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        public virtual bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialName { get; set; }

        /// <summary>
        /// 当前数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentQuantity { get; set; }

        /// <summary>
        /// 预测天数
        /// </summary>
        public int ForecastDays { get; set; }

        /// <summary>
        /// 预测数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ForecastedQuantity { get; set; }

        /// <summary>
        /// 日均入库量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AvgDailyInbound { get; set; }

        /// <summary>
        /// 日均出库量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AvgDailyOutbound { get; set; }

        /// <summary>
        /// 最小库存
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal MinStock { get; set; }

        /// <summary>
        /// 最大库存
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal MaxStock { get; set; }

        /// <summary>
        /// 预测时间
        /// </summary>
        public DateTime ForecastTime { get; set; }

        /// <summary>
        /// 建议
        /// </summary>
        [StringLength(1000)]
        public string Suggestions { get; set; }
    }

    /// <summary>
    /// 库存预警等级
    /// </summary>
    public enum InventoryAlertLevel
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 警告
        /// </summary>
        [Display(Name = "警告")]
        Warning = 1,

        /// <summary>
        /// 库存不足
        /// </summary>
        [Display(Name = "库存不足")]
        Low = 2,

        /// <summary>
        /// 库存过高
        /// </summary>
        [Display(Name = "库存过高")]
        High = 3
    }
}
