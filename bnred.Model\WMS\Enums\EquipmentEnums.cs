using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 设备类型枚举
    /// 用于标识不同的仓储设备类型
    /// </summary>
    public enum EquipmentType
    {
        /// <summary>
        /// 叉车
        /// 用于搬运和堆垛的叉车设备
        /// </summary>
        [Display(Name = "叉车")]
        Forklift = 0,

        /// <summary>
        /// 手推车
        /// 用于人工推动搬运的手推车
        /// </summary>
        [Display(Name = "手推车")]
        Trolley = 1,

        /// <summary>
        /// 输送机
        /// 用于物料传送的输送设备
        /// </summary>
        [Display(Name = "输送机")]
        Conveyor = 2,

        /// <summary>
        /// 堆垛机
        /// 用于高位货架存取的堆垛设备
        /// </summary>
        [Display(Name = "堆垛机")]
        Stacker = 3,

        /// <summary>
        /// 升降机
        /// 用于垂直运输的升降设备
        /// </summary>
        [Display(Name = "升降机")]
        Lift = 4,

        /// <summary>
        /// 扫码器
        /// 用于条码扫描的设备
        /// </summary>
        [Display(Name = "扫码器")]
        Scanner = 5,

        /// <summary>
        /// 打印机
        /// 用于标签打印的设备
        /// </summary>
        [Display(Name = "打印机")]
        Printer = 6
    }

    /// <summary>
    /// 设备状态枚举
    /// 用于标识设备的运行状态
    /// </summary>
    public enum EquipmentStatus
    {
        /// <summary>
        /// 正常运行
        /// 设备正常工作状态
        /// </summary>
        [Display(Name = "正常运行")]
        Running = 0,

        /// <summary>
        /// 空闲待命
        /// 设备空闲等待任务
        /// </summary>
        [Display(Name = "空闲待命")]
        Idle = 1,

        /// <summary>
        /// 维护保养
        /// 设备正在进行维护
        /// </summary>
        [Display(Name = "维护保养")]
        Maintenance = 2,

        /// <summary>
        /// 故障停用
        /// 设备发生故障停止使用
        /// </summary>
        [Display(Name = "故障停用")]
        Malfunction = 3,

        /// <summary>
        /// 已报废
        /// 设备已达到使用年限报废
        /// </summary>
        [Display(Name = "已报废")]
        Scrapped = 4
    }

    /// <summary>
    /// 维护类型枚举
    /// 用于标识设备维护的类型
    /// </summary>
    public enum MaintenanceType
    {
        /// <summary>
        /// 日常保养
        /// 日常的设备保养维护
        /// </summary>
        [Display(Name = "日常保养")]
        Daily = 0,

        /// <summary>
        /// 定期维护
        /// 按计划进行的定期维护
        /// </summary>
        [Display(Name = "定期维护")]
        Periodic = 1,

        /// <summary>
        /// 故障维修
        /// 设备发生故障时的维修
        /// </summary>
        [Display(Name = "故障维修")]
        Repair = 2,

        /// <summary>
        /// 年度大修
        /// 年度进行的全面检修
        /// </summary>
        [Display(Name = "年度大修")]
        Annual = 3,

        /// <summary>
        /// 紧急维修
        /// 突发情况的紧急维修
        /// </summary>
        [Display(Name = "紧急维修")]
        Emergency = 4
    }
} 