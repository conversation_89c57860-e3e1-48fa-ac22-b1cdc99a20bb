using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 打印类型枚举
    /// 用于标识不同的打印类型
    /// </summary>
    public enum PrintType
    {
        /// <summary>
        /// 入库标签
        /// 入库物料的标签打印
        /// </summary>
        [Display(Name = "入库标签")]
        InboundLabel = 0,

        /// <summary>
        /// 出库标签
        /// 出库物料的标签打印
        /// </summary>
        [Display(Name = "出库标签")]
        OutboundLabel = 1,

        /// <summary>
        /// 库位标签
        /// 库位信息的标签打印
        /// </summary>
        [Display(Name = "库位标签")]
        LocationLabel = 2,

        /// <summary>
        /// 托盘标签
        /// 托盘信息的标签打印
        /// </summary>
        [Display(Name = "托盘标签")]
        PalletLabel = 3,

        /// <summary>
        /// 装箱单
        /// 装箱明细的单据打印
        /// </summary>
        [Display(Name = "装箱单")]
        PackingList = 4,

        /// <summary>
        /// 拣货单
        /// 拣货任务的单据打印
        /// </summary>
        [Display(Name = "拣货单")]
        PickingList = 5,

        /// <summary>
        /// 盘点单
        /// 盘点任务的单据打印
        /// </summary>
        [Display(Name = "盘点单")]
        StockTakingList = 6
    }

    /// <summary>
    /// 打印模式枚举
    /// 用于标识打印的执行模式
    /// </summary>
    public enum PrintMode
    {
        /// <summary>
        /// 直接打印
        /// 不预览直接发送到打印机
        /// </summary>
        [Display(Name = "直接打印")]
        Direct = 0,

        /// <summary>
        /// 预览打印
        /// 打印前预览打印内容
        /// </summary>
        [Display(Name = "预览打印")]
        Preview = 1,

        /// <summary>
        /// 批量打印
        /// 多份内容一起打印
        /// </summary>
        [Display(Name = "批量打印")]
        Batch = 2,

        /// <summary>
        /// 远程打印
        /// 发送到远程打印机打印
        /// </summary>
        [Display(Name = "远程打印")]
        Remote = 3
    }

    /// <summary>
    /// 打印状态枚举
    /// 用于标识打印任务的状态
    /// </summary>
    public enum PrintStatus
    {
        /// <summary>
        /// 等待打印
        /// 打印任务等待执行
        /// </summary>
        [Display(Name = "等待打印")]
        Waiting = 0,

        /// <summary>
        /// 正在打印
        /// 打印任务正在执行
        /// </summary>
        [Display(Name = "正在打印")]
        Printing = 1,

        /// <summary>
        /// 打印完成
        /// 打印任务已完成
        /// </summary>
        [Display(Name = "打印完成")]
        Completed = 2,

        /// <summary>
        /// 打印失败
        /// 打印任务执行失败
        /// </summary>
        [Display(Name = "打印失败")]
        Failed = 3,

        /// <summary>
        /// 已取消
        /// 打印任务已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 4
    }
} 