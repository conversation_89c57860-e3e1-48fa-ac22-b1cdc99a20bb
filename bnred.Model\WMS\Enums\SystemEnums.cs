using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 系统参数类型枚举
    /// 用于标识不同的系统参数类型
    /// </summary>
    public enum SystemParameterType
    {
        /// <summary>
        /// 基础参数
        /// 系统基础运行参数
        /// </summary>
        [Display(Name = "基础参数")]
        Basic = 0,

        /// <summary>
        /// 业务参数
        /// 业务规则相关参数
        /// </summary>
        [Display(Name = "业务参数")]
        Business = 1,

        /// <summary>
        /// 接口参数
        /// 外部接口相关参数
        /// </summary>
        [Display(Name = "接口参数")]
        Interface = 2,

        /// <summary>
        /// 打印参数
        /// 打印模板相关参数
        /// </summary>
        [Display(Name = "打印参数")]
        Print = 3,

        /// <summary>
        /// 消息参数
        /// 消息通知相关参数
        /// </summary>
        [Display(Name = "消息参数")]
        Message = 4,

        /// <summary>
        /// 报表参数
        /// 报表配置相关参数
        /// </summary>
        [Display(Name = "报表参数")]
        Report = 5
    }

    /// <summary>
    /// 系统日志类型枚举
    /// 用于标识不同的系统日志类型
    /// </summary>
    public enum SystemLogType
    {
        /// <summary>
        /// 操作日志
        /// 记录用户操作行为
        /// </summary>
        [Display(Name = "操作日志")]
        Operation = 0,

        /// <summary>
        /// 登录日志
        /// 记录用户登录信息
        /// </summary>
        [Display(Name = "登录日志")]
        Login = 1,

        /// <summary>
        /// 异常日志
        /// 记录系统异常信息
        /// </summary>
        [Display(Name = "异常日志")]
        Exception = 2,

        /// <summary>
        /// 安全日志
        /// 记录安全相关信息
        /// </summary>
        [Display(Name = "安全日志")]
        Security = 3,

        /// <summary>
        /// 性能日志
        /// 记录性能监控信息
        /// </summary>
        [Display(Name = "性能日志")]
        Performance = 4
    }
} 