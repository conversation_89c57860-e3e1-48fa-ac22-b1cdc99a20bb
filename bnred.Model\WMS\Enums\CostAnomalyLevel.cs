using System.ComponentModel.DataAnnotations;

namespace bnred.Model
{
    /// <summary>
    /// 成本异常等级
    /// </summary>
    public enum CostAnomalyLevel
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 警告
        /// </summary>
        [Display(Name = "警告")]
        Warning = 1,

        /// <summary>
        /// 严重
        /// </summary>
        [Display(Name = "严重")]
        Critical = 2
    }
}