using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Batch;

namespace bnred.Model.WMS.Analysis
{
    /// <summary>
    /// 成本分析数据
    /// </summary>
    [Table("WMS_CostAnalysisData")]
    public class CostAnalysisData : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 物料ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "物料ID")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次ID")]
        [StringLength(50)]
        public string BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "库位ID")]
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public Warehouse.Location Location { get; set; }

        /// <summary>
        /// 分析日期
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "分析日期")]
        public DateTime AnalysisDate { get; set; }

        /// <summary>
        /// 异常等级
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "异常等级")]
        public CostAnomalyLevel AnomalyLevel { get; set; }

        /// <summary>
        /// 当前成本
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "当前成本")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal CurrentCost { get; set; }

        /// <summary>
        /// 标准成本
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "标准成本")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal StandardCost { get; set; }

        /// <summary>
        /// 成本差异
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "成本差异")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal CostDifference { get; set; }

        /// <summary>
        /// 差异率
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "差异率")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal DifferenceRate { get; set; }

        /// <summary>
        /// 分析结果
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "分析结果")]
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        public string AnalysisResult { get; set; }

        /// <summary>
        /// 建议措施
        /// </summary>
        [Display(Name = "建议措施")]
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        public string Suggestions { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        public string Remark { get; set; }
    }
}