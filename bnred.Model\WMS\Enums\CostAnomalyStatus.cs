using System.ComponentModel.DataAnnotations;

namespace bnred.Model
{
    /// <summary>
    /// 成本异常处理状态
    /// </summary>
    public enum CostAnomalyStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        [Display(Name = "待处理")]
        Pending = 1,

        /// <summary>
        /// 处理中
        /// </summary>
        [Display(Name = "处理中")]
        Processing = 2,

        /// <summary>
        /// 已处理
        /// </summary>
        [Display(Name = "已处理")]
        Processed = 3,

        /// <summary>
        /// 已忽略
        /// </summary>
        [Display(Name = "已忽略")]
        Ignored = 4,

        /// <summary>
        /// 已关闭
        /// </summary>
        [Display(Name = "已关闭")]
        Closed = 5
    }
} 