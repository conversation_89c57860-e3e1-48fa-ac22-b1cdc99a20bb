﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Codegen.ApiOnly" xml:space="preserve">
    <value>Generate api only, no frontend code</value>
  </data>
  <data name="Codegen.Attachment" xml:space="preserve">
    <value>Attachment</value>
  </data>
  <data name="Codegen.AuthMode" xml:space="preserve">
    <value>AuthMode</value>
  </data>
  <data name="Codegen.Confirm" xml:space="preserve">
    <value>This will generate following files, are you sure?</value>
  </data>
  <data name="Codegen.ControllerNs" xml:space="preserve">
    <value>ControllerNs</value>
  </data>
  <data name="Codegen.DataNs" xml:space="preserve">
    <value>DataNs</value>
  </data>
  <data name="Codegen.EnglishOnly" xml:space="preserve">
    <value>The {0} field must start with characters or underscores</value>
  </data>
  <data name="Codegen.FieldDes" xml:space="preserve">
    <value>Field des</value>
  </data>
  <data name="Codegen.FileName" xml:space="preserve">
    <value>File name</value>
  </data>
  <data name="Codegen.Gen" xml:space="preserve">
    <value>Generate code</value>
  </data>
  <data name="Codegen.GenApi" xml:space="preserve">
    <value>Generate Api</value>
  </data>
  <data name="Codegen.InputModuleName" xml:space="preserve">
    <value>Please input module name, such as xxx management</value>
  </data>
  <data name="Codegen.IsBatchField" xml:space="preserve">
    <value>Batch field</value>
  </data>
  <data name="Codegen.IsFormField" xml:space="preserve">
    <value>Form field</value>
  </data>
  <data name="Codegen.IsImportField" xml:space="preserve">
    <value>Import field</value>
  </data>
  <data name="Codegen.IsListField" xml:space="preserve">
    <value>List field</value>
  </data>
  <data name="Codegen.IsSearcherField" xml:space="preserve">
    <value>Search field</value>
  </data>
  <data name="Codegen.LinkedType" xml:space="preserve">
    <value>Link type</value>
  </data>
  <data name="Codegen.ManyToMany" xml:space="preserve">
    <value>ManyToMany</value>
  </data>
  <data name="Codegen.ModelNS" xml:space="preserve">
    <value>ModelNS</value>
  </data>
  <data name="Codegen.ModuleName" xml:space="preserve">
    <value>Module name</value>
  </data>
  <data name="Codegen.OneToMany" xml:space="preserve">
    <value>OneToMany</value>
  </data>
  <data name="Codegen.SelectedModelMustBeBasePoco" xml:space="preserve">
    <value>The model selected must inherit from TopBasePoco</value>
  </data>
  <data name="Codegen.SelectModule" xml:space="preserve">
    <value>Please select a module</value>
  </data>
  <data name="Codegen.Setup" xml:space="preserve">
    <value>Please setup fields</value>
  </data>
  <data name="Codegen.Start" xml:space="preserve">
    <value>Start generation</value>
  </data>
  <data name="Codegen.SubField" xml:space="preserve">
    <value>Display field of linked poco</value>
  </data>
  <data name="Codegen.Success" xml:space="preserve">
    <value>Code has been generated, please close the debug and rebuild the solution.</value>
  </data>
  <data name="Codegen.TestNs" xml:space="preserve">
    <value>TestNs</value>
  </data>
  <data name="Codegen.VMNs" xml:space="preserve">
    <value>VMNs</value>
  </data>
  <data name="Login.ChangePassword" xml:space="preserve">
    <value>Change password</value>
  </data>
  <data name="Login.ChangePasswordSuccess" xml:space="preserve">
    <value>The password has been modified successfully. Please log in with the new password next time.</value>
  </data>
  <data name="Login.InputPassword" xml:space="preserve">
    <value>Please input password</value>
  </data>
  <data name="Login.InputTenant" xml:space="preserve">
    <value>Please enter the tenant number</value>
  </data>
  <data name="Login.InputUserName" xml:space="preserve">
    <value>Please input user name</value>
  </data>
  <data name="Login.InputValidation" xml:space="preserve">
    <value>Validation code</value>
  </data>
  <data name="Login.ItcodeDuplicate" xml:space="preserve">
    <value>Account is duplicated</value>
  </data>
  <data name="Login.Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Login.LogOut" xml:space="preserve">
    <value>LogOut</value>
  </data>
  <data name="Login.NewPassword" xml:space="preserve">
    <value>New passwrod</value>
  </data>
  <data name="Login.NewPasswordComfirm" xml:space="preserve">
    <value>Passwrod again</value>
  </data>
  <data name="Login.OldPassword" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="Login.OldPasswrodWrong" xml:space="preserve">
    <value>Current password is wrong</value>
  </data>
  <data name="Login.PasswordNotSame" xml:space="preserve">
    <value>Inconsistent password</value>
  </data>
  <data name="Login.Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Login.RegTitle" xml:space="preserve">
    <value>New user registration</value>
  </data>
  <data name="Login.RememberMe" xml:space="preserve">
    <value>Remember me</value>
  </data>
  <data name="Login.ValidationFail" xml:space="preserve">
    <value>Incorrect verification code</value>
  </data>
  <data name="MenuKey.ActionLog" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="MenuKey.Api" xml:space="preserve">
    <value>Buildin Api</value>
  </data>
  <data name="MenuKey.DataPrivilege" xml:space="preserve">
    <value>Data Privilege</value>
  </data>
  <data name="MenuKey.FrameworkTenant" xml:space="preserve">
    <value>Tenant management</value>
  </data>
  <data name="MenuKey.GroupManagement" xml:space="preserve">
    <value>Dept Management</value>
  </data>
  <data name="MenuKey.Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="MenuKey.MenuMangement" xml:space="preserve">
    <value>Menu Mangement</value>
  </data>
  <data name="MenuKey.RoleManagement" xml:space="preserve">
    <value>Role Management</value>
  </data>
  <data name="MenuKey.SystemManagement" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="MenuKey.UserManagement" xml:space="preserve">
    <value>User Management</value>
  </data>
  <data name="Reg.Success" xml:space="preserve">
    <value>Registered successfully</value>
  </data>
  <data name="Sys.Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="Sys.Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="Sys.All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Sys.ApiDoc" xml:space="preserve">
    <value>Api Doc</value>
  </data>
  <data name="Sys.AttemptedValueIsInvalidAccessor" xml:space="preserve">
    <value>The value '{0}' is not valid for {1}.</value>
  </data>
  <data name="Sys.BackHome" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Sys.BatchDelete" xml:space="preserve">
    <value>Batch delete</value>
  </data>
  <data name="Sys.BatchDeleteConfirm" xml:space="preserve">
    <value>Are you sure to delete following data:</value>
  </data>
  <data name="Sys.BatchDeleteSuccess" xml:space="preserve">
    <value>{0} rows have been deleted</value>
  </data>
  <data name="Sys.BatchEdit" xml:space="preserve">
    <value>Batch edit</value>
  </data>
  <data name="Sys.BatchEditConfirm" xml:space="preserve">
    <value>Batch update following data</value>
  </data>
  <data name="Sys.BatchEditSuccess" xml:space="preserve">
    <value>{0} rows have been edited</value>
  </data>
  <data name="Sys.CannotDelete" xml:space="preserve">
    <value>{0} is used, it cannot be deleted</value>
  </data>
  <data name="Sys.CannotFindUser" xml:space="preserve">
    <value>Can not find user {0}</value>
  </data>
  <data name="Sys.CellIndex" xml:space="preserve">
    <value>CellIndex</value>
  </data>
  <data name="Sys.CheckExport" xml:space="preserve">
    <value>Check export</value>
  </data>
  <data name="Sys.Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Sys.CloseAllTags" xml:space="preserve">
    <value>Close all tags</value>
  </data>
  <data name="Sys.CloseOtherTags" xml:space="preserve">
    <value>Close other tags</value>
  </data>
  <data name="Sys.CloseThisTag" xml:space="preserve">
    <value>Close this tag</value>
  </data>
  <data name="Sys.CodeGen" xml:space="preserve">
    <value>CodeGen</value>
  </data>
  <data name="Sys.ColumnFilter" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="Sys.ComboBox" xml:space="preserve">
    <value>ComboBox</value>
  </data>
  <data name="Sys.Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="Sys.Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Sys.DataCannotDelete" xml:space="preserve">
    <value>Data is used, it cannot be deleted</value>
  </data>
  <data name="Sys.DataNotExist" xml:space="preserve">
    <value>Data does not exist</value>
  </data>
  <data name="Sys.DataRange" xml:space="preserve">
    <value>between {0} and {1}</value>
  </data>
  <data name="Sys.DebugOnly" xml:space="preserve">
    <value>The address can only be accessed in debug mode</value>
  </data>
  <data name="Sys.DefaultArea" xml:space="preserve">
    <value>Default area</value>
  </data>
  <data name="Sys.Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Sys.DeleteConfirm" xml:space="preserve">
    <value>Are you sure to delete this data</value>
  </data>
  <data name="Sys.DeleteFailed" xml:space="preserve">
    <value>Data is in use, delete failed.</value>
  </data>
  <data name="Sys.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Sys.Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="Sys.DownloadTemplate" xml:space="preserve">
    <value>Download template</value>
  </data>
  <data name="Sys.DuplicateError" xml:space="preserve">
    <value>The {0} field has duplicated data</value>
  </data>
  <data name="Sys.DuplicateGroupError" xml:space="preserve">
    <value>The {0} fields has duplicated data</value>
  </data>
  <data name="Sys.Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Sys.EditFailed" xml:space="preserve">
    <value>Edit failed</value>
  </data>
  <data name="Sys.Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Sys.Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Sys.ErrorHandle" xml:space="preserve">
    <value>Error handling</value>
  </data>
  <data name="Sys.ErrorMsg" xml:space="preserve">
    <value>Error message</value>
  </data>
  <data name="Sys.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Sys.ExportByIds" xml:space="preserve">
    <value>CheckExport</value>
  </data>
  <data name="Sys.ExportExcel" xml:space="preserve">
    <value>Export Excel</value>
  </data>
  <data name="Sys.FailedLoadData" xml:space="preserve">
    <value>Failed to load data</value>
  </data>
  <data name="Sys.Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="Sys.FileNotFound" xml:space="preserve">
    <value>File is not found</value>
  </data>
  <data name="Sys.ForSelect" xml:space="preserve">
    <value>For selection</value>
  </data>
  <data name="Sys.Get" xml:space="preserve">
    <value>Get</value>
  </data>
  <data name="Sys.Goto" xml:space="preserve">
    <value>Goto</value>
  </data>
  <data name="Sys.GotoButtonText" xml:space="preserve">
    <value>GO</value>
  </data>
  <data name="Sys.Have" xml:space="preserve">
    <value>Have</value>
  </data>
  <data name="Sys.HideShow" xml:space="preserve">
    <value>Hide/Show</value>
  </data>
  <data name="Sys.Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Sys.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="Sys.ImportError" xml:space="preserve">
    <value>Error occurs during import</value>
  </data>
  <data name="Sys.ImportStep1" xml:space="preserve">
    <value>Click to download template</value>
  </data>
  <data name="Sys.ImportStep2" xml:space="preserve">
    <value>Upload your template with data</value>
  </data>
  <data name="Sys.ImportStep3" xml:space="preserve">
    <value>Import failed, please download error file for details</value>
  </data>
  <data name="Sys.ImportSuccess" xml:space="preserve">
    <value>{0} rows have been imported</value>
  </data>
  <data name="Sys.Info" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="Sys.Invalid" xml:space="preserve">
    <value>Invalid</value>
  </data>
  <data name="Sys.Layout" xml:space="preserve">
    <value>Layout</value>
  </data>
  <data name="Sys.LayuiDateLan" xml:space="preserve">
    <value>EN</value>
  </data>
  <data name="Sys.LeftRight" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="Sys.LoadFailed" xml:space="preserve">
    <value>Failed to load</value>
  </data>
  <data name="Sys.LoginFailed" xml:space="preserve">
    <value>Login failed</value>
  </data>
  <data name="Sys.MainPage" xml:space="preserve">
    <value>MainPage</value>
  </data>
  <data name="Sys.Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="Sys.MoreSettings" xml:space="preserve">
    <value>More</value>
  </data>
  <data name="Sys.NeedLogin" xml:space="preserve">
    <value>You havn't logged in or your login is expired, please login again.</value>
  </data>
  <data name="Sys.No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Sys.NoData" xml:space="preserve">
    <value>No data</value>
  </data>
  <data name="Sys.NoMatchingData" xml:space="preserve">
    <value>No matching data</value>
  </data>
  <data name="Sys.None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Sys.NoPrivilege" xml:space="preserve">
    <value>You are not allowed to access this page</value>
  </data>
  <data name="Sys.NotHave" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Sys.OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Sys.Operation" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="Sys.OprationSuccess" xml:space="preserve">
    <value>Opration success</value>
  </data>
  <data name="Sys.Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="Sys.PageError" xml:space="preserve">
    <value>Error occurred</value>
  </data>
  <data name="Sys.PleaseInputDecimal" xml:space="preserve">
    <value>Please input decimal</value>
  </data>
  <data name="Sys.PleaseInputDecimalFormat" xml:space="preserve">
    <value>Please input with decimal format</value>
  </data>
  <data name="Sys.PleaseInputExistData" xml:space="preserve">
    <value>Please use data existed in the combobox</value>
  </data>
  <data name="Sys.PleaseInputNumber" xml:space="preserve">
    <value>Please input number</value>
  </data>
  <data name="Sys.PleaseInputNumberFormat" xml:space="preserve">
    <value>Please input with number format</value>
  </data>
  <data name="Sys.PleaseInputText" xml:space="preserve">
    <value>Please input text</value>
  </data>
  <data name="Sys.PleaseSelect" xml:space="preserve">
    <value>Please select</value>
  </data>
  <data name="Sys.PleaseUploadTemplate" xml:space="preserve">
    <value>Please upload template</value>
  </data>
  <data name="Sys.Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="Sys.Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Sys.Record" xml:space="preserve">
    <value>Record(s)</value>
  </data>
  <data name="Sys.RecordsPerPage" xml:space="preserve">
    <value />
  </data>
  <data name="Sys.Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Sys.Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Sys.Rollback" xml:space="preserve">
    <value>Rollback</value>
  </data>
  <data name="Sys.RowIndex" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="Sys.Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Sys.SearchCondition" xml:space="preserve">
    <value>Conditions</value>
  </data>
  <data name="Sys.Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Sys.Selected" xml:space="preserve">
    <value>Selected</value>
  </data>
  <data name="Sys.SelectOneRow" xml:space="preserve">
    <value>Please select a row</value>
  </data>
  <data name="Sys.SelectOneRowMax" xml:space="preserve">
    <value>Please select only one row</value>
  </data>
  <data name="Sys.SelectOneRowMin" xml:space="preserve">
    <value>Please select at least one row</value>
  </data>
  <data name="Sys.SinglePage" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="Sys.Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Sys.SubmitFailed" xml:space="preserve">
    <value>Failed to submit</value>
  </data>
  <data name="Sys.Tabs" xml:space="preserve">
    <value>Tabs</value>
  </data>
  <data name="Sys.Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="Sys.Total" xml:space="preserve">
    <value>Total:</value>
  </data>
  <data name="Sys.UpdateDone" xml:space="preserve">
    <value>Update successfully</value>
  </data>
  <data name="Sys.UpDown" xml:space="preserve">
    <value>Vertical</value>
  </data>
  <data name="Sys.UploadFailed" xml:space="preserve">
    <value>Upload failed</value>
  </data>
  <data name="Sys.UploadTemplate" xml:space="preserve">
    <value>Template</value>
  </data>
  <data name="Sys.Valid" xml:space="preserve">
    <value>Valid</value>
  </data>
  <data name="Sys.ValueIsInvalidAccessor" xml:space="preserve">
    <value>The value '{0}' is invalid.</value>
  </data>
  <data name="Sys.WrongTemplate" xml:space="preserve">
    <value>Wrong template</value>
  </data>
  <data name="Sys.WrongTextLength" xml:space="preserve">
    <value>The text is too long</value>
  </data>
  <data name="Sys.WtmDoc" xml:space="preserve">
    <value>WTM Doc</value>
  </data>
  <data name="Sys.Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Sys.{0}formaterror" xml:space="preserve">
    <value>The {0} field has an error format</value>
  </data>
  <data name="Sys.{0}ValueNotExist" xml:space="preserve">
    <value>The {0} field's value does not exist</value>
  </data>
  <data name="Sys.{0}ValueTypeNotAllowed" xml:space="preserve">
    <value>The {0} field's type is not supported</value>
  </data>
  <data name="Validate.{0}formaterror" xml:space="preserve">
    <value>The {0} field has an error format</value>
  </data>
  <data name="Validate.{0}number" xml:space="preserve">
    <value>The {0} field must be digit</value>
  </data>
  <data name="Validate.{0}rangemax{1}" xml:space="preserve">
    <value>{0} must be less than {1}</value>
  </data>
  <data name="Validate.{0}rangemin{1}" xml:space="preserve">
    <value>{0} must be at least {1}</value>
  </data>
  <data name="Validate.{0}range{1}{2}" xml:space="preserve">
    <value>{0} must between {1} and {2}</value>
  </data>
  <data name="Validate.{0}required" xml:space="preserve">
    <value>The {0} field is required</value>
  </data>
  <data name="Validate.{0}stringmax{1}" xml:space="preserve">
    <value>The {0} field is limited {1} characters</value>
  </data>
  <data name="_Admin.Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="_Admin.Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="_Admin.ActionLogApi" xml:space="preserve">
    <value>Log management Api</value>
  </data>
  <data name="_Admin.ActionName" xml:space="preserve">
    <value>Action name</value>
  </data>
  <data name="_Admin.ActionTime" xml:space="preserve">
    <value>Action time</value>
  </data>
  <data name="_Admin.AdditionInfo" xml:space="preserve">
    <value>Additional</value>
  </data>
  <data name="_Admin.Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="_Admin.AllDp" xml:space="preserve">
    <value>All privilege</value>
  </data>
  <data name="_Admin.Allowed" xml:space="preserve">
    <value>Allowed</value>
  </data>
  <data name="_Admin.AllowedDp" xml:space="preserve">
    <value>Allowed</value>
  </data>
  <data name="_Admin.AllowedRole" xml:space="preserve">
    <value>Allowed roles</value>
  </data>
  <data name="_Admin.AllPris" xml:space="preserve">
    <value>All privilege</value>
  </data>
  <data name="_Admin.BasicInfo" xml:space="preserve">
    <value>Basic</value>
  </data>
  <data name="_Admin.CellPhone" xml:space="preserve">
    <value>CellPhone</value>
  </data>
  <data name="_Admin.CheckPage" xml:space="preserve">
    <value>Check Page</value>
  </data>
  <data name="_Admin.Children" xml:space="preserve">
    <value>Children</value>
  </data>
  <data name="_Admin.ClassName" xml:space="preserve">
    <value>ClassName</value>
  </data>
  <data name="_Admin.CreateBy" xml:space="preserve">
    <value>CreateBy</value>
  </data>
  <data name="_Admin.CreateTime" xml:space="preserve">
    <value>CreateTime</value>
  </data>
  <data name="_Admin.DataPrivilege" xml:space="preserve">
    <value>Data Privilege</value>
  </data>
  <data name="_Admin.DataPrivilegeApi" xml:space="preserve">
    <value>Data privilege Api</value>
  </data>
  <data name="_Admin.DataPrivilegeCount" xml:space="preserve">
    <value>Dp count</value>
  </data>
  <data name="_Admin.DataPrivilegeName" xml:space="preserve">
    <value>Dp name</value>
  </data>
  <data name="_Admin.Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="_Admin.DisplayOrder" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="_Admin.Domain" xml:space="preserve">
    <value>Domain</value>
  </data>
  <data name="_Admin.DpTargetName" xml:space="preserve">
    <value>Target</value>
  </data>
  <data name="_Admin.DpType" xml:space="preserve">
    <value>Dp type</value>
  </data>
  <data name="_Admin.Duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="_Admin.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="_Admin.Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="_Admin.FieldName" xml:space="preserve">
    <value>Field name</value>
  </data>
  <data name="_Admin.FileApi" xml:space="preserve">
    <value>File Api</value>
  </data>
  <data name="_Admin.FileExt" xml:space="preserve">
    <value>File ext</value>
  </data>
  <data name="_Admin.FolderOnly" xml:space="preserve">
    <value>Folder</value>
  </data>
  <data name="_Admin.Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="_Admin.Group" xml:space="preserve">
    <value>Dept</value>
  </data>
  <data name="_Admin.GroupApi" xml:space="preserve">
    <value>Dept management Api</value>
  </data>
  <data name="_Admin.GroupCode" xml:space="preserve">
    <value>Dept code</value>
  </data>
  <data name="_Admin.GroupDp" xml:space="preserve">
    <value>Dept Dp</value>
  </data>
  <data name="_Admin.GroupManager" xml:space="preserve">
    <value>Dept Manager</value>
  </data>
  <data name="_Admin.GroupName" xml:space="preserve">
    <value>Dept name</value>
  </data>
  <data name="_Admin.HasMainHost" xml:space="preserve">
    <value>This operation cannot be performed. Please go to the main site for corresponding operation</value>
  </data>
  <data name="_Admin.HomePhone" xml:space="preserve">
    <value>HomePhone</value>
  </data>
  <data name="_Admin.Icon" xml:space="preserve">
    <value>Icon</value>
  </data>
  <data name="_Admin.IconFont" xml:space="preserve">
    <value>IconFont</value>
  </data>
  <data name="_Admin.Inside" xml:space="preserve">
    <value>Inside</value>
  </data>
  <data name="_Admin.IsInherit" xml:space="preserve">
    <value>Inherit</value>
  </data>
  <data name="_Admin.IsInside" xml:space="preserve">
    <value>Domain</value>
  </data>
  <data name="_Admin.IsPublic" xml:space="preserve">
    <value>Public</value>
  </data>
  <data name="_Admin.IsValid" xml:space="preserve">
    <value>IsValid</value>
  </data>
  <data name="_Admin.Job" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="_Admin.Length" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="_Admin.LoginApi" xml:space="preserve">
    <value>Login Api</value>
  </data>
  <data name="_Admin.LogType" xml:space="preserve">
    <value>Log type</value>
  </data>
  <data name="_Admin.MenuApi" xml:space="preserve">
    <value>Menu management Api</value>
  </data>
  <data name="_Admin.MenuItem" xml:space="preserve">
    <value>MenuItem</value>
  </data>
  <data name="_Admin.MethodName" xml:space="preserve">
    <value>Method name</value>
  </data>
  <data name="_Admin.Module" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="_Admin.ModuleHasSet" xml:space="preserve">
    <value>This module has already beed added</value>
  </data>
  <data name="_Admin.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="_Admin.NoIndexInModule" xml:space="preserve">
    <value>There is no View in this Module</value>
  </data>
  <data name="_Admin.NoPris" xml:space="preserve">
    <value>No privilege</value>
  </data>
  <data name="_Admin.Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="_Admin.Outside" xml:space="preserve">
    <value>Outside</value>
  </data>
  <data name="_Admin.PageFunction" xml:space="preserve">
    <value>Page privileges</value>
  </data>
  <data name="_Admin.PageName" xml:space="preserve">
    <value>Page name</value>
  </data>
  <data name="_Admin.Parent" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="_Admin.ParentFolder" xml:space="preserve">
    <value>Parent folder</value>
  </data>
  <data name="_Admin.Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="_Admin.Path" xml:space="preserve">
    <value>Path</value>
  </data>
  <data name="_Admin.Photo" xml:space="preserve">
    <value>Photo</value>
  </data>
  <data name="_Admin.Privileges" xml:space="preserve">
    <value>Privileges</value>
  </data>
  <data name="_Admin.RefreshMenu" xml:space="preserve">
    <value>Refresh menu</value>
  </data>
  <data name="_Admin.Remark" xml:space="preserve">
    <value>Remark</value>
  </data>
  <data name="_Admin.Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="_Admin.RoleApi" xml:space="preserve">
    <value>Role management Api</value>
  </data>
  <data name="_Admin.RoleCode" xml:space="preserve">
    <value>Role code</value>
  </data>
  <data name="_Admin.RoleName" xml:space="preserve">
    <value>Role name</value>
  </data>
  <data name="_Admin.SameRole" xml:space="preserve">
    <value>Except for administrators, users cannot modify the page permissions of their roles</value>
  </data>
  <data name="_Admin.SelectedModel" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="_Admin.SelectPris" xml:space="preserve">
    <value>Select privileges</value>
  </data>
  <data name="_Admin.ShowOnMenu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="_Admin.TableName" xml:space="preserve">
    <value>Privilege name</value>
  </data>
  <data name="_Admin.Tenant" xml:space="preserve">
    <value>Tenant</value>
  </data>
  <data name="_Admin.TenantAllowed" xml:space="preserve">
    <value>Tenant allowed</value>
  </data>
  <data name="_Admin.TenantChoose" xml:space="preserve">
    <value>Select tenant</value>
  </data>
  <data name="_Admin.TenantCode" xml:space="preserve">
    <value>Tenant code</value>
  </data>
  <data name="_Admin.TenantDb" xml:space="preserve">
    <value>Tenant database</value>
  </data>
  <data name="_Admin.TenantDbContext" xml:space="preserve">
    <value>Tenant datacontext</value>
  </data>
  <data name="_Admin.TenantDbError" xml:space="preserve">
    <value>Failed to create database</value>
  </data>
  <data name="_Admin.TenantDbType" xml:space="preserve">
    <value>Tenant dbtype</value>
  </data>
  <data name="_Admin.TenantDomain" xml:space="preserve">
    <value>Tenant host url</value>
  </data>
  <data name="_Admin.TenantEnableSub" xml:space="preserve">
    <value>Sub tenant allowed</value>
  </data>
  <data name="_Admin.TenantHost" xml:space="preserve">
    <value>Main Host</value>
  </data>
  <data name="_Admin.TenantName" xml:space="preserve">
    <value>Tenant name</value>
  </data>
  <data name="_Admin.TenantNotAllowed" xml:space="preserve">
    <value>The current tenant cannot use this feature</value>
  </data>
  <data name="_Admin.TenantRole" xml:space="preserve">
    <value>Use the permissions of the selected role to establish the administrator of the tenant, account admin and password 000000</value>
  </data>
  <data name="_Admin.UnsetPages" xml:space="preserve">
    <value>Unset pages</value>
  </data>
  <data name="_Admin.UpdateBy" xml:space="preserve">
    <value>UpdateBy</value>
  </data>
  <data name="_Admin.UpdateTime" xml:space="preserve">
    <value>Update time</value>
  </data>
  <data name="_Admin.User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="_Admin.UserApi" xml:space="preserve">
    <value>User management Api</value>
  </data>
  <data name="_Admin.UserDp" xml:space="preserve">
    <value>User Dp</value>
  </data>
  <data name="_Admin.UsersCount" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="_Admin.ZipCode" xml:space="preserve">
    <value>Zip</value>
  </data>
  <data name="MenuKey.Workflow" xml:space="preserve">
    <value>WorkFlow</value>
  </data>
  <data name="Sys.NoWorkflow" xml:space="preserve">
    <value>No related work flow is found</value>
  </data>
    <data name="_Workflow.Canceled" xml:space="preserve">
    <value>Canceled</value>
  </data>
  <data name="_Workflow.Finished" xml:space="preserve">
    <value>Finished</value>
  </data>
  <data name="_Workflow.NotStarted" xml:space="preserve">
    <value>Not Started</value>
  </data>
  <data name="_Workflow.Processing" xml:space="preserve">
    <value>In Process</value>
  </data>
     <data name="Sys.Aggree" xml:space="preserve">
    <value>Agree</value>
  </data>
    <data name="Sys.Approve" xml:space="preserve">
    <value>Approve</value>
  </data>
    <data name="Sys.Refuse" xml:space="preserve">
    <value>Refuse</value>
  </data>
    <data name="_Admin.WorkflowApi" xml:space="preserve">
    <value>Workflow Api</value>
  </data>
 </root>