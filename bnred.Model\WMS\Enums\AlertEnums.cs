using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 预警类型枚举
    /// 用于标识不同的预警类型
    /// </summary>
    public enum AlertType
    {
        /// <summary>
        /// 库存预警
        /// 库存达到预警阈值的提醒
        /// </summary>
        [Display(Name = "库存预警")]
        Inventory = 0,

        /// <summary>
        /// 效期预警
        /// 物料临近效期的提醒
        /// </summary>
        [Display(Name = "效期预警")]
        Expiry = 1,

        /// <summary>
        /// 设备预警
        /// 设备需要维护的提醒
        /// </summary>
        [Display(Name = "设备预警")]
        Equipment = 2,

        /// <summary>
        /// 容量预警
        /// 仓储容量不足的提醒
        /// </summary>
        [Display(Name = "容量预警")]
        Capacity = 3,

        /// <summary>
        /// 任务预警
        /// 任务执行异常的提醒
        /// </summary>
        [Display(Name = "任务预警")]
        Task = 4,

        /// <summary>
        /// 质量预警
        /// 质量问题的提醒
        /// </summary>
        [Display(Name = "质量预警")]
        Quality = 5
    }

    /// <summary>
    /// 预警级别枚举
    /// 用于标识预警的紧急程度
    /// </summary>
    public enum AlertLevel
    {
        /// <summary>
        /// 提示级别
        /// 一般性提醒信息
        /// </summary>
        [Display(Name = "提示级别")]
        Info = 0,

        /// <summary>
        /// 警告级别
        /// 需要关注的警告信息
        /// </summary>
        [Display(Name = "警告级别")]
        Warning = 1,

        /// <summary>
        /// 严重级别
        /// 需要及时处理的严重问题
        /// </summary>
        [Display(Name = "严重级别")]
        Critical = 2,

        /// <summary>
        /// 紧急级别
        /// 需要立即处理的紧急问题
        /// </summary>
        [Display(Name = "紧急级别")]
        Emergency = 3
    }
} 