﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;
 


namespace bnred.ViewModel.Warehouse.WarehouseVMs
{
    public partial class WarehouseTemplateVM : BaseTemplateVM
    {
        public ExcelPropety ID_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.ID);
        [Display(Name = "仓库编码")]
        public ExcelPropety Code_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Code);
        [Display(Name = "仓库名称")]
        public ExcelPropety Name_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Name);
        [Display(Name = "仓库类型")]
        public ExcelPropety Type_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Type);
        [Display(Name = "仓库状态")]
        public ExcelPropety Status_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Status);
        [Display(Name = "仓库地址")]
        public ExcelPropety Address_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Address);
        [Display(Name = "联系人")]
        public ExcelPropety ContactPerson_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.ContactPerson);
        [Display(Name = "联系电话")]
        public ExcelPropety ContactPhone_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.ContactPhone);
        [Display(Name = "面积(平方米)")]
        public ExcelPropety Area_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Area);
        [Display(Name = "容量(立方米)")]
        public ExcelPropety Volume_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Volume);
        public ExcelPropety Manager_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.ManagerID);
        [Display(Name = "描述")]
        public ExcelPropety Description_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Description);
        [Display(Name = "备注")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<bnred.Model.WMS.Warehouse.Warehouse>(x => x.Remark);

	    protected override void InitVM()
        {
            Manager_Excel.DataType = ColumnDataType.ComboBox;
            Manager_Excel.ListItems = DC.Set<FrameworkUser>().GetSelectListItems(Wtm, y => y.Name);
        }

    }

    public class WarehouseImportVM : BaseImportVM<WarehouseTemplateVM, bnred.Model.WMS.Warehouse.Warehouse>
    {

    }

}
