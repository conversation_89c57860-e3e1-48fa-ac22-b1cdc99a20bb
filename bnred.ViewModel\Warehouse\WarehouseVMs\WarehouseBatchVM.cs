﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS;


namespace bnred.ViewModel.Warehouse.WarehouseVMs
{
    public partial class WarehouseBatchVM : BaseBatchVM<bnred.Model.WMS.Warehouse.Warehouse, Warehouse_BatchEdit>
    {
        public WarehouseBatchVM()
        {
            ListVM = new WarehouseListVM();
            LinkedVM = new Warehouse_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class Warehouse_BatchEdit : BaseVM
    {
        [Display(Name = "仓库状态")]
        public WarehouseStatus? Status { get; set; }
        [Display(Name = "备注")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }

}
