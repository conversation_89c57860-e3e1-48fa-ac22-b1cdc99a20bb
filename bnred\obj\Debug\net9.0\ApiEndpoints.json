[{"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetMyApprove", "RelativePath": "_workflowapi/getmyapprove", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "entitytype", "Type": "System.String", "IsRequired": false}, {"Name": "entityid", "Type": "System.String", "IsRequired": false}, {"Name": "tag", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetTimeLine", "RelativePath": "_workflowapi/gettimeline", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "entitytype", "Type": "System.String", "IsRequired": false}, {"Name": "entityid", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetWorkflow", "RelativePath": "_workflowapi/getworkflow", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "entitytype", "Type": "System.String", "IsRequired": false}, {"Name": "entityid", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetWorkflowGroupManagers", "RelativePath": "_workflowapi/getworkflowgroupmanagers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetWorkflowGroups", "RelativePath": "_workflowapi/getworkflowgroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetWorkflowMyGroupManagers", "RelativePath": "_workflowapi/getworkflowmygroupmanagers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itcode", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetWorkflowRoles", "RelativePath": "_workflowapi/getworkflowroles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Mvc.WorkflowApiController", "Method": "GetWorkflowUsers", "RelativePath": "_workflowapi/getworkflowusers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itcode", "Type": "System.String[]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "ChangePassword", "RelativePath": "api/_account/changepassword", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.ChangePasswordVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "CheckUserInfo", "RelativePath": "api/_account/checkuserinfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IsApi", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "GetFrameworkGroups", "RelativePath": "api/_account/getframeworkgroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "GetFrameworkGroupsTree", "RelativePath": "api/_account/getframeworkgroupstree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "GetFrameworkRoles", "RelativePath": "api/_account/getframeworkroles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "GetUserByGroup", "RelativePath": "api/_account/getuserbygroup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keywords", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "GetUserById", "RelativePath": "api/_account/getuserbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keywords", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "GetUserByRole", "RelativePath": "api/_account/getuserbyrole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keywords", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "<PERSON><PERSON>", "RelativePath": "api/_account/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "account", "Type": "System.String", "IsRequired": false}, {"Name": "password", "Type": "System.String", "IsRequired": false}, {"Name": "tenant", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "LoginJwt", "RelativePath": "api/_account/loginjwt", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginInfo", "Type": "WalkingTec.Mvvm.Admin.Api.SimpleLogin", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "LoginRemote", "RelativePath": "api/_account/loginremote", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "_remotetoken", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "Logout", "RelativePath": "api/_account/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "RefreshToken", "RelativePath": "api/_account/refreshtoken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshToken", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Core.Support.Json.Token", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "Reg", "RelativePath": "api/_account/reg", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "regInfo", "Type": "WalkingTec.Mvvm.Admin.Api.SimpleReg", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.AccountController", "Method": "Set<PERSON>enant", "RelativePath": "api/_account/settenant", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.ActionLogController", "Method": "Get", "RelativePath": "api/_actionlog/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.ActionLogVMs.ActionLogVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.ActionLogController", "Method": "BatchDelete", "RelativePath": "api/_actionlog/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.ActionLogController", "Method": "ExportExcel", "RelativePath": "api/_actionlog/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.ActionLogVMs.ActionLogSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.ActionLogController", "Method": "ExportExcelByIds", "RelativePath": "api/_actionlog/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.ActionLogController", "Method": "Search", "RelativePath": "api/_actionlog/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.ActionLogVMs.ActionLogSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "Add", "RelativePath": "api/_dataprivilege/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.DataPrivilegeVMs.DataPrivilegeVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "Delete", "RelativePath": "api/_dataprivilege/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dp", "Type": "WalkingTec.Mvvm.Admin.Api.SimpleDpModel", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "Edit", "RelativePath": "api/_dataprivilege/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.DataPrivilegeVMs.DataPrivilegeVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "Get", "RelativePath": "api/_dataprivilege/get", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TableName", "Type": "System.String", "IsRequired": false}, {"Name": "TargetId", "Type": "System.String", "IsRequired": false}, {"Name": "DpType", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.DataPrivilegeVMs.DataPrivilegeVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "GetPrivilegeByTableName", "RelativePath": "api/_dataprivilege/getprivilegebytablename", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "table", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "GetPrivileges", "RelativePath": "api/_dataprivilege/getprivileges", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "GetUserGroups", "RelativePath": "api/_dataprivilege/getusergroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "GetUserGroupsTree", "RelativePath": "api/_dataprivilege/getusergroupstree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.DataPrivilegeController", "Method": "Search", "RelativePath": "api/_dataprivilege/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.DataPrivilegeVMs.DataPrivilegeSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "DeletedFile", "RelativePath": "api/_file/deletedfile/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "DownloadFile", "RelativePath": "api/_file/downloadfile/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "GetFile", "RelativePath": "api/_file/getfile/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "width", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "height", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "GetFileInfo", "RelativePath": "api/_file/getfileinfo/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "GetFileName", "RelativePath": "api/_file/getfilename/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "GetUserPhoto", "RelativePath": "api/_file/getuserphoto/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "width", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "height", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "Upload", "RelativePath": "api/_file/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sm", "Type": "System.String", "IsRequired": false}, {"Name": "groupName", "Type": "System.String", "IsRequired": false}, {"Name": "subdir", "Type": "System.String", "IsRequired": false}, {"Name": "extra", "Type": "System.String", "IsRequired": false}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FileApiController", "Method": "UploadImage", "RelativePath": "api/_file/uploadimage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "width", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "height", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sm", "Type": "System.String", "IsRequired": false}, {"Name": "groupName", "Type": "System.String", "IsRequired": false}, {"Name": "subdir", "Type": "System.String", "IsRequired": false}, {"Name": "extra", "Type": "System.String", "IsRequired": false}, {"Name": "csName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "Get", "RelativePath": "api/_frameworkgroup/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs.FrameworkGroupVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "Add", "RelativePath": "api/_frameworkgroup/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs.FrameworkGroupVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "BatchDelete", "RelativePath": "api/_frameworkgroup/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "Edit", "RelativePath": "api/_frameworkgroup/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs.FrameworkGroupVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "ExportExcel", "RelativePath": "api/_frameworkgroup/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs.FrameworkGroupSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "ExportExcelByIds", "RelativePath": "api/_frameworkgroup/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "GetExcelTemplate", "RelativePath": "api/_frameworkgroup/getexceltemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "GetParents", "RelativePath": "api/_frameworkgroup/getparents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "GetParentsTree", "RelativePath": "api/_frameworkgroup/getparentstree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "Import", "RelativePath": "api/_frameworkgroup/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs.FrameworkGroupImportVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkGroupController", "Method": "Search", "RelativePath": "api/_frameworkgroup/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkGroupVMs.FrameworkGroupSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "Get", "RelativePath": "api/_frameworkmenu/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkMenuVMs.FrameworkMenuVM2", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "Add", "RelativePath": "api/_frameworkmenu/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkMenuVMs.FrameworkMenuVM2", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "BatchDelete", "RelativePath": "api/_frameworkmenu/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "Edit", "RelativePath": "api/_frameworkmenu/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkMenuVMs.FrameworkMenuVM2", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "ExportExcel", "RelativePath": "api/_frameworkmenu/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Core.BaseSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "ExportExcelByIds", "RelativePath": "api/_frameworkmenu/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "GetActionsByModel", "RelativePath": "api/_frameworkmenu/getactionsbymodel", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ModelName", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "GetFolders", "RelativePath": "api/_frameworkmenu/getfolders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "GetIconItems", "RelativePath": "api/_frameworkmenu/geticonitems", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WalkingTec.Mvvm.Core.MenuItem, WalkingTec.Mvvm.Core, Version=8.1.12.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "GetIcons", "RelativePath": "api/_frameworkmenu/geticons", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WalkingTec.Mvvm.Core.ComboSelectListItem, WalkingTec.Mvvm.Core, Version=8.1.12.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "RefreshMenu", "RelativePath": "api/_frameworkmenu/refreshmenu", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "Search", "RelativePath": "api/_frameworkmenu/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Core.BaseSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkMenuController", "Method": "UnsetPages", "RelativePath": "api/_frameworkmenu/unsetpages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "Get", "RelativePath": "api/_frameworkrole/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "Add", "RelativePath": "api/_frameworkrole/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "BatchDelete", "RelativePath": "api/_frameworkrole/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "Edit", "RelativePath": "api/_frameworkrole/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "EditPrivilege", "RelativePath": "api/_frameworkrole/editprivilege", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleMDVM2", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "ExportExcel", "RelativePath": "api/_frameworkrole/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "ExportExcelByIds", "RelativePath": "api/_frameworkrole/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "GetExcelTemplate", "RelativePath": "api/_frameworkrole/getexceltemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "GetPageActions", "RelativePath": "api/_frameworkrole/getpageactions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleMDVM2", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "Import", "RelativePath": "api/_frameworkrole/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleImportVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkRoleController", "Method": "Search", "RelativePath": "api/_frameworkrole/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs.FrameworkRoleSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "Get", "RelativePath": "api/_frameworktenant/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs.FrameworkTenantVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "Add", "RelativePath": "api/_frameworktenant/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs.FrameworkTenantVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "BatchDelete", "RelativePath": "api/_frameworktenant/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "Edit", "RelativePath": "api/_frameworktenant/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs.FrameworkTenantVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "ExportExcel", "RelativePath": "api/_frameworktenant/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs.FrameworkTenantSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "ExportExcelByIds", "RelativePath": "api/_frameworktenant/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "GetExcelTemplate", "RelativePath": "api/_frameworktenant/getexceltemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "GetFrameworkTenants", "RelativePath": "api/_frameworktenant/getframeworktenants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parent", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "Import", "RelativePath": "api/_frameworktenant/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs.FrameworkTenantImportVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkTenantController", "Method": "Search", "RelativePath": "api/_frameworktenant/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkTenantVMs.FrameworkTenantSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "Get", "RelativePath": "api/_frameworkuser/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "Add", "RelativePath": "api/_frameworkuser/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "BatchDelete", "RelativePath": "api/_frameworkuser/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "Edit", "RelativePath": "api/_frameworkuser/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "ExportExcel", "RelativePath": "api/_frameworkuser/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "ExportExcelByIds", "RelativePath": "api/_frameworkuser/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetExcelTemplate", "RelativePath": "api/_frameworkuser/getexceltemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetFrameworkGroups", "RelativePath": "api/_frameworkuser/getframeworkgroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetFrameworkGroupsTree", "RelativePath": "api/_frameworkuser/getframeworkgroupstree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetFrameworkRoles", "RelativePath": "api/_frameworkuser/getframeworkroles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetUserByGroup", "RelativePath": "api/_frameworkuser/getuserbygroup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keywords", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetUserById", "RelativePath": "api/_frameworkuser/getuserbyid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keywords", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "GetUserByRole", "RelativePath": "api/_frameworkuser/getuserbyrole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keywords", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "Import", "RelativePath": "api/_frameworkuser/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserImportVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "Password", "RelativePath": "api/_frameworkuser/password", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WalkingTec.Mvvm.Admin.Api.FrameworkUserController", "Method": "Search", "RelativePath": "api/_frameworkuser/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms.FrameworkUserSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "Get", "RelativePath": "api/warehouse/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "bnred.ViewModel.Warehouse.WarehouseVMs.WarehouseVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "Add", "RelativePath": "api/warehouse/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "bnred.ViewModel.Warehouse.WarehouseVMs.WarehouseVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "BatchDelete", "RelativePath": "api/warehouse/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "Edit", "RelativePath": "api/warehouse/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "bnred.ViewModel.Warehouse.WarehouseVMs.WarehouseVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "ExportExcel", "RelativePath": "api/warehouse/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "bnred.ViewModel.Warehouse.WarehouseVMs.WarehouseSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "ExportExcelByIds", "RelativePath": "api/warehouse/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "GetExcelTemplate", "RelativePath": "api/warehouse/getexceltemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "GetFrameworkUsers", "RelativePath": "api/warehouse/getframeworkusers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "Import", "RelativePath": "api/warehouse/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "bnred.ViewModel.Warehouse.WarehouseVMs.WarehouseImportVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseController", "Method": "Search", "RelativePath": "api/warehouse/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "bnred.ViewModel.Warehouse.WarehouseVMs.WarehouseSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "Get", "RelativePath": "api/warehousearea/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "bnred.ViewModel.Warehouse.WarehouseAreaVMs.WarehouseAreaVM", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "Add", "RelativePath": "api/warehousearea/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "bnred.ViewModel.Warehouse.WarehouseAreaVMs.WarehouseAreaVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "BatchDelete", "RelativePath": "api/warehousearea/batchdelete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "Edit", "RelativePath": "api/warehousearea/edit", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "bnred.ViewModel.Warehouse.WarehouseAreaVMs.WarehouseAreaVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "ExportExcel", "RelativePath": "api/warehousearea/exportexcel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "bnred.ViewModel.Warehouse.WarehouseAreaVMs.WarehouseAreaSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "ExportExcelByIds", "RelativePath": "api/warehousearea/exportexcelbyids", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "GetExcelTemplate", "RelativePath": "api/warehousearea/getexceltemplate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "GetFrameworkUsers", "RelativePath": "api/warehousearea/getframeworkusers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "GetWarehouses", "RelativePath": "api/warehousearea/getwarehouses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "Import", "RelativePath": "api/warehousearea/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "vm", "Type": "bnred.ViewModel.Warehouse.WarehouseAreaVMs.WarehouseAreaImportVM", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "bnred.Controllers.WarehouseAreaController", "Method": "Search", "RelativePath": "api/warehousearea/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searcher", "Type": "bnred.ViewModel.Warehouse.WarehouseAreaVMs.WarehouseAreaSearcher", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Elsa.Activities.Http.Endpoints.Signals.DispatchEndpoint", "Method": "<PERSON><PERSON>", "RelativePath": "signals/dispatch/{token}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Elsa.Activities.Http.Endpoints.Signals.DispatchEndpoint", "Method": "<PERSON><PERSON>", "RelativePath": "signals/dispatch/{token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Elsa.Activities.Http.Endpoints.Signals.TriggerEndpoint", "Method": "<PERSON><PERSON>", "RelativePath": "signals/trigger/{token}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Elsa.Activities.Http.Endpoints.Signals.TriggerEndpoint", "Method": "<PERSON><PERSON>", "RelativePath": "signals/trigger/{token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Elsa.Server.Api.Endpoints.Activities.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/activities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Elsa.Metadata.ActivityDescriptor, Elsa.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Designer.RuntimeSelectListItems.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/designer/runtime-select-list", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Elsa.Server.Api.Endpoints.Designer.RuntimeSelectListItems.RuntimeSelectListContextHolder", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.OkObjectResult", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Features.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/features", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Scripting.JavaScript.TypeDefinitions.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/scripting/javascript/type-definitions/{workflowDefinitionId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}, {"Name": "context", "Type": "Elsa.Scripting.JavaScript.Models.IntellisenseContext", "IsRequired": false}, {"Name": "version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.FileContentResult", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Signals.Dispatch", "Method": "<PERSON><PERSON>", "RelativePath": "v1/signals/{signalName}/dispatch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "signalName", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Elsa.Server.Api.Endpoints.Signals.DispatchSignalRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.Signals.DispatchSignalResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Signals.Execute", "Method": "<PERSON><PERSON>", "RelativePath": "v1/signals/{signalName}/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "signalName", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Elsa.Server.Api.Endpoints.Signals.ExecuteSignalRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.Signals.ExecuteSignalResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Version.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/version", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowChannels.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-channels", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Models.PagedList`1[[Elsa.Server.Api.Endpoints.WorkflowDefinitions.WorkflowDefinitionSummaryModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Save", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Save+SaveWorkflowDefinitionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowDefinition", "MediaTypes": ["application/json"], "StatusCode": 201}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.DeleteByDefinition", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{definitionId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "definitionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 202}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.DeleteByDefinitionAndVersion", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{definitionId}/{versionOptions}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "definitionId", "Type": "System.String", "IsRequired": true}, {"Name": "versionOptions", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 202}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.History", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{definitionId}/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "definitionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.ICollection`1[[Elsa.Server.Api.Endpoints.WorkflowDefinitions.WorkflowDefinitionVersionModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Revert", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{definitionId}/revert/{version}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "definitionId", "Type": "System.String", "IsRequired": true}, {"Name": "version", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowDefinition", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.GetByVersionId", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{versionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "versionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowDefinition", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.GetByDefinitionAndVersion", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{workflowDefinitionId}/{versionOptions}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}, {"Name": "versionOptions", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowDefinition", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Export", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{workflowDefinitionId}/{versionOptions}/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}, {"Name": "versionOptions", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Import", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{workflowDefinitionId}/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}, {"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Publish", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{workflowDefinitionId}/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowDefinition", "MediaTypes": ["application/json"], "StatusCode": 202}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Retract", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/{workflowDefinitionId}/retract", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowDefinition", "MediaTypes": ["application/json"], "StatusCode": 202}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Backup", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/backup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": false}, {"Name": "version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/zip"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowDefinitions.Restore", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-definitions/restore", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflow", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "correlationId", "Type": "System.String", "IsRequired": false}, {"Name": "orderBy", "Type": "System.String", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Models.PagedList`1[[Elsa.Server.Api.Endpoints.WorkflowInstances.WorkflowInstanceSummaryModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.Delete", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Models.WorkflowInstance", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.Cancel", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowExecutionLog.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{id}/execution-log", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Models.PagedList`1[[Elsa.Models.WorkflowExecutionLogRecord, Elsa.Abstractions, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.Retry", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{id}/retry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "options", "Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.RetryWorkflowRequest", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 202}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.ActivityStats.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{workflowInstanceId}/activity-stats/{activityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowInstanceId", "Type": "System.String", "IsRequired": true}, {"Name": "activityId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.ActivityStats.ActivityStats", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.Dispatch", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{workflowInstanceId}/dispatch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowInstanceId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.DispatchWorkflowInstanceRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.DispatchWorkflowInstanceResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.Execute", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/{workflowInstanceId}/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowInstanceId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Elsa.Services.ExecuteWorkflowInstanceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.ExecuteWorkflowInstanceResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.BulkDelete", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/bulk", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.BulkDeleteWorkflowsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.BulkCancel", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/bulk/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.BulkCancelWorkflowsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowInstances.BulkRetry", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-instances/bulk/retry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Elsa.Server.Api.Endpoints.WorkflowInstances.BulkRetryWorkflowsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowProviders.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-providers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Elsa.Server.Api.Endpoints.WorkflowProviders.WorkflowProviderDescriptor, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowRegistry.ListAll", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-registry", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Elsa.Server.Api.Endpoints.WorkflowRegistry.WorkflowBlueprintSummaryModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowRegistry.Get", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-registry/{id}/{versionOptions}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "versionOptions", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Models.PagedList`1[[Elsa.Server.Api.Endpoints.WorkflowRegistry.WorkflowBlueprintModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowRegistry.ListByDefinitionVersionIds", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-registry/by-definition-version-ids", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Models.PagedList`1[[Elsa.Server.Api.Endpoints.WorkflowRegistry.WorkflowBlueprintSummaryModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowRegistry.ListByProvider", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-registry/by-provider/{providerName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "providerName", "Type": "System.String", "IsRequired": true}, {"Name": "page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Models.PagedList`1[[Elsa.Server.Api.Endpoints.WorkflowRegistry.WorkflowBlueprintSummaryModel, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.WorkflowStorageProviders.List", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflow-storage-providers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Elsa.Server.Api.Endpoints.WorkflowStorageProviders.WorkflowStorageDescriptor, Elsa.Server.Api, Version=********, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Workflows.Dispatch", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflows/{workflowDefinitionId}/dispatch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Elsa.Server.Api.Endpoints.Workflows.DispatchWorkflowDefinitionRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.Workflows.DispatchWorkflowDefinitionResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Workflows.Execute", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflows/{workflowDefinitionId}/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowDefinitionId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Elsa.Server.Api.Endpoints.Workflows.ExecuteWorkflowDefinitionRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.Workflows.ExecuteWorkflowDefinitionResponseModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "Elsa.Server.Api.Endpoints.Workflows.Trigger", "Method": "<PERSON><PERSON>", "RelativePath": "v1/workflows/trigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Elsa.Server.Api.Endpoints.Workflows.TriggerWorkflowsRequestModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Elsa.Server.Api.Endpoints.Workflows.TriggerWorkflowsRequestModel", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}]