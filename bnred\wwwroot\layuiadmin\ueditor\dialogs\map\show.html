<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head><meta charset="utf-8"><meta name="keywords" content="百度地图,百度地图API，百度地图自定义工具，百度地图所见即所得工具"><meta name="description" content="百度地图API自定义地图，帮助用户在可视化操作下生成百度地图"><title>百度地图API自定义地图</title><style>body,html{margin:0;padding:0;overflow:hidden}</style><script src="http://api.map.baidu.com/api?key=&v=1.1&services=true"></script></head><body onload="initMap()"><div style="width:697px;height:550px;border:#ccc solid 1px" id="dituContent"></div></body><script>function getParam(e){return location.href.match(new RegExp("[?#&]"+e+"=([^?#&]+)","i"))?RegExp.$1:""}var map,marker,centerParam=getParam("center"),zoomParam=getParam("zoom"),widthParam=getParam("width"),heightParam=getParam("height"),markersParam=getParam("markers"),markerStylesParam=getParam("markerStyles");function initMap(){if(window.BMap){var e=document.getElementById("dituContent");e.style.width=widthParam+"px",e.style.height=heightParam+"px",createMap(),setMapEvent(),addMapControl();var a=markersParam.split(","),r=new BMap.Point(a[0],a[1]);(marker=new BMap.Marker(r)).enableDragging(),map.addOverlay(marker),parent.editor&&"true"==parent.document.body.contentEditable&&setMapListener()}}function createMap(){map=new BMap.Map("dituContent");var e=centerParam.split(","),a=new BMap.Point(parseFloat(e[0]),parseFloat(e[1]));map.centerAndZoom(a,parseInt(zoomParam))}function setMapEvent(){map.enableDragging(),map.enableScrollWheelZoom(),map.enableDoubleClickZoom(),map.enableKeyboard()}function addMapControl(){var e=new BMap.NavigationControl({anchor:BMAP_ANCHOR_TOP_LEFT,type:BMAP_NAVIGATION_CONTROL_LARGE});map.addControl(e);var a=new BMap.OverviewMapControl({anchor:BMAP_ANCHOR_BOTTOM_RIGHT,isOpen:1});map.addControl(a);var r=new BMap.ScaleControl({anchor:BMAP_ANCHOR_BOTTOM_LEFT});map.addControl(r)}function setMapListener(){var t,n=parent.editor,e=parent.document.getElementsByTagName("iframe");for(var a in e)if(e[a].contentWindow==window){t=e[a];break}function r(){var e=map.getZoom(),a=map.getCenter(),r=window.marker.getPoint();t.src=t.src.replace(new RegExp("([?#&])center=([^?#&]+)","i"),"$1center="+a.lng+","+a.lat).replace(new RegExp("([?#&])markers=([^?#&]+)","i"),"$1markers="+r.lng+","+r.lat).replace(new RegExp("([?#&])zoom=([^?#&]+)","i"),"$1zoom="+e),n.fireEvent("saveScene")}t&&(map.addEventListener("moveend",r),map.addEventListener("zoomend",r),marker.addEventListener("dragend",r))}</script></html>