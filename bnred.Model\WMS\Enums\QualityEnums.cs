using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 检验类型
    /// 定义质量检验的不同类型
    /// </summary>
    public enum InspectionType
    {
        /// <summary>
        /// 入库检验 - 用于货物入库前的质量检查
        /// </summary>
        [Display(Name = "入库检验")]
        Inbound = 1,

        /// <summary>
        /// 出库检验 - 用于货物出库前的质量检查
        /// </summary>
        [Display(Name = "出库检验")]
        Outbound = 2,

        /// <summary>
        /// 生产检验 - 用于生产过程中的质量检查
        /// </summary>
        [Display(Name = "生产检验")]
        Production = 3,

        /// <summary>
        /// 周期检验 - 用于定期对库存物料进行的质量检查
        /// </summary>
        [Display(Name = "周期检验")]
        Periodic = 4,

        /// <summary>
        /// 抽样检验 - 用于随机抽样的质量检查
        /// </summary>
        [Display(Name = "抽样检验")]
        SampleTesting = 5,

        /// <summary>
        /// 退货检验 - 用于退货物料的质量检查
        /// </summary>
        [Display(Name = "退货检验")]
        Return = 6,

        /// <summary>
        /// 其他 - 其他类型的质量检查
        /// </summary>
        [Display(Name = "其他")]
        Other = 99
    }

    /// <summary>
    /// 检验状态
    /// 定义质量检验单的处理状态
    /// </summary>
    public enum InspectionStatus
    {
        /// <summary>
        /// 待检验 - 质检单已创建但尚未开始检验
        /// </summary>
        [Display(Name = "待检验")]
        Pending = 1,

        /// <summary>
        /// 检验中 - 质检单正在进行检验
        /// </summary>
        [Display(Name = "检验中")]
        InProcess = 2,

        /// <summary>
        /// 已完成 - 质检单检验工作已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 3,

        /// <summary>
        /// 部分完成 - 质检单部分项目已完成检验
        /// </summary>
        [Display(Name = "部分完成")]
        PartiallyCompleted = 4,

        /// <summary>
        /// 已取消 - 质检单已被取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 5,

        /// <summary>
        /// 异常 - 质检单在处理过程中出现异常
        /// </summary>
        [Display(Name = "异常")]
        Exception = 6
    }

    /// <summary>
    /// 检验明细状态
    /// 定义质量检验明细项的处理状态
    /// </summary>
    public enum InspectionDetailStatus
    {
        /// <summary>
        /// 待检验 - 检验项目尚未开始
        /// </summary>
        [Display(Name = "待检验")]
        Pending = 1,

        /// <summary>
        /// 检验中 - 检验项目正在进行
        /// </summary>
        [Display(Name = "检验中")]
        InProcess = 2,

        /// <summary>
        /// 已完成 - 检验项目已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 3,

        /// <summary>
        /// 已取消 - 检验项目已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 4,

        /// <summary>
        /// 异常 - 检验项目出现异常
        /// </summary>
        [Display(Name = "异常")]
        Exception = 5
    }

    /// <summary>
    /// 检验结果
    /// 定义质量检验的结果类型
    /// </summary>
    public enum InspectionResult
    {
        /// <summary>
        /// 合格 - 检验项目通过质量要求
        /// </summary>
        [Display(Name = "合格")]
        Qualified = 1,

        /// <summary>
        /// 不合格 - 检验项目未通过质量要求
        /// </summary>
        [Display(Name = "不合格")]
        Unqualified = 2,

        /// <summary>
        /// 部分合格 - 检验项目部分通过质量要求
        /// </summary>
        [Display(Name = "部分合格")]
        PartiallyQualified = 3,

        /// <summary>
        /// 待定 - 检验结果需要进一步确认
        /// </summary>
        [Display(Name = "待定")]
        Pending = 4
    }

    /// <summary>
    /// 处理方式
    /// 定义不合格物料的处理方式
    /// </summary>
    public enum DisposalMethod
    {
        /// <summary>
        /// 返工 - 对不合格物料进行修复后重新检验
        /// </summary>
        [Display(Name = "返工")]
        Rework = 1,

        /// <summary>
        /// 让步接收 - 在一定条件下接收不合格物料
        /// </summary>
        [Display(Name = "让步接收")]
        AcceptWithConcession = 2,

        /// <summary>
        /// 降级使用 - 将物料降级后使用
        /// </summary>
        [Display(Name = "降级使用")]
        Downgrade = 3,

        /// <summary>
        /// 报废 - 彻底报废不合格物料
        /// </summary>
        [Display(Name = "报废")]
        Scrap = 4,

        /// <summary>
        /// 退货 - 将不合格物料退回给供应商
        /// </summary>
        [Display(Name = "退货")]
        Return = 5,

        /// <summary>
        /// 其他 - 其他处理方式
        /// </summary>
        [Display(Name = "其他")]
        Other = 99
    }

    /// <summary>
    /// 关联单据类型
    /// 定义质检单可能关联的单据类型
    /// </summary>
    public enum RelatedOrderType
    {
        /// <summary>
        /// 入库单 - 入库相关的质检
        /// </summary>
        [Display(Name = "入库单")]
        Inbound = 1,

        /// <summary>
        /// 出库单 - 出库相关的质检
        /// </summary>
        [Display(Name = "出库单")]
        Outbound = 2,

        /// <summary>
        /// 生产单 - 生产相关的质检
        /// </summary>
        [Display(Name = "生产单")]
        Production = 3,

        /// <summary>
        /// 退货单 - 退货相关的质检
        /// </summary>
        [Display(Name = "退货单")]
        Return = 4,

        /// <summary>
        /// 移库单 - 移库相关的质检
        /// </summary>
        [Display(Name = "移库单")]
        Transfer = 5,

        /// <summary>
        /// 其他 - 其他类型的单据
        /// </summary>
        [Display(Name = "其他")]
        Other = 99
    }
} 