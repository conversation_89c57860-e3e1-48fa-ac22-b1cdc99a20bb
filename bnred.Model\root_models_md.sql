-- #############################################################################
-- # SQL DDL for Root Models (FrameworkUser, Language, Localization)
-- # Target File: bnred.Model/root_models_md.sql
-- #############################################################################

-- #############################################################################
-- # Table: WMS_FrameworkUsers (用户表)
-- # 来自: FrameworkUser.cs (继承自 WalkingTec.Mvvm.Core.FrameworkUserBase)
-- #############################################################################
IF OBJECT_ID('dbo.WMS_FrameworkUsers', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_FrameworkUsers;
GO

CREATE TABLE WMS_FrameworkUsers (
    ID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),         -- 主键，假定来自FrameworkUserBase或BasePoco
    ITCode NVARCHAR(50) NULL,                                -- 账户 (来自 FrameworkUserBase)
    Password NVARCHAR(255) NULL,                             -- 密码 (来自 FrameworkUserBase)
    Name NVARCHAR(50) NULL,                                  -- 姓名 (来自 FrameworkUserBase)
    Email NVARCHAR(50) NULL,                                 -- 邮箱
    Gender INT NULL,                                         -- 性别 (GenderEnum?)
    CellPhone NVARCHAR(MAX) NULL,                            -- 手机
    HomePhone NVARCHAR(30) NULL,                             -- 座机
    Address NVARCHAR(200) NULL,                              -- 地址
    ZipCode NVARCHAR(MAX) NULL,                              -- 邮编
    PhotoId UNIQUEIDENTIFIER NULL,                           -- 头像ID (来自 FrameworkUserBase)
    IsValid BIT NOT NULL DEFAULT 1,                          -- 是否有效 (来自 FrameworkUserBase)
    CreateTime DATETIME2 NULL,                               -- 创建时间 (来自 BasePoco/FrameworkUserBase)
    CreateBy NVARCHAR(255) NULL,                             -- 创建人 (来自 BasePoco/FrameworkUserBase)
    UpdateTime DATETIME2 NULL,                               -- 更新时间 (来自 BasePoco/FrameworkUserBase)
    UpdateBy NVARCHAR(255) NULL,                             -- 更新人 (来自 BasePoco/FrameworkUserBase)
    IsDeleted BIT NOT NULL DEFAULT 0                         -- 是否删除 (来自 BasePoco/FrameworkUserBase)
    -- TenantCode NVARCHAR(50) NULL, -- 根据您的 WTM 配置，可能还包含租户字段
);
GO

-- MS_Description for WMS_FrameworkUsers table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统用户表，存储用户基本信息。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers';
-- MS_Description for WMS_FrameworkUsers columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录账户/工号 (来自 FrameworkUserBase)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'ITCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录密码 (加密存储) (来自 FrameworkUserBase)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'Password';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户姓名 (来自 FrameworkUserBase)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'Name';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'电子邮箱' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'Email';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'性别 (关联 WMS_EnumDictionary, EnumType=\'WalkingTec.Mvvm.Core.GenderEnum\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'Gender';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'手机号码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'CellPhone';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'家庭电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'HomePhone';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'联系地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'Address';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮政编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'ZipCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户头像文件ID (关联FileAttachment) (来自 FrameworkUserBase)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'PhotoId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否有效账户 (来自 FrameworkUserBase)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'IsValid';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人账户' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人账户' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标记是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_FrameworkUsers', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Table: WMS_Language (语言表)
-- # 来自: Language.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_Language', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_Language;
GO

CREATE TABLE WMS_Language (
    ID NVARCHAR(50) PRIMARY KEY,                            -- 主键ID
    Name NVARCHAR(50) NOT NULL,                             -- 语言名称 (例如: English, 中文简体)
    CultureName NVARCHAR(10) NOT NULL UNIQUE,                -- 语言文化代码 (例如: en-US, zh-CN)
    DisplayOrder INT NOT NULL DEFAULT 0,                   -- 显示顺序
    -- Common fields from BasePoco
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO

-- MS_Description for WMS_Language table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统支持的语言信息。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language';
-- MS_Description for WMS_Language columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID，String类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'语言的显示名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'Name';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'语言的区域性区域性名称 (例如 en-US, zh-CN)，唯一' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'CultureName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'显示排序字段' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'DisplayOrder';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人账户' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人账户' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标记是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Language', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Table: WMS_Localization (本地化资源表)
-- # 来自: Localization.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_Localization', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_Localization;
GO

CREATE TABLE WMS_Localization (
    ID NVARCHAR(50) PRIMARY KEY,                        -- 主键ID
    EntityType NVARCHAR(50) NOT NULL,                   -- 实体类型名称 (例如: Product, Category)
    EntityId INT NOT NULL,                             -- 实体记录的ID
    lanID INT NOT NULL,                                -- 语言ID (实际应为语言表外键，例如 CultureName)
    Value NVARCHAR(MAX) NOT NULL,                      -- 本地化的值
    -- Common fields from BasePoco
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO

-- MS_Description for WMS_Localization table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'存储多语言本地化资源信息。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization';
-- MS_Description for WMS_Localization columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID，String类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'需要本地化的实体类型名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'EntityType';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'需要本地化的实体记录的ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'EntityId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'语言ID (应改为外键关联 WMS_Language.ID 或 WMS_Language.CultureName)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'lanID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'对应语言的本地化字符串值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'Value';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人账户' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人账户' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标记是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Localization', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Foreign Key Constraints for Root Models
-- #############################################################################

-- WMS_Localization.lanID (INT) is problematic for FK to WMS_Language.ID (NVARCHAR(50)) or WMS_Language.CultureName (NVARCHAR(10))
-- Assuming WMS_Localization.lanID should actually store CultureName or Language.ID directly and be of compatible type.
-- If lanID is meant to be an INT FK to an INT PK on WMS_Language, then WMS_Language PK needs to change.
-- For now, commenting out this FK due to type mismatch and ambiguity.
-- /*
-- ALTER TABLE WMS_Localization
-- ADD CONSTRAINT FK_Localization_Language FOREIGN KEY (lanID) REFERENCES WMS_Language(ID); -- Or CultureName if type matched
-- GO
-- */

PRINT 'SQL for root models (FrameworkUser, Language, Localization) created in root_models_md.sql';
GO
