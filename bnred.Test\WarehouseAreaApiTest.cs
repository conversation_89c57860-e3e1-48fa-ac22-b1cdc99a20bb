﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using bnred.Controllers;
using bnred.ViewModel.Warehouse.WarehouseAreaVMs;
using bnred.Model.WMS.Warehouse;
using bnred.DataAccess;


namespace bnred.Test
{
    [TestClass]
    public class WarehouseAreaApiTest
    {
        private WarehouseAreaController _controller;
        private string _seed;

        public WarehouseAreaApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<WarehouseAreaController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new WarehouseAreaSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            WarehouseAreaVM vm = _controller.Wtm.CreateVM<WarehouseAreaVM>();
            WarehouseArea v = new WarehouseArea();
            
            v.ID = "mnrLh62jzXDPYwU9ZUbw59bnAb7";
            v.Code = "c8cCsPCDd0aWbYSJ7NGCWwYrztFfpJ0o4ANEO";
            v.Name = "NW60s8i03Pd2VQ6oblFjyEOp2cogQrg9z07AKSrY";
            v.WarehouseId = AddWarehouse();
            v.Type = bnred.Model.WMS.Enums.AreaType.Staging;
            v.Status = bnred.Model.WMS.Enums.AreaStatus.Maintenance;
            v.Area = 63;
            v.Volume = 34;
            v.MaxCapacity = 11;
            v.CurrentCapacity = 84;
            v.ManagerId = AddFrameworkUser();
            v.Description = "D3xq3iQKMtQK7SgGNUAmtizI5RYgGmeBWWXRP8VKMXIhRFfZnpPtNKwRULs3Szxwo7AmnScyJ4givNHZQftLDJEgceMh2Pu4o4a2riAnQM6PWtXTtBIpli2ta3eKjoJwfe1rLjsAJioIqkzwjGZCLEg40osWtDq0fQrKEvhKzTqlmfzr3LD8TaKkb4SzRgg9OuaiBM4ElFMpB47qT2FP8D9dTpQU5XGXn2ildOM6fDqEi9ISNm0lHGlFaMq75QlCr9wUEqnKrUlfRfpFEJJReII6D9Aku9enUrHnCXlCKpp10zayESqMlI44qpo0mB67xzrHk9qa6PXTbH9nJKOelrIKGm2idxCgJ1ZbTumhlgwPIaGHB5xg6LqlfLGDW6ureS1AzGRqNNDR9Ryucs53QcsKpZHNdu4QumERrshKX4M1YKisnIH3vhPOmg5CXiRaun4DMvqfQXYHtqsDC4P";
            v.Remark = "n0Rq3ewtB5aFgLjAbbfjA";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<WarehouseArea>().Find(v.ID);
                
                Assert.AreEqual(data.ID, "mnrLh62jzXDPYwU9ZUbw59bnAb7");
                Assert.AreEqual(data.Code, "c8cCsPCDd0aWbYSJ7NGCWwYrztFfpJ0o4ANEO");
                Assert.AreEqual(data.Name, "NW60s8i03Pd2VQ6oblFjyEOp2cogQrg9z07AKSrY");
                Assert.AreEqual(data.Type, bnred.Model.WMS.Enums.AreaType.Staging);
                Assert.AreEqual(data.Status, bnred.Model.WMS.Enums.AreaStatus.Maintenance);
                Assert.AreEqual(data.Area, 63);
                Assert.AreEqual(data.Volume, 34);
                Assert.AreEqual(data.MaxCapacity, 11);
                Assert.AreEqual(data.CurrentCapacity, 84);
                Assert.AreEqual(data.Description, "D3xq3iQKMtQK7SgGNUAmtizI5RYgGmeBWWXRP8VKMXIhRFfZnpPtNKwRULs3Szxwo7AmnScyJ4givNHZQftLDJEgceMh2Pu4o4a2riAnQM6PWtXTtBIpli2ta3eKjoJwfe1rLjsAJioIqkzwjGZCLEg40osWtDq0fQrKEvhKzTqlmfzr3LD8TaKkb4SzRgg9OuaiBM4ElFMpB47qT2FP8D9dTpQU5XGXn2ildOM6fDqEi9ISNm0lHGlFaMq75QlCr9wUEqnKrUlfRfpFEJJReII6D9Aku9enUrHnCXlCKpp10zayESqMlI44qpo0mB67xzrHk9qa6PXTbH9nJKOelrIKGm2idxCgJ1ZbTumhlgwPIaGHB5xg6LqlfLGDW6ureS1AzGRqNNDR9Ryucs53QcsKpZHNdu4QumERrshKX4M1YKisnIH3vhPOmg5CXiRaun4DMvqfQXYHtqsDC4P");
                Assert.AreEqual(data.Remark, "n0Rq3ewtB5aFgLjAbbfjA");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            WarehouseArea v = new WarehouseArea();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ID = "mnrLh62jzXDPYwU9ZUbw59bnAb7";
                v.Code = "c8cCsPCDd0aWbYSJ7NGCWwYrztFfpJ0o4ANEO";
                v.Name = "NW60s8i03Pd2VQ6oblFjyEOp2cogQrg9z07AKSrY";
                v.WarehouseId = AddWarehouse();
                v.Type = bnred.Model.WMS.Enums.AreaType.Staging;
                v.Status = bnred.Model.WMS.Enums.AreaStatus.Maintenance;
                v.Area = 63;
                v.Volume = 34;
                v.MaxCapacity = 11;
                v.CurrentCapacity = 84;
                v.ManagerId = AddFrameworkUser();
                v.Description = "D3xq3iQKMtQK7SgGNUAmtizI5RYgGmeBWWXRP8VKMXIhRFfZnpPtNKwRULs3Szxwo7AmnScyJ4givNHZQftLDJEgceMh2Pu4o4a2riAnQM6PWtXTtBIpli2ta3eKjoJwfe1rLjsAJioIqkzwjGZCLEg40osWtDq0fQrKEvhKzTqlmfzr3LD8TaKkb4SzRgg9OuaiBM4ElFMpB47qT2FP8D9dTpQU5XGXn2ildOM6fDqEi9ISNm0lHGlFaMq75QlCr9wUEqnKrUlfRfpFEJJReII6D9Aku9enUrHnCXlCKpp10zayESqMlI44qpo0mB67xzrHk9qa6PXTbH9nJKOelrIKGm2idxCgJ1ZbTumhlgwPIaGHB5xg6LqlfLGDW6ureS1AzGRqNNDR9Ryucs53QcsKpZHNdu4QumERrshKX4M1YKisnIH3vhPOmg5CXiRaun4DMvqfQXYHtqsDC4P";
                v.Remark = "n0Rq3ewtB5aFgLjAbbfjA";
                context.Set<WarehouseArea>().Add(v);
                context.SaveChanges();
            }

            WarehouseAreaVM vm = _controller.Wtm.CreateVM<WarehouseAreaVM>();
            var oldID = v.ID;
            v = new WarehouseArea();
            v.ID = oldID;
       		
            v.Code = "tsUNn3I4qvJQJkkyCJNw9LktOwOfCRzAGbpU";
            v.Name = "8r2PNlDyrQ7qs7BCNXU6UUEZDXirxJSfc";
            v.Type = bnred.Model.WMS.Enums.AreaType.Shipping;
            v.Status = bnred.Model.WMS.Enums.AreaStatus.Normal;
            v.Area = 55;
            v.Volume = 52;
            v.MaxCapacity = 97;
            v.CurrentCapacity = 14;
            v.Description = "QFDhG26oNh0qs5t1aipe4QSlUTLMLGMxBH5B8S9wQm252XXMJRpBIhbUkeI9lk8FMMTYacvq2wrbZgRpTIDY17ifzDFbVIP2FLHfP0i2rIPSL16lBTUZB2AsemQd20GwhhSIshGxznKqLPsER6QgTU4vbQTXZo0voPrvGLHaT00auZfqZOs2BnUVGY1shfyJ4m3AwFJmeh3v11xKnqlgtWOAfnver11r1s8larQzODm5nDHPbrWRKwdHdqsZZcaQuRh6NFHttyqTTjBOKsE94I28BYpGXWvrpwr";
            v.Remark = "FARqfWFwyHdTXDpuuVmsuUxnrxgDftnRvbKKeMKrbiukFjwQtr7COPyzs0AlcxFPniGrxaTo9FgTT187O7SWUeOtOxv9HVaDPlLpV1OhPPMW0AfsxIvF2MLKSuYBGarBfGPODAmywF4RxeDkKqLK2DS0BnzEaQYXaSDd1LcO6OqfZRejKGozs6zEmrC8VzyOlSIGtv2T5yF";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ID", "");
            vm.FC.Add("Entity.Code", "");
            vm.FC.Add("Entity.Name", "");
            vm.FC.Add("Entity.WarehouseId", "");
            vm.FC.Add("Entity.Type", "");
            vm.FC.Add("Entity.Status", "");
            vm.FC.Add("Entity.Area", "");
            vm.FC.Add("Entity.Volume", "");
            vm.FC.Add("Entity.MaxCapacity", "");
            vm.FC.Add("Entity.CurrentCapacity", "");
            vm.FC.Add("Entity.ManagerId", "");
            vm.FC.Add("Entity.Description", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<WarehouseArea>().Find(v.ID);
 				
                Assert.AreEqual(data.Code, "tsUNn3I4qvJQJkkyCJNw9LktOwOfCRzAGbpU");
                Assert.AreEqual(data.Name, "8r2PNlDyrQ7qs7BCNXU6UUEZDXirxJSfc");
                Assert.AreEqual(data.Type, bnred.Model.WMS.Enums.AreaType.Shipping);
                Assert.AreEqual(data.Status, bnred.Model.WMS.Enums.AreaStatus.Normal);
                Assert.AreEqual(data.Area, 55);
                Assert.AreEqual(data.Volume, 52);
                Assert.AreEqual(data.MaxCapacity, 97);
                Assert.AreEqual(data.CurrentCapacity, 14);
                Assert.AreEqual(data.Description, "QFDhG26oNh0qs5t1aipe4QSlUTLMLGMxBH5B8S9wQm252XXMJRpBIhbUkeI9lk8FMMTYacvq2wrbZgRpTIDY17ifzDFbVIP2FLHfP0i2rIPSL16lBTUZB2AsemQd20GwhhSIshGxznKqLPsER6QgTU4vbQTXZo0voPrvGLHaT00auZfqZOs2BnUVGY1shfyJ4m3AwFJmeh3v11xKnqlgtWOAfnver11r1s8larQzODm5nDHPbrWRKwdHdqsZZcaQuRh6NFHttyqTTjBOKsE94I28BYpGXWvrpwr");
                Assert.AreEqual(data.Remark, "FARqfWFwyHdTXDpuuVmsuUxnrxgDftnRvbKKeMKrbiukFjwQtr7COPyzs0AlcxFPniGrxaTo9FgTT187O7SWUeOtOxv9HVaDPlLpV1OhPPMW0AfsxIvF2MLKSuYBGarBfGPODAmywF4RxeDkKqLK2DS0BnzEaQYXaSDd1LcO6OqfZRejKGozs6zEmrC8VzyOlSIGtv2T5yF");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            WarehouseArea v = new WarehouseArea();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ID = "mnrLh62jzXDPYwU9ZUbw59bnAb7";
                v.Code = "c8cCsPCDd0aWbYSJ7NGCWwYrztFfpJ0o4ANEO";
                v.Name = "NW60s8i03Pd2VQ6oblFjyEOp2cogQrg9z07AKSrY";
                v.WarehouseId = AddWarehouse();
                v.Type = bnred.Model.WMS.Enums.AreaType.Staging;
                v.Status = bnred.Model.WMS.Enums.AreaStatus.Maintenance;
                v.Area = 63;
                v.Volume = 34;
                v.MaxCapacity = 11;
                v.CurrentCapacity = 84;
                v.ManagerId = AddFrameworkUser();
                v.Description = "D3xq3iQKMtQK7SgGNUAmtizI5RYgGmeBWWXRP8VKMXIhRFfZnpPtNKwRULs3Szxwo7AmnScyJ4givNHZQftLDJEgceMh2Pu4o4a2riAnQM6PWtXTtBIpli2ta3eKjoJwfe1rLjsAJioIqkzwjGZCLEg40osWtDq0fQrKEvhKzTqlmfzr3LD8TaKkb4SzRgg9OuaiBM4ElFMpB47qT2FP8D9dTpQU5XGXn2ildOM6fDqEi9ISNm0lHGlFaMq75QlCr9wUEqnKrUlfRfpFEJJReII6D9Aku9enUrHnCXlCKpp10zayESqMlI44qpo0mB67xzrHk9qa6PXTbH9nJKOelrIKGm2idxCgJ1ZbTumhlgwPIaGHB5xg6LqlfLGDW6ureS1AzGRqNNDR9Ryucs53QcsKpZHNdu4QumERrshKX4M1YKisnIH3vhPOmg5CXiRaun4DMvqfQXYHtqsDC4P";
                v.Remark = "n0Rq3ewtB5aFgLjAbbfjA";
                context.Set<WarehouseArea>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            WarehouseArea v1 = new WarehouseArea();
            WarehouseArea v2 = new WarehouseArea();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ID = "mnrLh62jzXDPYwU9ZUbw59bnAb7";
                v1.Code = "c8cCsPCDd0aWbYSJ7NGCWwYrztFfpJ0o4ANEO";
                v1.Name = "NW60s8i03Pd2VQ6oblFjyEOp2cogQrg9z07AKSrY";
                v1.WarehouseId = AddWarehouse();
                v1.Type = bnred.Model.WMS.Enums.AreaType.Staging;
                v1.Status = bnred.Model.WMS.Enums.AreaStatus.Maintenance;
                v1.Area = 63;
                v1.Volume = 34;
                v1.MaxCapacity = 11;
                v1.CurrentCapacity = 84;
                v1.ManagerId = AddFrameworkUser();
                v1.Description = "D3xq3iQKMtQK7SgGNUAmtizI5RYgGmeBWWXRP8VKMXIhRFfZnpPtNKwRULs3Szxwo7AmnScyJ4givNHZQftLDJEgceMh2Pu4o4a2riAnQM6PWtXTtBIpli2ta3eKjoJwfe1rLjsAJioIqkzwjGZCLEg40osWtDq0fQrKEvhKzTqlmfzr3LD8TaKkb4SzRgg9OuaiBM4ElFMpB47qT2FP8D9dTpQU5XGXn2ildOM6fDqEi9ISNm0lHGlFaMq75QlCr9wUEqnKrUlfRfpFEJJReII6D9Aku9enUrHnCXlCKpp10zayESqMlI44qpo0mB67xzrHk9qa6PXTbH9nJKOelrIKGm2idxCgJ1ZbTumhlgwPIaGHB5xg6LqlfLGDW6ureS1AzGRqNNDR9Ryucs53QcsKpZHNdu4QumERrshKX4M1YKisnIH3vhPOmg5CXiRaun4DMvqfQXYHtqsDC4P";
                v1.Remark = "n0Rq3ewtB5aFgLjAbbfjA";
                v2.ID = "yNRfdmyaq3SgBJYA7vRs";
                v2.Code = "tsUNn3I4qvJQJkkyCJNw9LktOwOfCRzAGbpU";
                v2.Name = "8r2PNlDyrQ7qs7BCNXU6UUEZDXirxJSfc";
                v2.WarehouseId = v1.WarehouseId; 
                v2.Type = bnred.Model.WMS.Enums.AreaType.Shipping;
                v2.Status = bnred.Model.WMS.Enums.AreaStatus.Normal;
                v2.Area = 55;
                v2.Volume = 52;
                v2.MaxCapacity = 97;
                v2.CurrentCapacity = 14;
                v2.ManagerId = v1.ManagerId; 
                v2.Description = "QFDhG26oNh0qs5t1aipe4QSlUTLMLGMxBH5B8S9wQm252XXMJRpBIhbUkeI9lk8FMMTYacvq2wrbZgRpTIDY17ifzDFbVIP2FLHfP0i2rIPSL16lBTUZB2AsemQd20GwhhSIshGxznKqLPsER6QgTU4vbQTXZo0voPrvGLHaT00auZfqZOs2BnUVGY1shfyJ4m3AwFJmeh3v11xKnqlgtWOAfnver11r1s8larQzODm5nDHPbrWRKwdHdqsZZcaQuRh6NFHttyqTTjBOKsE94I28BYpGXWvrpwr";
                v2.Remark = "FARqfWFwyHdTXDpuuVmsuUxnrxgDftnRvbKKeMKrbiukFjwQtr7COPyzs0AlcxFPniGrxaTo9FgTT187O7SWUeOtOxv9HVaDPlLpV1OhPPMW0AfsxIvF2MLKSuYBGarBfGPODAmywF4RxeDkKqLK2DS0BnzEaQYXaSDd1LcO6OqfZRejKGozs6zEmrC8VzyOlSIGtv2T5yF";
                context.Set<WarehouseArea>().Add(v1);
                context.Set<WarehouseArea>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<WarehouseArea>().Find(v1.ID);
                var data2 = context.Set<WarehouseArea>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddFileAttachment()
        {
            FileAttachment v = new FileAttachment();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.FileName = "GHh";
                v.FileExt = "jbbh6vdLP";
                v.Path = "rjyTHLZ";
                v.Length = 99;
                v.UploadTime = DateTime.Parse("2026-04-25 16:04:29");
                v.SaveMode = "waomDBgvOEbWX";
                v.ExtraInfo = "5qF3fo9EVzIu";
                v.HandlerInfo = "hMAc";
                context.Set<FileAttachment>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddFrameworkUser()
        {
            FrameworkUser v = new FrameworkUser();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.Email = "Vq5ntHCItnIEdzf4GrJU3R5";
                v.Gender = WalkingTec.Mvvm.Core.GenderEnum.Male;
                v.CellPhone = "YqVCB1t1J";
                v.HomePhone = "mAw3SIrSyY7BGjmVCqnP0YiZ3k";
                v.Address = "4TKQmaLdml08PAxRArWEf475g4YenT6Dj6S4cj8pidoGmOnPLqlmygD3esgcmDGydLi4Mz5U9PAFhVcbkxnfK5fJ5YIsPnrhdHW7jIBGnpmRYr7ZWextC4vEvDcXrVED";
                v.ZipCode = "8T5S3894TBXGT0E4";
                v.ITCode = "7QIhIvcYqt0P";
                v.Password = "bmtOs";
                v.Name = "OrcHyWM1rRG0HXtVhjx7FHADjSjij6iwsWNeGHX";
                v.IsValid = true;
                v.PhotoId = AddFileAttachment();
                context.Set<FrameworkUser>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private String AddWarehouse()
        {
            Warehouse v = new Warehouse();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.ID = "Ou0dQE";
                v.Code = "58YMyb6r5EhxNC";
                v.Name = "LOM2Yqv2lbUMcwmXVskwMKoA5SY4cyym5fQaWqH4ZmbdFus";
                v.Type = bnred.Model.WMS.Enums.WarehouseType.Transit;
                v.Status = bnred.Model.WMS.Enums.WarehouseStatus.Suspended;
                v.Address = "cFF99FzaYP";
                v.ContactPerson = "IC1Rjl8IRTaZN7OW";
                v.ContactPhone = "zcf";
                v.Area = 48;
                v.Volume = 82;
                v.ManagerID = AddFrameworkUser();
                v.Description = "TRfP5QERk0MnO2YDg4lW26D5UxT4Km1Wrklxy5H7MN1mt50fyEbBzukoKSnFMHixQoLw6AHbo5oPFzZrTL4qWLtALahPsPDYuTZgPSiOKBMN1mlm0GBGeWKvjF1KMkGfVzx6Xfq8KCaJ7wOTpjof4g9";
                v.Remark = "pCtShpGMzmHCEp0jFlyTb8liMkA4glSl5yJHJQQP66iHiPGyPHYDLjSBNIAHcXKUoK2hwsXpICkPwZG4ULayRpyVJeEc4rQLtcyoZUEhVq8vHNmUk33kgbg3DzF7VbZBD1Q9";
                context.Set<Warehouse>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
