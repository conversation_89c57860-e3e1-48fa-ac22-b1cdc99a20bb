self.assetsManifest = {
  "version": "7zsm6FE4",
  "assets": [
    {
      "hash": "sha256-sFiCTv7ceAk6EYrB5/p1KQ8TMLkwmhbz1IlKHtczboE=",
      "url": "_content/BootstrapBlazor.Chart/Components/Chart/Chart.razor.js"
    },
    {
      "hash": "sha256-JNPyrzjsuHs9QdXfT2hoDGxwV0z5VHlD1HBid9TUK2I=",
      "url": "_content/BootstrapBlazor.Chart/js/chart.js"
    },
    {
      "hash": "sha256-Nb343W+lg3VS2+kKKHAaSrYRl6u7CRhE7h+tPRNt/hg=",
      "url": "_content/BootstrapBlazor.Chart/js/chartjs-plugin-datalabels.js"
    },
    {
      "hash": "sha256-7J4rKPcqU0YywMy5i2WiZO/PTTubEKoWR4Ng6CoPZKk=",
      "url": "_content/BootstrapBlazor.Markdown/Components/Markdown/Markdown.razor.js"
    },
    {
      "hash": "sha256-a2o8RsIK7Jpp16m3L1J1UNQRbCT9JN3gB2vbNopjlaI=",
      "url": "_content/BootstrapBlazor.Markdown/css/bootstrap.blazor.markdown.min.css"
    },
    {
      "hash": "sha256-YVT99G6OuFMOHGqWXBVMln1krZXJ29y0o6au7fZDKhg=",
      "url": "_content/BootstrapBlazor.Markdown/lib/tui.editor/toastui-editor-all.min.js"
    },
    {
      "hash": "sha256-rCjx3ZyaFuttjuwL252zvPR4pksEATuF8oNxz7f4er0=",
      "url": "_content/BootstrapBlazor.Markdown/lib/tui.editor/zh-cn.min.js"
    },
    {
      "hash": "sha256-Q1dyQI9rV6Q3Yw6HiW6l09X4xUMnuvCLA+tjrR6yrAI=",
      "url": "_content/BootstrapBlazor.Markdown/lib/tui.highlight/toastui-editor-plugin-code-syntax-highlight-all.min.js"
    },
    {
      "hash": "sha256-QeBdU6EUEQ0vj7hJtJM6+byhjsZEibo9Lec5VLMK+rw=",
      "url": "_content/BootstrapBlazor.SummerNote/Components/Editor/Editor.razor.js"
    },
    {
      "hash": "sha256-aTC02ahruydZxiZ75Hwch2JBNdz7eOMyMkc3od6QIdM=",
      "url": "_content/BootstrapBlazor.SummerNote/css/bootstrap.blazor.editor.min.css"
    },
    {
      "hash": "sha256-4OVKzab2aG6LyunRw1vhauxRbTiqxsNpsV0MqBufBn4=",
      "url": "_content/BootstrapBlazor.SummerNote/css/font/summernote.eot"
    },
    {
      "hash": "sha256-vAaZigJZsqS+QP0EPiEyT/kJIBG66MoNlju2M+3NMOE=",
      "url": "_content/BootstrapBlazor.SummerNote/css/font/summernote.ttf"
    },
    {
      "hash": "sha256-rSC9E/CRNx7PKJMAKAkqzkti7Folm4yVm22PnX1AXSU=",
      "url": "_content/BootstrapBlazor.SummerNote/css/font/summernote.woff"
    },
    {
      "hash": "sha256-auYrvu4DEzNjk9kB0FWwdJl4q94lI0OXlCYJ/vRSHB8=",
      "url": "_content/BootstrapBlazor.SummerNote/css/font/summernote.woff2"
    },
    {
      "hash": "sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=",
      "url": "_content/BootstrapBlazor.SummerNote/js/jquery-3.5.1.min.js"
    },
    {
      "hash": "sha256-u51lcUa0Ip1ShO/qh3bLWanDSXjc1x0K1dIWnKbSu7o=",
      "url": "_content/BootstrapBlazor.SummerNote/js/summernote-bs5.min.js"
    },
    {
      "hash": "sha256-zGkaeC1AnJPGuOwx5XUZgR3CR04m0h2YghZ8A9H5Fp0=",
      "url": "_content/BootstrapBlazor/Components/Anchor/Anchor.razor.js"
    },
    {
      "hash": "sha256-QNU+i7O+i3rdIdFQvDv1ljv53ZjeKU0p5w+n8iP7aVw=",
      "url": "_content/BootstrapBlazor/Components/AnchorLink/AnchorLink.razor.js"
    },
    {
      "hash": "sha256-MiseMIL2jwOY4gsjANwjbEcWREcMFY41GPL8B3wCrSA=",
      "url": "_content/BootstrapBlazor/Components/AutoComplete/AutoComplete.razor.js"
    },
    {
      "hash": "sha256-PGfIDILAprLLIQy5VPlL2Z33NDHwjDQNnrexgkUe/u0=",
      "url": "_content/BootstrapBlazor/Components/Button/Button.razor.js"
    },
    {
      "hash": "sha256-a778W+SIMwBS7Z7CrshQNWN/OUOIHFBDJBwK38We5sM=",
      "url": "_content/BootstrapBlazor/Components/Button/DialButton.razor.js"
    },
    {
      "hash": "sha256-RkTjKU1JroLxCxL1z/D6QS6uK94e+amguVZjNv5s9E0=",
      "url": "_content/BootstrapBlazor/Components/Button/PopConfirmButton.razor.js"
    },
    {
      "hash": "sha256-eqfo/FBzax2fexCF9dqGSG7oqlQX2Gx7/zCyYlCwjgg=",
      "url": "_content/BootstrapBlazor/Components/Button/PopConfirmButtonContent.razor.js"
    },
    {
      "hash": "sha256-vrEuw4htZnRT2xU+Q2i2gHa2BWaLQq1fQu1OZf0D8HA=",
      "url": "_content/BootstrapBlazor/Components/Button/SlideButton.razor.js"
    },
    {
      "hash": "sha256-EQLU0JqIBWIp4GHAj2cLaVi9G8O1Tt4shWRmleIrpgc=",
      "url": "_content/BootstrapBlazor/Components/Camera/Camera.razor.js"
    },
    {
      "hash": "sha256-j/TDy1o2HhHpriELCKIh0BmPD1y9/hbiwx+a9KiAhT4=",
      "url": "_content/BootstrapBlazor/Components/Captcha/Captcha.razor.js"
    },
    {
      "hash": "sha256-IfhGU6mPhHE7qjEgMnFtZxiZQPvpafxE0XfTt3ICBHU=",
      "url": "_content/BootstrapBlazor/Components/Card/Card.razor.js"
    },
    {
      "hash": "sha256-IYTjcVCalAs0VkjOsdgl1Y2sC4kyhttnkt6fIkL0ea4=",
      "url": "_content/BootstrapBlazor/Components/Carousel/Carousel.razor.js"
    },
    {
      "hash": "sha256-ttZ2J2RG5uwEVleeMY9eo0kQMYQcnGI05hvGNHvomWA=",
      "url": "_content/BootstrapBlazor/Components/Checkbox/Checkbox.razor.js"
    },
    {
      "hash": "sha256-jY+RbjTvYMn3XgWCH4pjzwSmidce9kDI4Ayjxp75pKU=",
      "url": "_content/BootstrapBlazor/Components/ClockPicker/ClockPicker.razor.js"
    },
    {
      "hash": "sha256-+OIt8BLy9ABkKO5Pe6TuHSBxNteFLmqMWMNaYfsmwH8=",
      "url": "_content/BootstrapBlazor/Components/ColorPicker/ColorPicker.razor.js"
    },
    {
      "hash": "sha256-CngQTBg3DeNmIXMnOumJAhFAPWeE8/43ozkAObHZUKI=",
      "url": "_content/BootstrapBlazor/Components/Console/Console.razor.js"
    },
    {
      "hash": "sha256-/h0HaErAfrCL+uUHjdp4RShlXKYLGt0FbgeEG60eGxs=",
      "url": "_content/BootstrapBlazor/Components/ContextMenu/ContextMenu.razor.js"
    },
    {
      "hash": "sha256-BNocyHiLSuTMLzInEUSXcc6RZgMCpXNBUhr1qe/UgEw=",
      "url": "_content/BootstrapBlazor/Components/CountUp/CountUp.razor.js"
    },
    {
      "hash": "sha256-zm3bLyr42trnUh79BdRkHmPM1kR3cekKVeMC1Gnjaq0=",
      "url": "_content/BootstrapBlazor/Components/DateTimePicker/DateTimePicker.razor.js"
    },
    {
      "hash": "sha256-o2djl64cA3UkacB6o+yOpn1ybyPHgzTP2yZzVgpLQjQ=",
      "url": "_content/BootstrapBlazor/Components/Dialog/EditDialog.razor.js"
    },
    {
      "hash": "sha256-sq9p1PqWeSfhtuInAKrttajScUDsaO7LR2r4+zW++b8=",
      "url": "_content/BootstrapBlazor/Components/Dialog/IconDialog.razor.js"
    },
    {
      "hash": "sha256-H07ich+ZPFMmgW0LbwQRYynhKswsGH0IIq2VtsSENLQ=",
      "url": "_content/BootstrapBlazor/Components/Drawer/Drawer.razor.js"
    },
    {
      "hash": "sha256-2FI29VA5M+JEB1iMWS6bZ8SGg/VO1/LUlfFWc8KQt48=",
      "url": "_content/BootstrapBlazor/Components/Dropdown/Dropdown.razor.js"
    },
    {
      "hash": "sha256-KrarV9m590y+BSLIe6kAwPhpvDoXKyixHleGJZJkV0Y=",
      "url": "_content/BootstrapBlazor/Components/DropdownWidget/DropdownWidget.razor.js"
    },
    {
      "hash": "sha256-ZiZUOU5BF4APYqEYydLhtj06Tz8+Q2L7nfU36ahI5C0=",
      "url": "_content/BootstrapBlazor/Components/Filters/MultiFilter.razor.js"
    },
    {
      "hash": "sha256-mggBE0IzvHsHdN3rkL7pnPE7VQr5Gv0qdZ8YivBCwhk=",
      "url": "_content/BootstrapBlazor/Components/Filters/TableFilter.razor.js"
    },
    {
      "hash": "sha256-lfe8UKQ9XPm5Qw7w8z07FhmtCqWkggxdUrUpPB9M950=",
      "url": "_content/BootstrapBlazor/Components/FlipClock/FlipClock.razor.js"
    },
    {
      "hash": "sha256-DcFJj53TZ2AzbOe9UNc3kf/Pgr+zGCriR0+/wyoxoZA=",
      "url": "_content/BootstrapBlazor/Components/GoTop/GoTop.razor.js"
    },
    {
      "hash": "sha256-LEx9KZtg22rQ04EvVzGZ599QUi2e07kbAm60pMxF0Js=",
      "url": "_content/BootstrapBlazor/Components/Handwritten/Handwritten.razor.js"
    },
    {
      "hash": "sha256-ZODD/WMyUf2DSWBa20SIt9jxdp4m8L7EZdjKiY4rGWI=",
      "url": "_content/BootstrapBlazor/Components/IFrame/IFrame.razor.js"
    },
    {
      "hash": "sha256-GkVtDvdwmsf8PisEkg/BdU7t4mGAi4ZalnMXvoQT6ag=",
      "url": "_content/BootstrapBlazor/Components/ImagePreviewer/ImagePreviewer.razor.js"
    },
    {
      "hash": "sha256-FSYhZe3Ksc0tu/WhsS1FCJ2k/XIxFddctNU0bRF4puA=",
      "url": "_content/BootstrapBlazor/Components/ImageViewer/ImageViewer.razor.js"
    },
    {
      "hash": "sha256-t59keiMDhjGxtSc5iHrPJ8iPgFy3oKXRHa4EqJCki8c=",
      "url": "_content/BootstrapBlazor/Components/Input/BootstrapInput.razor.js"
    },
    {
      "hash": "sha256-7nweNXBICzWH6ulQCb/u9FDsC8f1hGs0wAoab3ihjsk=",
      "url": "_content/BootstrapBlazor/Components/IntersectionObserver/IntersectionObserver.razor.js"
    },
    {
      "hash": "sha256-CQPwvZIBsLS3aQWpkQtcaDTkr7pgmz7swr1/JoVLtfE=",
      "url": "_content/BootstrapBlazor/Components/IpAddress/IpAddress.razor.js"
    },
    {
      "hash": "sha256-DejKOWiwuiyQFJRAnV5cvmYnKWgq9esV7AXbQbAl35U=",
      "url": "_content/BootstrapBlazor/Components/Layout/Layout.razor.js"
    },
    {
      "hash": "sha256-wJljcaUkHEGT2KpOavVJMtBacngyyiHOIhLTtdoSaQ8=",
      "url": "_content/BootstrapBlazor/Components/Mask/Mask.razor.js"
    },
    {
      "hash": "sha256-sAgInTx0D8KMHmZdq2gk6ljFdci3L9yFB6sgEy+GmeI=",
      "url": "_content/BootstrapBlazor/Components/Menu/Menu.razor.js"
    },
    {
      "hash": "sha256-6XbSr4uzcuRkwZctl5ZRCnIsjnINNqR3nJOoBdTDhs8=",
      "url": "_content/BootstrapBlazor/Components/Message/Message.razor.js"
    },
    {
      "hash": "sha256-X9gKUZEfKqt76HB461KKdGp27YMHlmXRhLahqyS0dRY=",
      "url": "_content/BootstrapBlazor/Components/Modal/Modal.razor.js"
    },
    {
      "hash": "sha256-7MElM6AyVPPloJE69oZUsQopREjsqrgJ6gIxmwPfpVw=",
      "url": "_content/BootstrapBlazor/Components/Modal/ModalDialog.razor.js"
    },
    {
      "hash": "sha256-BmYYbeunbanDzDSn/4gZVpP/FYO67nETHxXINyChsw4=",
      "url": "_content/BootstrapBlazor/Components/Popover/Popover.razor.js"
    },
    {
      "hash": "sha256-3A/jjFmYzTUz5tGAR30/ixPjBOcV8h0WVFboCaGmc2c=",
      "url": "_content/BootstrapBlazor/Components/Print/PrintButton.razor.js"
    },
    {
      "hash": "sha256-IFdRUnW6bf5/bZrB3N/IH/E/S5GcTbih6BXQIWf0fHI=",
      "url": "_content/BootstrapBlazor/Components/Reconnector/ReconnectorContent.razor.js"
    },
    {
      "hash": "sha256-zQh0LCRBCZhc64VIOSnAIBPK8QhVhQtSI5AyA3wKsxg=",
      "url": "_content/BootstrapBlazor/Components/RibbonTab/RibbonTab.razor.js"
    },
    {
      "hash": "sha256-e21iSM+rNigYZwrwHeh3gHVh1NgdfbbsaLcGZ56RSQk=",
      "url": "_content/BootstrapBlazor/Components/Row/Row.razor.js"
    },
    {
      "hash": "sha256-HcaDUPeAbMBxiyWRvdDMTIowmQL65PZInmxpAIG5+lw=",
      "url": "_content/BootstrapBlazor/Components/Segmented/Segmented.razor.js"
    },
    {
      "hash": "sha256-5pO/jWZEMsG0q55vGT9i2Sdpt0hng0HhkUm2o7JH6hc=",
      "url": "_content/BootstrapBlazor/Components/Select/MultiSelect.razor.js"
    },
    {
      "hash": "sha256-GN4G1bDRcVk9XV6tICpapemo5SPfsAXslEKyvelteOc=",
      "url": "_content/BootstrapBlazor/Components/Select/Select.razor.js"
    },
    {
      "hash": "sha256-V9HPhWuMg/iKYk+9VjN4JEArvAsqjzp4VbxmwX0yYHE=",
      "url": "_content/BootstrapBlazor/Components/Select/SelectObject.razor.js"
    },
    {
      "hash": "sha256-k2A41ZjQHHBhh+WMRO4T6NiyHKPDGnEpzKDdMYJbjh8=",
      "url": "_content/BootstrapBlazor/Components/Select/SelectTable.razor.js"
    },
    {
      "hash": "sha256-JLv1pNk7iZCUG6PITEkrdSn1TeW9DC1qboASS2DrBuw=",
      "url": "_content/BootstrapBlazor/Components/Select/SelectTree.razor.js"
    },
    {
      "hash": "sha256-Tq6xUkMwplVNbcOc/FV/2RlPccu8Ek8/tyr+qUaqAik=",
      "url": "_content/BootstrapBlazor/Components/Split/Split.razor.js"
    },
    {
      "hash": "sha256-ql8MjHebS5oee6iM72DN22V8no6NFrboQhntI4bmJV0=",
      "url": "_content/BootstrapBlazor/Components/Tab/Tab.razor.js"
    },
    {
      "hash": "sha256-31N/wWJTDXIpaaLjYyJh0DO7leTsZX6x/A6TL9C8MYg=",
      "url": "_content/BootstrapBlazor/Components/Table/Table.razor.js"
    },
    {
      "hash": "sha256-PvQ+Fc24Q+HTkWJpAh29E5Qtmsp+VPE6Qxafagx+KhQ=",
      "url": "_content/BootstrapBlazor/Components/Textarea/Textarea.razor.js"
    },
    {
      "hash": "sha256-3++qkhk5o23ny9MredPGpX2uWVjW15SQ1ALG5QevzPc=",
      "url": "_content/BootstrapBlazor/Components/ThemeProvider/ThemeProvider.razor.js"
    },
    {
      "hash": "sha256-SIzG2HO3ENk2nOy3aPjCcG7VFG/lRNHCzyTv/2fTrWY=",
      "url": "_content/BootstrapBlazor/Components/TimePicker/TimePickerCell.razor.js"
    },
    {
      "hash": "sha256-UmS0qBKgy9wCxmcayL8sNFGOqdgDajpCbT2uJQCXFmw=",
      "url": "_content/BootstrapBlazor/Components/Toast/Toast.razor.js"
    },
    {
      "hash": "sha256-ZkKTF/ERi0sw9ZOQjux2W9boa7lbxah3ikCLkZeD0r4=",
      "url": "_content/BootstrapBlazor/Components/Tooltip/Tooltip.razor.js"
    },
    {
      "hash": "sha256-AuYpsLEaD4yczvSK38BZ+YJDsbARc9KH9QcBkUXj1GM=",
      "url": "_content/BootstrapBlazor/Components/Transition/Transition.razor.js"
    },
    {
      "hash": "sha256-9vUxuUX5ygqkrBRsfOjve8/nBHWcd+37NDgP+rn0s3c=",
      "url": "_content/BootstrapBlazor/Components/TreeView/TreeView.razor.js"
    },
    {
      "hash": "sha256-867OOlIhOHeESKZw/GI94PAAzWXvR6DAecQE8dgc/5I=",
      "url": "_content/BootstrapBlazor/Components/ValidateForm/ValidateForm.razor.js"
    },
    {
      "hash": "sha256-e8ZX9oLXpDZZFijyDxPU4sorrFtPsFaGau0GT32d2MQ=",
      "url": "_content/BootstrapBlazor/Components/Waterfall/Waterfall.razor.js"
    },
    {
      "hash": "sha256-X7rrn44l1+AUO65h1LGALBbOc5C5bOstSYsNlv9MhT8=",
      "url": "_content/BootstrapBlazor/css/animate.min.css"
    },
    {
      "hash": "sha256-1ftsg68FXLI2jRen9AkmxzNdFlYTA5XSeP+OO9I4hPc=",
      "url": "_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css"
    },
    {
      "hash": "sha256-2bHww0UFNc7dwN2MO9PzD36/H6JjqpaSZwgaZetHCd8=",
      "url": "_content/BootstrapBlazor/css/bootstrap.blazor.bundle.rtl.min.css"
    },
    {
      "hash": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=",
      "url": "_content/BootstrapBlazor/css/bootstrap.min.css"
    },
    {
      "hash": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=",
      "url": "_content/BootstrapBlazor/css/bootstrap.rtl.min.css"
    },
    {
      "hash": "sha256-Vnj9AFQ1vEkDPdJhzf9k5PIeEoivgv8bUguy5lQ1tv0=",
      "url": "_content/BootstrapBlazor/css/bootstrapblazor.min.css"
    },
    {
      "hash": "sha256-+MxIqAU9NzGnrvvtFpFO681vijiCIGa6rhKK9amLY4U=",
      "url": "_content/BootstrapBlazor/css/motronic.min.css"
    },
    {
      "hash": "sha256-y5+CsSXMB9WLwSqsbpNvhYJ1HFb+0zU7HRMQz3ameks=",
      "url": "_content/BootstrapBlazor/css/nano.min.css"
    },
    {
      "hash": "sha256-IXo01kAQIXwlBA3gKF2oGWpDe/NP4NWjSSuPiIc23t0=",
      "url": "_content/BootstrapBlazor/css/rtl.css"
    },
    {
      "hash": "sha256-w/+sn4certEqueyBNzgzlhMdrb5fhyK/draUsHYRwvI=",
      "url": "_content/BootstrapBlazor/css/sweetalert2.css"
    },
    {
      "hash": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=",
      "url": "_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"
    },
    {
      "hash": "sha256-2Iav7zCT56knFN1EYEK3m+/8EOtafYFUjM0WdgEZDV8=",
      "url": "_content/BootstrapBlazor/lib/countUp/countUp.min.js"
    },
    {
      "hash": "sha256-LlQEvzXe0tiZyBYH2+8p0cXSOPDYvqpqMIHo9AkARWI=",
      "url": "_content/BootstrapBlazor/lib/floating-ui/floating-ui.core.esm.js"
    },
    {
      "hash": "sha256-+TjIiGYxY/j9yWbsi1fpcfqtukEx76R9PTI7GWgm6Xo=",
      "url": "_content/BootstrapBlazor/lib/floating-ui/floating-ui.dom.esm.js"
    },
    {
      "hash": "sha256-AVE6OUGgmfvRELzrILbp5a8TZC2sy093LueNk2J4iZE=",
      "url": "_content/BootstrapBlazor/lib/pickr/pickr.es5.min.js"
    },
    {
      "hash": "sha256-jY/GHRosXpqLj/X6mC+H0Sf45ESBI6++tHMhAt9vLHc=",
      "url": "_content/BootstrapBlazor/modules/ajax.js"
    },
    {
      "hash": "sha256-oEirc+D2sO8I66CvtbMYMXFdm8zmltqRAx6P/lIAxWo=",
      "url": "_content/BootstrapBlazor/modules/autoredirect.js"
    },
    {
      "hash": "sha256-K878rMKXhgw6QxuJ2e5ehCM6xHTqtEcpUNhX5Y83rd4=",
      "url": "_content/BootstrapBlazor/modules/base-popover.js"
    },
    {
      "hash": "sha256-z7QemTeZCDh+7cthBpjZV7uyKos2dZFp8vQMqGD+HS4=",
      "url": "_content/BootstrapBlazor/modules/browser.js"
    },
    {
      "hash": "sha256-X4WfR6lgR6L0+EbgCzEXG5cs0Y409Ff4EJpp7oKemeY=",
      "url": "_content/BootstrapBlazor/modules/bt.js"
    },
    {
      "hash": "sha256-rE7GpfElCPYCs9dwPYu6Ja4M9huXyF7e0rhl0Q1QO+I=",
      "url": "_content/BootstrapBlazor/modules/client.js"
    },
    {
      "hash": "sha256-72ZSXWGxUXaIxtK4z7xzPsV6XjKbJPWVhDGjbdPpFLY=",
      "url": "_content/BootstrapBlazor/modules/data.js"
    },
    {
      "hash": "sha256-r93uO3PIJZE29ltRfjkLH+TaT93vheHC/Ey8ORShix8=",
      "url": "_content/BootstrapBlazor/modules/debounce.js"
    },
    {
      "hash": "sha256-gCx9rJZhRIgRQf1FB8ya9/MKIqntVhaHsh2U1Ul5RzA=",
      "url": "_content/BootstrapBlazor/modules/download.js"
    },
    {
      "hash": "sha256-9xcXJw6OQztbgNlXhd+crK8rGQujHWEXl5563ktduL0=",
      "url": "_content/BootstrapBlazor/modules/drag.js"
    },
    {
      "hash": "sha256-XVvzXmbrid6+4tkn/pu9q7DQ0Pr6lwt3AhPaBFZuFDU=",
      "url": "_content/BootstrapBlazor/modules/event-handler.js"
    },
    {
      "hash": "sha256-rotsz69DC1r0UVBrZT90QzneyIGdp0NtQCKdXRmI8HA=",
      "url": "_content/BootstrapBlazor/modules/eye-dropper.js"
    },
    {
      "hash": "sha256-c9x4lFucX2Bp8HyHLQr2GcLm/KsNurZb4ogAZdPAzxQ=",
      "url": "_content/BootstrapBlazor/modules/floating-ui.js"
    },
    {
      "hash": "sha256-t2BfgLVZYLA+P78gbOtzf62KDfbP5y/vNV7D99wuZSI=",
      "url": "_content/BootstrapBlazor/modules/fullscreen.js"
    },
    {
      "hash": "sha256-sVQOKblDQoIwzKrlzZtj9ipoauc4dN7weNF5F/hexBg=",
      "url": "_content/BootstrapBlazor/modules/geo.js"
    },
    {
      "hash": "sha256-T5nTx36nhL08R8x3Nytv/2pRs3aGOx6GrVpf2ASieYk=",
      "url": "_content/BootstrapBlazor/modules/hub.js"
    },
    {
      "hash": "sha256-gcxFC1A6hl2HPQkmC6VGqsdCPI96Uno+CaMr0VELm3I=",
      "url": "_content/BootstrapBlazor/modules/input.js"
    },
    {
      "hash": "sha256-/tGtANABN/kTnpdVIClp/wQht5gtMf5Jtcea4njQiag=",
      "url": "_content/BootstrapBlazor/modules/noti.js"
    },
    {
      "hash": "sha256-jU+WdBzbZMP9yBGVeD8WTZva72+ZuWbv2+0R/GXO07o=",
      "url": "_content/BootstrapBlazor/modules/recognition.js"
    },
    {
      "hash": "sha256-v+iOXV3ma/5JRWAMkYlg3KZXcDG1vCt10XMoHIFzH74=",
      "url": "_content/BootstrapBlazor/modules/responsive.js"
    },
    {
      "hash": "sha256-kYd3RO4dajaubtEv/Eas8LGQ4sxvCke7S+lXX9RGdis=",
      "url": "_content/BootstrapBlazor/modules/serial.js"
    },
    {
      "hash": "sha256-3u7V23IlPSVaO2N3Iy4aqgCYOYuLyCLaw0TT+/e1UYw=",
      "url": "_content/BootstrapBlazor/modules/synthesis.js"
    },
    {
      "hash": "sha256-8MufSnxCkMzQ3JCa5CkYoB8JZ6fuuu0+JiBx4SQqRCk=",
      "url": "_content/BootstrapBlazor/modules/upload.js"
    },
    {
      "hash": "sha256-cw5r3h7YQw/I1viPgfGOdA30FT3PgNQ+1XF7lYB4O84=",
      "url": "_content/BootstrapBlazor/modules/utility.js"
    },
    {
      "hash": "sha256-KFdsj8qXHTtKN0g9DsJbYlXF6Gc64KE4HHtnRfkVhcc=",
      "url": "_content/BootstrapBlazor/modules/validate.js"
    },
    {
      "hash": "sha256-Isv13vbkc9fYXetDMhTomrulII20XRFUKWvwSqlkwk4=",
      "url": "_content/BootstrapBlazor/modules/viewer.js"
    },
    {
      "hash": "sha256-ZF/w6RPVWQKAofNojBEKe36gNlLuDK3jbT/T09U/3P0=",
      "url": "_content/bnred.Shared/bnred.Shared.gjyvc1v7oj.bundle.scp.css"
    },
    {
      "hash": "sha256-Or0X8eO62MgHNhhw9lromYgf5C6VNpHBV+UjfA5WEkg=",
      "url": "_content/bnred.Shared/css/loading.css"
    },
    {
      "hash": "sha256-UMOD+4CAsqRxoBgQ+bpKrRlsLnQLUUphG5cUFCnblR8=",
      "url": "_content/bnred.Shared/css/site.css"
    },
    {
      "hash": "sha256-NA0J0SFBow9T2HDWR/L0upMEdwkzHNRBxD23MBvVLWg=",
      "url": "_content/bnred.Shared/font-awesome/css/all.css"
    },
    {
      "hash": "sha256-AbA177XfpSnFEvgpYu1jMygiLabzPCJCRIBtR5jGc0k=",
      "url": "_content/bnred.Shared/font-awesome/css/all.min.css"
    },
    {
      "hash": "sha256-a+cU6KEtZr2ptiakv5JHZIbaZbln5po3kb4mZpF6zik=",
      "url": "_content/bnred.Shared/font-awesome/css/brands.css"
    },
    {
      "hash": "sha256-p+kl9hkqP3kHYh+d3or8R3UtZx1KeW8s1aU8+8B8IUs=",
      "url": "_content/bnred.Shared/font-awesome/css/brands.min.css"
    },
    {
      "hash": "sha256-X0K8shvftBEHtj9CHHOdu37JyyjU4cY23G5L0syjwiM=",
      "url": "_content/bnred.Shared/font-awesome/css/fontawesome.css"
    },
    {
      "hash": "sha256-hjq1Cjn8IDyo9hTO8UxsxwDuZL/qzUFCbc6e+MvZhQk=",
      "url": "_content/bnred.Shared/font-awesome/css/fontawesome.min.css"
    },
    {
      "hash": "sha256-d1hF6Zf2SffgC/TLwZh87fTT0BnPAenrxisXYP80PC4=",
      "url": "_content/bnred.Shared/font-awesome/css/regular.css"
    },
    {
      "hash": "sha256-fQbAY/eWwRfEFonJun0jdIopX5FSX7L/0Ke0e9Qopgw=",
      "url": "_content/bnred.Shared/font-awesome/css/regular.min.css"
    },
    {
      "hash": "sha256-pJNuqWMegUT4By6AZkdeILvY929AOF8osVsGsL868D4=",
      "url": "_content/bnred.Shared/font-awesome/css/solid.css"
    },
    {
      "hash": "sha256-el0hjI5A3DO5oMJ7ScKl0MlpbqU+5jcYgtNIoxEWrps=",
      "url": "_content/bnred.Shared/font-awesome/css/solid.min.css"
    },
    {
      "hash": "sha256-VtZ/2v8kywPjlkX4gn16zRwk+4r/9GPWMjFaf5csR/c=",
      "url": "_content/bnred.Shared/font-awesome/css/svg-with-js.css"
    },
    {
      "hash": "sha256-b5ReHVhq5faSFK/mRgLgLCSopzQYfsTXTSgT1Ugwd/c=",
      "url": "_content/bnred.Shared/font-awesome/css/svg-with-js.min.css"
    },
    {
      "hash": "sha256-ruEbEFtpO7Sh564p632nm0zJCvJMH9XetFIL2FOQY+M=",
      "url": "_content/bnred.Shared/font-awesome/css/v4-font-face.css"
    },
    {
      "hash": "sha256-Yb0fYtZssjFGBNrMLay89fIO6i0WrmdCdrzn+ENzBBY=",
      "url": "_content/bnred.Shared/font-awesome/css/v4-font-face.min.css"
    },
    {
      "hash": "sha256-18fh1PCsJWQcM8GES11ayrRrgZR0toOV+yGO7F6oz6E=",
      "url": "_content/bnred.Shared/font-awesome/css/v4-shims.css"
    },
    {
      "hash": "sha256-MCxjgA83JRG1xZgc5l8J3cmSZhmiirfI69I9CxICNqI=",
      "url": "_content/bnred.Shared/font-awesome/css/v4-shims.min.css"
    },
    {
      "hash": "sha256-aPG8E0pZTZGkK6jUXW4Zu+M8lx0DJ56CdV1UeCPIEj4=",
      "url": "_content/bnred.Shared/font-awesome/css/v5-font-face.css"
    },
    {
      "hash": "sha256-5mes899mWORHlgqSkCOWcrrkBXxqYxaMCMyNo/CIxKk=",
      "url": "_content/bnred.Shared/font-awesome/css/v5-font-face.min.css"
    },
    {
      "hash": "sha256-x64301uO3TIvXIW49U+Mt29ehEZYGuINpefjqb3ukAs=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.ttf"
    },
    {
      "hash": "sha256-P+iQ0Ijs8MybwbkGkgHlKXLbrWI3hlUkCQ4VmC0N5xg=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.woff2"
    },
    {
      "hash": "sha256-/cH3UzAhEX2HaXcV6sJro9Tu+SJcfu27+xsNDP/wq4w=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.ttf"
    },
    {
      "hash": "sha256-/mnZSEFGLTl/rv8lPuCabceUG+kx+UKlXmud7487BI0=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.woff2"
    },
    {
      "hash": "sha256-bVPHBvO3F4zPyCyU0/x5aIKNrNNRcX8nx1emXP2FMUc=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.ttf"
    },
    {
      "hash": "sha256-0nvHUhBcB5+KUW6RQkBqn8Esu0Cfm/hoHy3f4DYLUqY=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.woff2"
    },
    {
      "hash": "sha256-TXPygFlwFK6JInDO4XdROtMplV3pGtnSpFofV2CjL/o=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.ttf"
    },
    {
      "hash": "sha256-fRws5fNOGzVnU26NMDnGw0+CV3QpgsX/WltHqC5aZ9c=",
      "url": "_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.woff2"
    },
    {
      "hash": "sha256-nkiYuYvxXxcrsIhJnF4y434nBiz1EZPu4tE+M+1anzg=",
      "url": "_content/bnred.Shared/font/iconfont.css"
    },
    {
      "hash": "sha256-/7vu+wVSf4reckLRXX8CYRYjPzRPFPd8JyHOkm4YNho=",
      "url": "_content/bnred.Shared/font/iconfont.eot"
    },
    {
      "hash": "sha256-GRxT7ZuwCXa9u2E9jpB9ECmuC7ivHlmUoAbaq7ynVNw=",
      "url": "_content/bnred.Shared/font/iconfont.svg"
    },
    {
      "hash": "sha256-i/CtAtSy/rFJOv277RKJP8w65R62HtX+2Ushr2C+Hcg=",
      "url": "_content/bnred.Shared/font/iconfont.ttf"
    },
    {
      "hash": "sha256-MJ/NlenzDqZuMQ18ukUcRSPI0smYQk81K6O5SFY7bjU=",
      "url": "_content/bnred.Shared/font/iconfont.woff"
    },
    {
      "hash": "sha256-WKyOS84CKFwiXYL8GvtqMpwH0FD+8bVD2JOOGUZ8uco=",
      "url": "_content/bnred.Shared/font/iconfont.woff2"
    },
    {
      "hash": "sha256-1klqUsG3cqNU+QcVriw7Y9NIlHmSvJRuR6KCibgVjq4=",
      "url": "_content/bnred.Shared/images/logo.png"
    },
    {
      "hash": "sha256-/2asTqMX0qDknWi7/CqjeUJ7Ba3XJlPwKyjhM6ywnD4=",
      "url": "_content/bnred.Shared/js/common.js"
    },
    {
      "hash": "sha256-qfaeBfYrjX83khXiIEVeYtcAXv6uGRZYHyUqIjcauvo=",
      "url": "_framework/Aliyun.OSS.Core.t7l5cc07ca.wasm"
    },
    {
      "hash": "sha256-cQyh2VXLObWcWT4DMebhILyvQTQYLQG0wN7/OwDJX5g=",
      "url": "_framework/Azure.Core.jvicd3vwq9.wasm"
    },
    {
      "hash": "sha256-Z4d2gC7rBBgpCvtxUFKuedytnRnWY92gC2dLlsuh+xQ=",
      "url": "_framework/Azure.Identity.fy1anm9tzc.wasm"
    },
    {
      "hash": "sha256-fCppiYiDgQ7uV47N1hGQLnysFuS++geS390HPZDRYik=",
      "url": "_framework/BootstrapBlazor.Chart.gxtwmsrcg4.wasm"
    },
    {
      "hash": "sha256-qpS7BxhQveFIk4syx9BPYpgqBlhDEWv8TNHGf6lWcX0=",
      "url": "_framework/BootstrapBlazor.Markdown.ymbr5bvw02.wasm"
    },
    {
      "hash": "sha256-Vq42ITD7OONZD4W4oO6Ol/MlXebrKj0f4OF5IRrZClU=",
      "url": "_framework/BootstrapBlazor.SummerNote.mkf66tgnrw.wasm"
    },
    {
      "hash": "sha256-EDhmXblOByFGPHua04qTpVcphUPUpjjW15bN1TuXQiQ=",
      "url": "_framework/BootstrapBlazor.sxxpwcvk08.wasm"
    },
    {
      "hash": "sha256-P1cy6BLH6OVFDdKgxGUDd7pn0S0lCMsEeaUzQx5RFHs=",
      "url": "_framework/DistributedLock.Core.3zloyo5nf2.wasm"
    },
    {
      "hash": "sha256-Q+aSG/XwOpV9DyMyhE3d5MC9Sves+A3Tz02qxqLWiD4=",
      "url": "_framework/Elsa.Abstractions.flk49jt0mt.wasm"
    },
    {
      "hash": "sha256-YBPF3BHuzcuCVh9EC7IscZ2NjblPvy6t7+vYTcV0wTQ=",
      "url": "_framework/Elsa.Server.Core.w08cxyb8re.wasm"
    },
    {
      "hash": "sha256-spKLKpnpdwH+c7O+msjQQGwBBaXPXEVOVBWJiRCMaD4=",
      "url": "_framework/Fare.2zaj7m3k7u.wasm"
    },
    {
      "hash": "sha256-dVcUWknBJFjbYnQ0+cYyrVzr7SEMj1PcsVhYW/Upgmk=",
      "url": "_framework/ICSharpCode.SharpZipLib.zu3cmhprh8.wasm"
    },
    {
      "hash": "sha256-WnVuFkkw3WRDRWZqxnkpyu46aLzNFaX4QZW0L+rKUgM=",
      "url": "_framework/LinqKit.Core.qw81e1uhqz.wasm"
    },
    {
      "hash": "sha256-/db+nahzjeSd5EhxUqFrpFzJ998Up6Vgs1PHrV3jqkU=",
      "url": "_framework/MediatR.Contracts.f5sa8x1lz4.wasm"
    },
    {
      "hash": "sha256-93lYFBceclWDKFtONDoD9sfV7s0CiF+jqb4dIKC5Tho=",
      "url": "_framework/MediatR.lxpzgmne4p.wasm"
    },
    {
      "hash": "sha256-w5s/dAqt94v/nRfrBYI6V2v2cVs3QcwH9g67NuNDKI4=",
      "url": "_framework/Microsoft.AspNetCore.Authorization.1n8uphcmzt.wasm"
    },
    {
      "hash": "sha256-EFv+L1YnGvKwdvpSUAgxwhBJoxTLebypp66mmnzNKr4=",
      "url": "_framework/Microsoft.AspNetCore.Components.Authorization.kvcvutmuvo.wasm"
    },
    {
      "hash": "sha256-qXTiQR2ldnY3SA5wBUTo1cO6EG38EQ5yBkhdxSeT8J8=",
      "url": "_framework/Microsoft.AspNetCore.Components.Forms.hi92sn41c6.wasm"
    },
    {
      "hash": "sha256-WoHuxqOA/YjR3LnNX4t+GU6Gkb5+qVm204ailFTYJXA=",
      "url": "_framework/Microsoft.AspNetCore.Components.Web.e21d4xlhof.wasm"
    },
    {
      "hash": "sha256-MfJvwVqFhOyDaEGOyNxWfiAfa/ewRnueSODBzu8KuXA=",
      "url": "_framework/Microsoft.AspNetCore.Components.WebAssembly.v0robp45fa.wasm"
    },
    {
      "hash": "sha256-/lTMXvVAAKAFUdtyEtGDtqwRJcVd2m3HCe4p0K5Bzf8=",
      "url": "_framework/Microsoft.AspNetCore.Components.ugevqsv34c.wasm"
    },
    {
      "hash": "sha256-4oG8k3JRNkZNQ03eBGdc3Gpnt9UbTGNzfOZRqBKAHYA=",
      "url": "_framework/Microsoft.AspNetCore.Http.637kd11tas.wasm"
    },
    {
      "hash": "sha256-n0jM30XYPDDlywnVH3kArY5DgtWn77UPaIDBhOlrgB4=",
      "url": "_framework/Microsoft.AspNetCore.Http.Abstractions.t1mj9y5crt.wasm"
    },
    {
      "hash": "sha256-kmbLfLRYyDH7oLYNsLLcqhNu+oC/WsRNA4CikdGmpfg=",
      "url": "_framework/Microsoft.AspNetCore.Http.Features.2b62tgpozs.wasm"
    },
    {
      "hash": "sha256-atAcQGael2d3QFPxSQfIMAvup9SJDVdmJ++4UUmhwYQ=",
      "url": "_framework/Microsoft.AspNetCore.Metadata.mtcql6wkx0.wasm"
    },
    {
      "hash": "sha256-9Vwi5y+U3bAXFUwqL1THUAtCsjieKhuH7oRIZQj/4ao=",
      "url": "_framework/Microsoft.AspNetCore.WebUtilities.hxwhssdt5q.wasm"
    },
    {
      "hash": "sha256-uV6JPE8pFJZ3vgxDKUeimvMSk+wGuYyOKWg5xKqP7Qo=",
      "url": "_framework/Microsoft.Bcl.AsyncInterfaces.9wftx6xvg7.wasm"
    },
    {
      "hash": "sha256-sy5yldYD5Hx8ZUmlhDpZat7K/ty1N8K82PyPVohBG9k=",
      "url": "_framework/Microsoft.CSharp.zovhsua4hq.wasm"
    },
    {
      "hash": "sha256-788n92/1qdWDiGgzMY33kyjmfeVBu3UemJLWkOi0A9Q=",
      "url": "_framework/Microsoft.Data.SqlClient.57h4oq0dcp.wasm"
    },
    {
      "hash": "sha256-sBGO06nkYFOPjxHfmDaekEqnQbilBHm56itqVF6wIuo=",
      "url": "_framework/Microsoft.Data.Sqlite.8u5bso86jr.wasm"
    },
    {
      "hash": "sha256-RRqJ9Jzw1nddtq710Cb6ODUsoAr23kzbi7F5Mk9XozY=",
      "url": "_framework/Microsoft.EntityFrameworkCore.Abstractions.hl0lglu7t3.wasm"
    },
    {
      "hash": "sha256-azUUPKwkXoCBiEwt3FVvZB445mYl4/Gw58ChRRPNul0=",
      "url": "_framework/Microsoft.EntityFrameworkCore.InMemory.l4jzvsh4pj.wasm"
    },
    {
      "hash": "sha256-+f54ndqDsZijRoYwnVu8g2bmScb01MFklXnMAjgmcn4=",
      "url": "_framework/Microsoft.EntityFrameworkCore.Relational.rfdwp35wgv.wasm"
    },
    {
      "hash": "sha256-zBckesateZKPBSUzdoKS0aj21I4CW66EIfcrslMtOrk=",
      "url": "_framework/Microsoft.EntityFrameworkCore.SqlServer.wtshot98u7.wasm"
    },
    {
      "hash": "sha256-NE5D7g2XSKxUspDMRUFjS08dSFhRCLvtlTKwSCHGYqQ=",
      "url": "_framework/Microsoft.EntityFrameworkCore.Sqlite.09vkzki6y6.wasm"
    },
    {
      "hash": "sha256-KtwSidIRU4ieMHnzF3g7hqH1RI/dYjsi64a2gmZbToQ=",
      "url": "_framework/Microsoft.EntityFrameworkCore.uuwy9osuhf.wasm"
    },
    {
      "hash": "sha256-ygxx8pKlN4YllvAX2HADU+dKWSOZRMNfSP4nHkPMH+8=",
      "url": "_framework/Microsoft.Extensions.Caching.Abstractions.2ephyk4gcn.wasm"
    },
    {
      "hash": "sha256-K0Qzxs1gptgogO6juGZzfFp3Q7gUtMCJbo7ateym6cw=",
      "url": "_framework/Microsoft.Extensions.Caching.Memory.rwha3ym3zf.wasm"
    },
    {
      "hash": "sha256-UDGEQR7J3WTfzYMgOzxVIBwFQtKEeJvO8UgrWagypdE=",
      "url": "_framework/Microsoft.Extensions.Configuration.4njtqvtvgx.wasm"
    },
    {
      "hash": "sha256-yNdqbqDWGiJo943D7LPak5xryCBEsNH0wtdiuU1R9VE=",
      "url": "_framework/Microsoft.Extensions.Configuration.Abstractions.8kr5d0tjmo.wasm"
    },
    {
      "hash": "sha256-GSjil45tcp9Gt2YUvSdWcCKn7FqCK6B5Pf2C3ImL+bA=",
      "url": "_framework/Microsoft.Extensions.Configuration.Binder.pw80fn1fwq.wasm"
    },
    {
      "hash": "sha256-PGrjsG4o5zgWnfcyHa0yt7fC2pxo89CDKVLiEWDyAy4=",
      "url": "_framework/Microsoft.Extensions.Configuration.EnvironmentVariables.wbxytkwaq1.wasm"
    },
    {
      "hash": "sha256-w4E5uayo1SFMsRc1dLJu4EYYaRQpyP7cvyeJNAqH5+4=",
      "url": "_framework/Microsoft.Extensions.Configuration.FileExtensions.vpmbyydd3g.wasm"
    },
    {
      "hash": "sha256-ViitoeUHw71Xhs0JJ88lGca05eRKDi+p+NyaYc6cRIQ=",
      "url": "_framework/Microsoft.Extensions.Configuration.Json.ua9xbwdl0u.wasm"
    },
    {
      "hash": "sha256-4m5J6D1UjtBYOyimRA7fLpV89WdlUi2OueAnc4PZQSk=",
      "url": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.6btmtlwqmm.wasm"
    },
    {
      "hash": "sha256-NrPYML1ZOnKmz2Y+n4q5EFp+e4a6g47+gH22VofJd2k=",
      "url": "_framework/Microsoft.Extensions.DependencyInjection.y2i1jqa8ys.wasm"
    },
    {
      "hash": "sha256-IyD4KaP+t3H5fGWjmDcAAB0lQmXUDHqEiD+6bzCfwxI=",
      "url": "_framework/Microsoft.Extensions.DependencyModel.lqmfzbfzuh.wasm"
    },
    {
      "hash": "sha256-zZcH54DRmcuIAi/NQWV5AKf4l8zFSHQ5r+ZK4gGwnrY=",
      "url": "_framework/Microsoft.Extensions.Diagnostics.Abstractions.f96etlqg03.wasm"
    },
    {
      "hash": "sha256-ocjYvUYaqVp/zqoZgO2Jz9xDvZzLM7qR2qkAidM5DsU=",
      "url": "_framework/Microsoft.Extensions.Diagnostics.xg83zizj0a.wasm"
    },
    {
      "hash": "sha256-IUopixuV8E09mP+TIgRp/lXEbeacXbUTW7cu0v5B9Tc=",
      "url": "_framework/Microsoft.Extensions.FileProviders.Abstractions.1c7ksbormu.wasm"
    },
    {
      "hash": "sha256-MS+zB0xkKhKk/QdE32ZwKtdlrLMLI/y2NAKPUWhcBVg=",
      "url": "_framework/Microsoft.Extensions.FileProviders.Physical.rpvltkbyzt.wasm"
    },
    {
      "hash": "sha256-AnWUKd0qJ8ZNKkRC0AFK2cjjszB7reXiP+bXdIi8bic=",
      "url": "_framework/Microsoft.Extensions.FileSystemGlobbing.i464dwxnbb.wasm"
    },
    {
      "hash": "sha256-LeMuXUPH27W0BnNS9gH6zyrwvrJbcOtRnlNR4NzG+E8=",
      "url": "_framework/Microsoft.Extensions.Hosting.Abstractions.7ja2f7db4g.wasm"
    },
    {
      "hash": "sha256-EOHR/vTMOWbdshFAa+n27+jPgCvrczn8XGFLj2P06UI=",
      "url": "_framework/Microsoft.Extensions.Http.8u7hd4iwzw.wasm"
    },
    {
      "hash": "sha256-N6ZBgmVtKuTLyVca1QduGSfIrtLeu8VDZTMjHhRoW0Q=",
      "url": "_framework/Microsoft.Extensions.Localization.Abstractions.xe3ex8nc4u.wasm"
    },
    {
      "hash": "sha256-csBUd4QuytZ4vFjfWtJaYptncxX/miKR5lPhAB9gefg=",
      "url": "_framework/Microsoft.Extensions.Localization.qab9fk66hy.wasm"
    },
    {
      "hash": "sha256-Rx+UwnmyfTtMmlYd3HWJOvYVnSSwShUKrbPvqkfxmZk=",
      "url": "_framework/Microsoft.Extensions.Logging.78i0zw4gi3.wasm"
    },
    {
      "hash": "sha256-AL42sVLgQf/nX/uHvd/OHfIFwLq3fRt9qn4/jnfJ2CM=",
      "url": "_framework/Microsoft.Extensions.Logging.Abstractions.sxonw1u7mz.wasm"
    },
    {
      "hash": "sha256-OmLzArB+/ChfPSCclC9jPl8ju8VNzfdPjN/Y7GRniYA=",
      "url": "_framework/Microsoft.Extensions.Logging.Configuration.a0nn62srts.wasm"
    },
    {
      "hash": "sha256-heZX+Vu0EmnbTC7BWtu7WX1KdI77rAwS9LHYk12NayQ=",
      "url": "_framework/Microsoft.Extensions.Logging.Console.32z82tewvk.wasm"
    },
    {
      "hash": "sha256-2kThxFvxI+s6LmZGIMB1/gEYeNp3qo2o3zPl/6OvVhE=",
      "url": "_framework/Microsoft.Extensions.Logging.Debug.ejywa1axmv.wasm"
    },
    {
      "hash": "sha256-QZetqxYvi3nkXhozj7xV7zhL/fVafugPNHYk3elafLk=",
      "url": "_framework/Microsoft.Extensions.ObjectPool.339flyhalg.wasm"
    },
    {
      "hash": "sha256-C8ZA1VhhrMRPf8Qqv+OrW9za3ggJkCM9i1d8R/F1ETc=",
      "url": "_framework/Microsoft.Extensions.Options.ConfigurationExtensions.zyudcniekx.wasm"
    },
    {
      "hash": "sha256-5/m+yVFGRuY+N4jQnD+QETKH0AfhAsnVze5dJ5ogIVM=",
      "url": "_framework/Microsoft.Extensions.Options.jt8xzja2dj.wasm"
    },
    {
      "hash": "sha256-pVrYOTfjb2ITls3LKIByW1t8jwOAWFhmkIVDewtJ1GE=",
      "url": "_framework/Microsoft.Extensions.Primitives.lsakbjp1fg.wasm"
    },
    {
      "hash": "sha256-EaVF5zqGpfwxtkGu2AP/m5Q+WsqRXKvcYic7VRNGE5k=",
      "url": "_framework/Microsoft.Identity.Client.9qmsbm4g5e.wasm"
    },
    {
      "hash": "sha256-pv7S5E6yz8r/hXqu9DZNx/H2NAbzjDAkCzbWufoGD10=",
      "url": "_framework/Microsoft.Identity.Client.Extensions.Msal.qoq5jusks9.wasm"
    },
    {
      "hash": "sha256-zjEXw82eqpT6mBpJoKGq7h9owR8a9SXQEKdXAtxbxCA=",
      "url": "_framework/Microsoft.IdentityModel.Abstractions.ywtvda1e4c.wasm"
    },
    {
      "hash": "sha256-aW/J4yAm7Dnvw37F1GKWotc8DDnHtk+03sCjNtwD5dY=",
      "url": "_framework/Microsoft.IdentityModel.JsonWebTokens.39lcez2l12.wasm"
    },
    {
      "hash": "sha256-oDhSS9/aT0sejLJOBaAMY6Vj6YyGPXkm1pUrYprrbbA=",
      "url": "_framework/Microsoft.IdentityModel.Logging.8ithpgtr9z.wasm"
    },
    {
      "hash": "sha256-RgOe7J9L12oMsSaprXN4sC9zXbwXOeqduQY0fz5J0tA=",
      "url": "_framework/Microsoft.IdentityModel.Protocols.OpenIdConnect.eu4njaflvc.wasm"
    },
    {
      "hash": "sha256-qCZWN08Qq6E9fBApMutJZBSq1N7wWMKLsRqrfOtBd74=",
      "url": "_framework/Microsoft.IdentityModel.Protocols.sb86v23jm0.wasm"
    },
    {
      "hash": "sha256-aRkBevAl9X4Lsxqf6yFoTtx1W3wtpcifUVrVNu6ju70=",
      "url": "_framework/Microsoft.IdentityModel.Tokens.lpd4jqitqf.wasm"
    },
    {
      "hash": "sha256-cfPGjpEkdK37Tb2wyidoKNUsN3Og6k2NPfBW2z4kbB0=",
      "url": "_framework/Microsoft.JSInterop.3zy6yl0pdf.wasm"
    },
    {
      "hash": "sha256-8ZmspJkZmC3f4GMqPqk6W5CyBwEQmFOQfvB6+C/wXTI=",
      "url": "_framework/Microsoft.JSInterop.WebAssembly.negcs7bfj9.wasm"
    },
    {
      "hash": "sha256-frX5wFfUn8A5v6EMtOZdr3nwDsA3selu7FFE8a5CTWc=",
      "url": "_framework/Microsoft.Net.Http.Headers.uospsr3d4k.wasm"
    },
    {
      "hash": "sha256-Fig+5hq00gGQlXAgSnyFlUlWyhlx9f+yPJb4INt3gNc=",
      "url": "_framework/Microsoft.SqlServer.Server.yamodpu5qp.wasm"
    },
    {
      "hash": "sha256-uhOXvZ3qn4+UKnS5UwyF0rwxQaJZ56vKVYGMP3i7LWc=",
      "url": "_framework/Microsoft.VisualBasic.Core.m4iy4qjs8g.wasm"
    },
    {
      "hash": "sha256-wKsJe36CN9gdroFlia57BFnoIUCJC1lCjFJxYddWNMc=",
      "url": "_framework/Microsoft.VisualBasic.gcchvdkxdm.wasm"
    },
    {
      "hash": "sha256-KAdJGogYp1ANXBeVGdAV3n2skmmrmlkLAO9Tbhchm6M=",
      "url": "_framework/Microsoft.Win32.Primitives.weh8h3644m.wasm"
    },
    {
      "hash": "sha256-VkIgbLH2GUkEvvGRyGuPaHi4PAPsCT9F+HgCFXjX+20=",
      "url": "_framework/Microsoft.Win32.Registry.a32fqwbg5a.wasm"
    },
    {
      "hash": "sha256-1kUoFxSGzlbndQM4zal3G/YBdouUTf444lDjqhuWALU=",
      "url": "_framework/Microsoft.Win32.SystemEvents.asbyu30enw.wasm"
    },
    {
      "hash": "sha256-0h+KH44W9o5UmKhDIEPBC8zqVN/tYWRZU8WLwHGg2oI=",
      "url": "_framework/MySqlConnector.qqh4to9bka.wasm"
    },
    {
      "hash": "sha256-Khqs9AZ3nYwsNZdIGM1iLWcd9EXc0SQEA3W9ySCZUoM=",
      "url": "_framework/NPOI.2yeezd88sg.wasm"
    },
    {
      "hash": "sha256-xYauApSIEnZM3KRF+2/GHtMphPhN/Wsp8qUmlnGke6A=",
      "url": "_framework/NPOI.OOXML.1pw17hm2hn.wasm"
    },
    {
      "hash": "sha256-Arb6buvfKpV/BvUGvbqNMkr89g4MQd/stCYrFPd6zLQ=",
      "url": "_framework/NPOI.OpenXml4Net.ux8vztq15d.wasm"
    },
    {
      "hash": "sha256-Cf3B5xIwAa5N/hDV/pJVTyN2XVsxDLVoyVp/qEHk61Y=",
      "url": "_framework/NPOI.OpenXmlFormats.hu8xu9zevd.wasm"
    },
    {
      "hash": "sha256-GlXMWKvDs45M2pACoR3Y4Qh8mcrOZGljqmvJY+6JZ5s=",
      "url": "_framework/Newtonsoft.Json.qkbufwhni2.wasm"
    },
    {
      "hash": "sha256-rFn4snzcy7dSYxI8gloVlx9xMdsckXDQw5YnrmD0geE=",
      "url": "_framework/NodaTime.Serialization.JsonNet.ki1kpfcsab.wasm"
    },
    {
      "hash": "sha256-NFTWlfZnrA50+KBbX5bzwTZtoU6oGLxofQQ7ZSvekiA=",
      "url": "_framework/NodaTime.si9slch59e.wasm"
    },
    {
      "hash": "sha256-xECjRvvegO9+3hFyhwJqEn6VbiXfzu6rgbNUD2KBtqs=",
      "url": "_framework/Npgsql.447n1btx5d.wasm"
    },
    {
      "hash": "sha256-4EFji3iL6oqbN189ENrR2ssaNeM+HRsBrdRben7OyXY=",
      "url": "_framework/Npgsql.EntityFrameworkCore.PostgreSQL.wggn7d50ri.wasm"
    },
    {
      "hash": "sha256-RKCpkiv/HWH+2qMJZkQCh4bKjGArf6xbZT4E2tVsI84=",
      "url": "_framework/Open.Linq.AsyncExtensions.4y7miv2hnq.wasm"
    },
    {
      "hash": "sha256-Jx9rkwQk9zWbBOJXKHvmp1vrM7+CxU7f/U/4jQUGKQ0=",
      "url": "_framework/Oracle.EntityFrameworkCore.1290n74i0s.wasm"
    },
    {
      "hash": "sha256-jxNHpNyNYD0ok+ZzG4TReimPh2ajtqtHDYhRgm+ikbw=",
      "url": "_framework/Oracle.ManagedDataAccess.j78c3j58q8.wasm"
    },
    {
      "hash": "sha256-yTaC4yBuzRNADxT9XSih4vaMlXSDkYk4zdqzIf0q1Vw=",
      "url": "_framework/Pomelo.EntityFrameworkCore.MySql.dvida7d0u7.wasm"
    },
    {
      "hash": "sha256-m/J2z4SKICQsHuaE94XxQUmhq767ZwxvmcgcegMpsII=",
      "url": "_framework/Quartz.3y1zad6cq5.wasm"
    },
    {
      "hash": "sha256-bJGR3vfl85iq9nZIdKTJJIq4vKoqg9IQIfIPN7ychrA=",
      "url": "_framework/Rebus.w1t62hrba1.wasm"
    },
    {
      "hash": "sha256-AymqXfJmredfnqn6PxQs3nqqNoMXiBDEqaT3b0605F8=",
      "url": "_framework/SQLitePCLRaw.batteries_v2.7nwzpkdtm1.wasm"
    },
    {
      "hash": "sha256-5ncp/PNVntosOHmUwpvRVJZvvreYP08nlkqD9XgBkdA=",
      "url": "_framework/SQLitePCLRaw.core.artp1p3dts.wasm"
    },
    {
      "hash": "sha256-3TKRNpWG681LxI/l31HI8LuIjsCjG6XJ9dIEXu3m1zY=",
      "url": "_framework/SQLitePCLRaw.provider.e_sqlite3.5eqxv4u51m.wasm"
    },
    {
      "hash": "sha256-Fhge5J/nIBNQJODjTUlDWQqC2Mj6g307rQWTOdABqKs=",
      "url": "_framework/System.AppContext.uyzczx3u46.wasm"
    },
    {
      "hash": "sha256-jGP+cnqPRJYMskRWlNkHYMJ2uAD1PXvRFknHSFhRjcQ=",
      "url": "_framework/System.Buffers.gb72qmeqj8.wasm"
    },
    {
      "hash": "sha256-lpC5FJM3Un+A43tcLaHA8sbZEe0+1ZpXIkX2NPzb3ic=",
      "url": "_framework/System.Collections.Concurrent.aos1w28dak.wasm"
    },
    {
      "hash": "sha256-+ddbWwZ2/2sQuEbUeZtjeV/vb2+EmgGAWsDfWWlmr80=",
      "url": "_framework/System.Collections.Immutable.haoci47itp.wasm"
    },
    {
      "hash": "sha256-pFPwW2HyhDhWqf9ivgCxujBOdBsecSz/sDqxGuX26W0=",
      "url": "_framework/System.Collections.NonGeneric.ov3c11r7df.wasm"
    },
    {
      "hash": "sha256-HWLavIf2HNu3fMwEfRHSepIWc08ufs9/ESC4NpnpbDE=",
      "url": "_framework/System.Collections.Specialized.7r96r3kwk1.wasm"
    },
    {
      "hash": "sha256-HNYph/SSZDEMqnAw49QVoUZg2aN7pDLttRYTh/D1osc=",
      "url": "_framework/System.Collections.cgl25682z4.wasm"
    },
    {
      "hash": "sha256-ZOOhK1wFZkqWVLly3xUEBXcn/WDpOz52G2EceWepT6M=",
      "url": "_framework/System.ComponentModel.4qr1uf2xo0.wasm"
    },
    {
      "hash": "sha256-pMnJS1uZcJCEWzuffPMTzufUHiD+YGSW4TVkH7Ep6V8=",
      "url": "_framework/System.ComponentModel.Annotations.ckge75x1cm.wasm"
    },
    {
      "hash": "sha256-mIkpUSJssUXYSilIviXMGXMilF1EPfSnEV+6e17mTog=",
      "url": "_framework/System.ComponentModel.DataAnnotations.0a4vr959d1.wasm"
    },
    {
      "hash": "sha256-X6xDVOHy7/KL1yZ5+UmatueIazHrd7fg/zG6duqygLM=",
      "url": "_framework/System.ComponentModel.EventBasedAsync.thm3r1qmen.wasm"
    },
    {
      "hash": "sha256-qJKBi0WIe5FLpqVIgQIiPEVXIqlw7OaZzepEIyW3s70=",
      "url": "_framework/System.ComponentModel.Primitives.k9taspcy5h.wasm"
    },
    {
      "hash": "sha256-amDxVJ7oLVlKARtDkAC15REiIv29jMk+w+QH2fAzwhw=",
      "url": "_framework/System.ComponentModel.TypeConverter.ie2hye23kf.wasm"
    },
    {
      "hash": "sha256-rhhkfEkjrq0K08oGH1U2AAyj2j11qtEhMGzXZjbV+uY=",
      "url": "_framework/System.Configuration.27zm9566sv.wasm"
    },
    {
      "hash": "sha256-+PLYdcVZv3KxeaVwz0s4UYBHOGGP8xo3I8tCkSLGWjU=",
      "url": "_framework/System.Configuration.ConfigurationManager.okmrelmv0a.wasm"
    },
    {
      "hash": "sha256-LcI5lR7+FFZGiW2Qs8Ikgrepv2AZ+9MIXu9gBC+/62E=",
      "url": "_framework/System.Console.h3r09gl4vt.wasm"
    },
    {
      "hash": "sha256-76MUeyB9VoHVt7UTXfDcKS/H93O/MkiuKbru6z5Cn/k=",
      "url": "_framework/System.Core.hsttoe530p.wasm"
    },
    {
      "hash": "sha256-avotTlPdFNfkTPIFn/88RMBJwSkX42AICoCjUYe1B90=",
      "url": "_framework/System.Data.Common.6si2a0nqry.wasm"
    },
    {
      "hash": "sha256-YyfvRaDHJTqHlV7R2Z2URNsYtMrOHYjynnWis6oVsjU=",
      "url": "_framework/System.Data.DataSetExtensions.d8vm961ka5.wasm"
    },
    {
      "hash": "sha256-vu+xmeEili1dYql1w5F1sKnVVq7cQOtOE3FzGCnYVp4=",
      "url": "_framework/System.Data.merwdkqcmo.wasm"
    },
    {
      "hash": "sha256-GIIW2HNY1pjcz8G+bU28nTC1eyTWzk7GjR1yuu76C3Q=",
      "url": "_framework/System.Diagnostics.Contracts.w6z74jygrt.wasm"
    },
    {
      "hash": "sha256-917MdJ2EdP+qVOVs+kpHOBaBXl3ozwsGnVnlukZhGh0=",
      "url": "_framework/System.Diagnostics.Debug.d7gsc5690v.wasm"
    },
    {
      "hash": "sha256-N9iVmPv0Kclt3ZvqHwFbtrOj3dzrc8SYi6pQNCeKtc8=",
      "url": "_framework/System.Diagnostics.DiagnosticSource.zdk8f4csh4.wasm"
    },
    {
      "hash": "sha256-AR/68AahnEwHZ+ok9OQREXDpxOa4Cs8AdSKIlj9Ee9k=",
      "url": "_framework/System.Diagnostics.FileVersionInfo.dx9fb95hwm.wasm"
    },
    {
      "hash": "sha256-ZXV+mDplF0cF5dfnV2BJxoSNU3YMoC1tUKOs4Zlyr+A=",
      "url": "_framework/System.Diagnostics.PerformanceCounter.dip51t3o12.wasm"
    },
    {
      "hash": "sha256-8SS69AxUlX/AU557/S7YJZmY8NMjJSGJZ23wHHBzwDU=",
      "url": "_framework/System.Diagnostics.Process.nm6e2slhgg.wasm"
    },
    {
      "hash": "sha256-KY4tPVMicGmiJasZGo/XpWdHOjS12LWyyiPRs4MwaGQ=",
      "url": "_framework/System.Diagnostics.StackTrace.bjm35mqokl.wasm"
    },
    {
      "hash": "sha256-39DcrCx4FwPD1Rr/7ct+uPy9e3WThi3Dm1aq5T+JkpM=",
      "url": "_framework/System.Diagnostics.TextWriterTraceListener.thuf78ea6u.wasm"
    },
    {
      "hash": "sha256-lxCTBETPUhpUneoXO+GdwouBpkwWYIxmTMM9pp61KYk=",
      "url": "_framework/System.Diagnostics.Tools.zxt8non64j.wasm"
    },
    {
      "hash": "sha256-e79tgIiDZsYRlOlle9gC9hNH6+2eCBcRziDDk5KMiAo=",
      "url": "_framework/System.Diagnostics.TraceSource.v76sxw396t.wasm"
    },
    {
      "hash": "sha256-/fvbF8r2Yy8aZzh9c7hWCw5HZrkvsY/nu7F5Eq39vV8=",
      "url": "_framework/System.Diagnostics.Tracing.pt5yage1o4.wasm"
    },
    {
      "hash": "sha256-Bd5/CQdIBKl8uLmNvpykztE3OnxLi5IdUO6poGN5M5Y=",
      "url": "_framework/System.DirectoryServices.1p0mdd719e.wasm"
    },
    {
      "hash": "sha256-1I81dMVLqCq+Qv2pItcR3PLMbGIsIMeGn+0hBkQ6p7M=",
      "url": "_framework/System.DirectoryServices.Protocols.8lfxnz0b40.wasm"
    },
    {
      "hash": "sha256-K4kz7R8w/w1hXO31g3GdLNqPgu4KDENnDuMEnMfDDnc=",
      "url": "_framework/System.Drawing.Common.3y29mvytmn.wasm"
    },
    {
      "hash": "sha256-8Qeg2xIuoctsklCUNO+wz1hyIlsnBp/gxixyVEABdzw=",
      "url": "_framework/System.Drawing.Primitives.5f1v7y0rwb.wasm"
    },
    {
      "hash": "sha256-93TvgPvAxo193ngcPR8Fw9dyEM9JtgLv0pcpLVjY5fM=",
      "url": "_framework/System.Drawing.zptejx9h1x.wasm"
    },
    {
      "hash": "sha256-zyYu/PrJeLK2NlTeh20784zyo8SR3QyyJRv0jH5iWh8=",
      "url": "_framework/System.Dynamic.Runtime.9mhkzkfqxe.wasm"
    },
    {
      "hash": "sha256-/aTplf1Mqyvhoz6DlIl1WzotJuEqB0hwCI2zRmy5Xb0=",
      "url": "_framework/System.Formats.Asn1.fd28xzv6af.wasm"
    },
    {
      "hash": "sha256-LW0EJJCRAAtoy/ckHhusxZLE+RZ2zIKuTmOmsuZ1D34=",
      "url": "_framework/System.Formats.Tar.1zsubplx4d.wasm"
    },
    {
      "hash": "sha256-6Z1WPdkIDHZHLEh6Vg5VvT8CGaOCBEltSbFeMAoCYik=",
      "url": "_framework/System.Globalization.Calendars.9cby8evhb7.wasm"
    },
    {
      "hash": "sha256-6X2NOkwNwvDNXaHvxsiA7Q4KclB4fbFbW/1eGR5ROog=",
      "url": "_framework/System.Globalization.Extensions.rzt1w5alqv.wasm"
    },
    {
      "hash": "sha256-H+4G4xJXmCNZE3A4A9sD2hjBuXTAaNnquy4S+zxjl40=",
      "url": "_framework/System.Globalization.vc54yav9j7.wasm"
    },
    {
      "hash": "sha256-Ff9kGfc4demTYo8go+UJL0G/T/uV1/Jk/ougDv3Ghm4=",
      "url": "_framework/System.IO.Compression.Brotli.f76uea1xvc.wasm"
    },
    {
      "hash": "sha256-MJvRFO7RV0PRr2I23hxOXIahax65dHxNpa3sbe5mpDA=",
      "url": "_framework/System.IO.Compression.FileSystem.goa7fld2x2.wasm"
    },
    {
      "hash": "sha256-qPQeFAvclkZppk70tz6H4/Dig6Wfeak94to9g9wTUMw=",
      "url": "_framework/System.IO.Compression.ZipFile.svj8ec60o4.wasm"
    },
    {
      "hash": "sha256-YNorTu0lyBmTyG1gOOxGLskQvWLMyS1r0p6A12LQUfY=",
      "url": "_framework/System.IO.Compression.o6ju5pwdpc.wasm"
    },
    {
      "hash": "sha256-sGlm94KfgROWZ5u9kzR6IjheoHLssV50cgbC2QFps0g=",
      "url": "_framework/System.IO.FileSystem.AccessControl.knoyd66m4v.wasm"
    },
    {
      "hash": "sha256-buQAWwCfcU+s1xEfVYXC13iOslwim6bGz3sQx6yBqhg=",
      "url": "_framework/System.IO.FileSystem.DriveInfo.uhxsnjyfd0.wasm"
    },
    {
      "hash": "sha256-CaZh+HkQ//8ECDBBMFHAOwpZmyBICTueL0rv6A91xQ8=",
      "url": "_framework/System.IO.FileSystem.Primitives.ps8afflsjv.wasm"
    },
    {
      "hash": "sha256-8OlplxyShY41b5HlJj6GD5ym4EcNOctrXD11NmnJhN0=",
      "url": "_framework/System.IO.FileSystem.Watcher.gwei2b41uj.wasm"
    },
    {
      "hash": "sha256-TQT6z9jGhzbWjaN+jrmb7DngCj7/7das6tBmh4Wl1+w=",
      "url": "_framework/System.IO.FileSystem.zovl0unrgn.wasm"
    },
    {
      "hash": "sha256-9AoR1YJZb0gVTdVIkJbhjfyQi5r2mFWy4Uc/RbgW410=",
      "url": "_framework/System.IO.IsolatedStorage.s6cp1kp0sr.wasm"
    },
    {
      "hash": "sha256-oJstnpCNql3JgvWmJ2t8g8yf+IjeffPrKzK66mKO/tA=",
      "url": "_framework/System.IO.MemoryMappedFiles.ouhgi9p8ne.wasm"
    },
    {
      "hash": "sha256-mmpg7/txgxyvPuG0QYAFYH/KrY06fNni+oImDY+9u+M=",
      "url": "_framework/System.IO.Pipelines.azvmy69isl.wasm"
    },
    {
      "hash": "sha256-RRNaQBqINZLpRLvaojO1UklL8/wENahJQmWNXNazfVM=",
      "url": "_framework/System.IO.Pipes.AccessControl.n9i161jx4e.wasm"
    },
    {
      "hash": "sha256-x9uA99h6ue9U9q2YD7cDnwW0ElNgR/nHolKPugpc4Bo=",
      "url": "_framework/System.IO.Pipes.viel7aptuc.wasm"
    },
    {
      "hash": "sha256-RaSGwShc/YaOFoJOAMR4nVf9zPjOCFKKYGYFLGD1k/s=",
      "url": "_framework/System.IO.UnmanagedMemoryStream.vznpee190z.wasm"
    },
    {
      "hash": "sha256-4x6SIT4Q/CblIhzDJnI5u12zahzdZi0KRPYykz1ZkfE=",
      "url": "_framework/System.IO.lay2nsqfv6.wasm"
    },
    {
      "hash": "sha256-v5COmwdTSk+2jCebXcDk4w3eTs8HT3CFIXlUwrv8GdE=",
      "url": "_framework/System.IdentityModel.Tokens.Jwt.x02onqryz4.wasm"
    },
    {
      "hash": "sha256-2Q8JPN55HOHWN2/BC1w3Wgo085PkeDHsie9VMgCMr68=",
      "url": "_framework/System.Linq.Async.3pmhzg6vzq.wasm"
    },
    {
      "hash": "sha256-ya8xaUEjPz2ZMv9rkYPnj/NXKeLAY9Ucc3l3XZhaRcA=",
      "url": "_framework/System.Linq.Expressions.rf0szne3z1.wasm"
    },
    {
      "hash": "sha256-Gv8mw2N0RVTFAEUgIQMm20nxPlYyegTgMKgZkDsKzF0=",
      "url": "_framework/System.Linq.Parallel.2d5j3bvyi4.wasm"
    },
    {
      "hash": "sha256-k9yo6J9GQIJeNvfG3Vjw9BIgZkQUUEK6KruvBBU2Rd8=",
      "url": "_framework/System.Linq.Queryable.fha2j28yuo.wasm"
    },
    {
      "hash": "sha256-s9PVR2xRTvys/vdk5dU1w7B4Xv2c5d2eYUEk/Mnd4vo=",
      "url": "_framework/System.Linq.dsde3npfc5.wasm"
    },
    {
      "hash": "sha256-oa47oEq24c04+SqrF0aDKfadFgOh7up7I5vdcwdrLOw=",
      "url": "_framework/System.Memory.Data.9qfc8r8gag.wasm"
    },
    {
      "hash": "sha256-UrzM+4lD5Zsgc7RVh4K0QSWBTGxth/U/Dsn5lngR60Q=",
      "url": "_framework/System.Memory.mngwkvru1y.wasm"
    },
    {
      "hash": "sha256-2t6Vkd6HQrHCSavPOJKDwY9LkcAJkFEVeMUrWnZa/3M=",
      "url": "_framework/System.Net.Http.6sn75qa9hm.wasm"
    },
    {
      "hash": "sha256-Yb+zFqyUDEIu43PkeuWOkPPVyjYvWv2febFfZU49cV8=",
      "url": "_framework/System.Net.Http.Json.l3uod96fpn.wasm"
    },
    {
      "hash": "sha256-MV9iJqMFP5Y+zlvYQUYdE9oiHpt+NUvhymS85oRNqIs=",
      "url": "_framework/System.Net.HttpListener.lfch2dzlma.wasm"
    },
    {
      "hash": "sha256-8kqfavPBfGsVjvxuk4nGVdCCnfBbINSfyJnRdW3VhlA=",
      "url": "_framework/System.Net.Mail.ig2yuj0spl.wasm"
    },
    {
      "hash": "sha256-iv0B0Bl0IvsEjw4cfFaX4PW+i/uL0SZAxihedhs5gww=",
      "url": "_framework/System.Net.NameResolution.mpnqjoza32.wasm"
    },
    {
      "hash": "sha256-dhGJHYQ3/dga4vt1WHAw9KZ3G6omwpvQvFBax9sIZag=",
      "url": "_framework/System.Net.NetworkInformation.2xxvg1ez8o.wasm"
    },
    {
      "hash": "sha256-Vf6afHl1/gKl5KCsGPbPWD8vtduYKsnERZ8hI8IRGfY=",
      "url": "_framework/System.Net.Ping.ro7n7l6lcg.wasm"
    },
    {
      "hash": "sha256-VpgqsvdYH2yEgwyyBupC2tJI7/XNKSSwiqThzNoNzGM=",
      "url": "_framework/System.Net.Primitives.2a8v8ln5td.wasm"
    },
    {
      "hash": "sha256-TEMMdN69Qk09j2LSQfAPGNjijkb0BcG2MCUe7tvHEnc=",
      "url": "_framework/System.Net.Quic.s4ubipsvtg.wasm"
    },
    {
      "hash": "sha256-WInlfYMvz1+9lhwZ7yx1m1uUXiSG6Lx0D/na37+RXBA=",
      "url": "_framework/System.Net.Requests.4hk8tos0gu.wasm"
    },
    {
      "hash": "sha256-tUDMTcVd7/xrbCaYiGTjLxx8N0crEjr0wmnxQOsXyqg=",
      "url": "_framework/System.Net.Security.dejaoqcpgo.wasm"
    },
    {
      "hash": "sha256-/N++SIUQzcxwE2EpBkc4XNmBczIL72CAgy9osEEQMs4=",
      "url": "_framework/System.Net.ServicePoint.0zpaewrp5z.wasm"
    },
    {
      "hash": "sha256-ddmlUw22I8We10O5elRBvmlvsDlBd1GYX75iIDlVtNs=",
      "url": "_framework/System.Net.Sockets.vf9jkrhmru.wasm"
    },
    {
      "hash": "sha256-M8jcj5mBKl6ymw0wPM/isnwHwQGfiAfU4H3pH8al1go=",
      "url": "_framework/System.Net.WebClient.l2vieztfoi.wasm"
    },
    {
      "hash": "sha256-9fnW/fgSYEbVIUNhwmJivwd5rHeffIhh6GaT/aZkHBE=",
      "url": "_framework/System.Net.WebHeaderCollection.r0r1vmnjer.wasm"
    },
    {
      "hash": "sha256-L6hmcTFbUt6aKdtpD5r6OhyCrDs6x9HIN7KAhb9HYeo=",
      "url": "_framework/System.Net.WebProxy.tlg78s9mw3.wasm"
    },
    {
      "hash": "sha256-g8ZjEEwrQSXqVHQhCvAJHEMlnz9qIjJXEYrRcPLidgw=",
      "url": "_framework/System.Net.WebSockets.Client.hv182fev7c.wasm"
    },
    {
      "hash": "sha256-dBo0+aK2CIvj2zVujOd+VQV9Qgs1q8MeW4+3HTaSjcA=",
      "url": "_framework/System.Net.WebSockets.gz98x4nt25.wasm"
    },
    {
      "hash": "sha256-xNEUxE8ur71HbZGHtHBJ7Dgepk0ZXqqHbrloxyUDNvM=",
      "url": "_framework/System.Net.w9eavbqlj2.wasm"
    },
    {
      "hash": "sha256-Fctg1ziAnVGhImZOCCHskjtR5Tk2JObiCzsIGEVk3AI=",
      "url": "_framework/System.Numerics.Vectors.zqmzbv142i.wasm"
    },
    {
      "hash": "sha256-JO0jYT6KWlWVrGG0sW1WPemTDI9Kiziw1AaPB/61pnU=",
      "url": "_framework/System.Numerics.k2s3uxabyh.wasm"
    },
    {
      "hash": "sha256-gDSVJ7d1WeaCtcp+li4laJt1VUOrTi2w2q94WkPV/0k=",
      "url": "_framework/System.ObjectModel.0wo6os8au8.wasm"
    },
    {
      "hash": "sha256-SvlKQyOSwushlzp2th0dFRtuG039e446mqxz7rmv5XM=",
      "url": "_framework/System.Private.CoreLib.6pgvn7a8kc.wasm"
    },
    {
      "hash": "sha256-WY3qvCKLb4132diMdpomdAyITl0UFbhP2lcLTNdO0Mo=",
      "url": "_framework/System.Private.DataContractSerialization.tbn9n6ix5v.wasm"
    },
    {
      "hash": "sha256-D2AyNVPIxu862qUbhFTsPETdUD1QHou38gcqw8FU6B8=",
      "url": "_framework/System.Private.Uri.zzcx82pbz8.wasm"
    },
    {
      "hash": "sha256-TR0qG/Irh3SliqWg8VES1etx7hNd15I42s8KFayMF+I=",
      "url": "_framework/System.Private.Xml.Linq.jog5zz9w1v.wasm"
    },
    {
      "hash": "sha256-NSSyjmcrPj2wGouU3oUkI2a8Yfl0IidGB9pkdKf0idI=",
      "url": "_framework/System.Private.Xml.zqcpjfk006.wasm"
    },
    {
      "hash": "sha256-oln8TjtEb0sn7k6tXh3yQLR6hoqR51Hel9FpH9Kve7c=",
      "url": "_framework/System.Reflection.DispatchProxy.uhutqc14lw.wasm"
    },
    {
      "hash": "sha256-9dR6ezWyfaDtZhWj7y9PtqjjEcRH6XCGQTTLXkOvrx0=",
      "url": "_framework/System.Reflection.Emit.75ty343nwc.wasm"
    },
    {
      "hash": "sha256-diXKZomLiyPhAIgfDT24x+3/BoUVSXBosl2qmSY3GQY=",
      "url": "_framework/System.Reflection.Emit.ILGeneration.a04lrarvcj.wasm"
    },
    {
      "hash": "sha256-z/8ZU+cYDY2u18R13Qw47hGD+oki7suxI7ENYdFUmAk=",
      "url": "_framework/System.Reflection.Emit.Lightweight.lr1lcyzef1.wasm"
    },
    {
      "hash": "sha256-v5QMsoPh0XJ7fncBJW8cUrbEenUr8AZe2iaS7Kb6Lys=",
      "url": "_framework/System.Reflection.Extensions.zrdc0l1x3v.wasm"
    },
    {
      "hash": "sha256-KPY1ivt2qDyeHHFUk+FUZm1wZrKQb+zdDNZkhTcjJwo=",
      "url": "_framework/System.Reflection.Metadata.ktv7t8t9fp.wasm"
    },
    {
      "hash": "sha256-OrFBDvYPshlQ19uYYVTnPJWqdaG29cnAgwMP7ysxptY=",
      "url": "_framework/System.Reflection.Primitives.un27ocukhl.wasm"
    },
    {
      "hash": "sha256-E5W1q5uphIo7ldb2N2v48b1LvibIXsPQ6TsxenO61ao=",
      "url": "_framework/System.Reflection.TypeExtensions.3cavfw6ggz.wasm"
    },
    {
      "hash": "sha256-2B+kYtP2ETKXR5nJwmfeprNjjPx8mux4UtHY7rjscQs=",
      "url": "_framework/System.Reflection.khrghkngt3.wasm"
    },
    {
      "hash": "sha256-bUt6yVwIbWQmiKYg0iYBV9gGncck5iLnqfhK0D43zKU=",
      "url": "_framework/System.Resources.Reader.hrltcvxb22.wasm"
    },
    {
      "hash": "sha256-uMsLqurWN1wQz012d8Xd5EcAGhgSG+lUIcpvxX1tqX0=",
      "url": "_framework/System.Resources.ResourceManager.0izkjiu2yp.wasm"
    },
    {
      "hash": "sha256-URj9oceFlB6cvX8EHPATKLumT2D618E0q1SOT+S9FSI=",
      "url": "_framework/System.Resources.Writer.7iu1r33mz9.wasm"
    },
    {
      "hash": "sha256-kUsGJ8z0EZBNyez+M+cx/8etwLM4To14595QBe450TQ=",
      "url": "_framework/System.Runtime.Caching.x8ht7u7x3f.wasm"
    },
    {
      "hash": "sha256-OkMT4iOOtKkjvLyK32FqQkEeXnShySJIEOIMn8WsTkE=",
      "url": "_framework/System.Runtime.CompilerServices.Unsafe.mx06pw429a.wasm"
    },
    {
      "hash": "sha256-hiNmQ+i3eHtGBd1771EzrFPD8kZs9e1rHf1GBhWGX6Y=",
      "url": "_framework/System.Runtime.CompilerServices.VisualC.abfgywtr1q.wasm"
    },
    {
      "hash": "sha256-c5wvbSX+kksf804JzMyyJit6mVfvG+Dq6gFGMZ4MLpY=",
      "url": "_framework/System.Runtime.Extensions.vla77p0gdj.wasm"
    },
    {
      "hash": "sha256-WjneZ33oc7gHUv9yC9n3P3uLjbhr8V/oprbFu4d70Mc=",
      "url": "_framework/System.Runtime.Handles.mhaxazpdw0.wasm"
    },
    {
      "hash": "sha256-mZwkJwXYGqrjPQ5fI53s6dHh8mhESeytWrMhkreRxZI=",
      "url": "_framework/System.Runtime.InteropServices.JavaScript.bjvf21z03s.wasm"
    },
    {
      "hash": "sha256-qXFQ+paSbsIdxy0HtA2SwAwquJAyyDhc4QG9rOe3K7A=",
      "url": "_framework/System.Runtime.InteropServices.RuntimeInformation.tzu7xu2jt9.wasm"
    },
    {
      "hash": "sha256-qoS4Hmt4GMWUqdWhM1iQvUysKGtWrAmKDLuhgF1XU6o=",
      "url": "_framework/System.Runtime.InteropServices.q39lm5lg7m.wasm"
    },
    {
      "hash": "sha256-iQuTvQB9Haqk5+BKGF+OGwnaTvBVjrVfS4zZN8jo23A=",
      "url": "_framework/System.Runtime.Intrinsics.jpq5ew51k4.wasm"
    },
    {
      "hash": "sha256-jtiIqiyrDA7xGy/ccONue+Ofj0sE1OtmdbmxMJoS08Q=",
      "url": "_framework/System.Runtime.Loader.aaggt1tf7y.wasm"
    },
    {
      "hash": "sha256-6M/jf9fAred804V/f7lBmeIhH7kNARYJv+Q4YNvKUq8=",
      "url": "_framework/System.Runtime.Numerics.oobvbafpfd.wasm"
    },
    {
      "hash": "sha256-7SWpLefu41lNfej/pJNFUetPmNrQjkPSEPrrdGq5xsA=",
      "url": "_framework/System.Runtime.Serialization.Formatters.x75qheal4m.wasm"
    },
    {
      "hash": "sha256-4rso6RsG7KkfLsUqir2adjtxWw25WVRtbLI8Gz4ddzM=",
      "url": "_framework/System.Runtime.Serialization.Json.udblblravz.wasm"
    },
    {
      "hash": "sha256-E2wL6wCesmpMYAvbY5mbXacuNBIABN1ZKGG1MyyB/SY=",
      "url": "_framework/System.Runtime.Serialization.Primitives.fxua96fx1h.wasm"
    },
    {
      "hash": "sha256-CEg4R5GHw1N4NFjY5FNQFLm0h1EifeY8UGAdWS4jdaI=",
      "url": "_framework/System.Runtime.Serialization.Xml.ow7oak33im.wasm"
    },
    {
      "hash": "sha256-b1+BBP0DAiSbIE4aL63KlMsaIEYQuu2mmHBaXsK3iqw=",
      "url": "_framework/System.Runtime.Serialization.tlczeenl0p.wasm"
    },
    {
      "hash": "sha256-sVM+mMS+tjyoV4glgdH68cWYLqfftJ8rtfbw76QkKo4=",
      "url": "_framework/System.Runtime.rpnxqnm5kp.wasm"
    },
    {
      "hash": "sha256-OoPsIYlwjAjrmYIiQpY5t0dFeI1S4lO9i//cCHPEN4Y=",
      "url": "_framework/System.Security.AccessControl.uiaucge1bp.wasm"
    },
    {
      "hash": "sha256-cRsMPdnMzlZTuuz2pKOnG8dXGNKFBdmW0VYc7GYDkp0=",
      "url": "_framework/System.Security.Claims.dh4jyvgd77.wasm"
    },
    {
      "hash": "sha256-1dKITnspHuvj37yDItDuRWNh46ndTOV8OUbLAi6QSr8=",
      "url": "_framework/System.Security.Cryptography.7l7nzdtevg.wasm"
    },
    {
      "hash": "sha256-E5ttmJ6AXjNNHnybNVkOXWOqmCAu1HGiepbWsPns1/A=",
      "url": "_framework/System.Security.Cryptography.Algorithms.vp3ps84r0f.wasm"
    },
    {
      "hash": "sha256-dY3HXLqrjcvIGWCY1gxkoMTMEYDLYkFVjTxuduIghqk=",
      "url": "_framework/System.Security.Cryptography.Cng.324weailu2.wasm"
    },
    {
      "hash": "sha256-mLFIiMWUzkJyhEk9x46/WGNMmva2m0NMh2I47e//aq8=",
      "url": "_framework/System.Security.Cryptography.Csp.ktjtwy4qb3.wasm"
    },
    {
      "hash": "sha256-qAubhj4/2mc/gODSLqkL4ewovgr7MXcQRrqt0v8VfTw=",
      "url": "_framework/System.Security.Cryptography.Encoding.wigcptmqzb.wasm"
    },
    {
      "hash": "sha256-+pgp/Je+0/Y8ritYtYe2Sl6OxQ3Z/Y4oV1VBCcpyx5U=",
      "url": "_framework/System.Security.Cryptography.OpenSsl.efhjennniz.wasm"
    },
    {
      "hash": "sha256-m3KN0NgzXJ2LXiZMz8/l/P1frmDY5/FMUu7qWHdwp3U=",
      "url": "_framework/System.Security.Cryptography.Primitives.xhizdix629.wasm"
    },
    {
      "hash": "sha256-W5tMSDmYEB2xQ9h2R6YF/fHX/B7hkusJMm/i/cqRTV4=",
      "url": "_framework/System.Security.Cryptography.ProtectedData.15bha6zbjy.wasm"
    },
    {
      "hash": "sha256-soTGkGk4nrzxIwWb2HV9PsK9YbrwaChU7Om9OKzxVWk=",
      "url": "_framework/System.Security.Cryptography.X509Certificates.qdmguy0gfg.wasm"
    },
    {
      "hash": "sha256-Kq3ZZSKQkbZ2H5kMG7Mqq4KycqKa35CdKT9M9DM6w4U=",
      "url": "_framework/System.Security.Permissions.m9hbxwov85.wasm"
    },
    {
      "hash": "sha256-gSxNfiXD0BZhLt/ah22UD+eSYYqSM2QpvMMOg2waCvk=",
      "url": "_framework/System.Security.Principal.Windows.th4rlqoajg.wasm"
    },
    {
      "hash": "sha256-V4My1T5K0KSs4qR03yyCp6N+3fBxQ6L9DoshTpegMz0=",
      "url": "_framework/System.Security.Principal.p8fu879nth.wasm"
    },
    {
      "hash": "sha256-NhEWZoDGjnio3KDgIpH5rVZLhZv1aPvI71HiADyho4c=",
      "url": "_framework/System.Security.SecureString.mhx91fa0v9.wasm"
    },
    {
      "hash": "sha256-YavuKBs2cr3egcpn1nG6OOhurx6QOwKJ/WfC+NZxAvc=",
      "url": "_framework/System.Security.vw08eg5pfj.wasm"
    },
    {
      "hash": "sha256-+882XsXOZFL8av+tjHXzFMZBrxy/un4/Tly+x3MmwX0=",
      "url": "_framework/System.ServiceModel.Web.dynzolko2p.wasm"
    },
    {
      "hash": "sha256-cIMbH/DQSYVcwzEUqlYhHwnq7JToNTOcHQ9RUZuIfVY=",
      "url": "_framework/System.ServiceProcess.cpe8zhimrr.wasm"
    },
    {
      "hash": "sha256-ij79VY532RznuafTYZgrw/UdOnzmlkBQ7VmNISkFne8=",
      "url": "_framework/System.Text.Encoding.CodePages.abukrso2s5.wasm"
    },
    {
      "hash": "sha256-J7VJvWTlocq8krVUNfu13fdAkZ8aZo3EEH26D/w5NoI=",
      "url": "_framework/System.Text.Encoding.Extensions.1umlsaq9nw.wasm"
    },
    {
      "hash": "sha256-KHh0iLGlbBugs3QpWZxunDKZPgjRYSZCQzZ8NKgCPYg=",
      "url": "_framework/System.Text.Encoding.wuo0ag8www.wasm"
    },
    {
      "hash": "sha256-OWu1vu0o+6SEClP3FV+6eEw4MEkR5ZDQhT7yYJOiHhk=",
      "url": "_framework/System.Text.Encodings.Web.vuf6bd4i5s.wasm"
    },
    {
      "hash": "sha256-OUu50EcCqdn5a8R629tvzOS7XwqrAq03uFV58Iqk3hY=",
      "url": "_framework/System.Text.Json.v6l21fiiky.wasm"
    },
    {
      "hash": "sha256-MuOrN304DJNN2WnAHQAl/UvmHT9wRRonm+iewrlhMp4=",
      "url": "_framework/System.Text.RegularExpressions.2gfobphiq3.wasm"
    },
    {
      "hash": "sha256-bZ//e1C2iTBnshO5XzOdipwlc/pbMCJcTmKJqtNM/vc=",
      "url": "_framework/System.Threading.Channels.5u3ggi9wzq.wasm"
    },
    {
      "hash": "sha256-aq6YIUZTSMYH8L0M/7AC6vUFxb/jYylknsFLHxNVNFA=",
      "url": "_framework/System.Threading.Overlapped.a8c6rgvw7l.wasm"
    },
    {
      "hash": "sha256-HWriXkR8OPELDo7cXxF+zWBUDNXqYVoNbN6S21VvjDI=",
      "url": "_framework/System.Threading.Tasks.1bd3cmqv9e.wasm"
    },
    {
      "hash": "sha256-cXdw9sRnOZQjKUhWyNfYLzn7jW+Q/399EZs+rA5LxLQ=",
      "url": "_framework/System.Threading.Tasks.Dataflow.dpqdfsdu9x.wasm"
    },
    {
      "hash": "sha256-qC7lXzyxugprj3VnL601B772Lx4RV1zow8T9zy5F5PY=",
      "url": "_framework/System.Threading.Tasks.Extensions.4921l2b5k5.wasm"
    },
    {
      "hash": "sha256-SiCqODdZWBRENRvEb50oitlrgBvLjrIoTUFfy+yECZI=",
      "url": "_framework/System.Threading.Tasks.Parallel.a0si6j1nyg.wasm"
    },
    {
      "hash": "sha256-GAI7GsW4Cs+4GiCgiWmKQpVFd2G7d59f+GjiLtWL1gs=",
      "url": "_framework/System.Threading.Thread.8svcmv6b7k.wasm"
    },
    {
      "hash": "sha256-rsEPgVF7cf5G8gFvOLzKYT0S4f7lrIjD8/gOU/rrABE=",
      "url": "_framework/System.Threading.ThreadPool.6q4r5fv3jz.wasm"
    },
    {
      "hash": "sha256-vNTLqFNbOl5VQz1MUHdPAKeSx8VjOOcpq4pkbzFMChU=",
      "url": "_framework/System.Threading.Timer.o9mg8u4uw7.wasm"
    },
    {
      "hash": "sha256-WQ8dmNYxjcyMvHBs+/risUxqRBZfrfZ9pacp3zFBq9I=",
      "url": "_framework/System.Threading.fqj3qc2lds.wasm"
    },
    {
      "hash": "sha256-wlW9KffwYNowHUC57Jq1qjY/ACrhMLuN7w8/o/hwmmA=",
      "url": "_framework/System.Transactions.Local.2c9m9phvrm.wasm"
    },
    {
      "hash": "sha256-Lmp5hem9ax9r9n3QjntfvWoPK4GEVXZAlbtIl0Jp0KM=",
      "url": "_framework/System.Transactions.eklci80wey.wasm"
    },
    {
      "hash": "sha256-LzbYYeC7hTYg2mAxkFOt6u8U3KrL8RVhswvWnPo37pg=",
      "url": "_framework/System.ValueTuple.fwu7iv3lz0.wasm"
    },
    {
      "hash": "sha256-YAiGEw2oETzJMIYtBqHeujzxhF65ABQv3AZhQcvuzBU=",
      "url": "_framework/System.Web.HttpUtility.kfn8vo7u5k.wasm"
    },
    {
      "hash": "sha256-3UXFE0xJv9mfftqgKAhVXuOIPhJfwpkBcps/JxxkHRw=",
      "url": "_framework/System.Web.flq6ql2geb.wasm"
    },
    {
      "hash": "sha256-dbvnogomuD3PpNd7e07R+ZlEo3FjvcWC06CCnG5djMU=",
      "url": "_framework/System.Windows.7tfud34p1p.wasm"
    },
    {
      "hash": "sha256-Eh4ZyIvqo34A19igrKJBmrt1iW+vrM/Cjy/zUQHTPe8=",
      "url": "_framework/System.Windows.Extensions.quwz1fxlaw.wasm"
    },
    {
      "hash": "sha256-uP0sJDAxWU4NNil5foTGkKquABLDT0CkZ6EMrGfQfrk=",
      "url": "_framework/System.Xml.Linq.klzmsxmq67.wasm"
    },
    {
      "hash": "sha256-BGIY0lXfXXku1llw2EvAipeT803FBHDEtF5amt0Oirk=",
      "url": "_framework/System.Xml.ReaderWriter.s2a8to6yxv.wasm"
    },
    {
      "hash": "sha256-ROqfxc98U2WiQHwtRS+tv6GpRD1qE6RvwGprpDPs5jU=",
      "url": "_framework/System.Xml.Serialization.4inb2qu00n.wasm"
    },
    {
      "hash": "sha256-hAZ/WIf9BTvSMhJ0tPUVF3tAnQCRy60hwHu0kY36L0U=",
      "url": "_framework/System.Xml.XDocument.c8hpq78uqn.wasm"
    },
    {
      "hash": "sha256-pjm9mwvv/pHUm9SVvDYrC5Z7vXxccOL1nkFeJuVV94Q=",
      "url": "_framework/System.Xml.XPath.XDocument.yeo34d112s.wasm"
    },
    {
      "hash": "sha256-Jjc9HNlzYFpKvLDlNxT3JzKDZojn06lBld7gwVTVOw0=",
      "url": "_framework/System.Xml.XPath.umz7z51d51.wasm"
    },
    {
      "hash": "sha256-68Kdevsw++xcQ1Pp0uqYARQvPpBzW6VKe+sQUpp0Ijk=",
      "url": "_framework/System.Xml.XmlDocument.bsbtqmbjoe.wasm"
    },
    {
      "hash": "sha256-PPNqmBxxE3mM8mUa9lNQRzad2HCzSrmEadOKzlDSWgI=",
      "url": "_framework/System.Xml.XmlSerializer.0djf5ei3zy.wasm"
    },
    {
      "hash": "sha256-CDLxjpYTNYxxgFZh+JeANURFeZmlDNNoCIh79YgE9dQ=",
      "url": "_framework/System.Xml.guke5f9yfb.wasm"
    },
    {
      "hash": "sha256-CTLdf/mibWx4jb0JEg+qYVH4j0O1mXI0o6jRfzVwdJQ=",
      "url": "_framework/System.l50h3hda5q.wasm"
    },
    {
      "hash": "sha256-OxuWyPwMwG6LSgFXhFM1g3nJ2tF0e1EL36rXYjAvYDA=",
      "url": "_framework/WalkingTec.Mvvm.Core.9rrp0bga7v.wasm"
    },
    {
      "hash": "sha256-Bd+l972cOS1eVeLzrQNlgD6LPWV1QXBSzJhv8Tx1WGI=",
      "url": "_framework/WindowsBase.tuf34ieu7y.wasm"
    },
    {
      "hash": "sha256-DTEUnQa/ebEMt2qwsxj/pu5W9N45ikwLyykxVqoNv3A=",
      "url": "_framework/blazor.boot.json"
    },
    {
      "hash": "sha256-I4LggERZ/f+YpdIbmQq/DkpppLSwVDlVHxq6Yvx/QAc=",
      "url": "_framework/blazor.webassembly.js"
    },
    {
      "hash": "sha256-hM7ktJp+9jv22N2RyPOkftLbsXAJ/H3azOQ9VaqVPgA=",
      "url": "_framework/bnred.Client.k2iwa9wk54.wasm"
    },
    {
      "hash": "sha256-3CIj8Y4ZtX3HSGK68Xn2f0Y+2dzOjAu96Vxrqxqt7UE=",
      "url": "_framework/bnred.Client.wpgigq4v7p.pdb"
    },
    {
      "hash": "sha256-5VMW+gpQoYYRb6hcZ3b0OO9NUOg0eHL5H+iNdJgYUkE=",
      "url": "_framework/bnred.Model.5cv9phjmmz.wasm"
    },
    {
      "hash": "sha256-9srml1s8HIuQXy17aC+e2VctJz0UL2ABpoqeCxcwIug=",
      "url": "_framework/bnred.Model.unzamzmfcz.pdb"
    },
    {
      "hash": "sha256-1phKEC/E3+p0wiCpmaPXd426XyC7UvKn+MBX1WW062E=",
      "url": "_framework/bnred.Shared.il1754ucc8.wasm"
    },
    {
      "hash": "sha256-kDz/2FqRl3e0PFoT5z8tWKwVpO5uYdCpXyHSjl+oV7k=",
      "url": "_framework/bnred.Shared.ouqq9kkonm.pdb"
    },
    {
      "hash": "sha256-4GgOjx68hksB8XevBEiBEMgeL728GQjRo2fwq6IbNuU=",
      "url": "_framework/bnred.ViewModel.4f4ya17jjw.wasm"
    },
    {
      "hash": "sha256-ElzcviaXD7ya3Vvsif775E/oQOhpYcxyLfwDXfCiwco=",
      "url": "_framework/bnred.ViewModel.66t98geu7c.pdb"
    },
    {
      "hash": "sha256-/pcrNUZIFxvVrfML8zazMdEZ+IB0TdSKqJ6mAPvV+w4=",
      "url": "_framework/dotnet.js"
    },
    {
      "hash": "sha256-uZ78dzkpKpqOOREKMu96HZuyG1XQJN8TJtajfR7vh7I=",
      "url": "_framework/dotnet.js.map"
    },
    {
      "hash": "sha256-S0WrZZJ79pR4+FZimp4xQxsWirMI2rmsfB52phkCJGg=",
      "url": "_framework/dotnet.native.b4n90iegql.js"
    },
    {
      "hash": "sha256-ZOirrcSdGKtUoeljhPMAvDnCzHgYF4Of/bejd7dqXQM=",
      "url": "_framework/dotnet.native.ozuu0s0chs.wasm"
    },
    {
      "hash": "sha256-ZmDmkgcXhChDWRHCx3XZMJe7cgx2e4YsJg8qr2YME8Y=",
      "url": "_framework/dotnet.runtime.js.map"
    },
    {
      "hash": "sha256-82FoDmY+LsehdN2u8aSGEutGEKXJHcYaSX/3zptbsCw=",
      "url": "_framework/dotnet.runtime.tsg4gsv2hg.js"
    },
    {
      "hash": "sha256-Ab5zUdDSc9FRa92WvWBFPRaFubsXeuPji0yOl61bNjk=",
      "url": "_framework/e_sqlite3.a"
    },
    {
      "hash": "sha256-yPvFfGFxM8PumGOK5fRAtNAfB4QBCwTPjK3TQqKn674=",
      "url": "_framework/en/bnred.Shared.resources.8s4g8wk7wv.wasm"
    },
    {
      "hash": "sha256-tO5O5YzMTVSaKBboxAqezOQL9ewmupzV2JrB5Rkc8a4=",
      "url": "_framework/icudt.oh1zvcfom8.dat"
    },
    {
      "hash": "sha256-g9cI1sECkF7bxMqOSB+W0a4utdDsqTrqZzyiAEeA65g=",
      "url": "_framework/mscorlib.5q5u0swn6q.wasm"
    },
    {
      "hash": "sha256-QFR5QNPfvyZTexW3IA/AFx6ZmIF/TecvOUsSK4gM4po=",
      "url": "_framework/netstandard.w8gk1pigg1.wasm"
    },
    {
      "hash": "sha256-RKto0/rdj7HUJlbMHvetwhJZaklSWJfX3kEPXZLk2S8=",
      "url": "_framework/zh/bnred.Shared.resources.gnncehyimn.wasm"
    },
    {
      "hash": "sha256-2TW/ZJs6ZmmfRka/1hC60gXkTAzen2hE6A6hk6SHNqA=",
      "url": "appsettings.json"
    },
    {
      "hash": "sha256-mHtuuUFoUsnj5MvTOqUVEafjR468RLeI0U1+zBXeFlY=",
      "url": "bnred.Client.styles.css"
    },
    {
      "hash": "sha256-vur7WppiFGepb4SZSLILZMuKtNQ37IXfeOZfYtz3jHM=",
      "url": "fav.ico"
    },
    {
      "hash": "sha256-zZNzDHG/12PI7IY79y8Ck0asad+Q5/O9f3wDhCm2t4A=",
      "url": "index.html"
    },
    {
      "hash": "sha256-j9N/O1q/NmP6RuXAvu4vdKXGLgniM1+LB5laVu/LeHk=",
      "url": "manifest.json"
    },
    {
      "hash": "sha256-YVcb4Zj3PzZ5wDhYyo3QoR4z3btsHT1gKOak3me7Rqg=",
      "url": "pwaicon.png"
    }
  ]
};
