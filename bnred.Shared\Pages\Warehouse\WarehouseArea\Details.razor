﻿@page "/Warehouse/WarehouseArea/Details/{id}"
@using bnred.ViewModel.Warehouse.WarehouseAreaVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.ID"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Code"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Name"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.WarehouseId" Lookup="@AllWarehouses"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Type"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Status"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Area"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Volume"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.MaxCapacity"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.CurrentCapacity"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ManagerId" Lookup="@AllFrameworkUsers"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Description"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private WarehouseAreaVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllWarehouses = new List<SelectedItem>();

    private List<SelectedItem> AllFrameworkUsers = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllWarehouses = await WtmBlazor.Api.CallItemsApi("/api/WarehouseArea/GetWarehouses", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllFrameworkUsers = await WtmBlazor.Api.CallItemsApi("/api/WarehouseArea/GetFrameworkUsers", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<WarehouseAreaVM>($"/api/WarehouseArea/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
