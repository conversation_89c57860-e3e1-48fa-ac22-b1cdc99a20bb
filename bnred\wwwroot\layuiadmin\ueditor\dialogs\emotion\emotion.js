/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;function initImgName(){for(var e in emotion.SmilmgName){var t=emotion.SmilmgName[e],o=emotion.SmileyBox[e],i="";if(o.length)return;for(var a=1;a<=t[1];a++)i=t[0],a<10&&(i+="0"),i=i+a+".gif",o.push(i)}}function initEvtHandler(e){for(var t=$G(e),o=0,i=0;o<t.childNodes.length;o++){var a=t.childNodes[o];1==a.nodeType&&(domUtils.on(a,"click",function(e){return function(){switchTab(e)}}(i)),i++)}switchTab(0),$G("tabIconReview").style.display="none"}function InsertSmiley(e,t){var o={src:editor.options.emotionLocalization?editor.options.UEDITOR_HOME_URL+"dialogs/emotion/"+e:e};o._src=o.src,editor.execCommand("insertimage",o),t.ctrlKey||dialog.popup.hide()}function switchTab(e){autoHeight(e),0==emotion.tabExist[e]&&(emotion.tabExist[e]=1,createTab("tab"+e));for(var t=$G("tabHeads").getElementsByTagName("span"),o=$G("tabBodys").getElementsByTagName("div"),i=0,a=t.length;i<a;i++)t[i].className="",o[i].style.display="none";t[e].className="focus",o[e].style.display="block"}function autoHeight(e){var t=dialog.getDom("iframe"),o=t.parentNode.parentNode;switch(e){case 0:t.style.height="380px",o.style.height="392px";break;case 1:t.style.height="220px",o.style.height="232px";break;case 2:t.style.height="260px",o.style.height="272px";break;case 3:t.style.height="300px",o.style.height="312px";break;case 4:t.style.height="140px",o.style.height="152px";break;case 5:t.style.height="260px",o.style.height="272px";break;case 6:t.style.height="230px",o.style.height="242px"}}function createTab(e){for(var t,o,i,a,n,s,r="?v=1.1",l=$G(e),c=emotion.SmileyPath+emotion.imageFolders[e],m=5.5,h=iHeight=35,g=3,p=emotion.imageCss[e],d=emotion.imageCssOffset[e],u=['<table class="smileytable">'],y=0,b=emotion.SmileyBox[e].length,f=11;y<b;){u.push("<tr>");for(var v=0;v<f;v++,y++)t=emotion.SmileyBox[e][y],t?(o=c+t+r,i=c+t,a=v<m?0:1,n=d*y*-1-1,s=emotion.SmileyInfor[e][y],u.push('<td  class="'+p+'"   border="1" width="'+g+'%" style="border-collapse:collapse;" align="center"  bgcolor="transparent" onclick="InsertSmiley(\''+i.replace(/'/g,"\\'")+"',event)\" onmouseover=\"over(this,'"+o+"','"+a+'\')" onmouseout="out(this)">'),u.push("<span>"),u.push('<img  style="background-position:left '+n+'px;" title="'+s+'" src="'+emotion.SmileyPath+(editor.options.emotionLocalization?'0.gif" width="':'default/0.gif" width="')+h+'" height="'+iHeight+'"></img>'),u.push("</span>")):u.push('<td width="'+g+'%"   bgcolor="#FFFFFF">'),u.push("</td>");u.push("</tr>")}u.push("</table>"),u=u.join(""),l.innerHTML=u}function over(e,t,o){e.style.backgroundColor="#ACCD3C",$G("faceReview").style.backgroundImage="url("+t+")",1==o&&($G("tabIconReview").className="show"),$G("tabIconReview").style.display="block"}function out(e){e.style.backgroundColor="transparent";var t=$G("tabIconReview");t.className="",t.style.display="none"}function createTabList(e){for(var t={},o=0;o<e;o++)t["tab"+o]=[];return t}function createArr(e){for(var t=[],o=0;o<e;o++)t[o]=0;return t}window.onload=function(){editor.setOpt({emotionLocalization:!1}),emotion.SmileyPath=editor.options.emotionLocalization===!0?"images/":"http://img.baidu.com/hi/",emotion.SmileyBox=createTabList(emotion.tabNum),emotion.tabExist=createArr(emotion.tabNum),initImgName(),initEvtHandler("tabHeads")};