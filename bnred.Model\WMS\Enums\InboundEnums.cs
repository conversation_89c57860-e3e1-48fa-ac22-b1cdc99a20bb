using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 入库类型枚举
    /// 用于标识不同的入库业务类型
    /// </summary>
    public enum InboundType
    {
        /// <summary>
        /// 采购入库
        /// 采购订单的入库
        /// </summary>
        [Display(Name = "采购入库")]
        Purchase = 0,

        /// <summary>
        /// 生产入库
        /// 生产完成的入库
        /// </summary>
        [Display(Name = "生产入库")]
        Production = 1,

        /// <summary>
        /// 退货入库
        /// 客户退货的入库
        /// </summary>
        [Display(Name = "退货入库")]
        Return = 2,

        /// <summary>
        /// 调拨入库
        /// 其他仓库调拨的入库
        /// </summary>
        [Display(Name = "调拨入库")]
        Transfer = 3,

        /// <summary>
        /// 盘盈入库
        /// 盘点盈余的入库
        /// </summary>
        [Display(Name = "盘盈入库")]
        StockGain = 4
    }

    /// <summary>
    /// 入库状态枚举
    /// 用于标识入库单的处理状态
    /// </summary>
    public enum InboundStatus
    {
        /// <summary>
        /// 待收货
        /// 入库单已创建待收货
        /// </summary>
        [Display(Name = "待收货")]
        PendingReceive = 0,

        /// <summary>
        /// 收货中
        /// 正在执行收货作业
        /// </summary>
        [Display(Name = "收货中")]
        Receiving = 1,

        /// <summary>
        /// 待检验
        /// 收货完成待质检
        /// </summary>
        [Display(Name = "待检验")]
        PendingCheck = 2,

        /// <summary>
        /// 待上架
        /// 检验完成待上架
        /// </summary>
        [Display(Name = "待上架")]
        PendingPutaway = 3,

        /// <summary>
        /// 上架中
        /// 正在执行上架作业
        /// </summary>
        [Display(Name = "上架中")]
        PuttingAway = 4,

        /// <summary>
        /// 已完成
        /// 入库流程已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 5,

        /// <summary>
        /// 已取消
        /// 入库单已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 6
    }
}