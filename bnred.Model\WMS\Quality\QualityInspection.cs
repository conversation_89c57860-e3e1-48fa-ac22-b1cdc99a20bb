using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;
    
namespace bnred.Model.WMS.Quality
{
    /// <summary>
    /// 质量检验单
    /// 用于记录对物料的质量检验信息
    /// </summary>
    [Table("WMS_QualityInspections")]
    public class QualityInspection : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 质检单号
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50, ErrorMessage = "{0}最多输入{1}个字符")]
        [Display(Name = "质检单号")]
        public string InspectionNo { get; set; }

        /// <summary>
        /// 质检类型
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "质检类型")]
        public InspectionType Type { get; set; }

        /// <summary>
        /// 质检状态
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "质检状态")]
        public InspectionStatus Status { get; set; } = InspectionStatus.Pending;

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 关联单据ID
        /// </summary>
        [Display(Name = "关联单据")]
        [StringLength(50)]
        public string RelatedOrderId { get; set; }

        /// <summary>
        /// 关联单据号
        /// </summary>
        [StringLength(50, ErrorMessage = "{0}最多输入{1}个字符")]
        [Display(Name = "关联单据号")]
        public string RelatedOrderNo { get; set; }

        /// <summary>
        /// 关联单据类型
        /// </summary>
        [Display(Name = "关联单据类型")]
        public RelatedOrderType? RelatedOrderType { get; set; }

        /// <summary>
        /// 计划检验时间
        /// </summary>
        [Display(Name = "计划检验时间")]
        public DateTime? PlannedInspectionTime { get; set; }

        /// <summary>
        /// 实际检验时间
        /// </summary>
        [Display(Name = "实际检验时间")]
        public DateTime? ActualInspectionTime { get; set; }

        /// <summary>
        /// 质检员ID
        /// </summary>
        [Display(Name = "质检员")]
    
        public Guid? InspectorId { get; set; }
 
        public FrameworkUser Inspector { get; set; }

        /// <summary>
        /// 检验结果
        /// </summary>
        [Display(Name = "检验结果")]
        public InspectionResult? Result { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "优先级")]
        public TaskPriority Priority { get; set; } = TaskPriority.Normal;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        [Display(Name = "备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 质检明细列表
        /// </summary>
        [Display(Name = "质检明细列表")]
        public List<QualityInspectionDetail> Details { get; set; }
    }
} 