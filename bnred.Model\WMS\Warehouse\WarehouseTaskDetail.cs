using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using WalkingTec.Mvvm.Core;
namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseTaskDetails")]
    public class WarehouseTaskDetail : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string TaskId { get; set; }
        public virtual WarehouseTask Task { get; set; }

        [StringLength(50)]
        public string MaterialId { get; set; }
        public virtual Material.Material Material { get; set; }

        [StringLength(50)]
        public string BatchId { get; set; }
        public virtual Batch.Batch Batch { get; set; }

        [Required]
        [StringLength(50)]
        public string FromLocationId { get; set; }
        public virtual Location FromLocation { get; set; }

        [Required]
        [StringLength(50)]
        public string ToLocationId { get; set; }
        public virtual Location ToLocation { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal PlanQuantity { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal? ActualQuantity { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 