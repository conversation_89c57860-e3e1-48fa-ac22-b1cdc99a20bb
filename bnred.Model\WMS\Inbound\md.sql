-- ----------------------------
-- Table structure for WMS_Inbound
-- ----------------------------
DROP TABLE IF EXISTS [WMS_Inbound];
CREATE TABLE [WMS_Inbound] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [InboundNo] nvarchar(50) NOT NULL, -- 入库单号
  [Type] int NOT NULL, -- 入库类型 (需要映射到 WMS_EnumDictionary, C#类型: DocumentType)
  [MaterialId] nvarchar(50) NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [WarehouseId] nvarchar(50) NULL, -- 仓库ID
  [LocationId] nvarchar(50) NULL, -- 位置ID
  [Quantity] decimal(18,2) NOT NULL, -- 数量
  [Unit] nvarchar(20) NULL, -- 单位
  [UnitPrice] decimal(18,2) NOT NULL, -- 单价
  [TotalAmount] decimal(18,2) NOT NULL, -- 总金额
  [SupplierId] nvarchar(50) NULL, -- 供应商ID
  [DocumentDate] datetime2(7) NOT NULL, -- 单据日期
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_Inbound
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_InboundOrder
-- ----------------------------
DROP TABLE IF EXISTS [WMS_InboundOrder];
CREATE TABLE [WMS_InboundOrder] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [OrderNo] nvarchar(50) NOT NULL, -- 入库单号
  [Type] int NOT NULL, -- 入库类型 (需要映射到 WMS_EnumDictionary, C#类型: InboundType)
  [WarehouseId] nvarchar(50) NOT NULL, -- 仓库ID
  [PlanTime] datetime2(7) NULL, -- 计划时间
  [ActualTime] datetime2(7) NULL, -- 实际时间
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_InboundOrder
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_InboundOrderDetail
-- ----------------------------
DROP TABLE IF EXISTS [WMS_InboundOrderDetail];
CREATE TABLE [WMS_InboundOrderDetail] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [InboundOrderId] nvarchar(50) NOT NULL, -- 入库单ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [LocationId] nvarchar(50) NOT NULL, -- 库位ID
  [PlanQuantity] decimal(18,4) NOT NULL, -- 计划数量
  [ActualQuantity] decimal(18,4) NULL, -- 实际数量
  [UnitPrice] decimal(18,4) NULL, -- 单价
  [TotalAmount] decimal(18,4) NULL, -- 总金额
  [ProductionDate] datetime2(7) NULL, -- 生产日期
  [ExpiryDate] datetime2(7) NULL, -- 过期日期
  [QualityStatus] int NOT NULL, -- 质检状态 (需要映射到 WMS_EnumDictionary, C#类型: QualityStatus)
  [QualityRemark] nvarchar(500) NULL, -- 质检备注
  [LineNumber] int NOT NULL, -- 行号
  [Remark] nvarchar(200) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_InboundOrderDetail
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_Inbound
-- ----------------------------
ALTER TABLE [WMS_Inbound] ADD CONSTRAINT [FK_WMS_Inbound_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Inbound] ADD CONSTRAINT [FK_WMS_Inbound_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Inbound] ADD CONSTRAINT [FK_WMS_Inbound_Warehouse] FOREIGN KEY ([WarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Inbound] ADD CONSTRAINT [FK_WMS_Inbound_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Inbound] ADD CONSTRAINT [FK_WMS_Inbound_Supplier] FOREIGN KEY ([SupplierId]) REFERENCES [WMS_Supplier] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- Foreign Keys structure for table WMS_InboundOrder
-- ----------------------------
ALTER TABLE [WMS_InboundOrder] ADD CONSTRAINT [FK_WMS_InboundOrder_Warehouse] FOREIGN KEY ([WarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- Foreign Keys structure for table WMS_InboundOrderDetail
-- ----------------------------
ALTER TABLE [WMS_InboundOrderDetail] ADD CONSTRAINT [FK_WMS_InboundOrderDetail_InboundOrder] FOREIGN KEY ([InboundOrderId]) REFERENCES [WMS_InboundOrder] ([ID]) ON DELETE CASCADE ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_InboundOrderDetail] ADD CONSTRAINT [FK_WMS_InboundOrderDetail_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_InboundOrderDetail] ADD CONSTRAINT [FK_WMS_InboundOrderDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_InboundOrderDetail] ADD CONSTRAINT [FK_WMS_InboundOrderDetail_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- MS_Description for table WMS_Inbound
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'InboundNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'Type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'WarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'位置ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'Quantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单位' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'Unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'UnitPrice'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总金额' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'TotalAmount'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'SupplierId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单据日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'DocumentDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Inbound', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_InboundOrder
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库订单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'OrderNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'Type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'WarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'PlanTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'ActualTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrder', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_InboundOrderDetail
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库单明细' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入库单ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'InboundOrderId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'PlanQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'ActualQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'UnitPrice'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总金额' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'TotalAmount'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'生产日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'ProductionDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'过期日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'ExpiryDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'QualityStatus'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'质检备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'QualityRemark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'行号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'LineNumber'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InboundOrderDetail', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO
