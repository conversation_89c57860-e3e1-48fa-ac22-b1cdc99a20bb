using System.ComponentModel.DataAnnotations;
using WalkingTec.Mvvm.Core;
using System;

namespace bnred.Model
{
    public class Language : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        [Required]
        [StringLength(10)]
        public string CultureName { get; set; }

        public int DisplayOrder { get; set; }
    }
}