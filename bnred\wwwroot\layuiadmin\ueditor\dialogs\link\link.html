<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"><html><head><title></title><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><script src="../internal.js"></script><style>*{margin:0;padding:0;color:#838383}table{font-size:12px;margin:10px;line-height:30px}.txt{width:300px;height:21px;line-height:21px;border:1px solid #d7d7d7}</style></head><body><table><tr><td><label for="text"><var id="lang_input_text"></var></label></td><td><input class="txt" id="text" type="text" disabled></td></tr><tr><td><label for="href"><var id="lang_input_url"></var></label></td><td><input class="txt" id="href" type="text"></td></tr><tr><td><label for="title"><var id="lang_input_title"></var></label></td><td><input class="txt" id="title" type="text"></td></tr><tr><td colspan="2"><label for="target"><var id="lang_input_target"></var></label> <input id="target" type="checkbox"></td></tr><tr><td colspan="2" id="msg"></td></tr></table><script>var url,orgText,range=editor.selection.getRange(),link=range.collapsed?editor.queryCommandValue("link"):editor.selection.getStart(),text=$G("text"),rangeLink=domUtils.findParentByTagName(range.getCommonAncestor(),"a",!0);function handleDialogOk(){var e=$G("href").value.replace(/^\s+|\s+$/g,"");if(e){hrefStartWith(e,["http","/","ftp://","#"])||(e="http://"+e);var t={href:e,target:$G("target").checked?"_blank":"_self",title:$G("title").value.replace(/^\s+|\s+$/g,""),_href:e};orgText&&text.value!=orgText&&(link[browser.ie?"innerText":"textContent"]=t.textValue=text.value,range.selectNode(link).select()),range.collapsed&&(t.textValue=text.value),editor.execCommand("link",utils.clearEmptyAttrs(t)),dialog.close()}}function hrefStartWith(e,t){e=e.replace(/^\s+|\s+$/g,"");for(var l,r=0;l=t[r++];)if(0==e.indexOf(l))return!0;return!1}(link=domUtils.findParentByTagName(link,"a",!0))?(url=utils.html(link.getAttribute("_href")||link.getAttribute("href",2)),rangeLink!==link||link.getElementsByTagName("img").length?(text.setAttribute("disabled","true"),text.value=lang.validLink):(text.removeAttribute("disabled"),orgText=text.value=link[browser.ie?"innerText":"textContent"])):range.collapsed?(text.removeAttribute("disabled"),text.value=""):(text.setAttribute("disabled","true"),text.value=lang.validLink),$G("title").value=url?link.title:"",$G("href").value=url||"",$G("target").checked=!(!url||"_blank"!=link.target),$focus($G("href")),dialog.onok=handleDialogOk,$G("href").onkeydown=$G("title").onkeydown=function(e){if(13==(e=e||window.event).keyCode)return handleDialogOk(),!1},$G("href").onblur=function(){hrefStartWith(this.value,["http","/","ftp://","#"])?$G("msg").innerHTML="":$G("msg").innerHTML="<span style='color: red'>"+lang.httpPrompt+"</span>"}</script></body></html>