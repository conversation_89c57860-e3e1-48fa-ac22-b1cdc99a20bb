using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;
namespace bnred.Model.WMS.Analysis
{
    [Table("WMS_InventoryAnalysis")]
    public class InventoryAnalysis : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [Display(Name = "分析日期")]
        public DateTime AnalysisDate { get; set; }

        [Required]
        [Display(Name = "物料ID")]
        [StringLength(50)]
        public string MaterialId { get; set; }
        public virtual Material.Material Material { get; set; }

        [Required]
        [Display(Name = "仓库ID")]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse.Warehouse Warehouse { get; set; }

        [Required]
        [Display(Name = "平均库存")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal AverageInventory { get; set; }

        [Required]
        [Display(Name = "周转率")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal TurnoverRate { get; set; }

        [Required]
        [Display(Name = "周转天数")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal TurnoverDays { get; set; }

        [Required]
        [Display(Name = "缺货率")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal StockoutRate { get; set; }

        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
} 