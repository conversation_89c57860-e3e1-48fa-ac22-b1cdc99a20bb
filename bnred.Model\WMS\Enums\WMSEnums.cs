using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 防尘等级
    /// </summary>
    public enum DustProofLevel
    {
        [Display(Name = "无要求")]
        None = 0,
        [Display(Name = "一级")]
        Level1 = 1,
        [Display(Name = "二级")]
        Level2 = 2,
        [Display(Name = "三级")]
        Level3 = 3
    }

    /// <summary>
    /// 防潮等级
    /// </summary>
    public enum MoistureProofLevel
    {
        [Display(Name = "无要求")]
        None = 0,
        [Display(Name = "一级")]
        Level1 = 1,
        [Display(Name = "二级")]
        Level2 = 2,
        [Display(Name = "三级")]
        Level3 = 3
    }

    /// <summary>
    /// 库存调整原因
    /// </summary>
    public enum InventoryAdjustmentReason
    {
        [Display(Name = "盘盈")]
        Gain = 0,
        [Display(Name = "盘亏")]
        Loss = 1,
        [Display(Name = "报损")]
        Damage = 2,
        [Display(Name = "其他")]
        Other = 3
    }

    /// <summary>
    /// 仓库区域类型
    /// </summary>
    public enum WarehouseAreaType
    {
        [Display(Name = "存储区")]
        Storage = 0,
        [Display(Name = "收货区")]
        Receiving = 1,
        [Display(Name = "发货区")]
        Shipping = 2,
        [Display(Name = "待检区")]
        QualityCheck = 3,
        [Display(Name = "退货区")]
        Return = 4
    }
}