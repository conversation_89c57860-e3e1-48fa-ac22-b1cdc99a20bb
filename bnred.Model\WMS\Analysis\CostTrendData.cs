using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Analysis
{
    /// <summary>
    /// 成本趋势数据类
    /// 用于记录和分析成本变化趋势
    /// </summary>
    [Table("WMS_CostTrendData")]
    public class CostTrendData : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 平均单位成本
        /// 当期的平均单位成本
        /// </summary>
        public decimal AverageUnitCost { get; set; }

        /// <summary>
        /// 变化率
        /// 相比上期的成本变化百分比
        /// </summary>
        public decimal ChangeRate { get; set; }

        /// <summary>
        /// 移动平均成本
        /// 最近几期的移动平均成本
        /// </summary>
        public decimal MovingAverageCost { get; set; }

        /// <summary>
        /// 总价值
        /// 当期库存总价值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 趋势方向
        /// Up-上升，Down-下降，Stable-稳定
        /// </summary>
        public string TrendDirection { get; set; }

        /// <summary>
        /// 物料类别
        /// 如果是按类别分析时的类别名称
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 数据来源
        /// 数据的来源系统或计算方法
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// 备注
        /// 额外的说明信息
        /// </summary>
        public string Notes { get; set; }
    }
} 