-- #############################################################################
-- # SQL DDL for Supplier Module
-- # Target File: bnred.Model/Supplier/md.sql
-- #############################################################################

-- #############################################################################
-- # Table: WMS_Suppliers (供应商信息表)
-- # 来自: Supplier.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_Suppliers', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_Suppliers;
GO
CREATE TABLE WMS_Suppliers (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    Code NVARCHAR(50) NOT NULL UNIQUE,                   -- 供应商编码
    Name NVARCHAR(100) NOT NULL,                         -- 供应商名称
    Type INT NOT NULL,                                   -- 供应商类型 (关联 WMS_EnumDictionary, EnumType='bnred.Model.Supplier.SupplierType')
    ContactPerson NVARCHAR(50) NULL,                     -- 联系人
    ContactPhone NVARCHAR(20) NULL,                      -- 联系电话
    Email NVARCHAR(100) NULL,                            -- 电子邮箱
    Fax NVARCHAR(20) NULL,                               -- 传真
    Address NVARCHAR(200) NULL,                          -- 地址
    PostalCode NVARCHAR(20) NULL,                        -- 邮政编码
    Country NVARCHAR(50) NULL,                           -- 国家/地区
    Province NVARCHAR(50) NULL,                          -- 省/州
    City NVARCHAR(50) NULL,                              -- 城市
    Website NVARCHAR(100) NULL,                          -- 网站
    TaxNumber NVARCHAR(50) NULL,                         -- 税号
    BankName NVARCHAR(100) NULL,                         -- 银行名称
    BankAccount NVARCHAR(50) NULL,                       -- 银行账号
    CreditRating INT NOT NULL,                           -- 信用等级 (关联 WMS_EnumDictionary, EnumType='bnred.Model.Supplier.SupplierCreditRating')
    PaymentTerms NVARCHAR(200) NULL,                     -- 付款条件
    DeliveryMethod NVARCHAR(100) NULL,                   -- 交货方式
    Status INT NOT NULL,                                 -- 供应商状态 (关联 WMS_EnumDictionary, EnumType='bnred.Model.Supplier.SupplierStatus')
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_Suppliers table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商信息表，用于存储供应商的基本信息。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers';
-- MS_Description for WMS_Suppliers columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商编码，唯一，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Code';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商名称，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Name';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商类型 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.Supplier.SupplierType\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Type';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主要联系人姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'ContactPerson';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'联系人电话号码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'ContactPhone';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商电子邮箱地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Email';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商传真号码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Fax';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商公司地址' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Address';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮政编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'PostalCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商所在国家或地区' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Country';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商所在省份或州' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Province';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商所在城市' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'City';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商官方网站URL' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Website';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'纳税人识别号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'TaxNumber';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'开户银行名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'BankName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'银行账号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'BankAccount';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商信用等级 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.Supplier.SupplierCreditRating\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'CreditRating';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'付款条件描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'PaymentTerms';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主要交货方式' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'DeliveryMethod';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'供应商当前状态 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.Supplier.SupplierStatus\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Suppliers', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Foreign Key Constraints for Supplier Module (if any within this file)
-- #############################################################################
-- No internal FKs defined in Supplier.cs for WMS_Suppliers table itself.
-- FKs from other tables (e.g., WMS_Materials) to WMS_Suppliers will be in their respective module files.

PRINT 'SQL for Supplier module created in bnred.Model/Supplier/md.sql';
GO 