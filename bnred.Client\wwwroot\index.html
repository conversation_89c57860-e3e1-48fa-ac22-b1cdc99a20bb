﻿<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>Wtm Blazor Wasm</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
  <meta content="bootstrap,blazor,wasm,webassembly,UI,netcore,web,assembly" name="Keywords">
  <base href="/">
  <link rel="icon" href="fav.ico" type="image/x-icon">
  <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
  <link href="manifest.json" rel="manifest" />
  <link rel="apple-touch-icon" sizes="144x144" href="pwaicon.png" />
  <link href="_content/bnred.Shared/font-awesome/css/all.min.css" rel="stylesheet">
  <link rel="stylesheet" href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css">
  <link rel="stylesheet" href="_content/bnred.Shared/css/site.css">
  <link rel="stylesheet" href="_content/bnred.Shared/css/loading.css">
  <link rel="stylesheet" href="_content/bnred.Shared/font/iconfont.css">
  <link rel="stylesheet" href="bnred.styles.css">
  <link rel="stylesheet" href="_content/BootstrapBlazor.Chart/css/bootstrap.blazor.chart.bundle.min.css" />
  <link rel="stylesheet" href="_content/BootstrapBlazor.Markdown/css/bootstrap.blazor.markdown.min.css" />
</head>

<body class="overflow-hidden">
  <app></app>

  <div class="loader" id="loading">
    <div class="logo">
      <div class="one common"></div>
      <div class="two common"></div>
      <div class="three common"></div>
      <div class="four common"></div>
      <div class="five common"></div>
      <div class="six common"></div>
      <div class="seven common"></div>
      <div class="eight common"></div>
    </div>
    <div class="intro">
      <img src="_content/bnred.Shared/images/logo.png" />
      <span>WTM Loading</span>
    </div>
    <div class="bar">
      <div class="progress"></div>
    </div>
  </div>

  <div id="blazor-error-ui">
    <environment include="Staging,Production">
      An error has occurred. This application may no longer respond until reloaded.
    </environment>
    <environment include="Development">
      An unhandled exception has occurred. See browser dev tools for details.
    </environment>
    <a href="" class="reload">Reload</a>
    <a class="dismiss"><i class="fa fa-times"></i></a>
  </div>

  <script src="_framework/blazor.webassembly.js"></script>
  <script>navigator.serviceWorker.register('service-worker.js');</script>
  <script src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"></script>
  <script src="_content/bnred.Shared/js/common.js"></script>
  <script src="_content/BootstrapBlazor.Chart/js/bootstrap.blazor.chart.bundle.min.js"></script>
  <script src="_content/BootstrapBlazor.Markdown/js/bootstrap.blazor.markdown.min.js"></script>
</body>

</html>
