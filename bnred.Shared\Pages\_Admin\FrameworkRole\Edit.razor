@page "/_Admin/FrameworkRole/Edit/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <BootstrapInput @bind-Value="@Model.Entity.RoleCode" />
    <BootstrapInput @bind-Value="@Model.Entity.RoleName" />
    <BootstrapInput @bind-Value="@Model.Entity.RoleRemark" />
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>
@code {
    private FrameworkRoleVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var rv = await WtmBlazor.Api.CallAPI<FrameworkRoleVM>($"/api/_FrameworkRole/{id}");
        Model = rv.Data;
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_FrameworkRole/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
