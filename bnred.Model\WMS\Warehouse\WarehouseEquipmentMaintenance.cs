using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseEquipmentMaintenances")]
    public class WarehouseEquipmentMaintenance : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string EquipmentId { get; set; }
        public WarehouseEquipment Equipment { get; set; }

        [Required]
        public DateTime MaintenanceDate { get; set; }

        [Required]
        [StringLength(100)]
        public string MaintenanceBy { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MaintenanceCost { get; set; }

        [StringLength(1000)]
        public string MaintenanceDetails { get; set; }

        [Required]
        public bool IsCompleted { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 