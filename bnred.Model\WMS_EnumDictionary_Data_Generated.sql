-- Inserting enums from bnred.Model.WMS.Enums.AlertEnums

-- Enum: AlertType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('AlertType', 0, 'Inventory', '库存预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 1, 'Expiry', '效期预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 2, 'Equipment', '设备预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 3, 'Capacity', '容量预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 4, 'Task', '任务预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 5, 'Quality', '质量预警', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: AlertLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('AlertLevel', 0, 'Info', '提示级别', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertLevel', 1, 'Warning', '警告级别', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertLevel', 2, 'Critical', '严重级别', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertLevel', 3, 'Emergency', '紧急级别', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.AnalysisEnums

-- Enum: CostAnomalyLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnomalyLevel', 0, 'Normal', '正常', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 1, 'Minor', '轻微', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 2, 'Moderate', '中等', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 3, 'Severe', '严重', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 4, 'Critical', '极端', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnalysisType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnalysisType', 0, 'Material', '物料成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 1, 'Inventory', '库存成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 2, 'Warehousing', '仓储成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 3, 'Operation', '运营成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 4, 'Comprehensive', '综合成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostTrend
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostTrend', 0, 'Stable', '稳定', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 1, 'SlowlyRising', '缓慢上升', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 2, 'RapidlyRising', '迅速上升', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 3, 'SlowlyDecreasing', '缓慢下降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 4, 'RapidlyDecreasing', '迅速下降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 5, 'Fluctuating', '波动', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.BarcodeEnums

-- Enum: BarcodeType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BarcodeType', 0, 'Material', '物料条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 1, 'Location', '库位条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 2, 'Container', '容器条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 3, 'Pallet', '托盘条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 4, 'Package', '包装条码', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: BarcodeStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BarcodeStatus', 0, 'Generated', '已生成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeStatus', 1, 'Used', '已使用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeStatus', 2, 'Voided', '已作废', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeStatus', 3, 'Expired', '已失效', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.BatchEnums

-- Enum: BatchQualityStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BatchQualityStatus', 0, 'Pending', 'Pending', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 1, 'Qualified', 'Qualified', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 2, 'Unqualified', 'Unqualified', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 3, 'ReInspection', 'ReInspection', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 4, 'Scrapped', 'Scrapped', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: BatchStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BatchStatus', 0, 'PendingIn', 'PendingIn', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 1, 'InStock', 'InStock', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 2, 'PendingOut', 'PendingOut', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 3, 'OutStock', 'OutStock', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 4, 'Frozen', 'Frozen', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 5, 'Scrapped', 'Scrapped', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.ContainerEnums

-- Enum: ContainerType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ContainerType', 0, 'Box', '周转箱', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 1, 'Pallet', '托盘', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 2, 'Rack', '料架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 3, 'Cage', '笼车', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 4, 'Shelf', '货架', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ContainerStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ContainerStatus', 0, 'Empty', '空闲中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerStatus', 1, 'InUse', '使用中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerStatus', 2, 'Maintenance', '维修中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerStatus', 3, 'Scrapped', '已报废', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ContainerSpecification
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ContainerSpecification', 0, 'Small', '小型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 1, 'Medium', '中型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 2, 'Large', '大型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 3, 'ExtraLarge', '特大型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 4, 'Custom', '定制型', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.Enums.CostAnalysisType

-- Enum: CostAnalysisType (from bnred.Model.Enums)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LegacyCostAnalysisType', 1, 'Daily', '按日分析', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to LegacyCostAnalysisType to avoid conflict
    ('LegacyCostAnalysisType', 2, 'Weekly', '按周分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 3, 'Monthly', '按月分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 4, 'Quarterly', '按季度分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 5, 'Yearly', '按年分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 6, 'ByCategory', '按类别分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 7, 'ByRegion', '按区域分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 8, 'BySupplier', '按供应商分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 9, 'ByCostCenter', '按成本中心分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 10, 'ByBatch', '按批次分析', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Analysis.CostAnomalyEnums

-- Enum: CostAnomalyType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnomalyType', 0, 'SuddenIncrease', '成本突增', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 1, 'SuddenDecrease', '成本突降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 2, 'ContinuousIncrease', '持续上涨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 3, 'ContinuousDecrease', '持续下跌', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 4, 'AbnormalFluctuation', '异常波动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 5, 'OverBudget', '超出预算', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 99, 'Other', '其他异常', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyLevel (from bnred.Model.Analysis)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LegacyCostAnomalyLevel', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to LegacyCostAnomalyLevel
    ('LegacyCostAnomalyLevel', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnomalyLevel', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnomalyStatus', 0, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyStatus', 1, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyStatus', 2, 'Processed', '已处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyStatus', 3, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.CostAnomalyLevel

-- Enum: CostAnomalyLevel (from bnred.Model root)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RootCostAnomalyLevel', 0, 'Normal', '正常', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to RootCostAnomalyLevel
    ('RootCostAnomalyLevel', 1, 'Warning', '警告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyLevel', 2, 'Critical', '严重', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.CostAnomalyStatus

-- Enum: CostAnomalyStatus (from bnred.Model root)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RootCostAnomalyStatus', 1, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to RootCostAnomalyStatus
    ('RootCostAnomalyStatus', 2, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 3, 'Processed', '已处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 4, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 5, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Enums.CostAnomalyType

-- Enum: CostAnomalyType (from bnred.Model.Enums)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EnumsCostAnomalyType', 1, 'CostSpike', '成本突增', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to EnumsCostAnomalyType
    ('EnumsCostAnomalyType', 2, 'CostDrop', '成本突降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 3, 'PersistentlyHigh', '持续偏高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 4, 'PersistentlyLow', '持续偏低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 5, 'AbnormalFluctuation', '异常波动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 6, 'CalculationError', '计算错误', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 7, 'DataMissing', '数据缺失', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 8, 'ExceedLimit', '超出限制', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 9, 'Inconsistency', '不一致', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 10, 'Unclassified', '未分类异常', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyLevel (from bnred.Model.Enums.CostAnomalyType.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EnumsCostAnomalyLevel', 1, 'Low', '低风险', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to EnumsCostAnomalyLevel
    ('EnumsCostAnomalyLevel', 2, 'Medium', '中等风险', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyLevel', 3, 'High', '高风险', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyLevel', 4, 'Critical', '严重风险', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyStatus (from bnred.Model.Enums.CostAnomalyType.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EnumsCostAnomalyStatus', 1, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1), -- Renamed to EnumsCostAnomalyStatus
    ('EnumsCostAnomalyStatus', 2, 'InProgress', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 3, 'Resolved', '已解决', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 4, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 5, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 6, 'NeedsFollowUp', '需要跟进', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.CostEnums

-- Enum: CostType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostType', 0, 'Purchase', '采购成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 1, 'Storage', '仓储成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 2, 'Transportation', '运输成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 3, 'Labor', '人工成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 4, 'Management', '管理成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 5, 'Equipment', '设备成本', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostMethod
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostMethod', 0, 'MovingAverage', '移动平均法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 1, 'FIFO', '先进先出法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 2, 'LIFO', '后进先出法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 3, 'WeightedAverage', '加权平均法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 4, 'StandardCost', '标准成本法', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.CultureEnums

-- Enum: SupportedCulture
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SupportedCulture', 0, 'zh_CN', '简体中文', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 1, 'en_US', 'English', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 2, 'zh_TW', '繁體中文', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 3, 'ja_JP', '日本語', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 4, 'ko_KR', '한국어', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 5, 'fr_FR', 'Français', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.DefaultSettings

-- Enum: DefaultSettings
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DefaultSettings', 0, 'System', 'System', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DefaultSettings', 1, 'Custom', 'Custom', NULL, GETDATE(), NULL, NULL, NULL, 1); 

    // ... existing code ...

-- Inserting enums from bnred.Model.DocumentType

-- Enum: LegacyDocumentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LegacyDocumentType', 1, 'PurchaseIn', '采购入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 2, 'SalesOut', '销售出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 3, 'StockAdjustment', '库存调整单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 4, 'StockTaking', '盘点单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 5, 'OtherIn', '其他入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 6, 'OtherOut', '其他出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 7, 'ProductionIn', '生产入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 8, 'ProductionOut', '生产领料', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 9, 'ReturnIn', '退货入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 10, 'ReturnOut', '退货出库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.DurationUnit

-- Enum: DurationUnit
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DurationUnit', 0, 'Seconds', '秒', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 1, 'Minutes', '分钟', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 2, 'Hours', '小时', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 3, 'Days', '天', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 4, 'Weeks', '周', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 5, 'Months', '月', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 6, 'Years', '年', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.EquipmentEnums

-- Enum: EquipmentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EquipmentType', 0, 'Forklift', '叉车', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 1, 'AGV', 'AGV', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 2, 'Conveyor', '传送带', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 3, 'Stacker', '堆垛机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 4, 'RFIDReader', 'RFID读取器', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 5, 'Sensor', '传感器', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: EquipmentStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EquipmentStatus', 0, 'Idle', '空闲', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 1, 'InUse', '使用中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 2, 'Maintenance', '维护中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 3, 'Faulty', '故障', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 4, 'Scrapped', '报废', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: MaintenanceType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MaintenanceType', 0, 'Preventive', '预防性维护', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 1, 'Corrective', '纠正性维护', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 2, 'Emergency', '紧急维护', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 3, 'Scheduled', '计划性维护', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.GlobalEnums

-- Enum: ActiveStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ActiveStatus', 0, 'Inactive', '未激活', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ActiveStatus', 1, 'Active', '已激活', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ApprovalStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ApprovalStatus', 0, 'Pending', '待审批', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ApprovalStatus', 1, 'Approved', '已批准', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ApprovalStatus', 2, 'Rejected', '已拒绝', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ApprovalStatus', 3, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PriorityLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PriorityLevel', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PriorityLevel', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PriorityLevel', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PriorityLevel', 3, 'Urgent', '紧急', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InboundEnums

-- Enum: InboundStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InboundStatus', 0, 'Pending', '待收货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 1, 'Receiving', '收货中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 2, 'Received', '已收货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 3, 'Putaway', '上架中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 4, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 6, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InboundType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InboundType', 0, 'Purchase', '采购入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 1, 'Transfer', '调拨入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 2, 'Return', '退货入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 3, 'Production', '生产入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 99, 'Other', '其他入库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InboundDocumentType (Matches WMS.Enums.DocumentType, but scoped here)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InboundDocumentType', 0, 'ASN', 'ASN (Advanced Shipment Notice)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 1, 'PO', 'PO (Purchase Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 2, 'STO', 'STO (Stock Transfer Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 3, 'RMA', 'RMA (Return Material Authorization)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 4, 'WO', 'WO (Work Order)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InventoryEnums

-- Enum: InventoryStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryStatus', 0, 'Available', '可用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 1, 'Hold', '冻结', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 2, 'Damaged', '损坏', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 3, 'Reserved', '预留', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 4, 'InTransit', '在途', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 5, 'Expired', '过期', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InventoryAdjustmentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryAdjustmentType', 0, 'StockIn', '入库调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 1, 'StockOut', '出库调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 2, 'CycleCount', '盘点调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 3, 'Damage', '损坏调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 4, 'Loss', '丢失调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 99, 'Other', '其他调整', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InventoryMovementType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryMovementType', 0, 'Receiving', '收货移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 1, 'Putaway', '上架移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 2, 'Picking', '拣货移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 3, 'Replenishment', '补货移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 4, 'Transfer', '库内转移', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 5, 'Adjustment', '调整移动', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InventoryLedgerEnums

-- Enum: LedgerTransactionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LedgerTransactionType', 0, 'Inbound', '入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 1, 'Outbound', '出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 2, 'Adjustment', '调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 3, 'Transfer', '转移', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 4, 'Stocktake', '盘点', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.LabelEnums

-- Enum: LabelType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LabelType', 0, 'Material', '物料标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 1, 'Location', '库位标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 2, 'Container', '容器标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 3, 'Shipping', '发货标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 4, 'Receiving', '收货标签', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: LabelFormat
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LabelFormat', 0, 'ZPL', 'ZPL (Zebra Programming Language)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelFormat', 1, 'PDF', 'PDF (Portable Document Format)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelFormat', 2, 'Image', '图片格式 (PNG, JPG)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelFormat', 3, 'Text', '纯文本格式', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.LocationEnums

-- Enum: LocationType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LocationType', 0, 'Storage', '存储区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 1, 'Receiving', '收货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 2, 'Shipping', '发货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 3, 'Picking', '拣货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 4, 'Staging', '暂存区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 5, 'QualityCheck', '质检区', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: LocationStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LocationStatus', 0, 'Empty', '空', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationStatus', 1, 'Occupied', '占用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationStatus', 2, 'Reserved', '预留', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationStatus', 3, 'Unavailable', '不可用', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.MaterialEnums

-- Enum: MaterialType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MaterialType', 0, 'RawMaterial', '原材料', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 1, 'FinishedGood', '成品', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 2, 'SemiFinishedGood', '半成品', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 3, 'SparePart', '备件', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 4, 'Consumable', '消耗品', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: MaterialStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MaterialStatus', 0, 'Active', '启用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialStatus', 1, 'Inactive', '停用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialStatus', 2, 'Obsolete', '废弃', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialStatus', 3, 'PendingApproval', '待审核', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: UnitOfMeasure
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('UnitOfMeasure', 0, 'EA', '个 (Each)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 1, 'KG', '千克 (Kilogram)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 2, 'M', '米 (Meter)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 3, 'L', '升 (Liter)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 4, 'BOX', '箱 (Box)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 5, 'PALLET', '托盘 (Pallet)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Notification.NotificationEnums

-- Enum: NotificationType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('NotificationType', 0, 'System', '系统通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 1, 'Alert', '预警通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 2, 'TaskAssignment', '任务分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 3, 'ApprovalRequest', '审批请求', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 4, 'Information', '信息通知', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: NotificationStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('NotificationStatus', 0, 'Unread', '未读', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationStatus', 1, 'Read', '已读', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationStatus', 2, 'Archived', '已归档', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationStatus', 3, 'Deleted', '已删除', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: NotificationChannel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('NotificationChannel', 0, 'InApp', '应用内通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationChannel', 1, 'Email', '邮件通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationChannel', 2, 'SMS', '短信通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationChannel', 3, 'Push', '推送通知', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.OutboundEnums

-- Enum: OutboundStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('OutboundStatus', 0, 'Pending', '待拣货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 1, 'Picking', '拣货中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 2, 'Picked', '已拣货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 3, 'Packing', '包装中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 4, 'Packed', '已包装', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 5, 'Shipped', '已发运', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 6, 'Delivered', '已送达', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 7, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 8, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: OutboundType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('OutboundType', 0, 'Sales', '销售出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 1, 'Transfer', '调拨出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 2, 'ReturnToSupplier', '退供出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 3, 'ProductionMaterial', '生产领料出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 99, 'Other', '其他出库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: OutboundDocumentType (Matches WMS.Enums.DocumentType, but scoped here)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('OutboundDocumentType', 0, 'SO', 'SO (Sales Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundDocumentType', 1, 'STO', 'STO (Stock Transfer Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundDocumentType', 2, 'RTV', 'RTV (Return to Vendor)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundDocumentType', 3, 'WO', 'WO (Work Order/Material Issue)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Enums.PermissionEnums

-- Enum: PermissionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PermissionType', 1, 'Menu', '菜单权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 2, 'Button', '按钮权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 3, 'Api', 'API权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 4, 'Data', '数据权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 5, 'Field', '字段权限', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PermissionScope
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PermissionScope', 1, 'Global', '全局范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 2, 'Role', '角色范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 3, 'User', '用户范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 4, 'Department', '部门范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 5, 'Custom', '自定义范围', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.PlanEnums

-- Enum: PlanType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PlanType', 0, 'Inbound', '入库计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 1, 'Outbound', '出库计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 2, 'Production', '生产计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 3, 'Replenishment', '补货计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 4, 'CycleCount', '盘点计划', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PlanStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PlanStatus', 0, 'Draft', '草稿', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 1, 'PendingApproval', '待审批', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 2, 'Approved', '已审批', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 3, 'InProgress', '执行中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 4, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 6, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.PrinterEnums

-- Enum: PrinterType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PrinterType', 0, 'Label', '标签打印机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterType', 1, 'Document', '文档打印机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterType', 2, 'Network', '网络打印机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterType', 3, 'Local', '本地打印机', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PrinterStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PrinterStatus', 0, 'Online', '在线', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterStatus', 1, 'Offline', '离线', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterStatus', 2, 'Error', '错误', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterStatus', 3, 'Busy', '繁忙', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.QualityEnums

-- Enum: QualityInspectionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityInspectionType', 0, 'Incoming', '来料检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 1, 'InProcess', '过程检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 2, 'Outgoing', '出货检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 3, 'Inventory', '库存抽检', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: QualityInspectionStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityInspectionStatus', 0, 'Pending', '待检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionStatus', 1, 'InProgress', '检验中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionStatus', 3, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: QualityResult
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityResult', 0, 'Pass', '合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityResult', 1, 'Fail', '不合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityResult', 2, 'ConditionalPass', '让步接收', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: DefectLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DefectLevel', 0, 'Critical', '致命缺陷', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DefectLevel', 1, 'Major', '主要缺陷', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DefectLevel', 2, 'Minor', '次要缺陷', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.ReportEnums

-- Enum: ReportType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ReportType', 0, 'InventoryStatus', '库存状态报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 1, 'InboundPerformance', '入库绩效报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 2, 'OutboundPerformance', '出库绩效报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 3, 'StorageUtilization', '库容利用率报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 4, 'CycleCountAccuracy', '盘点准确率报告', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ReportFormat
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ReportFormat', 0, 'PDF', 'PDF格式', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFormat', 1, 'Excel', 'Excel格式', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFormat', 2, 'CSV', 'CSV格式', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFormat', 3, 'HTML', 'HTML格式', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ReportFrequency
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ReportFrequency', 0, 'Daily', '每日', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 1, 'Weekly', '每周', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 2, 'Monthly', '每月', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 3, 'Quarterly', '每季度', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 4, 'Annually', '每年', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 5, 'OnDemand', '按需', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Enums.RoleType

-- Enum: RoleType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RoleType', 1, 'SystemAdmin', '系统管理员', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 2, 'TenantAdmin', '租户管理员', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 3, 'WarehouseManager', '仓库经理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 4, 'Operator', '操作员', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 5, 'Viewer', '查看员', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.RuleEnums

-- Enum: RuleType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RuleType', 0, 'Putaway', '上架规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 1, 'Picking', '拣货规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 2, 'Replenishment', '补货规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 3, 'Allocation', '分配规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 4, 'Rotation', '周转规则 (FIFO/LIFO)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: RuleConditionOperator
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RuleConditionOperator', 0, 'Equals', '等于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 1, 'NotEquals', '不等于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 2, 'GreaterThan', '大于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 3, 'LessThan', '小于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 4, 'Contains', '包含', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 5, 'StartsWith', '开头是', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 6, 'EndsWith', '结尾是', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.ShippingEnums

-- Enum: ShipmentStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ShipmentStatus', 0, 'Pending', '待发运', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 1, 'InTransit', '运输中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 2, 'Delivered', '已送达', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 3, 'Delayed', '已延迟', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CarrierType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CarrierType', 0, 'ThirdPartyLogistics', '第三方物流', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CarrierType', 1, 'OwnFleet', '自有车队', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CarrierType', 2, 'Express', '快递公司', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CarrierType', 3, 'Postal', '邮政', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.StockTakingEnums

-- Enum: StockTakingStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StockTakingStatus', 0, 'Pending', '待盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 1, 'InProgress', '盘点中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 3, 'Auditing', '审核中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 4, 'Adjusted', '已调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: StockTakingType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StockTakingType', 0, 'Full', '全盘', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingType', 1, 'CycleCount', '循环盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingType', 2, 'ByLocation', '按库位盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingType', 3, 'ByMaterial', '按物料盘点', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.StrategyEnums

-- Enum: StrategyType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StrategyType', 0, 'Putaway', '上架策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyType', 1, 'Picking', '拣货策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyType', 2, 'Replenishment', '补货策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyType', 3, 'Rotation', '周转策略 (如FIFO, FEFO)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: StrategyStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StrategyStatus', 0, 'Active', '激活', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyStatus', 1, 'Inactive', '未激活', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyStatus', 2, 'Draft', '草稿', NULL, GETDATE(), NULL, NULL, NULL, 1);

// ... existing code ...

-- Inserting enums from bnred.Model.WMS.Enums.TaskEnums

-- Enum: TaskType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskType', 0, 'Putaway', '上架任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 1, 'Picking', '拣货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 2, 'Replenishment', '补货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 3, 'CycleCount', '盘点任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 4, 'Movement', '移库任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 5, 'QualityInspection', '质检任务', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskStatus', 0, 'Pending', '待分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 1, 'Assigned', '已分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 2, 'InProgress', '执行中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 3, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 5, 'OnHold', '已暂停', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskPriority
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskPriority', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskPriority', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskPriority', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskPriority', 3, 'Urgent', '紧急', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.TransactionEnums

-- Enum: TransactionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransactionType', 0, 'Inbound', '入库事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 1, 'Outbound', '出库事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 2, 'Adjustment', '调整事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 3, 'Transfer', '转移事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 4, 'Stocktake', '盘点事务', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TransactionStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransactionStatus', 0, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 1, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 3, 'Failed', '已失败', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.TransferEnums

-- Enum: TransferType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransferType', 0, 'WarehouseToWarehouse', '仓库间调拨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferType', 1, 'LocationToLocation', '库位间调拨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferType', 2, 'StockToStock', '库存状态间调拨', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TransferStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransferStatus', 0, 'Pending', '待调拨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 1, 'InProgress', '调拨中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 2, 'Shipped', '已发运 (出库方)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 3, 'Received', '已接收 (入库方)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 4, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

    -- Inserting enums from bnred.Model.Enums.UserType

-- Enum: UserType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('UserType', 1, 'Internal', '内部用户', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UserType', 2, 'External', '外部用户', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UserType', 3, 'System', '系统用户', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UserType', 4, 'ServiceAccount', '服务账户', NULL, GETDATE(), NULL, NULL, NULL, 1);

    -- Enum: WarehouseType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WarehouseType', 0, 'DistributionCenter', '配送中心', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 1, 'RegionalHub', '区域枢纽', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 2, 'LocalBranch', '本地分仓', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 3, 'BondedWarehouse', '保税仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 4, 'CrossDock', '越库中转仓', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: WarehouseStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WarehouseStatus', 0, 'Active', '运营中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseStatus', 1, 'Inactive', '已停用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseStatus', 2, 'UnderConstruction', '建设中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseStatus', 3, 'Maintenance', '维护中', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Enum Type: InventoryAlertLevel
-- Source File: bnred.Model/WMS/Analysis/WarehouseAnalytics.cs
INSERT INTO [WMS_EnumDictionary] ([EnumTypeName], [EnumName], [EnumValue], [EnumDescription], [Remark], [SortOrder], [TenantCode], [CreateTime], [CreateBy], [UpdateTime], [UpdateBy]) VALUES (N'InventoryAlertLevel', N'Normal', 0, N'正常', N'来自 WarehouseAnalytics.cs', 0, N'SYSTEM', GETDATE(), N'SYSTEM', GETDATE(), N'SYSTEM');
INSERT INTO [WMS_EnumDictionary] ([EnumTypeName], [EnumName], [EnumValue], [EnumDescription], [Remark], [SortOrder], [TenantCode], [CreateTime], [CreateBy], [UpdateTime], [UpdateBy]) VALUES (N'InventoryAlertLevel', N'Warning', 1, N'警告', N'来自 WarehouseAnalytics.cs', 1, N'SYSTEM', GETDATE(), N'SYSTEM', GETDATE(), N'SYSTEM');
INSERT INTO [WMS_EnumDictionary] ([EnumTypeName], [EnumName], [EnumValue], [EnumDescription], [Remark], [SortOrder], [TenantCode], [CreateTime], [CreateBy], [UpdateTime], [UpdateBy]) VALUES (N'InventoryAlertLevel', N'Low', 2, N'库存不足', N'来自 WarehouseAnalytics.cs', 2, N'SYSTEM', GETDATE(), N'SYSTEM', GETDATE(), N'SYSTEM');
INSERT INTO [WMS_EnumDictionary] ([EnumTypeName], [EnumName], [EnumValue], [EnumDescription], [Remark], [SortOrder], [TenantCode], [CreateTime], [CreateBy], [UpdateTime], [UpdateBy]) VALUES (N'InventoryAlertLevel', N'High', 3, N'库存过高', N'来自 WarehouseAnalytics.cs', 3, N'SYSTEM', GETDATE(), N'SYSTEM', GETDATE(), N'SYSTEM');
GO    