using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Material
{
    /// <summary>
    /// 物料本地化信息
    /// 用于存储物料的多语言信息
    /// </summary>
    [Table("MaterialLocalizations")]
    [Index(nameof(MaterialId), nameof(LanguageCode), IsUnique = true)]
    public class MaterialLocalization : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 物料ID
        /// </summary>
        [Display(Name = "物料")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material Material { get; set; }

        /// <summary>
        /// 语言代码
        /// </summary>
        [Display(Name = "语言代码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(10)]
        public string LanguageCode { get; set; }

        /// <summary>
        /// 本地化名称
        /// </summary>
        [Display(Name = "本地化名称")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(100)]
        public string LocalizedName { get; set; }

        /// <summary>
        /// 本地化描述
        /// </summary>
        [Display(Name = "本地化描述")]
        [StringLength(500)]
        public string LocalizedDescription { get; set; }

        /// <summary>
        /// 本地化规格
        /// </summary>
        [Display(Name = "本地化规格")]
        [StringLength(100)]
        public string LocalizedSpecification { get; set; }

        /// <summary>
        /// 本地化单位
        /// </summary>
        [Display(Name = "本地化单位")]
        [StringLength(20)]
        public string LocalizedUnit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
}