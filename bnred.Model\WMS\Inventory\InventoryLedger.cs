using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Inventory
{
    /// <summary>
    /// 库存台账
    /// 记录库存变动的完整历史
    /// </summary>
    [Table("WMS_InventoryLedgers")]
    public class InventoryLedger : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 台账编号
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "台账编号")]
        public string LedgerNo { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string? BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        
        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [Display(Name = "库位")]
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public Location Location { get; set; }

        /// <summary>
        /// 变动类型
        /// </summary>
        [Required]
        [Display(Name = "变动类型")]
        public MovementType MovementType { get; set; }

        /// <summary>
        /// 变动前数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        [Display(Name = "变动前数量")]
        public decimal QuantityBefore { get; set; }

        /// <summary>
        /// 变动数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        [Display(Name = "变动数量")]
        public decimal QuantityChange { get; set; }

        /// <summary>
        /// 变动后数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        [Display(Name = "变动后数量")]
        public decimal QuantityAfter { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [StringLength(20)]
        [Display(Name = "单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        [Required]
        [Display(Name = "操作人")]
     
        public Guid?  OperatorId { get; set; }

 
        public FrameworkUser Operator { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Required]
        [Display(Name = "操作时间")]
        public DateTime OperationTime { get; set; }

        /// <summary>
        /// 关联单据号
        /// </summary>
        [StringLength(50)]
        [Display(Name = "关联单据号")]
        public string RelatedOrderNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}