-- 仓库表 (基于 Warehouse.cs 和 WarehouseBase.cs)
CREATE TABLE WMS_Warehouses (
    ID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(), -- 主键
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    WarehouseCode NVARCHAR(50) NOT NULL UNIQUE, -- 仓库代码，唯一
    WarehouseName NVARCHAR(255) NOT NULL, -- 仓库名称
    WarehouseType NVARCHAR(50), -- 仓库类型 (例如: RawMaterial, FinishedGoods, DistributionCenter)
    Description NVARCHAR(MAX), -- 仓库描述
    Country NVARCHAR(100), -- 国家
    Province NVARCHAR(100), -- 省份
    City NVARCHAR(100), -- 城市
    District NVARCHAR(100), -- 区/县
    StreetAddress NVARCHAR(255), -- 街道地址
    PostalCode NVARCHAR(20), -- 邮政编码
    Latitude DECIMAL(9,6), -- 纬度
    Longitude DECIMAL(9,6), -- 经度
    ContactPerson NVARCHAR(100), -- 联系人
    ContactPhone NVARCHAR(50), -- 联系电话
    ContactEmail NVARCHAR(100), -- 联系邮箱
    Status NVARCHAR(50) DEFAULT 'Active', -- 状态 (例如: Active, Inactive, UnderMaintenance)
    Capacity DECIMAL(18,4), -- 总容量
    CapacityUnit NVARCHAR(20), -- 容量单位 (例如: SQM, CBM, KG)
    TemperatureControlled BIT DEFAULT 0, -- 是否温控
    MinTemperature DECIMAL(5,2), -- 最低温度 (如果温控)
    MaxTemperature DECIMAL(5,2), -- 最高温度 (如果温控)
    SecurityLevel NVARCHAR(50), -- 安全级别
    OperatingHours NVARCHAR(100), -- 营业时间
    IsDefault BIT DEFAULT 0, -- 是否为默认仓库
    CustomAttributes NVARCHAR(MAX), -- 自定义属性 (JSON格式)
    IsEnabled BIT DEFAULT 1, -- 是否启用
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID) -- 假设存在 WMS_FrameworkTenants 表
    -- 其他外键约束，例如 CreateBy, UpdateBy 可能关联到 WMS_FrameworkUsers 表
);

-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '仓库代码，唯一', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'WarehouseCode';
EXEC sp_addextendedproperty 'MS_Description', '仓库名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'WarehouseName';
EXEC sp_addextendedproperty 'MS_Description', '仓库类型 (例如: RawMaterial, FinishedGoods, DistributionCenter)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'WarehouseType';
EXEC sp_addextendedproperty 'MS_Description', '仓库描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Description';
EXEC sp_addextendedproperty 'MS_Description', '国家', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Country';
EXEC sp_addextendedproperty 'MS_Description', '省份', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Province';
EXEC sp_addextendedproperty 'MS_Description', '城市', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'City';
EXEC sp_addextendedproperty 'MS_Description', '区/县', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'District';
EXEC sp_addextendedproperty 'MS_Description', '街道地址', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'StreetAddress';
EXEC sp_addextendedproperty 'MS_Description', '邮政编码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'PostalCode';
EXEC sp_addextendedproperty 'MS_Description', '纬度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Latitude';
EXEC sp_addextendedproperty 'MS_Description', '经度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Longitude';
EXEC sp_addextendedproperty 'MS_Description', '联系人', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'ContactPerson';
EXEC sp_addextendedproperty 'MS_Description', '联系电话', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'ContactPhone';
EXEC sp_addextendedproperty 'MS_Description', '联系邮箱', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'ContactEmail';
EXEC sp_addextendedproperty 'MS_Description', '状态 (例如: Active, Inactive, UnderMaintenance)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Status';
EXEC sp_addextendedproperty 'MS_Description', '总容量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'Capacity';
EXEC sp_addextendedproperty 'MS_Description', '容量单位 (例如: SQM, CBM, KG)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'CapacityUnit';
EXEC sp_addextendedproperty 'MS_Description', '是否温控', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'TemperatureControlled';
EXEC sp_addextendedproperty 'MS_Description', '最低温度 (如果温控)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'MinTemperature';
EXEC sp_addextendedproperty 'MS_Description', '最高温度 (如果温控)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'MaxTemperature';
EXEC sp_addextendedproperty 'MS_Description', '安全级别', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'SecurityLevel';
EXEC sp_addextendedproperty 'MS_Description', '营业时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'OperatingHours';
EXEC sp_addextendedproperty 'MS_Description', '是否为默认仓库', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'IsDefault';
EXEC sp_addextendedproperty 'MS_Description', '自定义属性 (JSON格式)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'CustomAttributes';
EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'IsEnabled';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Warehouses', 'COLUMN', 'UpdateBy';

-- 仓库库区表 (基于 WarehouseArea.cs)
CREATE TABLE WMS_WarehouseAreas (
    ID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(), -- 主键
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 所属仓库ID
    AreaCode NVARCHAR(50) NOT NULL, -- 库区代码
    AreaName NVARCHAR(255) NOT NULL, -- 库区名称
    AreaType NVARCHAR(50), -- 库区类型 (例如: Staging, Storage, Picking, Shipping)
    Description NVARCHAR(MAX), -- 描述
    Capacity DECIMAL(18,4), -- 容量
    CapacityUnit NVARCHAR(20), -- 容量单位
    TemperatureRange NVARCHAR(50), -- 温度范围
    HumidityRange NVARCHAR(50), -- 湿度范围
    Status NVARCHAR(50) DEFAULT 'Active', -- 状态
    IsDefault BIT DEFAULT 0, -- 是否默认库区
    CustomAttributes NVARCHAR(MAX), -- 自定义属性 (JSON格式)
    IsEnabled BIT DEFAULT 1, -- 是否启用
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    UNIQUE (WarehouseId, AreaCode) -- 仓库内库区代码唯一
);

-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库库区信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '所属仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '库区代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'AreaCode';
EXEC sp_addextendedproperty 'MS_Description', '库区名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'AreaName';
EXEC sp_addextendedproperty 'MS_Description', '库区类型 (例如: Staging, Storage, Picking, Shipping)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'AreaType';
EXEC sp_addextendedproperty 'MS_Description', '描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'Description';
EXEC sp_addextendedproperty 'MS_Description', '容量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'Capacity';
EXEC sp_addextendedproperty 'MS_Description', '容量单位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'CapacityUnit';
EXEC sp_addextendedproperty 'MS_Description', '温度范围', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'TemperatureRange';
EXEC sp_addextendedproperty 'MS_Description', '湿度范围', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'HumidityRange';
EXEC sp_addextendedproperty 'MS_Description', '状态', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'Status';
EXEC sp_addextendedproperty 'MS_Description', '是否默认库区', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'IsDefault';
EXEC sp_addextendedproperty 'MS_Description', '自定义属性 (JSON格式)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'CustomAttributes';
EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'IsEnabled';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAreas', 'COLUMN', 'UpdateBy';

-- 仓库库位表 (基于 Location.cs)
CREATE TABLE WMS_Locations (
    ID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(), -- 主键
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    WarehouseAreaId UNIQUEIDENTIFIER NOT NULL, -- 所属库区ID
    LocationCode NVARCHAR(50) NOT NULL, -- 库位代码
    LocationName NVARCHAR(255), -- 库位名称 (可选)
    LocationType NVARCHAR(50), -- 库位类型 (例如: Rack, Floor, Bin)
    Barcode NVARCHAR(100) UNIQUE, -- 库位条码，唯一
    Description NVARCHAR(MAX), -- 描述
    X_Coordinate DECIMAL(10,2), -- X坐标 (货架层内位置)
    Y_Coordinate DECIMAL(10,2), -- Y坐标 (货架列)
    Z_Coordinate DECIMAL(10,2), -- Z坐标 (货架层)
    Aisle NVARCHAR(50), --巷道
    Rack NVARCHAR(50), --货架号
    LevelInRack NVARCHAR(50), --货架层
    PositionInLevel NVARCHAR(50), --货架层内位置
    DimensionUnit NVARCHAR(20) DEFAULT 'CM', -- 尺寸单位
    Length DECIMAL(18,4), -- 长度
    Width DECIMAL(18,4), -- 宽度
    Height DECIMAL(18,4), -- 高度
    MaxWeight DECIMAL(18,4), -- 最大承重
    WeightUnit NVARCHAR(20) DEFAULT 'KG', -- 重量单位
    Status NVARCHAR(50) DEFAULT 'Empty', -- 库位状态 (例如: Empty, Occupied, Reserved, Damaged, Unavailable)
    StorageType NVARCHAR(50), -- 存储类型 (例如: Pallet, Box, Each)
    PickingZone NVARCHAR(50), --拣货区域
    IsPickable BIT DEFAULT 1, -- 是否可拣货
    IsReceiving BIT DEFAULT 1, -- 是否可收货
    IsShipping BIT DEFAULT 0, -- 是否发货暂存位
    LastActivityDate DATETIME2, -- 最近活动日期
    CustomAttributes NVARCHAR(MAX), -- 自定义属性 (JSON格式)
    IsEnabled BIT DEFAULT 1, -- 是否启用
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    FOREIGN KEY (WarehouseAreaId) REFERENCES WMS_WarehouseAreas(ID) ON DELETE CASCADE,
    UNIQUE (WarehouseAreaId, LocationCode) -- 库区内库位代码唯一
);

-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库库位信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '所属库区ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'WarehouseAreaId';
EXEC sp_addextendedproperty 'MS_Description', '库位代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'LocationCode';
EXEC sp_addextendedproperty 'MS_Description', '库位名称 (可选)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'LocationName';
EXEC sp_addextendedproperty 'MS_Description', '库位类型 (例如: Rack, Floor, Bin)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'LocationType';
EXEC sp_addextendedproperty 'MS_Description', '库位条码，唯一', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Barcode';
EXEC sp_addextendedproperty 'MS_Description', '描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Description';
EXEC sp_addextendedproperty 'MS_Description', 'X坐标 (货架层内位置)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'X_Coordinate';
EXEC sp_addextendedproperty 'MS_Description', 'Y坐标 (货架列)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Y_Coordinate';
EXEC sp_addextendedproperty 'MS_Description', 'Z坐标 (货架层)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Z_Coordinate';
EXEC sp_addextendedproperty 'MS_Description', '巷道', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Aisle';
EXEC sp_addextendedproperty 'MS_Description', '货架号', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Rack';
EXEC sp_addextendedproperty 'MS_Description', '货架层', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'LevelInRack';
EXEC sp_addextendedproperty 'MS_Description', '货架层内位置', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'PositionInLevel';
EXEC sp_addextendedproperty 'MS_Description', '尺寸单位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'DimensionUnit';
EXEC sp_addextendedproperty 'MS_Description', '长度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Length';
EXEC sp_addextendedproperty 'MS_Description', '宽度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Width';
EXEC sp_addextendedproperty 'MS_Description', '高度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Height';
EXEC sp_addextendedproperty 'MS_Description', '最大承重', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'MaxWeight';
EXEC sp_addextendedproperty 'MS_Description', '重量单位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'WeightUnit';
EXEC sp_addextendedproperty 'MS_Description', '库位状态 (例如: Empty, Occupied, Reserved, Damaged, Unavailable)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'Status';
EXEC sp_addextendedproperty 'MS_Description', '存储类型 (例如: Pallet, Box, Each)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'StorageType';
EXEC sp_addextendedproperty 'MS_Description', '拣货区域', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'PickingZone';
EXEC sp_addextendedproperty 'MS_Description', '是否可拣货', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'IsPickable';
EXEC sp_addextendedproperty 'MS_Description', '是否可收货', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'IsReceiving';
EXEC sp_addextendedproperty 'MS_Description', '是否发货暂存位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'IsShipping';
EXEC sp_addextendedproperty 'MS_Description', '最近活动日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'LastActivityDate';
EXEC sp_addextendedproperty 'MS_Description', '自定义属性 (JSON格式)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'CustomAttributes';
EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'IsEnabled';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_Locations', 'COLUMN', 'UpdateBy';

-- 请注意:
-- 1. WarehouseType, AreaType, LocationType, Status (WMS_Warehouses, WMS_WarehouseAreas, WMS_Locations), CapacityUnit, DimensionUnit, WeightUnit 等字段可能适合作为枚举存储在 WMS_EnumDictionary 表中。
--    请检查这些模型中是否定义了相应的枚举类，并将它们添加到 WMS_EnumDictionary_Data.sql。
-- 2. TenantId, CreateBy, UpdateBy 通常会引用外部的租户和用户表。这里假设为 WMS_FrameworkTenants 和 WMS_FrameworkUsers。
--    如果实际表名不同，请相应修改 FOREIGN KEY 约束。
-- 3. WMS_Locations.Barcode 字段添加了 UNIQUE 约束，确保库位条码的唯一性。
-- 4. WMS_Warehouses.WarehouseCode, WMS_WarehouseAreas (WarehouseId, AreaCode), WMS_Locations (WarehouseAreaId, LocationCode) 添加了组合唯一约束。

-- 库位扩展信息表 (基于 LocationExtension.cs)
CREATE TABLE WMS_LocationExtensions (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID, 对应C#中的string ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (假设继承自BasePoco或上下文中存在)
    LocationId UNIQUEIDENTIFIER NOT NULL, -- 库位ID
    Length DECIMAL(18,4) NOT NULL, -- 长度
    Width DECIMAL(18,4) NOT NULL, -- 宽度
    Height DECIMAL(18,4) NOT NULL, -- 高度
    MaxWeight DECIMAL(18,4) NOT NULL, -- 最大承重
    MaxVolume DECIMAL(18,4) NOT NULL, -- 最大体积
    AllowMixed BIT NOT NULL, -- 是否允许混放
    AllowNested BIT NOT NULL, -- 是否允许嵌套（例如托盘上再放物料）
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (LocationId) REFERENCES WMS_Locations(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID) -- 假设的租户表
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '库位扩展信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '库位ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'LocationId';
EXEC sp_addextendedproperty 'MS_Description', '长度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'Length';
EXEC sp_addextendedproperty 'MS_Description', '宽度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'Width';
EXEC sp_addextendedproperty 'MS_Description', '高度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'Height';
EXEC sp_addextendedproperty 'MS_Description', '最大承重', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'MaxWeight';
EXEC sp_addextendedproperty 'MS_Description', '最大体积', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'MaxVolume';
EXEC sp_addextendedproperty 'MS_Description', '是否允许混放', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'AllowMixed';
EXEC sp_addextendedproperty 'MS_Description', '是否允许嵌套', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'AllowNested';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationExtensions', 'COLUMN', 'UpdateBy';

-- 库位多语言表 (基于 LocationLocalization.cs)
CREATE TABLE WMS_LocationLocalizations (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    LocationId UNIQUEIDENTIFIER NOT NULL, -- 库位ID
    Culture NVARCHAR(10) NOT NULL, -- 语言文化代码 (例如: zh-CN, en-US)
    Name NVARCHAR(100) NOT NULL, -- 库位在此语言下的名称
    Description NVARCHAR(200), -- 描述
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (LocationId) REFERENCES WMS_Locations(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    UNIQUE (LocationId, Culture) -- 同一库位同一语言只能有一条记录
);
EXEC sp_addextendedproperty 'MS_Description', '库位多语言信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '库位ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'LocationId';
EXEC sp_addextendedproperty 'MS_Description', '语言文化代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'Culture';
EXEC sp_addextendedproperty 'MS_Description', '库位在此语言下的名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'Name';
EXEC sp_addextendedproperty 'MS_Description', '描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'Description';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_LocationLocalizations', 'COLUMN', 'Remark';

-- 仓库地址表 (基于 WarehouseAddress.cs)
-- 注意: WMS_Warehouses 表中已有地址字段。此表用于一个仓库有多个地址或更详细的地址信息。
CREATE TABLE WMS_WarehouseAddresses (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER,
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 仓库ID
    Address NVARCHAR(200) NOT NULL, -- 详细地址
    City NVARCHAR(50), -- 城市
    State NVARCHAR(50), -- 省份/州
    Country NVARCHAR(50), -- 国家
    ZipCode NVARCHAR(20), -- 邮政编码
    Latitude DECIMAL(18,6), -- 纬度
    Longitude DECIMAL(18,6), -- 经度
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID)
);
EXEC sp_addextendedproperty 'MS_Description', '仓库地址表 (用于一个仓库多个地址或补充地址信息)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '详细地址', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'Address';
EXEC sp_addextendedproperty 'MS_Description', '城市', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'City';
EXEC sp_addextendedproperty 'MS_Description', '省份/州', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'State';
EXEC sp_addextendedproperty 'MS_Description', '国家', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'Country';
EXEC sp_addextendedproperty 'MS_Description', '邮政编码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'ZipCode';
EXEC sp_addextendedproperty 'MS_Description', '纬度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'Latitude';
EXEC sp_addextendedproperty 'MS_Description', '经度', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'Longitude';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseAddresses', 'COLUMN', 'Remark';

-- 仓库营业时间表 (基于 WarehouseBusinessHour.cs)
CREATE TABLE WMS_WarehouseBusinessHours (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER,
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 仓库ID
    DayOfWeek INT NOT NULL, --星期几 (0=Sunday, 1=Monday, ... 6=Saturday, 与C# DayOfWeek对应)
    OpenTime TIME NOT NULL, -- 开始营业时间
    CloseTime TIME NOT NULL, -- 结束营业时间
    IsOpen BIT NOT NULL, -- 当天是否营业
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    UNIQUE (WarehouseId, DayOfWeek) --一个仓库一天只能有一条营业时间记录
);
EXEC sp_addextendedproperty 'MS_Description', '仓库营业时间表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '星期几 (0=Sunday...6=Saturday)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'DayOfWeek';
EXEC sp_addextendedproperty 'MS_Description', '开始营业时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'OpenTime';
EXEC sp_addextendedproperty 'MS_Description', '结束营业时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'CloseTime';
EXEC sp_addextendedproperty 'MS_Description', '当天是否营业', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'IsOpen';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseBusinessHours', 'COLUMN', 'Remark';

-- 仓库联系人表 (基于 WarehouseContact.cs)
-- 注意: WMS_Warehouses 表中已有主要联系人字段。此表用于存储多个或更详细的联系人信息。
CREATE TABLE WMS_WarehouseContacts (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER,
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 仓库ID
    Name NVARCHAR(50) NOT NULL, -- 联系人姓名
    Position NVARCHAR(50) NOT NULL, -- 职位
    Phone NVARCHAR(20), -- 电话
    Email NVARCHAR(100), -- 邮箱
    IsPrimary BIT NOT NULL DEFAULT 0, -- 是否主要联系人
    IsActive BIT NOT NULL DEFAULT 1, -- 是否有效
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID)
);
EXEC sp_addextendedproperty 'MS_Description', '仓库联系人表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '联系人姓名', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'Name';
EXEC sp_addextendedproperty 'MS_Description', '职位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'Position';
EXEC sp_addextendedproperty 'MS_Description', '电话', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'Phone';
EXEC sp_addextendedproperty 'MS_Description', '邮箱', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'Email';
EXEC sp_addextendedproperty 'MS_Description', '是否主要联系人', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'IsPrimary';
EXEC sp_addextendedproperty 'MS_Description', '是否有效', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'IsActive';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseContacts', 'COLUMN', 'Remark';


-- 仓库文档表 (基于 WarehouseDocument.cs)
CREATE TABLE WMS_WarehouseDocuments (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER,
    DocumentNo NVARCHAR(50) NOT NULL UNIQUE, -- 文档编号，唯一
    DocumentName NVARCHAR(100) NOT NULL, -- 文档名称
    DocumentType INT NOT NULL, -- 文档类型 (对应 DocumentType 枚举, 请在 WMS_EnumDictionary 中定义)
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 所属仓库ID
    FilePath NVARCHAR(500), -- 文件路径
    FileSize BIGINT, -- 文件大小(KB)
    FileType NVARCHAR(50), -- 文件类型 (例如: pdf, docx, jpg)
    UploadTime DATETIME2 NOT NULL DEFAULT GETDATE(), -- 上传时间
    UploaderId UNIQUEIDENTIFIER, -- 上传人ID (关联 WMS_FrameworkUsers)
    IsActive BIT NOT NULL DEFAULT 1, -- 是否有效
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (UploaderId) REFERENCES WMS_FrameworkUsers(ID),
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID)
);
EXEC sp_addextendedproperty 'MS_Description', '仓库文档信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '文档编号，唯一', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'DocumentNo';
EXEC sp_addextendedproperty 'MS_Description', '文档名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'DocumentName';
EXEC sp_addextendedproperty 'MS_Description', '文档类型 (枚举)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'DocumentType';
EXEC sp_addextendedproperty 'MS_Description', '所属仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '文件路径', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'FilePath';
EXEC sp_addextendedproperty 'MS_Description', '文件大小(KB)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'FileSize';
EXEC sp_addextendedproperty 'MS_Description', '文件类型', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'FileType';
EXEC sp_addextendedproperty 'MS_Description', '上传时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'UploadTime';
EXEC sp_addextendedproperty 'MS_Description', '上传人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'UploaderId';
EXEC sp_addextendedproperty 'MS_Description', '是否有效', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'IsActive';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseDocuments', 'COLUMN', 'Remark';
-- 提醒: DocumentType 枚举需要添加到 WMS_EnumDictionary_Data.sql

-- 仓库设备表 (基于 WarehouseEquipment.cs)
CREATE TABLE WMS_WarehouseEquipments (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER,
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 所属仓库ID
    Code NVARCHAR(50) NOT NULL, -- 设备编码
    Name NVARCHAR(100) NOT NULL, -- 设备名称
    Type INT NOT NULL, -- 设备类型 (对应 EquipmentType 枚举)
    Status INT NOT NULL, -- 设备状态 (对应 EquipmentStatus 枚举)
    LastMaintenanceDate DATETIME2, -- 上次维护日期
    NextMaintenanceDate DATETIME2, -- 下次维护日期
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    UNIQUE (WarehouseId, Code) --同一仓库设备编码唯一
);
EXEC sp_addextendedproperty 'MS_Description', '仓库设备信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '所属仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '设备编码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'Code';
EXEC sp_addextendedproperty 'MS_Description', '设备名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'Name';
EXEC sp_addextendedproperty 'MS_Description', '设备类型 (枚举)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'Type';
EXEC sp_addextendedproperty 'MS_Description', '设备状态 (枚举)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'Status';
EXEC sp_addextendedproperty 'MS_Description', '上次维护日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'LastMaintenanceDate';
EXEC sp_addextendedproperty 'MS_Description', '下次维护日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'NextMaintenanceDate';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipments', 'COLUMN', 'Remark';
-- 提醒: EquipmentType, EquipmentStatus 枚举需要添加到 WMS_EnumDictionary_Data.sql

-- 仓库设备维护记录表 (基于 WarehouseEquipmentMaintenance.cs)
CREATE TABLE WMS_WarehouseEquipmentMaintenances (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER,
    EquipmentId NVARCHAR(50) NOT NULL, -- 设备ID
    MaintenanceDate DATETIME2 NOT NULL, -- 维护日期
    MaintenanceBy NVARCHAR(100) NOT NULL, -- 维护人/单位
    MaintenanceCost DECIMAL(18,2) NOT NULL, -- 维护成本
    MaintenanceDetails NVARCHAR(1000), -- 维护详情
    IsCompleted BIT NOT NULL DEFAULT 0, -- 是否完成
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(),
    CreateBy UNIQUEIDENTIFIER,
    UpdateTime DATETIME2,
    UpdateBy UNIQUEIDENTIFIER,
    FOREIGN KEY (EquipmentId) REFERENCES WMS_WarehouseEquipments(ID) ON DELETE CASCADE, 
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID)
);
EXEC sp_addextendedproperty 'MS_Description', '仓库设备维护记录表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances';
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '设备ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'EquipmentId';
EXEC sp_addextendedproperty 'MS_Description', '维护日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'MaintenanceDate';
EXEC sp_addextendedproperty 'MS_Description', '维护人/单位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'MaintenanceBy';
EXEC sp_addextendedproperty 'MS_Description', '维护成本', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'MaintenanceCost';
EXEC sp_addextendedproperty 'MS_Description', '维护详情', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'MaintenanceDetails';
EXEC sp_addextendedproperty 'MS_Description', '是否完成', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'IsCompleted';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseEquipmentMaintenances', 'COLUMN', 'Remark'; 


-- 仓库扩展信息表 (基于 WarehouseExtension.cs)
-- 此表用于存储WMS_Warehouses表无法直接包含的额外属性或特定配置。
CREATE TABLE WMS_WarehouseExtensions (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 关联的仓库ID
    ExtensionKey NVARCHAR(100) NOT NULL, -- 扩展属性的键
    ExtensionValue NVARCHAR(MAX) NOT NULL, -- 扩展属性的值 (可以是JSON或其他格式)
    DataType NVARCHAR(50), -- 扩展属性值的数据类型 (例如: string, number, boolean, json)
    Description NVARCHAR(500), -- 扩展属性的描述
    IsSystemDefined BIT DEFAULT 0, -- 是否为系统预定义扩展
    IsActive BIT DEFAULT 1, -- 是否激活
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    UNIQUE (WarehouseId, ExtensionKey) -- 同一仓库的扩展键必须唯一
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库扩展信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '关联的仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '扩展属性的键', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'ExtensionKey';
EXEC sp_addextendedproperty 'MS_Description', '扩展属性的值', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'ExtensionValue';
EXEC sp_addextendedproperty 'MS_Description', '扩展属性值的数据类型', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'DataType';
EXEC sp_addextendedproperty 'MS_Description', '扩展属性的描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'Description';
EXEC sp_addextendedproperty 'MS_Description', '是否为系统预定义扩展', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'IsSystemDefined';
EXEC sp_addextendedproperty 'MS_Description', '是否激活', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'IsActive';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseExtensions', 'COLUMN', 'UpdateBy';

-- 仓库KPI指标表 (基于 WarehouseKPI.cs)
CREATE TABLE WMS_WarehouseKPIs (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 关联的仓库ID
    KPIName NVARCHAR(100) NOT NULL, -- KPI名称 (例如: 库存准确率, 订单准时发货率)
    KPICategory NVARCHAR(50), -- KPI类别 (例如: 库存管理, 订单履行, 成本控制)
    KPIType INT, -- KPI类型 (可能对应枚举, 例如: Percentage, Value, Ratio. 请在WMS_EnumDictionary中定义)
    TargetValue DECIMAL(18,4), -- 目标值
    ActualValue DECIMAL(18,4), -- 实际值
    Variance DECIMAL(18,4), -- 差异 (实际值 - 目标值)
    MeasurementDate DATETIME2 NOT NULL, -- 测量日期/周期结束日期
    CalculationPeriod NVARCHAR(50), -- 计算周期 (例如: Daily, Weekly, Monthly)
    UnitOfMeasure NVARCHAR(20), -- 度量单位 (例如: %, $, 件)
    Trend NVARCHAR(20), -- 趋势 (例如: Improving, Declining, Stable)
    DataSource NVARCHAR(255), -- 数据来源描述
    ResponsiblePersonId UNIQUEIDENTIFIER, -- 负责人ID (关联WMS_FrameworkUsers)
    Remarks NVARCHAR(MAX), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    FOREIGN KEY (ResponsiblePersonId) REFERENCES WMS_FrameworkUsers(ID) -- 假设的用户表
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库KPI指标表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '关联的仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', 'KPI名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'KPIName';
EXEC sp_addextendedproperty 'MS_Description', 'KPI类别', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'KPICategory';
EXEC sp_addextendedproperty 'MS_Description', 'KPI类型 (枚举)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'KPIType';
EXEC sp_addextendedproperty 'MS_Description', '目标值', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'TargetValue';
EXEC sp_addextendedproperty 'MS_Description', '实际值', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'ActualValue';
EXEC sp_addextendedproperty 'MS_Description', '差异 (实际值 - 目标值)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'Variance';
EXEC sp_addextendedproperty 'MS_Description', '测量日期/周期结束日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'MeasurementDate';
EXEC sp_addextendedproperty 'MS_Description', '计算周期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'CalculationPeriod';
EXEC sp_addextendedproperty 'MS_Description', '度量单位', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'UnitOfMeasure';
EXEC sp_addextendedproperty 'MS_Description', '趋势', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'Trend';
EXEC sp_addextendedproperty 'MS_Description', '数据来源描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'DataSource';
EXEC sp_addextendedproperty 'MS_Description', '负责人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'ResponsiblePersonId';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'Remarks';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseKPIs', 'COLUMN', 'UpdateBy';
-- 提醒: KPIType 枚举需要添加到 WMS_EnumDictionary_Data.sql

-- 仓库多语言信息表 (基于 WarehouseLocalization.cs)
-- 此表用于WMS_Warehouses表中的名称、描述等字段的多语言支持。
CREATE TABLE WMS_WarehouseLocalizations (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID
    WarehouseId UNIQUEIDENTIFIER NOT NULL, -- 关联的仓库ID
    Culture NVARCHAR(10) NOT NULL, -- 语言文化代码 (例如: zh-CN, en-US)
    WarehouseName NVARCHAR(255), -- 仓库在该语言下的名称
    Description NVARCHAR(MAX), -- 仓库在该语言下的描述
    StreetAddress NVARCHAR(255), -- 街道地址 (本地化)
    Notes NVARCHAR(MAX), -- 其他备注 (本地化)
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID
    UpdateTime DATETIME2, -- 更新时间
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID
    FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) ON DELETE CASCADE,
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    UNIQUE (WarehouseId, Culture) -- 同一仓库的同一语言只能有一条本地化记录
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库多语言信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '关联的仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '语言文化代码 (例如: zh-CN, en-US)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'Culture';
EXEC sp_addextendedproperty 'MS_Description', '仓库在该语言下的名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'WarehouseName';
EXEC sp_addextendedproperty 'MS_Description', '仓库在该语言下的描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'Description';
EXEC sp_addextendedproperty 'MS_Description', '街道地址 (本地化)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'StreetAddress';
EXEC sp_addextendedproperty 'MS_Description', '其他备注 (本地化)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'Notes';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseLocalizations', 'COLUMN', 'UpdateBy';
-- 仓库规则表 (基于 WarehouseRule.cs)
CREATE TABLE WMS_WarehouseRules (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    WarehouseId NVARCHAR(50) NOT NULL, -- 仓库ID (注意: 模型中是string，这里假设关联 WMS_Warehouses.ID (UNIQUEIDENTIFIER), 请确认或修改为 NVARCHAR(50))
    RuleCode NVARCHAR(50) NOT NULL, -- 规则代码
    RuleName NVARCHAR(100) NOT NULL, -- 规则名称
    Type INT NOT NULL, -- 规则类型 (对应 bnred.Model.WMS.Enums.RuleType 枚举, 请在 WMS_EnumDictionary 中定义)
    Priority INT NOT NULL, -- 优先级
    IsActive BIT NOT NULL, -- 是否激活
    RuleContent NVARCHAR(1000) NOT NULL, -- 规则内容 (具体定义规则逻辑的字符串，例如JSON或特定DSL)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    -- FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) -- 如果WarehouseId是外键, 取消注释并确认类型
    UNIQUE (WarehouseId, RuleCode) -- 同一仓库下规则代码唯一
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库规则配置表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '规则代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'RuleCode';
EXEC sp_addextendedproperty 'MS_Description', '规则名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'RuleName';
EXEC sp_addextendedproperty 'MS_Description', '规则类型 (枚举: RuleType)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'Type';
EXEC sp_addextendedproperty 'MS_Description', '优先级', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'Priority';
EXEC sp_addextendedproperty 'MS_Description', '是否激活', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'IsActive';
EXEC sp_addextendedproperty 'MS_Description', '规则内容', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'RuleContent';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseRules', 'COLUMN', 'UpdateBy';
-- 提醒: RuleType 枚举 (来自 bnred.Model.WMS.Enums) 需要添加到 WMS_EnumDictionary_Data.sql


-- 仓库安全信息表 (基于 WarehouseSecurity.cs)
CREATE TABLE WMS_WarehouseSecurities (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    WarehouseId NVARCHAR(50) NOT NULL, -- 仓库ID (外键关联 WMS_Warehouses.ID, 请确认类型是否一致)
    HasFireSystem BIT NOT NULL, -- 是否有消防系统
    HasSecuritySystem BIT NOT NULL, -- 是否有安防系统
    HasMonitoringSystem BIT NOT NULL, -- 是否有监控系统
    HasEmergencyPower BIT NOT NULL, -- 是否有应急电源
    LastFireDrillDate DATETIME2, -- 上次消防演练日期
    NextFireDrillDate DATETIME2, -- 下次消防演练日期
    EmergencyProcedure NVARCHAR(1000), -- 应急预案
    EmergencyContact NVARCHAR(500), -- 应急联系方式
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    -- FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) -- 如果WarehouseId是外键, 取消注释并确认类型
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库安全相关信息表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '是否有消防系统', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'HasFireSystem';
EXEC sp_addextendedproperty 'MS_Description', '是否有安防系统', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'HasSecuritySystem';
EXEC sp_addextendedproperty 'MS_Description', '是否有监控系统', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'HasMonitoringSystem';
EXEC sp_addextendedproperty 'MS_Description', '是否有应急电源', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'HasEmergencyPower';
EXEC sp_addextendedproperty 'MS_Description', '上次消防演练日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'LastFireDrillDate';
EXEC sp_addextendedproperty 'MS_Description', '下次消防演练日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'NextFireDrillDate';
EXEC sp_addextendedproperty 'MS_Description', '应急预案', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'EmergencyProcedure';
EXEC sp_addextendedproperty 'MS_Description', '应急联系方式', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'EmergencyContact';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseSecurities', 'COLUMN', 'UpdateBy';


-- 仓库统计数据表 (基于 WarehouseStatistics.cs)
CREATE TABLE WMS_WarehouseStatistics (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    WarehouseId NVARCHAR(50) NOT NULL, -- 仓库ID (外键关联 WMS_Warehouses.ID, 请确认类型是否一致)
    StatisticsDate DATETIME2 NOT NULL, -- 统计日期
    TotalInventoryQuantity DECIMAL(18,4) NOT NULL, -- 总库存数量
    TotalInventoryValue DECIMAL(18,4) NOT NULL, -- 总库存价值
    InboundQuantity DECIMAL(18,4) NOT NULL, -- 入库数量 (当天的或特定周期的)
    OutboundQuantity DECIMAL(18,4) NOT NULL, -- 出库数量 (当天的或特定周期的)
    TotalLocations INT NOT NULL, -- 总库位数
    UsedLocations INT NOT NULL, -- 已用库位数
    EmptyLocations INT NOT NULL, -- 空库位数
    SpaceUtilization DECIMAL(18,2) NOT NULL, -- 空间利用率 (%)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    -- FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) -- 如果WarehouseId是外键, 取消注释并确认类型
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库运营统计数据表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '统计日期', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'StatisticsDate';
EXEC sp_addextendedproperty 'MS_Description', '总库存数量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'TotalInventoryQuantity';
EXEC sp_addextendedproperty 'MS_Description', '总库存价值', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'TotalInventoryValue';
EXEC sp_addextendedproperty 'MS_Description', '入库数量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'InboundQuantity';
EXEC sp_addextendedproperty 'MS_Description', '出库数量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'OutboundQuantity';
EXEC sp_addextendedproperty 'MS_Description', '总库位数', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'TotalLocations';
EXEC sp_addextendedproperty 'MS_Description', '已用库位数', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'UsedLocations';
EXEC sp_addextendedproperty 'MS_Description', '空库位数', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'EmptyLocations';
EXEC sp_addextendedproperty 'MS_Description', '空间利用率 (%)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'SpaceUtilization';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStatistics', 'COLUMN', 'UpdateBy';


-- 仓库策略表 (基于 WarehouseStrategy.cs)
CREATE TABLE WMS_WarehouseStrategies (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    WarehouseId NVARCHAR(50) NOT NULL, -- 仓库ID (外键关联 WMS_Warehouses.ID, 请确认类型是否一致)
    StrategyCode NVARCHAR(50) NOT NULL, -- 策略代码
    StrategyName NVARCHAR(100) NOT NULL, -- 策略名称
    Type INT NOT NULL, -- 策略类型 (对应 bnred.Model.WMS.Enums.StrategyType 枚举, 请在 WMS_EnumDictionary 中定义)
    IsDefault BIT NOT NULL, -- 是否为默认策略
    IsActive BIT NOT NULL, -- 是否激活
    StrategyContent NVARCHAR(1000) NOT NULL, -- 策略内容 (例如JSON或特定DSL描述策略逻辑)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    -- FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID) -- 如果WarehouseId是外键, 取消注释并确认类型
    UNIQUE (WarehouseId, StrategyCode) -- 同一仓库下策略代码唯一
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库作业策略配置表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '策略代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'StrategyCode';
EXEC sp_addextendedproperty 'MS_Description', '策略名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'StrategyName';
EXEC sp_addextendedproperty 'MS_Description', '策略类型 (枚举: StrategyType)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'Type';
EXEC sp_addextendedproperty 'MS_Description', '是否为默认策略', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'IsDefault';
EXEC sp_addextendedproperty 'MS_Description', '是否激活', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'IsActive';
EXEC sp_addextendedproperty 'MS_Description', '策略内容', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'StrategyContent';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategies', 'COLUMN', 'UpdateBy';
-- 提醒: StrategyType 枚举 (来自 bnred.Model.WMS.Enums) 需要添加到 WMS_EnumDictionary_Data.sql


-- 仓库策略条件表 (基于 WarehouseStrategyCondition.cs)
CREATE TABLE WMS_WarehouseStrategyConditions (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    StrategyId NVARCHAR(50) NOT NULL, -- 策略ID (外键关联 WMS_WarehouseStrategies.ID)
    ConditionCode NVARCHAR(50) NOT NULL, -- 条件代码
    ConditionName NVARCHAR(100) NOT NULL, -- 条件名称
    FieldName NVARCHAR(50) NOT NULL, -- 条件应用的字段名 (例如物料属性、库位属性等)
    Operator NVARCHAR(50) NOT NULL, -- 操作符 (例如 =, >, <, LIKE, IN)
    FieldValue NVARCHAR(500) NOT NULL, -- 字段值 (与操作符配合使用)
    Priority INT NOT NULL, -- 条件优先级 (当多个条件满足时，按优先级处理)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    FOREIGN KEY (StrategyId) REFERENCES WMS_WarehouseStrategies(ID) ON DELETE CASCADE
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库策略的条件规则表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '策略ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'StrategyId';
EXEC sp_addextendedproperty 'MS_Description', '条件代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'ConditionCode';
EXEC sp_addextendedproperty 'MS_Description', '条件名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'ConditionName';
EXEC sp_addextendedproperty 'MS_Description', '条件应用的字段名', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'FieldName';
EXEC sp_addextendedproperty 'MS_Description', '操作符', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'Operator';
EXEC sp_addextendedproperty 'MS_Description', '字段值', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'FieldValue';
EXEC sp_addextendedproperty 'MS_Description', '条件优先级', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'Priority';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyConditions', 'COLUMN', 'UpdateBy';


-- 仓库策略参数表 (基于 WarehouseStrategyParameter.cs)
CREATE TABLE WMS_WarehouseStrategyParameters (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    StrategyId NVARCHAR(50) NOT NULL, -- 策略ID (外键关联 WMS_WarehouseStrategies.ID)
    ParameterCode NVARCHAR(50) NOT NULL, -- 参数代码
    ParameterName NVARCHAR(100) NOT NULL, -- 参数名称
    ParameterValue NVARCHAR(50) NOT NULL, -- 参数值
    IsRequired BIT NOT NULL, -- 是否必需
    SortOrder INT NOT NULL, -- 排序号 (参数应用的顺序或显示顺序)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID), -- 假设的租户表
    FOREIGN KEY (StrategyId) REFERENCES WMS_WarehouseStrategies(ID) ON DELETE CASCADE,
    UNIQUE (StrategyId, ParameterCode) -- 同一策略下参数代码唯一
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库策略的参数配置表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters';
-- 列注释
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '租户ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'TenantId';
EXEC sp_addextendedproperty 'MS_Description', '策略ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'StrategyId';
EXEC sp_addextendedproperty 'MS_Description', '参数代码', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'ParameterCode';
EXEC sp_addextendedproperty 'MS_Description', '参数名称', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'ParameterName';
EXEC sp_addextendedproperty 'MS_Description', '参数值', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'ParameterValue';
EXEC sp_addextendedproperty 'MS_Description', '是否必需', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'IsRequired';
EXEC sp_addextendedproperty 'MS_Description', '排序号', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'SortOrder';
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'Remark';
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'CreateTime';
EXEC sp_addextendedproperty 'MS_Description', '创建人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'CreateBy';
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'UpdateTime';
EXEC sp_addextendedproperty 'MS_Description', '更新人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseStrategyParameters', 'COLUMN', 'UpdateBy';


-- 仓库任务表 (基于 WarehouseTask.cs)
CREATE TABLE WMS_WarehouseTasks (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    TaskNo NVARCHAR(50) NOT NULL UNIQUE, -- 任务编号，唯一
    TaskType INT NOT NULL, -- 任务类型 (对应 bnred.Model.WMS.Enums.TaskType 枚举)
    TaskStatus INT NOT NULL, -- 任务状态 (对应 bnred.Model.WMS.Enums.TaskStatus 枚举)
    WarehouseId NVARCHAR(50) NOT NULL, -- 仓库ID (外键关联 WMS_Warehouses.ID, 请确认类型是否一致)
    RelatedOrderNo NVARCHAR(50), -- 关联业务单号 (例如入库单号、出库单号)
    RelatedOrderId NVARCHAR(50), -- 关联业务单据ID (具体单据的主键)
    PlannedStartTime DATETIME2, -- 计划开始时间
    PlannedEndTime DATETIME2, -- 计划完成时间
    ActualStartTime DATETIME2, -- 实际开始时间
    ActualEndTime DATETIME2, -- 实际完成时间
    AssignedById UNIQUEIDENTIFIER, -- 分配人ID (关联 WMS_FrameworkUsers.ID)
    ExecutorId UNIQUEIDENTIFIER, -- 执行人ID (关联 WMS_FrameworkUsers.ID)
    Priority INT NOT NULL DEFAULT 1, -- 优先级 (对应 bnred.Model.WMS.Enums.TaskPriority 枚举, Normal=1)
    HasException BIT NOT NULL DEFAULT 0, -- 是否有异常标记
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    -- FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID), -- 确认类型后启用
    FOREIGN KEY (AssignedById) REFERENCES WMS_FrameworkUsers(ID),
    FOREIGN KEY (ExecutorId) REFERENCES WMS_FrameworkUsers(ID)
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库作业任务表', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks';
-- 列注释 (部分关键字段)
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '任务编号，唯一', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'TaskNo';
EXEC sp_addextendedproperty 'MS_Description', '任务类型 (枚举: TaskType)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'TaskType';
EXEC sp_addextendedproperty 'MS_Description', '任务状态 (枚举: TaskStatus)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'TaskStatus';
EXEC sp_addextendedproperty 'MS_Description', '仓库ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'WarehouseId';
EXEC sp_addextendedproperty 'MS_Description', '关联业务单号', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'RelatedOrderNo';
EXEC sp_addextendedproperty 'MS_Description', '分配人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'AssignedById';
EXEC sp_addextendedproperty 'MS_Description', '执行人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'ExecutorId';
EXEC sp_addextendedproperty 'MS_Description', '优先级 (枚举: TaskPriority)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'Priority';
EXEC sp_addextendedproperty 'MS_Description', '是否有异常标记', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTasks', 'COLUMN', 'HasException';
-- 提醒: TaskType, TaskStatus, TaskPriority 枚举 (来自 bnred.Model.WMS.Enums) 需要添加到 WMS_EnumDictionary_Data.sql


-- 仓库任务明细表 (基于 WarehouseTaskDetail.cs)
CREATE TABLE WMS_WarehouseTaskDetails (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    TaskId NVARCHAR(50) NOT NULL, -- 任务ID (外键关联 WMS_WarehouseTasks.ID)
    MaterialId NVARCHAR(50), -- 物料ID (外键关联 WMS_Materials.ID, 请确认类型)
    BatchId NVARCHAR(50), -- 批次ID (外键关联 WMS_Batches.ID, 请确认类型)
    FromLocationId NVARCHAR(50) NOT NULL, -- 源库位ID (外键关联 WMS_Locations.ID, 请确认类型)
    ToLocationId NVARCHAR(50) NOT NULL, -- 目标库位ID (外键关联 WMS_Locations.ID, 请确认类型)
    PlanQuantity DECIMAL(18,4) NOT NULL, -- 计划数量
    ActualQuantity DECIMAL(18,4), -- 实际数量
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    FOREIGN KEY (TaskId) REFERENCES WMS_WarehouseTasks(ID) ON DELETE CASCADE
    -- FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID), -- 确认类型后启用
    -- FOREIGN KEY (BatchId) REFERENCES WMS_Batches(ID), -- 确认类型后启用
    -- FOREIGN KEY (FromLocationId) REFERENCES WMS_Locations(ID), -- 确认类型后启用
    -- FOREIGN KEY (ToLocationId) REFERENCES WMS_Locations(ID) -- 确认类型后启用
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '仓库任务的详细作业条目', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails';
-- 列注释 (部分关键字段)
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '任务ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'TaskId';
EXEC sp_addextendedproperty 'MS_Description', '物料ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'MaterialId';
EXEC sp_addextendedproperty 'MS_Description', '批次ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'BatchId';
EXEC sp_addextendedproperty 'MS_Description', '源库位ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'FromLocationId';
EXEC sp_addextendedproperty 'MS_Description', '目标库位ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'ToLocationId';
EXEC sp_addextendedproperty 'MS_Description', '计划数量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'PlanQuantity';
EXEC sp_addextendedproperty 'MS_Description', '实际数量', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskDetails', 'COLUMN', 'ActualQuantity';


-- 仓库任务异常记录表 (基于 WarehouseTaskException.cs)
CREATE TABLE WMS_WarehouseTaskExceptions (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    TaskId NVARCHAR(50) NOT NULL, -- 任务ID (外键关联 WMS_WarehouseTasks.ID)
    ExceptionType NVARCHAR(50) NOT NULL, -- 异常类型 (建议使用枚举或标准化文本)
    ExceptionDescription NVARCHAR(500) NOT NULL, -- 异常详细描述
    OccurrenceTime DATETIME2 NOT NULL, -- 异常发生时间
    ReporterId UNIQUEIDENTIFIER, -- 报告人ID (关联 WMS_FrameworkUsers.ID)
    Status INT NOT NULL, -- 处理状态 (建议使用枚举, 例如: Pending, InProgress, Resolved, Cancelled)
    Resolution NVARCHAR(500), -- 解决方案描述
    ResolutionTime DATETIME2, -- 解决时间
    ResolverId UNIQUEIDENTIFIER, -- 解决人ID (关联 WMS_FrameworkUsers.ID)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    FOREIGN KEY (TaskId) REFERENCES WMS_WarehouseTasks(ID) ON DELETE CASCADE,
    FOREIGN KEY (ReporterId) REFERENCES WMS_FrameworkUsers(ID),
    FOREIGN KEY (ResolverId) REFERENCES WMS_FrameworkUsers(ID)
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '记录仓库任务执行过程中的异常情况', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions';
-- 列注释 (部分关键字段)
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '任务ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'TaskId';
EXEC sp_addextendedproperty 'MS_Description', '异常类型', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'ExceptionType';
EXEC sp_addextendedproperty 'MS_Description', '异常详细描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'ExceptionDescription';
EXEC sp_addextendedproperty 'MS_Description', '发生时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'OccurrenceTime';
EXEC sp_addextendedproperty 'MS_Description', '报告人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'ReporterId';
EXEC sp_addextendedproperty 'MS_Description', '处理状态 (枚举建议)', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'Status';
EXEC sp_addextendedproperty 'MS_Description', '解决人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskExceptions', 'COLUMN', 'ResolverId';
-- 提醒: ExceptionType 和 Status 字段如果使用了枚举，请在 WMS_EnumDictionary_Data.sql 中定义。


-- 仓库任务日志表 (基于 WarehouseTaskLog.cs)
CREATE TABLE WMS_WarehouseTaskLogs (
    ID NVARCHAR(50) PRIMARY KEY, -- 主键ID
    TenantId UNIQUEIDENTIFIER, -- 租户ID (来自 BasePoco)
    TaskId NVARCHAR(50) NOT NULL, -- 任务ID (外键关联 WMS_WarehouseTasks.ID)
    OperatorId UNIQUEIDENTIFIER, -- 操作人ID (关联 WMS_FrameworkUsers.ID)
    OperationTime DATETIME2 NOT NULL, -- 操作时间
    OperationType NVARCHAR(50) NOT NULL, -- 操作类型 (例如: Create, Start, Pause, Complete, UpdateStatus, AddItem)
    OperationContent NVARCHAR(500) NOT NULL, -- 操作内容描述
    OperationResult NVARCHAR(50) NOT NULL, -- 操作结果 (例如: Success, Failed, PartialSuccess)
    Remark NVARCHAR(500), -- 备注
    CreateTime DATETIME2 DEFAULT GETDATE(), -- 创建时间 (来自 BasePoco)
    CreateBy UNIQUEIDENTIFIER, -- 创建人ID (来自 BasePoco)
    UpdateTime DATETIME2, -- 更新时间 (来自 BasePoco)
    UpdateBy UNIQUEIDENTIFIER, -- 更新人ID (来自 BasePoco)
    FOREIGN KEY (TenantId) REFERENCES WMS_FrameworkTenants(ID),
    FOREIGN KEY (TaskId) REFERENCES WMS_WarehouseTasks(ID) ON DELETE CASCADE,
    FOREIGN KEY (OperatorId) REFERENCES WMS_FrameworkUsers(ID)
);
-- 表注释
EXEC sp_addextendedproperty 'MS_Description', '记录仓库任务执行的详细操作日志', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs';
-- 列注释 (部分关键字段)
EXEC sp_addextendedproperty 'MS_Description', '主键ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'ID';
EXEC sp_addextendedproperty 'MS_Description', '任务ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'TaskId';
EXEC sp_addextendedproperty 'MS_Description', '操作人ID', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'OperatorId';
EXEC sp_addextendedproperty 'MS_Description', '操作时间', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'OperationTime';
EXEC sp_addextendedproperty 'MS_Description', '操作类型', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'OperationType';
EXEC sp_addextendedproperty 'MS_Description', '操作内容描述', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'OperationContent';
EXEC sp_addextendedproperty 'MS_Description', '操作结果', 'SCHEMA', 'dbo', 'TABLE', 'WMS_WarehouseTaskLogs', 'COLUMN', 'OperationResult';

-- 注意事项:
-- 1. 所有 NVARCHAR(50) 类型的主键 (ID) 是基于C#模型中 string ID = Guid.CreateVersion7().ToString() 的设定。
-- 2. 对于模型中的外键属性，如 WarehouseId, MaterialId, BatchId, FromLocationId, ToLocationId 等，其数据类型 (NVARCHAR(50) 或 UNIQUEIDENTIFIER) 需要与被引用的主表主键类型保持一致。SQL DDL中已用注释标出需要确认的地方。请务必检查并修改以确保外键约束的正确性。
-- 3. 涉及到枚举的字段 (例如 RuleType, StrategyType, TaskType, TaskStatus, TaskPriority) 已在注释中提醒，需要将这些枚举的详细信息添加到 WMS_EnumDictionary 和 WMS_EnumDictionary_Data.sql 文件中。
-- 4. CreateBy, UpdateBy, AssignedById, ExecutorId, ReporterId, ResolverId, OperatorId 等用户ID字段，均假设关联到 WMS_FrameworkUsers(ID) (UNIQUEIDENTIFIER 类型)。如果实际用户表主键类型不同，请调整。
-- 5. TenantId 字段假设关联到 WMS_FrameworkTenants(ID) (UNIQUEIDENTIFIER 类型)。
