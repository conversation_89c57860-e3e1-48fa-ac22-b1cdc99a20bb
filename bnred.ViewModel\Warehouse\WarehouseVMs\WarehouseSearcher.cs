﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;


namespace bnred.ViewModel.Warehouse.WarehouseVMs
{
    public partial class WarehouseSearcher : BaseSearcher
    {
        public String ID { get; set; }
        [Display(Name = "仓库编码")]
        public String Code { get; set; }
        [Display(Name = "仓库名称")]
        public String Name { get; set; }
        [Display(Name = "仓库类型")]
        public WarehouseType? Type { get; set; }
        [Display(Name = "仓库状态")]
        public WarehouseStatus? Status { get; set; }
        [Display(Name = "仓库地址")]
        public String Address { get; set; }
        [Display(Name = "联系人")]
        public String ContactPerson { get; set; }
        [Display(Name = "联系电话")]
        public String ContactPhone { get; set; }
        [Display(Name = "面积(平方米)")]
        public Decimal? Area { get; set; }
        [Display(Name = "容量(立方米)")]
        public Decimal? Volume { get; set; }
        public Guid? ManagerID { get; set; }
        [Display(Name = "描述")]
        public String Description { get; set; }
        [Display(Name = "备注")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }
}
