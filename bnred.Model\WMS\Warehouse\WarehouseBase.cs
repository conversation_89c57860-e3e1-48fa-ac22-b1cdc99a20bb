using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;
namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库基类
    /// 包含仓库的基本信息
    /// </summary>
    [Table("WMS_Warehouses")]
    public class WarehouseBase : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 仓库编码
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "仓库编码")]
        public string Code { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Required]
        [StringLength(100)]
        [Display(Name = "仓库名称")]
        public string Name { get; set; }

        /// <summary>
        /// 仓库类型
        /// </summary>
        [Display(Name = "仓库类型")]
        public WarehouseType Type { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        [StringLength(200)]
        [Display(Name = "仓库地址")]
        public string Address { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        [Display(Name = "联系人")]
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        [Display(Name = "联系电话")]
        public string ContactPhone { get; set; }

        /// <summary>
        /// 仓库面积(平方米)
        /// </summary>
        [Display(Name = "仓库面积")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Area { get; set; }

        /// <summary>
        /// 仓库容量(立方米)
        /// </summary>
        [Display(Name = "仓库容量")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Capacity { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Display(Name = "是否启用")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
} 