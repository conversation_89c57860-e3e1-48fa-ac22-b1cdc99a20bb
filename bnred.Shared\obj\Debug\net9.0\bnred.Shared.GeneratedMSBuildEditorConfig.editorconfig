is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = bnred.Shared
build_property.RootNamespace = bnred.Shared
build_property.ProjectDir = D:\work\wms2025\bnred.Shared\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\work\wms2025\bnred.Shared
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/work/wms2025/bnred.Shared/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Index_en.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXhfZW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Index_zh.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXhfemgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Material/Material/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTWF0ZXJpYWxcTWF0ZXJpYWxcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Material/Material/MaterialForm.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTWF0ZXJpYWxcTWF0ZXJpYWxcTWF0ZXJpYWxGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/WarehouseArea/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZUFyZWFcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/WarehouseArea/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZUFyZWFcRGV0YWlscy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/WarehouseArea/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZUFyZWFcRWRpdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/WarehouseArea/Import.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZUFyZWFcSW1wb3J0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/WarehouseArea/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZUFyZWFcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/Warehouse/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZVxDcmVhdGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/Warehouse/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZVxEZXRhaWxzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/Warehouse/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZVxFZGl0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/Warehouse/Import.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZVxJbXBvcnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/Warehouse/Warehouse/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2FyZWhvdXNlXFdhcmVob3VzZVxJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/ActionLog/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEFjdGlvbkxvZ1xEZXRhaWxzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/ActionLog/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEFjdGlvbkxvZ1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/DataPrivilege/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXERhdGFQcml2aWxlZ2VcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/DataPrivilege/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXERhdGFQcml2aWxlZ2VcRGV0YWlscy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/DataPrivilege/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXERhdGFQcml2aWxlZ2VcRWRpdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/DataPrivilege/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXERhdGFQcml2aWxlZ2VcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkGroup/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya0dyb3VwXENyZWF0ZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkGroup/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya0dyb3VwXERldGFpbHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkGroup/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya0dyb3VwXEVkaXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkGroup/Import.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya0dyb3VwXEltcG9ydC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkGroup/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya0dyb3VwXEluZGV4LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkMenu/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya01lbnVcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkMenu/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya01lbnVcRGV0YWlscy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkMenu/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya01lbnVcRWRpdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkMenu/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya01lbnVcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkRole/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1JvbGVcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkRole/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1JvbGVcRGV0YWlscy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkRole/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1JvbGVcRWRpdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkRole/Import.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1JvbGVcSW1wb3J0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkRole/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1JvbGVcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkRole/PageFunction.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1JvbGVcUGFnZUZ1bmN0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkTenant/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1RlbmFudFxDcmVhdGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkTenant/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1RlbmFudFxEZXRhaWxzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkTenant/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1RlbmFudFxFZGl0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkTenant/Import.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1RlbmFudFxJbXBvcnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkTenant/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1RlbmFudFxJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkUser/Create.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1VzZXJcQ3JlYXRlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkUser/Details.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1VzZXJcRGV0YWlscy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkUser/Edit.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1VzZXJcRWRpdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkUser/Import.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1VzZXJcSW1wb3J0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkUser/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1VzZXJcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/FrameworkUser/Password.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXEZyYW1ld29ya1VzZXJcUGFzc3dvcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Pages/_Admin/WorkFlow/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0FkbWluXFdvcmtGbG93XEluZGV4LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Shared/EmptyLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXEVtcHR5TGF5b3V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Shared/Outside.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE91dHNpZGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Shared/Password.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXFBhc3N3b3JkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Shared/RemoteEntry.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXFJlbW90ZUVudHJ5LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/WtmBlazorUtils/WTDateRange.razor]
build_metadata.AdditionalFiles.TargetPath = V3RtQmxhem9yVXRpbHNcV1REYXRlUmFuZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/WtmBlazorUtils/WTSearchPanel.razor]
build_metadata.AdditionalFiles.TargetPath = V3RtQmxhem9yVXRpbHNcV1RTZWFyY2hQYW5lbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/WtmBlazorUtils/WTSimpleUpload.razor]
build_metadata.AdditionalFiles.TargetPath = V3RtQmxhem9yVXRpbHNcV1RTaW1wbGVVcGxvYWQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/WtmBlazorUtils/WTUploadFile.razor]
build_metadata.AdditionalFiles.TargetPath = V3RtQmxhem9yVXRpbHNcV1RVcGxvYWRGaWxlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/WtmBlazorUtils/WTUploadImage.razor]
build_metadata.AdditionalFiles.TargetPath = V3RtQmxhem9yVXRpbHNcV1RVcGxvYWRJbWFnZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/work/wms2025/bnred.Shared/Shared/Login.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXExvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = b-ex9qjyd1md

[D:/work/wms2025/bnred.Shared/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-czfl8wvr2h
