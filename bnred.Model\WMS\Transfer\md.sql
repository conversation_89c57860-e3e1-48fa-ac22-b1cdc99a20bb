-- ----------------------------
-- Table structure for WMS_TransferOrder
-- ----------------------------
DROP TABLE IF EXISTS [WMS_TransferOrder];
CREATE TABLE [WMS_TransferOrder] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [TransferNo] nvarchar(50) NOT NULL, -- 移库单号
  [Type] int NOT NULL, -- 移库类型 (需要映射到 WMS_EnumDictionary, C#类型: TransferType)
  [Status] int NOT NULL, -- 移库状态 (需要映射到 WMS_EnumDictionary, C#类型: TransferStatus)
  [SourceWarehouseId] nvarchar(50) NOT NULL, -- 源仓库ID
  [TargetWarehouseId] nvarchar(50) NOT NULL, -- 目标仓库ID
  [PlanDate] datetime2(7) NOT NULL, -- 计划日期
  [ActualDate] datetime2(7) NULL, -- 实际完成日期
  [CreatorId] uniqueidentifier NULL, -- 创建人ID (外键关联 FrameworkUser)
  [ExecutorId] uniqueidentifier NULL, -- 执行人ID (外键关联 FrameworkUser)
  [Priority] int NOT NULL, -- 优先级 (需要映射到 WMS_EnumDictionary, C#类型: TaskPriority)
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_TransferOrder
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_TransferOrderDetail
-- ----------------------------
DROP TABLE IF EXISTS [WMS_TransferOrderDetail];
CREATE TABLE [WMS_TransferOrderDetail] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [TransferOrderId] nvarchar(50) NOT NULL, -- 移库单ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [SourceLocationId] nvarchar(50) NOT NULL, -- 源库位ID
  [TargetLocationId] nvarchar(50) NOT NULL, -- 目标库位ID
  [PlanQuantity] decimal(18,4) NOT NULL, -- 计划数量
  [ActualQuantity] decimal(18,4) NULL, -- 实际数量
  [Unit] nvarchar(20) NOT NULL, -- 单位
  [Status] int NOT NULL, -- 状态 (需要映射到 WMS_EnumDictionary, C#类型: TransferDetailStatus)
  [CompletionTime] datetime2(7) NULL, -- 完成时间
  [OperatorId] uniqueidentifier NULL, -- 操作人员ID (外键关联 FrameworkUser)
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_TransferOrderDetail
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_TransferOrder
-- ----------------------------
ALTER TABLE [WMS_TransferOrder] ADD CONSTRAINT [FK_WMS_TransferOrder_SourceWarehouse] FOREIGN KEY ([SourceWarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_TransferOrder] ADD CONSTRAINT [FK_WMS_TransferOrder_TargetWarehouse] FOREIGN KEY ([TargetWarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
-- 注意: CreatorId 和 ExecutorId 外键关联 WMS_FrameworkUser (或其他用户表), 请根据实际用户表名修改。
-- ALTER TABLE [WMS_TransferOrder] ADD CONSTRAINT [FK_WMS_TransferOrder_Creator] FOREIGN KEY ([CreatorId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO
-- ALTER TABLE [WMS_TransferOrder] ADD CONSTRAINT [FK_WMS_TransferOrder_Executor] FOREIGN KEY ([ExecutorId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO

-- ----------------------------
-- Foreign Keys structure for table WMS_TransferOrderDetail
-- ----------------------------
ALTER TABLE [WMS_TransferOrderDetail] ADD CONSTRAINT [FK_WMS_TransferOrderDetail_TransferOrder] FOREIGN KEY ([TransferOrderId]) REFERENCES [WMS_TransferOrder] ([ID]) ON DELETE CASCADE ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_TransferOrderDetail] ADD CONSTRAINT [FK_WMS_TransferOrderDetail_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_TransferOrderDetail] ADD CONSTRAINT [FK_WMS_TransferOrderDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_TransferOrderDetail] ADD CONSTRAINT [FK_WMS_TransferOrderDetail_SourceLocation] FOREIGN KEY ([SourceLocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_TransferOrderDetail] ADD CONSTRAINT [FK_WMS_TransferOrderDetail_TargetLocation] FOREIGN KEY ([TargetLocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
-- 注意: OperatorId 外键关联 WMS_FrameworkUser (或其他用户表), 请根据实际用户表名修改。
-- ALTER TABLE [WMS_TransferOrderDetail] ADD CONSTRAINT [FK_WMS_TransferOrderDetail_Operator] FOREIGN KEY ([OperatorId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO

-- ----------------------------
-- MS_Description for table WMS_TransferOrder
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移库单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移库单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'TransferNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移库类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'Type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移库状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'源仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'SourceWarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'目标仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'TargetWarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'PlanDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际完成日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'ActualDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'CreatorId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'执行人ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'ExecutorId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'优先级' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'Priority'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrder', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_TransferOrderDetail
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移库单明细' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移库单ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'TransferOrderId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'源库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'SourceLocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'目标库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'TargetLocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'PlanQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'ActualQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单位' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'Unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'完成时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'CompletionTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作人员ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'OperatorId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_TransferOrderDetail', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO 