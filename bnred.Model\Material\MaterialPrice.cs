using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Material
{
    [Table("WMS_MaterialPrices")]
    public class MaterialPrice : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }
        public virtual bnred.Model.Material.Material Material { get; set; }

        [StringLength(50)]
        public string SupplierId { get; set; }
        public virtual bnred.Model.Supplier.Supplier Supplier { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Price { get; set; }

        [Required]
        public DateTime EffectiveDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [Required]
        public bool IsActive { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 