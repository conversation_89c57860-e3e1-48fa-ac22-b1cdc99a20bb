using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 仓库文档类型枚举
    /// 注意：此枚举与DocumentType区别是它专门用于文档管理，而DocumentType用于业务操作
    /// </summary>
    public enum DocumentTypeEnum
    {
        /// <summary>
        /// 入库单
        /// </summary>
        [Display(Name = "入库单")]
        InboundOrder = 0,

        /// <summary>
        /// 出库单
        /// </summary>
        [Display(Name = "出库单")]
        OutboundOrder = 1,

        /// <summary>
        /// 移库单
        /// </summary>
        [Display(Name = "移库单")]
        TransferOrder = 2,

        /// <summary>
        /// 调拨单
        /// </summary>
        [Display(Name = "调拨单")]
        AllocationOrder = 3,

        /// <summary>
        /// 盘点单
        /// </summary>
        [Display(Name = "盘点单")]
        StockTakingOrder = 4,

        /// <summary>
        /// 报损单
        /// </summary>
        [Display(Name = "报损单")]
        DamageOrder = 5,

        /// <summary>
        /// 报溢单
        /// </summary>
        [Display(Name = "报溢单")]
        OverflowOrder = 6,

        /// <summary>
        /// 其他文档
        /// </summary>
        [Display(Name = "其他文档")]
        Other = 99
    }
} 