/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function(t,i){function e(t){return"number"==typeof t}function n(t){return t!==a&&null!==t}var a,s,r,o=t.Chart,h=t.extend,l=t.each;r=["path","rect","circle"],s={top:0,left:0,center:.5,middle:.5,bottom:1,right:1};var p=i.inArray,c=t.merge,d=function(){this.init.apply(this,arguments)};d.prototype={init:function(t,i){var e=i.shape&&i.shape.type;this.chart=t;var n,a;a={xAxis:0,yAxis:0,title:{style:{},text:"",x:0,y:0},shape:{params:{stroke:"#000000",fill:"transparent",strokeWidth:2}}},n={circle:{params:{x:0,y:0}}},n[e]&&(a.shape=c(a.shape,n[e])),this.options=c({},a,i)},render:function(t){var i=this.chart,e=this.chart.renderer,n=this.group,a=this.title,s=this.shape,o=this.options,h=o.title,l=o.shape;n||(n=this.group=e.g()),!s&&l&&p(l.type,r)!==-1&&(s=this.shape=e[o.shape.type](l.params),s.add(n)),!a&&h&&(a=this.title=e.label(h),a.add(n)),n.add(i.annotations.group),this.linkObjects(),t!==!1&&this.redraw()},redraw:function(){var i,a,r,o,l=this.options,c=this.chart,d=this.group,u=this.title,y=this.shape,f=this.linkedObject,x=c.xAxis[l.xAxis],g=c.yAxis[l.yAxis],b=l.width,m=l.height,v=s[l.anchorY],w=s[l.anchorX];if(f&&(i=f instanceof t.Point?"point":f instanceof t.Series?"series":null,"point"===i?(l.xValue=f.x,l.yValue=f.y,a=f.series):"series"===i&&(a=f),d.visibility!==a.group.visibility&&d.attr({visibility:a.group.visibility})),f=n(l.xValue)?x.toPixels(l.xValue+x.minPointOffset)-x.minPixelPadding:l.x,i=n(l.yValue)?g.toPixels(l.yValue):l.y,!isNaN(f)&&!isNaN(i)&&e(f)&&e(i)){if(u&&(u.attr(l.title),u.css(l.title.style)),y){if(u=h({},l.shape.params),"values"===l.units){for(r in u)p(r,["width","x"])>-1?u[r]=x.translate(u[r]):p(r,["height","y"])>-1&&(u[r]=g.translate(u[r]));if(u.width&&(u.width-=x.toPixels(0)-x.left),u.x&&(u.x+=x.minPixelPadding),"path"===l.shape.type){r=u.d,a=f;for(var j=i,k=r.length,O=0;O<k;)"number"==typeof r[O]&&"number"==typeof r[O+1]?(r[O]=x.toPixels(r[O])-a,r[O+1]=g.toPixels(r[O+1])-j,O+=2):O+=1}}"circle"===l.shape.type&&(u.x+=u.r,u.y+=u.r),y.attr(u)}d.bBox=null,e(b)||(o=d.getBBox(),b=o.width),e(m)||(o||(o=d.getBBox()),m=o.height),e(w)||(w=s.center),e(v)||(v=s.center),f-=b*w,i-=m*v,c.animation&&n(d.translateX)&&n(d.translateY)?d.animate({translateX:f,translateY:i}):d.translate(f,i)}},destroy:function(){var t=this,i=this.chart.annotations.allItems,e=i.indexOf(t);e>-1&&i.splice(e,1),l(["title","shape","group"],function(i){t[i]&&(t[i].destroy(),t[i]=null)}),t.group=t.title=t.shape=t.chart=t.options=null},update:function(t,i){h(this.options,t),this.linkObjects(),this.render(i)},linkObjects:function(){var t=this.chart,i=this.linkedObject,e=i&&(i.id||i.options.id),a=this.options.linkedTo;n(a)?n(i)&&a===e||(this.linkedObject=t.get(a)):this.linkedObject=null}},h(o.prototype,{annotations:{add:function(t,i){var e,n,a=this.allItems,s=this.chart;for("[object Array]"===Object.prototype.toString.call(t)||(t=[t]),n=t.length;n--;)e=new d(s,t[n]),a.push(e),e.render(i)},redraw:function(){l(this.allItems,function(t){t.redraw()})}}}),o.prototype.callbacks.push(function(i){var e,n=i.options.annotations;e=i.renderer.g("annotations"),e.attr({zIndex:7}),e.add(),i.annotations.allItems=[],i.annotations.chart=i,i.annotations.group=e,"[object Array]"===Object.prototype.toString.call(n)&&n.length>0&&i.annotations.add(i.options.annotations),t.addEvent(i,"redraw",function(){i.annotations.redraw()})})}(Highcharts,HighchartsAdapter);