#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1080;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.layout-drawer {
    padding: 13px;
    margin-right: -1rem;
    cursor: pointer;
}

    .layout-drawer:hover {
        background-color: #1893a7;
    }


.layout-item {
    cursor: pointer;
    border: 2px solid #e9ecef;
    padding: 4px;
    border-radius: 4px;
    height: 80px;
    width: 120px;
    transition: border .3s linear;
}

    .layout-item:hover,
    .layout-item.active {
        border: 2px solid #28a745;
    }

    .layout-item .layout-left {
        width: 30%;
    }

        .layout-item .layout-left .layout-left-header {
            height: 16px;
            background-color: #367fa9;
        }

        .layout-item .layout-left .layout-left-body,
        .layout-item .layout-body .layout-left {
            background-color: #2f4050;
        }

    .layout-item .layout-right .layout-right-header,
    .layout-item .layout-top {
        background-color: #17a2b8;
        height: 16px;
    }

    .layout-item .layout-right .layout-right-footer,
    .layout-item .layout-footer {
        background-color: #5b6e84;
        height: 12px;
    }

    .layout-item .layout-top,
    .layout-item .layout-body,
    .layout-item .layout-footer {
        width: 100%;
    }

.layout.is-page .layout-right,
.layout.is-page .layout-main {
    background-color: #fff;
}

.color {
    width: 1.5rem;
    height: 1.5rem;
    display: block;
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    transition: border .3s linear;
}

    .color:hover {
        border: 2px solid #28a745;
    }

.layout.is-page.color1 .layout-header {
    background-color: #ffffff;
    color: #333;
    border-bottom: 1px solid #ccc;
}

.layout.is-page.color1 .layout-side .layout-banner {
    background-color: #03152A
}
.layout.is-page.color1 .layout-side .menu {
    --bs-nav-link-color: #c0c4cc;
}
.layout {
    --bb-layout-title-color: #c0c4cc;
}

    .layout.is-page.color1 .layout-side {
        background-color: #03152A;
        color: #c0c4cc;
    }

    .layout.is-page.color1 .layout-footer {
        background-color: #ffffff
    }

    .layout.is-page.color1 .layout-header-bar {
        background-color: #2b7cd0;
        border-color: #014186;
    }

    .layout.is-page.color1 .layout-drawer:hover {
        background-color: #3184dc;
    }

.widget .dropdown-body h3 {
    color: #666666;
    font-size: 14px;
    margin-bottom: 10px;
}

.widget .dropdown-body h4 {
    color: #444444;
    font-size: 15px;
    margin: 0;
}

.widget .dropdown-body small {
    color: #999999;
    font-size: 10px;
    position: absolute;
    top: 0;
    right: 0;
}

.widget .dropdown-item > div:not(.progress):last-child {
    width: calc(100% - 40px);
}

.widget .dropdown-item {
    padding: 0.5rem 1rem;
}

.widget .progress {
    height: 7px;
}

.widget .dropdown-item.active,
.widget .dropdown-item:active {
    color: inherit;
}

.widget .dropdown-item:not(:nth-of-type(odd)):active {
    background-color: inherit;
}

.groupbox {
    margin-top: 10px;
}

@media (min-width: 800px) {

    .table-toolbar .table-toolbar-button {
        float: right !important;
    }
}

.btn-group .btn {
    padding-left: 10px !important;
}

.table-cell .btn-group .btn {
    margin-left: 3px !important;
    border-radius: 2px !important;
}

.table-modal-footer {
    margin-top: 10px !important;
}

.toast {
    background-color: white;
}

.tabs-body {
    display: flex;
    flex: auto;
    flex-direction: column;
}

    .tabs-body .tabs-body-content {
        flex: auto;
        display: flex;
        flex-direction: column;
    }
/*.table-pagination {
    right: 0px;
    position: absolute;
}*/
