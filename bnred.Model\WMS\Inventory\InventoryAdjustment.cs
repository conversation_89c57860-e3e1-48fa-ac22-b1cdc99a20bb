using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums.Inventory;
namespace bnred.Model.WMS.Inventory
{
    /// <summary>
    /// 库存调整记录
    /// 记录手动调整库存的历史
    /// </summary>
    [Table("WMS_InventoryAdjustments")]
    public class InventoryAdjustment : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 调整单号
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "调整单号")]
        public string AdjustmentNo { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string? BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [Display(Name = "库位")]
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public Location Location { get; set; }

        /// <summary>
        /// 调整前数量
        /// </summary>
        [Required]
        [Display(Name = "调整前数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal QuantityBefore { get; set; }

        /// <summary>
        /// 调整后数量
        /// </summary>
        [Required]
        [Display(Name = "调整后数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal QuantityAfter { get; set; }

        /// <summary>
        /// 调整数量差值
        /// </summary>
        [Display(Name = "调整数量差值")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Difference { get; set; }

        /// <summary>
        /// 调整类型
        /// </summary>
        [Required]
        [Display(Name = "调整类型")]
        public AdjustmentType AdjustmentType { get; set; }

        /// <summary>
        /// 调整人ID
        /// </summary>
        [Required]
        [Display(Name = "调整人")]
   
        public Guid? AdjusterId { get; set; }
 
        public FrameworkUser Adjuster { get; set; }

        /// <summary>
        /// 调整时间
        /// </summary>
        [Required]
        [Display(Name = "调整时间")]
        public DateTime AdjustmentTime { get; set; }

        /// <summary>
        /// 调整原因
        /// </summary>
        [Required]
        [Display(Name = "调整原因")]
        [StringLength(500)]
        public string Reason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
}