using System.ComponentModel.DataAnnotations;

namespace bnred.Model.Analysis
{
    /// <summary>
    /// 成本异常类型
    /// </summary>
    public enum CostAnomalyType
    {
        /// <summary>
        /// 成本突增
        /// </summary>
        [Display(Name = "成本突增")]
        SuddenIncrease = 0,

        /// <summary>
        /// 成本突降
        /// </summary>
        [Display(Name = "成本突降")]
        SuddenDecrease = 1,

        /// <summary>
        /// 持续上涨
        /// </summary>
        [Display(Name = "持续上涨")]
        ContinuousIncrease = 2,

        /// <summary>
        /// 持续下跌
        /// </summary>
        [Display(Name = "持续下跌")]
        ContinuousDecrease = 3,

        /// <summary>
        /// 异常波动
        /// </summary>
        [Display(Name = "异常波动")]
        AbnormalFluctuation = 4,

        /// <summary>
        /// 超出预算
        /// </summary>
        [Display(Name = "超出预算")]
        OverBudget = 5,

        /// <summary>
        /// 其他异常
        /// </summary>
        [Display(Name = "其他异常")]
        Other = 99
    }

    /// <summary>
    /// 成本异常等级
    /// </summary>
    public enum CostAnomalyLevel
    {
        /// <summary>
        /// 低
        /// </summary>
        [Display(Name = "低")]
        Low = 0,

        /// <summary>
        /// 中
        /// </summary>
        [Display(Name = "中")]
        Medium = 1,

        /// <summary>
        /// 高
        /// </summary>
        [Display(Name = "高")]
        High = 2
    }

    /// <summary>
    /// 成本异常处理状态
    /// </summary>
    public enum CostAnomalyStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        [Display(Name = "待处理")]
        Pending = 0,

        /// <summary>
        /// 处理中
        /// </summary>
        [Display(Name = "处理中")]
        Processing = 1,

        /// <summary>
        /// 已处理
        /// </summary>
        [Display(Name = "已处理")]
        Processed = 2,

        /// <summary>
        /// 已忽略
        /// </summary>
        [Display(Name = "已忽略")]
        Ignored = 3
    }
} 