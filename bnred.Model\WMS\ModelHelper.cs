using System;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS
{
    /// <summary>
    /// 模型帮助类
    /// 提供常用类型的引用说明，方便开发者正确引用类型
    /// </summary>
    public static class ModelHelper
    {
        /// <summary>
        /// 说明：WMS模型的基类
        /// 正确引用方式：
        /// using WalkingTec.Mvvm.Core;
        /// 
        /// public class YourClass : BasePoco
        /// {
        ///    // 属性定义
        /// }
        /// </summary>
        public static readonly Type BasePocoType = typeof(BasePoco);

        /// <summary>
        /// 说明：WMS模型中用户引用
        /// 正确引用方式：
        /// using WalkingTec.Mvvm.Core;
        /// 
        /// public FrameworkUser User { get; set; }
        /// </summary>
        public static readonly Type FrameworkUserType = typeof(FrameworkUser);

        /// <summary>
        /// 生成GUID的推荐方法
        /// </summary>
        /// <returns>GUID字符串</returns>
        public static string CreateGuid()
        {
            return Guid.CreateVersion7().ToString();
        }
    }
} 