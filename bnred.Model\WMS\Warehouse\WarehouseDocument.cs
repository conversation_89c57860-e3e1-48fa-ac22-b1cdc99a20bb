using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core; 

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库文档
    /// 用于管理与仓库相关的文档和附件
    /// </summary>
    [Table("WMS_WarehouseDocuments")]
    public class WarehouseDocument :  BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 文档编号
        /// </summary>
        [Display(Name = "文档编号")]
        [Required]
        [StringLength(50)]
        public string DocumentNo { get; set; }

        /// <summary>
        /// 文档名称
        /// </summary>
        [Display(Name = "文档名称")]
        [Required]
        [StringLength(100)]
        public string DocumentName { get; set; }

        /// <summary>
        /// 文档类型
        /// </summary>
        [Display(Name = "文档类型")]
        public DocumentType DocumentType { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public Warehouse Warehouse { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Display(Name = "文件路径")]
        [StringLength(500)]
        public string FilePath { get; set; }

        /// <summary>
        /// 文件大小(KB)
        /// </summary>
        [Display(Name = "文件大小")]
        public long? FileSize { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        [Display(Name = "文件类型")]
        [StringLength(50)]
        public string FileType { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        [Display(Name = "上传时间")]
        public DateTime UploadTime { get; set; }

        /// <summary>
        /// 上传人ID
        /// </summary>
 
        public Guid UploaderId { get; set; }

        /// <summary>
        /// 上传人
        /// </summary>
        public FrameworkUser Uploader { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        [Display(Name = "是否有效")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
} 