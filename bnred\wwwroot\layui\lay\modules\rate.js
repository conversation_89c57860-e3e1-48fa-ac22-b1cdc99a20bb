/** layui-v2.5.7 MIT License */
 ;layui.define("jquery",function(e){"use strict";var a=layui.jquery,l={config:{},index:layui.rate?layui.rate.index+1e4:0,set:function(e){var l=this;return l.config=a.extend({},l.config,e),l},on:function(e,a){return layui.onevent.call(this,n,e,a)}},i=function(){var e=this,a=e.config;return{setvalue:function(a){e.setvalue.call(e,a)},config:a}},n="rate",t="layui-rate",o="layui-icon-rate",u="layui-icon-rate-solid",s="layui-icon-rate-half",r="layui-icon-rate-solid layui-icon-rate-half",c="layui-icon-rate-solid layui-icon-rate",f="layui-icon-rate layui-icon-rate-half",v=function(e){var i=this;i.index=++l.index,i.config=a.extend({},i.config,l.config,e),i.render()};v.prototype.config={length:5,text:!1,readonly:!1,half:!1,value:0,theme:""},v.prototype.render=function(){var e=this,l=e.config,i=l.theme?'style="color: '+l.theme+';"':"";l.elem=a(l.elem),l.value>l.length&&(l.value=l.length),parseInt(l.value)!==l.value&&(l.half||(l.value=Math.ceil(l.value)-l.value<.5?Math.ceil(l.value):Math.floor(l.value)));for(var n='<ul class="layui-rate" '+(l.readonly?"readonly":"")+">",s=1;s<=l.length;s++){var r='<li class="layui-inline"><i class="layui-icon '+(s>Math.floor(l.value)?o:u)+'" '+i+"></i></li>";l.half&&parseInt(l.value)!==l.value&&s==Math.ceil(l.value)?n=n+'<li><i class="layui-icon layui-icon-rate-half" '+i+"></i></li>":n+=r}n+="</ul>"+(l.text?'<span class="layui-inline">'+l.value+"星":"")+"</span>";var c=l.elem,f=c.next("."+t);f[0]&&f.remove(),e.elemTemp=a(n),l.span=e.elemTemp.next("span"),l.setText&&l.setText(l.value),c.html(e.elemTemp),c.addClass("layui-inline"),l.readonly||e.action()},v.prototype.setvalue=function(e){var a=this,l=a.config;l.value=e,a.render()},v.prototype.action=function(){var e=this,l=e.config,i=e.elemTemp,n=i.find("i").width();i.children("li").each(function(e){var t=e+1,v=a(this);v.on("click",function(e){if(l.value=t,l.half){var o=e.pageX-a(this).offset().left;o<=n/2&&(l.value=l.value-.5)}l.text&&i.next("span").text(l.value+"星"),l.choose&&l.choose(l.value),l.setText&&l.setText(l.value)}),v.on("mousemove",function(e){if(i.find("i").each(function(){a(this).addClass(o).removeClass(r)}),i.find("i:lt("+t+")").each(function(){a(this).addClass(u).removeClass(f)}),l.half){var c=e.pageX-a(this).offset().left;c<=n/2&&v.children("i").addClass(s).removeClass(u)}}),v.on("mouseleave",function(){i.find("i").each(function(){a(this).addClass(o).removeClass(r)}),i.find("i:lt("+Math.floor(l.value)+")").each(function(){a(this).addClass(u).removeClass(f)}),l.half&&parseInt(l.value)!==l.value&&i.children("li:eq("+Math.floor(l.value)+")").children("i").addClass(s).removeClass(c)})})},v.prototype.events=function(){var e=this;e.config},l.render=function(e){var a=new v(e);return i.call(a)},e(n,l)});