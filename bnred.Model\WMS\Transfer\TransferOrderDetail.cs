using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;
 

namespace bnred.Model.WMS.Transfer
{
    /// <summary>
    /// 移库单明细
    /// 用于记录移库单中的物料明细信息
    /// </summary>
    [Table("WMS_TransferOrderDetails")]
    public class TransferOrderDetail : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 移库单ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string TransferOrderId { get; set; }

        /// <summary>
        /// 移库单
        /// </summary>
        [Display(Name = "移库单")]
        public TransferOrder TransferOrder { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [StringLength(50)]
        public string BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public bnred.Model.WMS.Batch.Batch Batch { get; set; }

        /// <summary>
        /// 源库位ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string SourceLocationId { get; set; }

        /// <summary>
        /// 源库位
        /// </summary>
        [Display(Name = "源库位")]
        public Location SourceLocation { get; set; }

        /// <summary>
        /// 目标库位ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string TargetLocationId { get; set; }

        /// <summary>
        /// 目标库位
        /// </summary>
        [Display(Name = "目标库位")]
        public Location TargetLocation { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "计划数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        [Display(Name = "实际数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? ActualQuantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "单位")]
        [StringLength(20, ErrorMessage = "{0}最多输入{1}个字符")]
        public string Unit { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "状态")]
        public TransferDetailStatus Status { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [Display(Name = "完成时间")]
        public DateTime? CompletionTime { get; set; }

        /// <summary>
        /// 操作人员ID
        /// </summary>
        [Display(Name = "操作人员")]
 
        public Guid? OperatorId { get; set; }

 
        public FrameworkUser Operator { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        public string Remark { get; set; }
    }
} 