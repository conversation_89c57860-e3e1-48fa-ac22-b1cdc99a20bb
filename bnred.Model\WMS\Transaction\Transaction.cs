using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;
 
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;

namespace bnred.Model.WMS.Transaction
{
    /// <summary>
    /// 交易信息
    /// </summary>
    [Table("Transactions")]
    public class Transaction : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 交易编号
        /// </summary>
        [Required(ErrorMessage = "交易编号不能为空")]
        [StringLength(50, ErrorMessage = "交易编号长度不能超过50个字符")]
        [Display(Name = "交易编号")]
        public string Code { get; set; }

        /// <summary>
        /// 交易类型
        /// </summary>
        [Required(ErrorMessage = "交易类型不能为空")]
        [Display(Name = "交易类型")]
        public WarehouseTransactionType Type { get; set; }

        /// <summary>
        /// 交易状态
        /// </summary>
        [Required(ErrorMessage = "交易状态不能为空")]
        [Display(Name = "交易状态")]
        public TransactionStatus Status { get; set; }

        /// <summary>
        /// 交易来源
        /// </summary>
        [Required(ErrorMessage = "交易来源不能为空")]
        [Display(Name = "交易来源")]
        public TransactionSource Source { get; set; }

        /// <summary>
        /// 交易优先级
        /// </summary>
        [Required(ErrorMessage = "交易优先级不能为空")]
        [Display(Name = "交易优先级")]
        public TransactionPriority Priority { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required(ErrorMessage = "物料不能为空")]
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public virtual bnred.Model.Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public virtual bnred.Model.WMS.Batch.Batch? Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required(ErrorMessage = "仓库不能为空")]
        [Display(Name = "仓库")]
        [StringLength(50)]
        [ForeignKey("Warehouse")]
        public string WarehouseID { get; set; }
        public virtual bnred.Model.WMS.Warehouse.Warehouse  Warehouse { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required(ErrorMessage = "库位不能为空")]
        [Display(Name = "库位")]
        [StringLength(50)]
        public string LocationId { get; set; }
        Location Location { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required(ErrorMessage = "数量不能为空")]
        [Range(0, double.MaxValue, ErrorMessage = "数量不能小于0")]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "数量")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位成本
        /// </summary>
        [Required(ErrorMessage = "单位成本不能为空")]
        [Range(0, double.MaxValue, ErrorMessage = "单位成本不能小于0")]
        [Column(TypeName = "decimal(18,4)")]
        [Display(Name = "单位成本")]
        public decimal UnitCost { get; set; }

        /// <summary>
        /// 总成本
        /// </summary>
        [Required(ErrorMessage = "总成本不能为空")]
        [Range(0, double.MaxValue, ErrorMessage = "总成本不能小于0")]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "总成本")]
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 单据类型
        /// </summary>
        [Required(ErrorMessage = "单据类型不能为空")]
        [Display(Name = "单据类型")]
        public DocumentType DocumentType { get; set; }

        /// <summary>
        /// 单据编号
        /// </summary>
        [Required(ErrorMessage = "单据编号不能为空")]
        [StringLength(50, ErrorMessage = "单据编号长度不能超过50个字符")]
        [Display(Name = "单据编号")]
        public string DocumentNo { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        [Required(ErrorMessage = "业务日期不能为空")]
        [Display(Name = "业务日期")]
        public DateTime BusinessDate { get; set; }

        /// <summary>
        /// 计划执行时间
        /// </summary>
        [Required(ErrorMessage = "计划执行时间不能为空")]
        [Display(Name = "计划执行时间")]
        public DateTime PlannedTime { get; set; }

        /// <summary>
        /// 实际执行时间
        /// </summary>
        [Display(Name = "实际执行时间")]
        public DateTime? ActualTime { get; set; }

        /// <summary>
        /// 审批人ID
        /// </summary>
        [Display(Name = "审批人")]
     
        public Guid? ApproverId { get; set; }
        
 
        public FrameworkUser Approver { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        [Display(Name = "审核时间")]
        public DateTime? ApproveTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        [Display(Name = "备注")]
        public string? Remark { get; set; }

        /// <summary>
        /// 来源仓库ID
        /// </summary>
        [Required(ErrorMessage = "来源仓库不能为空")]
        [Display(Name = "来源仓库")]
        [StringLength(50)]
        [ForeignKey("FromWarehouseID")]
        public string FromWarehouseID { get; set; }
        public virtual bnred.Model.WMS.Warehouse.Warehouse FromWarehouse { get; set; }
    }
}