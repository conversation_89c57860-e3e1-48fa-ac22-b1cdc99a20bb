using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 任务异常类型
    /// </summary>
    public enum TaskExceptionType
    {
        [Display(Name = "库存不足")]
        InsufficientStock = 0,

        [Display(Name = "库位不存在")]
        LocationNotFound = 1,

        [Display(Name = "条码错误")]
        BarcodeError = 2,

        [Display(Name = "设备故障")]
        EquipmentFailure = 3,

        [Display(Name = "人员缺失")]
        StaffAbsent = 4,

        [Display(Name = "其他")]
        Other = 99
    }

    /// <summary>
    /// 任务异常状态
    /// </summary>
    public enum TaskExceptionStatus
    {
        [Display(Name = "待处理")]
        Pending = 0,

        [Display(Name = "处理中")]
        Processing = 1,

        [Display(Name = "已解决")]
        Resolved = 2,

        [Display(Name = "已关闭")]
        Closed = 3
    }
} 