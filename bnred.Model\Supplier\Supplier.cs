using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Batch;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Supplier
{
    /// <summary>
    /// 供应商信息
    /// 用于存储供应商的基本信息
    /// </summary>
    [Table("Suppliers")]
    public class Supplier : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 供应商编码
        /// </summary>
        [Display(Name = "供应商编码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Display(Name = "供应商名称")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 供应商类型
        /// </summary>
        [Display(Name = "供应商类型")]
        public SupplierType Type { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Display(Name = "联系人")]
        [StringLength(50)]
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [Display(Name = "联系电话")]
        [StringLength(20)]
        public string ContactPhone { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Display(Name = "电子邮箱")]
        [StringLength(100)]
        public string Email { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        [Display(Name = "传真")]
        [StringLength(20)]
        public string Fax { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [Display(Name = "地址")]
        [StringLength(200)]
        public string Address { get; set; }

        /// <summary>
        /// 邮政编码
        /// </summary>
        [Display(Name = "邮政编码")]
        [StringLength(20)]
        public string PostalCode { get; set; }

        /// <summary>
        /// 国家/地区
        /// </summary>
        [Display(Name = "国家/地区")]
        [StringLength(50)]
        public string Country { get; set; }

        /// <summary>
        /// 省/州
        /// </summary>
        [Display(Name = "省/州")]
        [StringLength(50)]
        public string Province { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        [Display(Name = "城市")]
        [StringLength(50)]
        public string City { get; set; }

        /// <summary>
        /// 网站
        /// </summary>
        [Display(Name = "网站")]
        [StringLength(100)]
        public string Website { get; set; }

        /// <summary>
        /// 税号
        /// </summary>
        [Display(Name = "税号")]
        [StringLength(50)]
        public string TaxNumber { get; set; }

        /// <summary>
        /// 银行名称
        /// </summary>
        [Display(Name = "银行名称")]
        [StringLength(100)]
        public string BankName { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>
        [Display(Name = "银行账号")]
        [StringLength(50)]
        public string BankAccount { get; set; }

        /// <summary>
        /// 信用等级
        /// </summary>
        [Display(Name = "信用等级")]
        public SupplierCreditRating CreditRating { get; set; }

        /// <summary>
        /// 付款条件
        /// </summary>
        [Display(Name = "付款条件")]
        [StringLength(200)]
        public string PaymentTerms { get; set; }

        /// <summary>
        /// 交货方式
        /// </summary>
        [Display(Name = "交货方式")]
        [StringLength(100)]
        public string DeliveryMethod { get; set; }

        /// <summary>
        /// 供应商状态
        /// </summary>
        [Display(Name = "供应商状态")]
        public SupplierStatus Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 批次列表
        /// </summary>
        [Display(Name = "批次列表")]
        public List<Batch> Batches { get; set; }
    }

    /// <summary>
    /// 供应商类型枚举
    /// </summary>
    public enum SupplierType
    {
        /// <summary>
        /// 原材料供应商
        /// </summary>
        [Display(Name = "原材料供应商")]
        RawMaterial = 0,

        /// <summary>
        /// 设备供应商
        /// </summary>
        [Display(Name = "设备供应商")]
        Equipment = 1,

        /// <summary>
        /// 服务供应商
        /// </summary>
        [Display(Name = "服务供应商")]
        Service = 2,

        /// <summary>
        /// 物流供应商
        /// </summary>
        [Display(Name = "物流供应商")]
        Logistics = 3,

        /// <summary>
        /// 其他供应商
        /// </summary>
        [Display(Name = "其他供应商")]
        Other = 4
    }

    /// <summary>
    /// 供应商信用等级枚举
    /// </summary>
    public enum SupplierCreditRating
    {
        /// <summary>
        /// A级(优秀)
        /// </summary>
        [Display(Name = "A级(优秀)")]
        A = 0,

        /// <summary>
        /// B级(良好)
        /// </summary>
        [Display(Name = "B级(良好)")]
        B = 1,

        /// <summary>
        /// C级(一般)
        /// </summary>
        [Display(Name = "C级(一般)")]
        C = 2,

        /// <summary>
        /// D级(较差)
        /// </summary>
        [Display(Name = "D级(较差)")]
        D = 3,

        /// <summary>
        /// E级(很差)
        /// </summary>
        [Display(Name = "E级(很差)")]
        E = 4
    }

    /// <summary>
    /// 供应商状态枚举
    /// </summary>
    public enum SupplierStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 禁用
        /// </summary>
        [Display(Name = "禁用")]
        Disabled = 1,

        /// <summary>
        /// 待审核
        /// </summary>
        [Display(Name = "待审核")]
        PendingApproval = 2,

        /// <summary>
        /// 黑名单
        /// </summary>
        [Display(Name = "黑名单")]
        Blacklisted = 3
    }
} 