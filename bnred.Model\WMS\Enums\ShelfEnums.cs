using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 货架类型枚举
    /// 用于标识不同的货架类型
    /// </summary>
    public enum ShelfType
    {
        /// <summary>
        /// 标准货架
        /// 常规标准货架
        /// </summary>
        [Display(Name = "标准货架")]
        Standard = 0,

        /// <summary>
        /// 重型货架
        /// 承重较大的货架
        /// </summary>
        [Display(Name = "重型货架")]
        Heavy = 1,

        /// <summary>
        /// 流利式货架
        /// 带有滚轮的流利货架
        /// </summary>
        [Display(Name = "流利式货架")]
        Flow = 2,

        /// <summary>
        /// 穿梭式货架
        /// 配合穿梭车使用的货架
        /// </summary>
        [Display(Name = "穿梭式货架")]
        Shuttle = 3,

        /// <summary>
        /// 悬臂式货架
        /// 单面悬臂式货架
        /// </summary>
        [Display(Name = "悬臂式货架")]
        Cantilever = 4,

        /// <summary>
        /// 阁���式货架
        /// 多层阁楼式货架
        /// </summary>
        [Display(Name = "阁楼式货架")]
        Mezzanine = 5
    }

    /// <summary>
    /// 货架状态枚举
    /// 用于标识货架的使用状态
    /// </summary>
    public enum ShelfStatus
    {
        /// <summary>
        /// 正常使用
        /// 货架正常使用中
        /// </summary>
        [Display(Name = "正常使用")]
        Normal = 0,

        /// <summary>
        /// 维护中
        /// 货架正在维护中
        /// </summary>
        [Display(Name = "维护中")]
        Maintenance = 1,

        /// <summary>
        /// 已满载
        /// 货架已装满货物
        /// </summary>
        [Display(Name = "已满载")]
        Full = 2,

        /// <summary>
        /// 已锁定
        /// 货架已被锁定
        /// </summary>
        [Display(Name = "已锁定")]
        Locked = 3,

        /// <summary>
        /// 已停用
        /// 货架已停止使用
        /// </summary>
        [Display(Name = "已停用")]
        Disabled = 4
    }
} 