﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using bnred.Model.WMS.Warehouse;


namespace bnred.ViewModel.Warehouse.WarehouseAreaVMs
{
    public partial class WarehouseAreaBatchVM : BaseBatchVM<WarehouseArea, WarehouseArea_BatchEdit>
    {
        public WarehouseAreaBatchVM()
        {
            ListVM = new WarehouseAreaListVM();
            LinkedVM = new WarehouseArea_BatchEdit();
        }

    }

	/// <summary>
    /// Class to define batch edit fields
    /// </summary>
    public class WarehouseArea_BatchEdit : BaseVM
    {
        public String ID { get; set; }
        [Display(Name = "区域编码")]
        public String Code { get; set; }
        [Display(Name = "区域名称")]
        public String Name { get; set; }
        [Display(Name = "面积(平方米)")]
        public Decimal? Area { get; set; }
        [Display(Name = "最大存储量")]
        public Int32? MaxCapacity { get; set; }
        [Display(Name = "当前存储量")]
        public Int32? CurrentCapacity { get; set; }
        public Guid? ManagerId { get; set; }
        [Display(Name = "描述")]
        public String Description { get; set; }
        [Display(Name = "备注")]
        public String Remark { get; set; }

        protected override void InitVM()
        {
        }

    }

}
