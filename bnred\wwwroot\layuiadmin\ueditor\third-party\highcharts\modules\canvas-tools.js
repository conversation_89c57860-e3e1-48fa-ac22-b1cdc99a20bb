/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;function RGBColor(t){this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6));var e,t=t.replace(/ /g,""),t=t.toLowerCase(),i={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};for(e in i)t==e&&(t=i[e]);var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}];for(e=0;e<n.length;e++){var s=n[e].process,a=n[e].re.exec(t);a&&(channels=s(a),this.r=channels[0],this.g=channels[1],this.b=channels[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),i=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==i.length&&(i="0"+i),"#"+t+e+i},this.getHelpXML=function(){for(var t=[],e=0;e<n.length;e++)for(var s=n[e].example,a=0;a<s.length;a++)t[t.length]=s[a];for(var r in i)t[t.length]=r;for(s=document.createElement("ul"),s.setAttribute("id","rgbcolor-examples"),e=0;e<t.length;e++)try{var o=document.createElement("li"),h=new RGBColor(t[e]),l=document.createElement("div");l.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),l.appendChild(document.createTextNode("test"));var u=document.createTextNode(" "+t[e]+" -> "+h.toRGB()+" -> "+h.toHex());o.appendChild(l),o.appendChild(u),s.appendChild(o)}catch(f){}return s}}window.console||(window.console={},window.console.log=function(){},window.console.dir=function(){}),Array.prototype.indexOf||(Array.prototype.indexOf=function(t){for(var e=0;e<this.length;e++)if(this[e]==t)return e;return-1}),function(){function t(){var t={FRAMERATE:30,MAX_VIRTUAL_PIXELS:3e4};return t.init=function(e){t.Definitions={},t.Styles={},t.Animations=[],t.Images=[],t.ctx=e,t.ViewPort=new function(){this.viewPorts=[],this.Clear=function(){this.viewPorts=[]},this.SetCurrent=function(t,e){this.viewPorts.push({width:t,height:e})},this.RemoveCurrent=function(){this.viewPorts.pop()},this.Current=function(){return this.viewPorts[this.viewPorts.length-1]},this.width=function(){return this.Current().width},this.height=function(){return this.Current().height},this.ComputeSize=function(t){return null!=t&&"number"==typeof t?t:"x"==t?this.width():"y"==t?this.height():Math.sqrt(Math.pow(this.width(),2)+Math.pow(this.height(),2))/Math.sqrt(2)}}},t.init(),t.ImagesLoaded=function(){for(var e=0;e<t.Images.length;e++)if(!t.Images[e].loaded)return!1;return!0},t.trim=function(t){return t.replace(/^\s+|\s+$/g,"")},t.compressSpaces=function(t){return t.replace(/[\s\r\t\n]+/gm," ")},t.ajax=function(t){var e;return(e=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"))?(e.open("GET",t,!1),e.send(null),e.responseText):null},t.parseXml=function(t){if(window.DOMParser)return(new DOMParser).parseFromString(t,"text/xml");var t=t.replace(/<!DOCTYPE svg[^>]*>/,""),e=new ActiveXObject("Microsoft.XMLDOM");return e.async="false",e.loadXML(t),e},t.Property=function(e,i){this.name=e,this.value=i,this.hasValue=function(){return null!=this.value&&""!==this.value},this.numValue=function(){if(!this.hasValue())return 0;var t=parseFloat(this.value);return(this.value+"").match(/%$/)&&(t/=100),t},this.valueOrDefault=function(t){return this.hasValue()?this.value:t},this.numValueOrDefault=function(t){return this.hasValue()?this.numValue():t};var n=this;this.Color={addOpacity:function(e){var i=n.value;if(null!=e&&""!=e){var s=new RGBColor(n.value);s.ok&&(i="rgba("+s.r+", "+s.g+", "+s.b+", "+e+")")}return new t.Property(n.name,i)}},this.Definition={getDefinition:function(){var e=n.value.replace(/^(url\()?#([^\)]+)\)?$/,"$2");return t.Definitions[e]},isUrl:function(){return 0==n.value.indexOf("url(")},getFillStyle:function(e){var i=this.getDefinition();return null!=i&&i.createGradient?i.createGradient(t.ctx,e):null!=i&&i.createPattern?i.createPattern(t.ctx,e):null}},this.Length={DPI:function(){return 96},EM:function(e){var i=12,n=new t.Property("fontSize",t.Font.Parse(t.ctx.font).fontSize);return n.hasValue()&&(i=n.Length.toPixels(e)),i},toPixels:function(e){if(!n.hasValue())return 0;var i=n.value+"";return i.match(/em$/)?n.numValue()*this.EM(e):i.match(/ex$/)?n.numValue()*this.EM(e)/2:i.match(/px$/)?n.numValue():i.match(/pt$/)?1.25*n.numValue():i.match(/pc$/)?15*n.numValue():i.match(/cm$/)?n.numValue()*this.DPI(e)/2.54:i.match(/mm$/)?n.numValue()*this.DPI(e)/25.4:i.match(/in$/)?n.numValue()*this.DPI(e):i.match(/%$/)?n.numValue()*t.ViewPort.ComputeSize(e):n.numValue()}},this.Time={toMilliseconds:function(){if(!n.hasValue())return 0;var t=n.value+"";return t.match(/s$/)?1e3*n.numValue():(t.match(/ms$/),n.numValue())}},this.Angle={toRadians:function(){if(!n.hasValue())return 0;var t=n.value+"";return t.match(/deg$/)?n.numValue()*(Math.PI/180):t.match(/grad$/)?n.numValue()*(Math.PI/200):t.match(/rad$/)?n.numValue():n.numValue()*(Math.PI/180)}}},t.Font=new function(){this.Styles=["normal","italic","oblique","inherit"],this.Variants=["normal","small-caps","inherit"],this.Weights="normal,bold,bolder,lighter,100,200,300,400,500,600,700,800,900,inherit".split(","),this.CreateFont=function(e,i,n,s,a,r){return r=null!=r?this.Parse(r):this.CreateFont("","","","","",t.ctx.font),{fontFamily:a||r.fontFamily,fontSize:s||r.fontSize,fontStyle:e||r.fontStyle,fontWeight:n||r.fontWeight,fontVariant:i||r.fontVariant,toString:function(){return[this.fontStyle,this.fontVariant,this.fontWeight,this.fontSize,this.fontFamily].join(" ")}}};var e=this;this.Parse=function(i){for(var n={},i=t.trim(t.compressSpaces(i||"")).split(" "),s=!1,a=!1,r=!1,o=!1,h="",l=0;l<i.length;l++)a||e.Styles.indexOf(i[l])==-1?o||e.Variants.indexOf(i[l])==-1?r||e.Weights.indexOf(i[l])==-1?s?"inherit"!=i[l]&&(h+=i[l]):("inherit"!=i[l]&&(n.fontSize=i[l].split("/")[0]),a=o=r=s=!0):("inherit"!=i[l]&&(n.fontWeight=i[l]),a=o=r=!0):("inherit"!=i[l]&&(n.fontVariant=i[l]),a=o=!0):("inherit"!=i[l]&&(n.fontStyle=i[l]),a=!0);return""!=h&&(n.fontFamily=h),n}},t.ToNumberArray=function(e){for(var e=t.trim(t.compressSpaces((e||"").replace(/,/g," "))).split(" "),i=0;i<e.length;i++)e[i]=parseFloat(e[i]);return e},t.Point=function(t,e){this.x=t,this.y=e,this.angleTo=function(t){return Math.atan2(t.y-this.y,t.x-this.x)},this.applyTransform=function(t){var e=this.x*t[1]+this.y*t[3]+t[5];this.x=this.x*t[0]+this.y*t[2]+t[4],this.y=e}},t.CreatePoint=function(e){return e=t.ToNumberArray(e),new t.Point(e[0],e[1])},t.CreatePath=function(e){for(var e=t.ToNumberArray(e),i=[],n=0;n<e.length;n+=2)i.push(new t.Point(e[n],e[n+1]));return i},t.BoundingBox=function(t,e,n,s){this.y2=this.x2=this.y1=this.x1=Number.NaN,this.x=function(){return this.x1},this.y=function(){return this.y1},this.width=function(){return this.x2-this.x1},this.height=function(){return this.y2-this.y1},this.addPoint=function(t,e){null!=t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x2=this.x1=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),null!=e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y2=this.y1=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))},this.addX=function(t){this.addPoint(t,null)},this.addY=function(t){this.addPoint(null,t)},this.addBoundingBox=function(t){this.addPoint(t.x1,t.y1),this.addPoint(t.x2,t.y2)},this.addQuadraticCurve=function(t,e,i,n,s,a){i=t+2/3*(i-t),n=e+2/3*(n-e),this.addBezierCurve(t,e,i,i+1/3*(s-t),n,n+1/3*(a-e),s,a)},this.addBezierCurve=function(t,e,n,s,a,r,o,h){var l=[t,e],u=[n,s],f=[a,r],d=[o,h];for(this.addPoint(l[0],l[1]),this.addPoint(d[0],d[1]),i=0;i<=1;i++)t=function(t){return Math.pow(1-t,3)*l[i]+3*Math.pow(1-t,2)*t*u[i]+3*(1-t)*Math.pow(t,2)*f[i]+Math.pow(t,3)*d[i]},e=6*l[i]-12*u[i]+6*f[i],n=-3*l[i]+9*u[i]-9*f[i]+3*d[i],s=3*u[i]-3*l[i],0==n?0!=e&&(e=-s/e,0<e&&e<1&&(0==i&&this.addX(t(e)),1==i&&this.addY(t(e)))):(s=Math.pow(e,2)-4*s*n,s<0||(a=(-e+Math.sqrt(s))/(2*n),0<a&&a<1&&(0==i&&this.addX(t(a)),1==i&&this.addY(t(a))),e=(-e-Math.sqrt(s))/(2*n),0<e&&e<1&&(0==i&&this.addX(t(e)),1==i&&this.addY(t(e)))))},this.isPointInBox=function(t,e){return this.x1<=t&&t<=this.x2&&this.y1<=e&&e<=this.y2},this.addPoint(t,e),this.addPoint(n,s)},t.Transform=function(e){var i=this;this.Type={},this.Type.translate=function(e){this.p=t.CreatePoint(e),this.apply=function(t){t.translate(this.p.x||0,this.p.y||0)},this.applyToPoint=function(t){t.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0])}},this.Type.rotate=function(e){e=t.ToNumberArray(e),this.angle=new t.Property("angle",e[0]),this.cx=e[1]||0,this.cy=e[2]||0,this.apply=function(t){t.translate(this.cx,this.cy),t.rotate(this.angle.Angle.toRadians()),t.translate(-this.cx,-this.cy)},this.applyToPoint=function(t){var e=this.angle.Angle.toRadians();t.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0]),t.applyTransform([Math.cos(e),Math.sin(e),-Math.sin(e),Math.cos(e),0,0]),t.applyTransform([1,0,0,1,-this.p.x||0,-this.p.y||0])}},this.Type.scale=function(e){this.p=t.CreatePoint(e),this.apply=function(t){t.scale(this.p.x||1,this.p.y||this.p.x||1)},this.applyToPoint=function(t){t.applyTransform([this.p.x||0,0,0,this.p.y||0,0,0])}},this.Type.matrix=function(e){this.m=t.ToNumberArray(e),this.apply=function(t){t.transform(this.m[0],this.m[1],this.m[2],this.m[3],this.m[4],this.m[5])},this.applyToPoint=function(t){t.applyTransform(this.m)}},this.Type.SkewBase=function(e){this.base=i.Type.matrix,this.base(e),this.angle=new t.Property("angle",e)},this.Type.SkewBase.prototype=new this.Type.matrix,this.Type.skewX=function(t){this.base=i.Type.SkewBase,this.base(t),this.m=[1,0,Math.tan(this.angle.Angle.toRadians()),1,0,0]},this.Type.skewX.prototype=new this.Type.SkewBase,this.Type.skewY=function(t){this.base=i.Type.SkewBase,this.base(t),this.m=[1,Math.tan(this.angle.Angle.toRadians()),0,1,0,0]},this.Type.skewY.prototype=new this.Type.SkewBase,this.transforms=[],this.apply=function(t){for(var e=0;e<this.transforms.length;e++)this.transforms[e].apply(t)},this.applyToPoint=function(t){for(var e=0;e<this.transforms.length;e++)this.transforms[e].applyToPoint(t)};for(var e=t.trim(t.compressSpaces(e)).split(/\s(?=[a-z])/),n=0;n<e.length;n++){var s=e[n].split("(")[0],a=e[n].split("(")[1].replace(")","");this.transforms.push(new this.Type[s](a))}},t.AspectRatio=function(e,i,n,s,a,r,o,h,l,u){var i=t.compressSpaces(i),i=i.replace(/^defer\s/,""),f=i.split(" ")[0]||"xMidYMid",i=i.split(" ")[1]||"meet",d=n/s,c=a/r,m=Math.min(d,c),p=Math.max(d,c);"meet"==i&&(s*=m,r*=m),"slice"==i&&(s*=p,r*=p),l=new t.Property("refX",l),u=new t.Property("refY",u),l.hasValue()&&u.hasValue()?e.translate(-m*l.Length.toPixels("x"),-m*u.Length.toPixels("y")):(f.match(/^xMid/)&&("meet"==i&&m==c||"slice"==i&&p==c)&&e.translate(n/2-s/2,0),f.match(/YMid$/)&&("meet"==i&&m==d||"slice"==i&&p==d)&&e.translate(0,a/2-r/2),f.match(/^xMax/)&&("meet"==i&&m==c||"slice"==i&&p==c)&&e.translate(n-s,0),f.match(/YMax$/)&&("meet"==i&&m==d||"slice"==i&&p==d)&&e.translate(0,a-r)),"none"==f?e.scale(d,c):"meet"==i?e.scale(m,m):"slice"==i&&e.scale(p,p),e.translate(null==o?0:-o,null==h?0:-h)},t.Element={},t.Element.ElementBase=function(e){if(this.attributes={},this.styles={},this.children=[],this.attribute=function(e,i){var n=this.attributes[e];return null!=n?n:(n=new t.Property(e,""),1==i&&(this.attributes[e]=n),n)},this.style=function(e,i){var n=this.styles[e];return null!=n?n:(n=this.attribute(e),null!=n&&n.hasValue()?n:(n=this.parent,null!=n&&(n=n.style(e),null!=n&&n.hasValue())?n:(n=new t.Property(e,""),1==i&&(this.styles[e]=n),n)))},this.render=function(t){if("none"!=this.style("display").value&&"hidden"!=this.attribute("visibility").value){if(t.save(),this.setContext(t),this.attribute("mask").hasValue()){var e=this.attribute("mask").Definition.getDefinition();null!=e&&e.apply(t,this)}else this.style("filter").hasValue()?(e=this.style("filter").Definition.getDefinition(),null!=e&&e.apply(t,this)):this.renderChildren(t);this.clearContext(t),t.restore()}},this.setContext=function(){},this.clearContext=function(){},this.renderChildren=function(t){for(var e=0;e<this.children.length;e++)this.children[e].render(t)},this.addChild=function(e,i){var n=e;i&&(n=t.CreateElement(e)),n.parent=this,this.children.push(n)},null!=e&&1==e.nodeType){for(var i=0;i<e.childNodes.length;i++){var n=e.childNodes[i];1==n.nodeType&&this.addChild(n,!0)}for(i=0;i<e.attributes.length;i++)n=e.attributes[i],this.attributes[n.nodeName]=new t.Property(n.nodeName,n.nodeValue);if(n=t.Styles[e.nodeName],null!=n)for(var s in n)this.styles[s]=n[s];if(this.attribute("class").hasValue())for(var i=t.compressSpaces(this.attribute("class").value).split(" "),a=0;a<i.length;a++){if(n=t.Styles["."+i[a]],null!=n)for(s in n)this.styles[s]=n[s];if(n=t.Styles[e.nodeName+"."+i[a]],null!=n)for(s in n)this.styles[s]=n[s]}if(this.attribute("style").hasValue())for(n=this.attribute("style").value.split(";"),i=0;i<n.length;i++)""!=t.trim(n[i])&&(e=n[i].split(":"),s=t.trim(e[0]),e=t.trim(e[1]),this.styles[s]=new t.Property(s,e));this.attribute("id").hasValue()&&null==t.Definitions[this.attribute("id").value]&&(t.Definitions[this.attribute("id").value]=this)}},t.Element.RenderedElementBase=function(e){this.base=t.Element.ElementBase,this.base(e),this.setContext=function(e){if(this.style("fill").Definition.isUrl()){var i=this.style("fill").Definition.getFillStyle(this);null!=i&&(e.fillStyle=i)}else this.style("fill").hasValue()&&(i=this.style("fill"),this.style("fill-opacity").hasValue()&&(i=i.Color.addOpacity(this.style("fill-opacity").value)),e.fillStyle="none"==i.value?"rgba(0,0,0,0)":i.value);this.style("stroke").Definition.isUrl()?(i=this.style("stroke").Definition.getFillStyle(this),null!=i&&(e.strokeStyle=i)):this.style("stroke").hasValue()&&(i=this.style("stroke"),this.style("stroke-opacity").hasValue()&&(i=i.Color.addOpacity(this.style("stroke-opacity").value)),e.strokeStyle="none"==i.value?"rgba(0,0,0,0)":i.value),this.style("stroke-width").hasValue()&&(e.lineWidth=this.style("stroke-width").Length.toPixels()),this.style("stroke-linecap").hasValue()&&(e.lineCap=this.style("stroke-linecap").value),this.style("stroke-linejoin").hasValue()&&(e.lineJoin=this.style("stroke-linejoin").value),this.style("stroke-miterlimit").hasValue()&&(e.miterLimit=this.style("stroke-miterlimit").value),"undefined"!=typeof e.font&&(e.font=t.Font.CreateFont(this.style("font-style").value,this.style("font-variant").value,this.style("font-weight").value,this.style("font-size").hasValue()?this.style("font-size").Length.toPixels()+"px":"",this.style("font-family").value).toString()),this.attribute("transform").hasValue()&&new t.Transform(this.attribute("transform").value).apply(e),this.attribute("clip-path").hasValue()&&(i=this.attribute("clip-path").Definition.getDefinition(),null!=i&&i.apply(e)),this.style("opacity").hasValue()&&(e.globalAlpha=this.style("opacity").numValue())}},t.Element.RenderedElementBase.prototype=new t.Element.ElementBase,t.Element.PathElementBase=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.path=function(e){return null!=e&&e.beginPath(),new t.BoundingBox},this.renderChildren=function(e){this.path(e),t.Mouse.checkPath(this,e),""!=e.fillStyle&&e.fill(),""!=e.strokeStyle&&e.stroke();var i=this.getMarkers();if(null!=i){if(this.style("marker-start").Definition.isUrl()){var n=this.style("marker-start").Definition.getDefinition();n.render(e,i[0][0],i[0][1])}if(this.style("marker-mid").Definition.isUrl())for(var n=this.style("marker-mid").Definition.getDefinition(),s=1;s<i.length-1;s++)n.render(e,i[s][0],i[s][1]);this.style("marker-end").Definition.isUrl()&&(n=this.style("marker-end").Definition.getDefinition(),n.render(e,i[i.length-1][0],i[i.length-1][1]))}},this.getBoundingBox=function(){return this.path()},this.getMarkers=function(){return null}},t.Element.PathElementBase.prototype=new t.Element.RenderedElementBase,t.Element.svg=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.baseClearContext=this.clearContext,this.clearContext=function(e){this.baseClearContext(e),t.ViewPort.RemoveCurrent()},this.baseSetContext=this.setContext,this.setContext=function(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4,this.baseSetContext(e),this.attribute("x").hasValue()&&this.attribute("y").hasValue()&&e.translate(this.attribute("x").Length.toPixels("x"),this.attribute("y").Length.toPixels("y"));var i=t.ViewPort.width(),n=t.ViewPort.height();if("undefined"==typeof this.root&&this.attribute("width").hasValue()&&this.attribute("height").hasValue()){var i=this.attribute("width").Length.toPixels("x"),n=this.attribute("height").Length.toPixels("y"),s=0,a=0;this.attribute("refX").hasValue()&&this.attribute("refY").hasValue()&&(s=-this.attribute("refX").Length.toPixels("x"),a=-this.attribute("refY").Length.toPixels("y")),e.beginPath(),e.moveTo(s,a),e.lineTo(i,a),e.lineTo(i,n),e.lineTo(s,n),e.closePath(),e.clip()}if(t.ViewPort.SetCurrent(i,n),this.attribute("viewBox").hasValue()){var s=t.ToNumberArray(this.attribute("viewBox").value),a=s[0],r=s[1],i=s[2],n=s[3];t.AspectRatio(e,this.attribute("preserveAspectRatio").value,t.ViewPort.width(),i,t.ViewPort.height(),n,a,r,this.attribute("refX").value,this.attribute("refY").value),t.ViewPort.RemoveCurrent(),t.ViewPort.SetCurrent(s[2],s[3])}}},t.Element.svg.prototype=new t.Element.RenderedElementBase,t.Element.rect=function(e){this.base=t.Element.PathElementBase,this.base(e),this.path=function(e){var i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=this.attribute("width").Length.toPixels("x"),a=this.attribute("height").Length.toPixels("y"),r=this.attribute("rx").Length.toPixels("x"),o=this.attribute("ry").Length.toPixels("y");return this.attribute("rx").hasValue()&&!this.attribute("ry").hasValue()&&(o=r),this.attribute("ry").hasValue()&&!this.attribute("rx").hasValue()&&(r=o),null!=e&&(e.beginPath(),e.moveTo(i+r,n),e.lineTo(i+s-r,n),e.quadraticCurveTo(i+s,n,i+s,n+o),e.lineTo(i+s,n+a-o),e.quadraticCurveTo(i+s,n+a,i+s-r,n+a),e.lineTo(i+r,n+a),e.quadraticCurveTo(i,n+a,i,n+a-o),e.lineTo(i,n+o),e.quadraticCurveTo(i,n,i+r,n),e.closePath()),new t.BoundingBox(i,n,i+s,n+a)}},t.Element.rect.prototype=new t.Element.PathElementBase,t.Element.circle=function(e){this.base=t.Element.PathElementBase,this.base(e),this.path=function(e){var i=this.attribute("cx").Length.toPixels("x"),n=this.attribute("cy").Length.toPixels("y"),s=this.attribute("r").Length.toPixels();return null!=e&&(e.beginPath(),e.arc(i,n,s,0,2*Math.PI,!0),e.closePath()),new t.BoundingBox(i-s,n-s,i+s,n+s)}},t.Element.circle.prototype=new t.Element.PathElementBase,t.Element.ellipse=function(e){this.base=t.Element.PathElementBase,this.base(e),this.path=function(e){var i=4*((Math.sqrt(2)-1)/3),n=this.attribute("rx").Length.toPixels("x"),s=this.attribute("ry").Length.toPixels("y"),a=this.attribute("cx").Length.toPixels("x"),r=this.attribute("cy").Length.toPixels("y");return null!=e&&(e.beginPath(),e.moveTo(a,r-s),e.bezierCurveTo(a+i*n,r-s,a+n,r-i*s,a+n,r),e.bezierCurveTo(a+n,r+i*s,a+i*n,r+s,a,r+s),e.bezierCurveTo(a-i*n,r+s,a-n,r+i*s,a-n,r),e.bezierCurveTo(a-n,r-i*s,a-i*n,r-s,a,r-s),e.closePath()),new t.BoundingBox(a-n,r-s,a+n,r+s)}},t.Element.ellipse.prototype=new t.Element.PathElementBase,t.Element.line=function(e){this.base=t.Element.PathElementBase,this.base(e),this.getPoints=function(){return[new t.Point(this.attribute("x1").Length.toPixels("x"),this.attribute("y1").Length.toPixels("y")),new t.Point(this.attribute("x2").Length.toPixels("x"),this.attribute("y2").Length.toPixels("y"))]},this.path=function(e){var i=this.getPoints();return null!=e&&(e.beginPath(),e.moveTo(i[0].x,i[0].y),e.lineTo(i[1].x,i[1].y)),new t.BoundingBox(i[0].x,i[0].y,i[1].x,i[1].y)},this.getMarkers=function(){var t=this.getPoints(),e=t[0].angleTo(t[1]);return[[t[0],e],[t[1],e]]}},t.Element.line.prototype=new t.Element.PathElementBase,t.Element.polyline=function(e){this.base=t.Element.PathElementBase,this.base(e),this.points=t.CreatePath(this.attribute("points").value),this.path=function(e){var i=new t.BoundingBox(this.points[0].x,this.points[0].y);null!=e&&(e.beginPath(),e.moveTo(this.points[0].x,this.points[0].y));for(var n=1;n<this.points.length;n++)i.addPoint(this.points[n].x,this.points[n].y),null!=e&&e.lineTo(this.points[n].x,this.points[n].y);return i},this.getMarkers=function(){for(var t=[],e=0;e<this.points.length-1;e++)t.push([this.points[e],this.points[e].angleTo(this.points[e+1])]);return t.push([this.points[this.points.length-1],t[t.length-1][1]]),t}},t.Element.polyline.prototype=new t.Element.PathElementBase,t.Element.polygon=function(e){this.base=t.Element.polyline,this.base(e),this.basePath=this.path,this.path=function(t){var e=this.basePath(t);return null!=t&&(t.lineTo(this.points[0].x,this.points[0].y),t.closePath()),e}},t.Element.polygon.prototype=new t.Element.polyline,t.Element.path=function(e){this.base=t.Element.PathElementBase,this.base(e),e=this.attribute("d").value,e=e.replace(/,/gm," "),e=e.replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2"),e=e.replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2"),e=e.replace(/([MmZzLlHhVvCcSsQqTtAa])([^\s])/gm,"$1 $2"),e=e.replace(/([^\s])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2"),e=e.replace(/([0-9])([+\-])/gm,"$1 $2"),e=e.replace(/(\.[0-9]*)(\.)/gm,"$1 $2"),e=e.replace(/([Aa](\s+[0-9]+){3})\s+([01])\s*([01])/gm,"$1 $3 $4 "),e=t.compressSpaces(e),e=t.trim(e),this.PathParser=new function(e){this.tokens=e.split(" "),this.reset=function(){this.i=-1,this.previousCommand=this.command="",this.start=new t.Point(0,0),this.control=new t.Point(0,0),this.current=new t.Point(0,0),this.points=[],this.angles=[]},this.isEnd=function(){return this.i>=this.tokens.length-1},this.isCommandOrEnd=function(){return!!this.isEnd()||null!=this.tokens[this.i+1].match(/^[A-Za-z]$/)},this.isRelativeCommand=function(){return this.command==this.command.toLowerCase()},this.getToken=function(){return this.i+=1,this.tokens[this.i]},this.getScalar=function(){return parseFloat(this.getToken())},this.nextCommand=function(){this.previousCommand=this.command,this.command=this.getToken()},this.getPoint=function(){return this.makeAbsolute(new t.Point(this.getScalar(),this.getScalar()))},this.getAsControlPoint=function(){var t=this.getPoint();return this.control=t},this.getAsCurrentPoint=function(){var t=this.getPoint();return this.current=t},this.getReflectedControlPoint=function(){return"c"!=this.previousCommand.toLowerCase()&&"s"!=this.previousCommand.toLowerCase()?this.current:new t.Point(2*this.current.x-this.control.x,2*this.current.y-this.control.y)},this.makeAbsolute=function(t){return this.isRelativeCommand()&&(t.x=this.current.x+t.x,t.y=this.current.y+t.y),t},this.addMarker=function(t,e,i){null!=i&&this.angles.length>0&&null==this.angles[this.angles.length-1]&&(this.angles[this.angles.length-1]=this.points[this.points.length-1].angleTo(i)),this.addMarkerAngle(t,null==e?null:e.angleTo(t))},this.addMarkerAngle=function(t,e){this.points.push(t),this.angles.push(e)},this.getMarkerPoints=function(){return this.points},this.getMarkerAngles=function(){for(var t=0;t<this.angles.length;t++)if(null==this.angles[t])for(var e=t+1;e<this.angles.length;e++)if(null!=this.angles[e]){this.angles[t]=this.angles[e];break}return this.angles}}(e),this.path=function(e){var i=this.PathParser;i.reset();var n=new t.BoundingBox;for(null!=e&&e.beginPath();!i.isEnd();)switch(i.nextCommand(),i.command.toUpperCase()){case"M":var s=i.getAsCurrentPoint();for(i.addMarker(s),n.addPoint(s.x,s.y),null!=e&&e.moveTo(s.x,s.y),i.start=i.current;!i.isCommandOrEnd();)s=i.getAsCurrentPoint(),i.addMarker(s,i.start),n.addPoint(s.x,s.y),null!=e&&e.lineTo(s.x,s.y);break;case"L":for(;!i.isCommandOrEnd();){var a=i.current,s=i.getAsCurrentPoint();i.addMarker(s,a),n.addPoint(s.x,s.y),null!=e&&e.lineTo(s.x,s.y)}break;case"H":for(;!i.isCommandOrEnd();)s=new t.Point((i.isRelativeCommand()?i.current.x:0)+i.getScalar(),i.current.y),i.addMarker(s,i.current),i.current=s,n.addPoint(i.current.x,i.current.y),null!=e&&e.lineTo(i.current.x,i.current.y);break;case"V":for(;!i.isCommandOrEnd();)s=new t.Point(i.current.x,(i.isRelativeCommand()?i.current.y:0)+i.getScalar()),i.addMarker(s,i.current),i.current=s,n.addPoint(i.current.x,i.current.y),null!=e&&e.lineTo(i.current.x,i.current.y);break;case"C":for(;!i.isCommandOrEnd();){var r=i.current,a=i.getPoint(),o=i.getAsControlPoint(),s=i.getAsCurrentPoint();i.addMarker(s,o,a),n.addBezierCurve(r.x,r.y,a.x,a.y,o.x,o.y,s.x,s.y),null!=e&&e.bezierCurveTo(a.x,a.y,o.x,o.y,s.x,s.y)}break;case"S":for(;!i.isCommandOrEnd();)r=i.current,a=i.getReflectedControlPoint(),o=i.getAsControlPoint(),s=i.getAsCurrentPoint(),i.addMarker(s,o,a),n.addBezierCurve(r.x,r.y,a.x,a.y,o.x,o.y,s.x,s.y),null!=e&&e.bezierCurveTo(a.x,a.y,o.x,o.y,s.x,s.y);break;case"Q":for(;!i.isCommandOrEnd();)r=i.current,o=i.getAsControlPoint(),s=i.getAsCurrentPoint(),i.addMarker(s,o,o),n.addQuadraticCurve(r.x,r.y,o.x,o.y,s.x,s.y),null!=e&&e.quadraticCurveTo(o.x,o.y,s.x,s.y);break;case"T":for(;!i.isCommandOrEnd();)r=i.current,o=i.getReflectedControlPoint(),i.control=o,s=i.getAsCurrentPoint(),i.addMarker(s,o,o),n.addQuadraticCurve(r.x,r.y,o.x,o.y,s.x,s.y),null!=e&&e.quadraticCurveTo(o.x,o.y,s.x,s.y);break;case"A":for(;!i.isCommandOrEnd();){var r=i.current,h=i.getScalar(),l=i.getScalar(),a=i.getScalar()*(Math.PI/180),u=i.getScalar(),o=i.getScalar(),s=i.getAsCurrentPoint(),f=new t.Point(Math.cos(a)*(r.x-s.x)/2+Math.sin(a)*(r.y-s.y)/2,-Math.sin(a)*(r.x-s.x)/2+Math.cos(a)*(r.y-s.y)/2),d=Math.pow(f.x,2)/Math.pow(h,2)+Math.pow(f.y,2)/Math.pow(l,2);d>1&&(h*=Math.sqrt(d),l*=Math.sqrt(d)),u=(u==o?-1:1)*Math.sqrt((Math.pow(h,2)*Math.pow(l,2)-Math.pow(h,2)*Math.pow(f.y,2)-Math.pow(l,2)*Math.pow(f.x,2))/(Math.pow(h,2)*Math.pow(f.y,2)+Math.pow(l,2)*Math.pow(f.x,2))),isNaN(u)&&(u=0);var c=new t.Point(u*h*f.y/l,u*-l*f.x/h),r=new t.Point((r.x+s.x)/2+Math.cos(a)*c.x-Math.sin(a)*c.y,(r.y+s.y)/2+Math.sin(a)*c.x+Math.cos(a)*c.y),m=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))*Math.sqrt(Math.pow(e[0],2)+Math.pow(e[1],2)))},p=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(m(t,e))},u=p([1,0],[(f.x-c.x)/h,(f.y-c.y)/l]),d=[(f.x-c.x)/h,(f.y-c.y)/l],c=[(-f.x-c.x)/h,(-f.y-c.y)/l],f=p(d,c);m(d,c)<=-1&&(f=Math.PI),m(d,c)>=1&&(f=0),0==o&&f>0&&(f-=2*Math.PI),1==o&&f<0&&(f+=2*Math.PI),d=new t.Point(r.x-h*Math.cos((u+f)/2),r.y-l*Math.sin((u+f)/2)),i.addMarkerAngle(d,(u+f)/2+(0==o?1:-1)*Math.PI/2),i.addMarkerAngle(s,f+(0==o?1:-1)*Math.PI/2),n.addPoint(s.x,s.y),null!=e&&(m=h>l?h:l,s=h>l?1:h/l,h=h>l?l/h:1,e.translate(r.x,r.y),e.rotate(a),e.scale(s,h),e.arc(0,0,m,u,u+f,1-o),e.scale(1/s,1/h),e.rotate(-a),e.translate(-r.x,-r.y))}break;case"Z":null!=e&&e.closePath(),i.current=i.start}return n},this.getMarkers=function(){for(var t=this.PathParser.getMarkerPoints(),e=this.PathParser.getMarkerAngles(),i=[],n=0;n<t.length;n++)i.push([t[n],e[n]]);return i}},t.Element.path.prototype=new t.Element.PathElementBase,t.Element.pattern=function(e){this.base=t.Element.ElementBase,this.base(e),this.createPattern=function(e){var i=new t.Element.svg;i.attributes.viewBox=new t.Property("viewBox",this.attribute("viewBox").value),i.attributes.x=new t.Property("x",this.attribute("x").value),i.attributes.y=new t.Property("y",this.attribute("y").value),i.attributes.width=new t.Property("width",this.attribute("width").value),i.attributes.height=new t.Property("height",this.attribute("height").value),i.children=this.children;var n=document.createElement("canvas");return n.width=this.attribute("width").Length.toPixels("x"),n.height=this.attribute("height").Length.toPixels("y"),i.render(n.getContext("2d")),e.createPattern(n,"repeat")}},t.Element.pattern.prototype=new t.Element.ElementBase,t.Element.marker=function(e){this.base=t.Element.ElementBase,this.base(e),this.baseRender=this.render,this.render=function(e,i,n){e.translate(i.x,i.y),"auto"==this.attribute("orient").valueOrDefault("auto")&&e.rotate(n),"strokeWidth"==this.attribute("markerUnits").valueOrDefault("strokeWidth")&&e.scale(e.lineWidth,e.lineWidth),e.save();var s=new t.Element.svg;s.attributes.viewBox=new t.Property("viewBox",this.attribute("viewBox").value),s.attributes.refX=new t.Property("refX",this.attribute("refX").value),s.attributes.refY=new t.Property("refY",this.attribute("refY").value),s.attributes.width=new t.Property("width",this.attribute("markerWidth").value),s.attributes.height=new t.Property("height",this.attribute("markerHeight").value),s.attributes.fill=new t.Property("fill",this.attribute("fill").valueOrDefault("black")),s.attributes.stroke=new t.Property("stroke",this.attribute("stroke").valueOrDefault("none")),s.children=this.children,s.render(e),e.restore(),"strokeWidth"==this.attribute("markerUnits").valueOrDefault("strokeWidth")&&e.scale(1/e.lineWidth,1/e.lineWidth),"auto"==this.attribute("orient").valueOrDefault("auto")&&e.rotate(-n),e.translate(-i.x,-i.y)}},t.Element.marker.prototype=new t.Element.ElementBase,t.Element.defs=function(e){
this.base=t.Element.ElementBase,this.base(e),this.render=function(){}},t.Element.defs.prototype=new t.Element.ElementBase,t.Element.GradientBase=function(e){for(this.base=t.Element.ElementBase,this.base(e),this.gradientUnits=this.attribute("gradientUnits").valueOrDefault("objectBoundingBox"),this.stops=[],e=0;e<this.children.length;e++)this.stops.push(this.children[e]);this.getGradient=function(){},this.createGradient=function(e,i){var n=this;this.attribute("xlink:href").hasValue()&&(n=this.attribute("xlink:href").Definition.getDefinition());for(var s=this.getGradient(e,i),a=0;a<n.stops.length;a++)s.addColorStop(n.stops[a].offset,n.stops[a].color);if(this.attribute("gradientTransform").hasValue()){n=t.ViewPort.viewPorts[0],a=new t.Element.rect,a.attributes.x=new t.Property("x",-t.MAX_VIRTUAL_PIXELS/3),a.attributes.y=new t.Property("y",-t.MAX_VIRTUAL_PIXELS/3),a.attributes.width=new t.Property("width",t.MAX_VIRTUAL_PIXELS),a.attributes.height=new t.Property("height",t.MAX_VIRTUAL_PIXELS);var r=new t.Element.g;return r.attributes.transform=new t.Property("transform",this.attribute("gradientTransform").value),r.children=[a],a=new t.Element.svg,a.attributes.x=new t.Property("x",0),a.attributes.y=new t.Property("y",0),a.attributes.width=new t.Property("width",n.width),a.attributes.height=new t.Property("height",n.height),a.children=[r],r=document.createElement("canvas"),r.width=n.width,r.height=n.height,n=r.getContext("2d"),n.fillStyle=s,a.render(n),n.createPattern(r,"no-repeat")}return s}},t.Element.GradientBase.prototype=new t.Element.ElementBase,t.Element.linearGradient=function(e){this.base=t.Element.GradientBase,this.base(e),this.getGradient=function(t,e){var i=e.getBoundingBox(),n="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("x1").numValue():this.attribute("x1").Length.toPixels("x"),s="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("y1").numValue():this.attribute("y1").Length.toPixels("y"),a="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("x2").numValue():this.attribute("x2").Length.toPixels("x"),i="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("y2").numValue():this.attribute("y2").Length.toPixels("y");return t.createLinearGradient(n,s,a,i)}},t.Element.linearGradient.prototype=new t.Element.GradientBase,t.Element.radialGradient=function(e){this.base=t.Element.GradientBase,this.base(e),this.getGradient=function(t,e){var i=e.getBoundingBox(),n="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("cx").numValue():this.attribute("cx").Length.toPixels("x"),s="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("cy").numValue():this.attribute("cy").Length.toPixels("y"),a=n,r=s;return this.attribute("fx").hasValue()&&(a="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("fx").numValue():this.attribute("fx").Length.toPixels("x")),this.attribute("fy").hasValue()&&(r="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("fy").numValue():this.attribute("fy").Length.toPixels("y")),i="objectBoundingBox"==this.gradientUnits?(i.width()+i.height())/2*this.attribute("r").numValue():this.attribute("r").Length.toPixels(),t.createRadialGradient(a,r,0,n,s,i)}},t.Element.radialGradient.prototype=new t.Element.GradientBase,t.Element.stop=function(e){this.base=t.Element.ElementBase,this.base(e),this.offset=this.attribute("offset").numValue(),e=this.style("stop-color"),this.style("stop-opacity").hasValue()&&(e=e.Color.addOpacity(this.style("stop-opacity").value)),this.color=e.value},t.Element.stop.prototype=new t.Element.ElementBase,t.Element.AnimateBase=function(e){this.base=t.Element.ElementBase,this.base(e),t.Animations.push(this),this.duration=0,this.begin=this.attribute("begin").Time.toMilliseconds(),this.maxDuration=this.begin+this.attribute("dur").Time.toMilliseconds(),this.getProperty=function(){var t=this.attribute("attributeType").value,e=this.attribute("attributeName").value;return"CSS"==t?this.parent.style(e,!0):this.parent.attribute(e,!0)},this.initialValue=null,this.removed=!1,this.calcValue=function(){return""},this.update=function(t){if(null==this.initialValue&&(this.initialValue=this.getProperty().value),this.duration>this.maxDuration){if("indefinite"!=this.attribute("repeatCount").value)return"remove"==this.attribute("fill").valueOrDefault("remove")&&!this.removed&&(this.removed=!0,this.getProperty().value=this.initialValue,!0);this.duration=0}return this.duration+=t,t=!1,this.begin<this.duration&&(t=this.calcValue(),this.attribute("type").hasValue()&&(t=this.attribute("type").value+"("+t+")"),this.getProperty().value=t,t=!0),t},this.progress=function(){return(this.duration-this.begin)/(this.maxDuration-this.begin)}},t.Element.AnimateBase.prototype=new t.Element.ElementBase,t.Element.animate=function(e){this.base=t.Element.AnimateBase,this.base(e),this.calcValue=function(){var t=this.attribute("from").numValue(),e=this.attribute("to").numValue();return t+(e-t)*this.progress()}},t.Element.animate.prototype=new t.Element.AnimateBase,t.Element.animateColor=function(e){this.base=t.Element.AnimateBase,this.base(e),this.calcValue=function(){var t=new RGBColor(this.attribute("from").value),e=new RGBColor(this.attribute("to").value);if(t.ok&&e.ok){var i=t.r+(e.r-t.r)*this.progress(),n=t.g+(e.g-t.g)*this.progress(),t=t.b+(e.b-t.b)*this.progress();return"rgb("+parseInt(i,10)+","+parseInt(n,10)+","+parseInt(t,10)+")"}return this.attribute("from").value}},t.Element.animateColor.prototype=new t.Element.AnimateBase,t.Element.animateTransform=function(e){this.base=t.Element.animate,this.base(e)},t.Element.animateTransform.prototype=new t.Element.animate,t.Element.font=function(e){for(this.base=t.Element.ElementBase,this.base(e),this.horizAdvX=this.attribute("horiz-adv-x").numValue(),this.isArabic=this.isRTL=!1,this.missingGlyph=this.fontFace=null,this.glyphs=[],e=0;e<this.children.length;e++){var i=this.children[e];"font-face"==i.type?(this.fontFace=i,i.style("font-family").hasValue()&&(t.Definitions[i.style("font-family").value]=this)):"missing-glyph"==i.type?this.missingGlyph=i:"glyph"==i.type&&(""!=i.arabicForm?(this.isArabic=this.isRTL=!0,"undefined"==typeof this.glyphs[i.unicode]&&(this.glyphs[i.unicode]=[]),this.glyphs[i.unicode][i.arabicForm]=i):this.glyphs[i.unicode]=i)}},t.Element.font.prototype=new t.Element.ElementBase,t.Element.fontface=function(e){this.base=t.Element.ElementBase,this.base(e),this.ascent=this.attribute("ascent").value,this.descent=this.attribute("descent").value,this.unitsPerEm=this.attribute("units-per-em").numValue()},t.Element.fontface.prototype=new t.Element.ElementBase,t.Element.missingglyph=function(e){this.base=t.Element.path,this.base(e),this.horizAdvX=0},t.Element.missingglyph.prototype=new t.Element.path,t.Element.glyph=function(e){this.base=t.Element.path,this.base(e),this.horizAdvX=this.attribute("horiz-adv-x").numValue(),this.unicode=this.attribute("unicode").value,this.arabicForm=this.attribute("arabic-form").value},t.Element.glyph.prototype=new t.Element.path,t.Element.text=function(e){if(this.base=t.Element.RenderedElementBase,this.base(e),null!=e){this.children=[];for(var i=0;i<e.childNodes.length;i++){var n=e.childNodes[i];1==n.nodeType?this.addChild(n,!0):3==n.nodeType&&this.addChild(new t.Element.tspan(n),!1)}}this.baseSetContext=this.setContext,this.setContext=function(t){this.baseSetContext(t),this.style("dominant-baseline").hasValue()&&(t.textBaseline=this.style("dominant-baseline").value),this.style("alignment-baseline").hasValue()&&(t.textBaseline=this.style("alignment-baseline").value)},this.renderChildren=function(t){for(var e=this.style("text-anchor").valueOrDefault("start"),i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=0;s<this.children.length;s++){var a=this.children[s];if(a.attribute("x").hasValue()?a.x=a.attribute("x").Length.toPixels("x"):(a.attribute("dx").hasValue()&&(i+=a.attribute("dx").Length.toPixels("x")),a.x=i),i=a.measureText(t),"start"!=e&&(0==s||a.attribute("x").hasValue())){for(var r=i,o=s+1;o<this.children.length;o++){var h=this.children[o];if(h.attribute("x").hasValue())break;r+=h.measureText(t)}a.x-="end"==e?r:r/2}i=a.x+i,a.attribute("y").hasValue()?a.y=a.attribute("y").Length.toPixels("y"):(a.attribute("dy").hasValue()&&(n+=a.attribute("dy").Length.toPixels("y")),a.y=n),n=a.y,a.render(t)}}},t.Element.text.prototype=new t.Element.RenderedElementBase,t.Element.TextElementBase=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.getGlyph=function(t,e,i){var n=e[i],s=null;if(t.isArabic){var a="isolated";(0==i||" "==e[i-1])&&i<e.length-2&&" "!=e[i+1]&&(a="terminal"),i>0&&" "!=e[i-1]&&i<e.length-2&&" "!=e[i+1]&&(a="medial"),i>0&&" "!=e[i-1]&&(i==e.length-1||" "==e[i+1])&&(a="initial"),"undefined"!=typeof t.glyphs[n]&&(s=t.glyphs[n][a],null==s&&"glyph"==t.glyphs[n].type&&(s=t.glyphs[n]))}else s=t.glyphs[n];return null==s&&(s=t.missingGlyph),s},this.renderChildren=function(e){var i=this.parent.style("font-family").Definition.getDefinition();if(null!=i){var n=this.parent.style("font-size").numValueOrDefault(t.Font.Parse(t.ctx.font).fontSize),s=this.parent.style("font-style").valueOrDefault(t.Font.Parse(t.ctx.font).fontStyle),a=this.getText();i.isRTL&&(a=a.split("").reverse().join(""));for(var r=t.ToNumberArray(this.parent.attribute("dx").value),o=0;o<a.length;o++){var h=this.getGlyph(i,a,o),l=n/i.fontFace.unitsPerEm;e.translate(this.x,this.y),e.scale(l,-l);var u=e.lineWidth;e.lineWidth=e.lineWidth*i.fontFace.unitsPerEm/n,"italic"==s&&e.transform(1,0,.4,1,0,0),h.render(e),"italic"==s&&e.transform(1,0,-.4,1,0,0),e.lineWidth=u,e.scale(1/l,-1/l),e.translate(-this.x,-this.y),this.x+=n*(h.horizAdvX||i.horizAdvX)/i.fontFace.unitsPerEm,"undefined"!=typeof r[o]&&!isNaN(r[o])&&(this.x+=r[o])}}else""!=e.strokeStyle&&e.strokeText(t.compressSpaces(this.getText()),this.x,this.y),""!=e.fillStyle&&e.fillText(t.compressSpaces(this.getText()),this.x,this.y)},this.getText=function(){},this.measureText=function(e){var i=this.parent.style("font-family").Definition.getDefinition();if(null!=i){var e=this.parent.style("font-size").numValueOrDefault(t.Font.Parse(t.ctx.font).fontSize),n=0,s=this.getText();i.isRTL&&(s=s.split("").reverse().join(""));for(var a=t.ToNumberArray(this.parent.attribute("dx").value),r=0;r<s.length;r++){var o=this.getGlyph(i,s,r);n+=(o.horizAdvX||i.horizAdvX)*e/i.fontFace.unitsPerEm,"undefined"!=typeof a[r]&&!isNaN(a[r])&&(n+=a[r])}return n}return i=t.compressSpaces(this.getText()),e.measureText?(e.save(),this.setContext(e),i=e.measureText(i).width,e.restore(),i):10*i.length}},t.Element.TextElementBase.prototype=new t.Element.RenderedElementBase,t.Element.tspan=function(e){this.base=t.Element.TextElementBase,this.base(e),this.text=3==e.nodeType?e.nodeValue:e.childNodes.length>0?e.childNodes[0].nodeValue:e.text,this.getText=function(){return this.text}},t.Element.tspan.prototype=new t.Element.TextElementBase,t.Element.tref=function(e){this.base=t.Element.TextElementBase,this.base(e),this.getText=function(){var t=this.attribute("xlink:href").Definition.getDefinition();if(null!=t)return t.children[0].getText()}},t.Element.tref.prototype=new t.Element.TextElementBase,t.Element.a=function(e){this.base=t.Element.TextElementBase,this.base(e),this.hasText=!0;for(var i=0;i<e.childNodes.length;i++)3!=e.childNodes[i].nodeType&&(this.hasText=!1);this.text=this.hasText?e.childNodes[0].nodeValue:"",this.getText=function(){return this.text},this.baseRenderChildren=this.renderChildren,this.renderChildren=function(e){if(this.hasText){this.baseRenderChildren(e);var i=new t.Property("fontSize",t.Font.Parse(t.ctx.font).fontSize);t.Mouse.checkBoundingBox(this,new t.BoundingBox(this.x,this.y-i.Length.toPixels("y"),this.x+this.measureText(e),this.y))}else i=new t.Element.g,i.children=this.children,i.parent=this,i.render(e)},this.onclick=function(){window.open(this.attribute("xlink:href").value)},this.onmousemove=function(){t.ctx.canvas.style.cursor="pointer"}},t.Element.a.prototype=new t.Element.TextElementBase,t.Element.image=function(e){this.base=t.Element.RenderedElementBase,this.base(e),t.Images.push(this),this.img=document.createElement("img"),this.loaded=!1;var i=this;this.img.onload=function(){i.loaded=!0},this.img.src=this.attribute("xlink:href").value,this.renderChildren=function(e){var i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=this.attribute("width").Length.toPixels("x"),a=this.attribute("height").Length.toPixels("y");0==s||0==a||(e.save(),e.translate(i,n),t.AspectRatio(e,this.attribute("preserveAspectRatio").value,s,this.img.width,a,this.img.height,0,0),e.drawImage(this.img,0,0),e.restore())}},t.Element.image.prototype=new t.Element.RenderedElementBase,t.Element.g=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.getBoundingBox=function(){for(var e=new t.BoundingBox,i=0;i<this.children.length;i++)e.addBoundingBox(this.children[i].getBoundingBox());return e}},t.Element.g.prototype=new t.Element.RenderedElementBase,t.Element.symbol=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.baseSetContext=this.setContext,this.setContext=function(e){if(this.baseSetContext(e),this.attribute("viewBox").hasValue()){var i=t.ToNumberArray(this.attribute("viewBox").value),n=i[0],s=i[1];width=i[2],height=i[3],t.AspectRatio(e,this.attribute("preserveAspectRatio").value,this.attribute("width").Length.toPixels("x"),width,this.attribute("height").Length.toPixels("y"),height,n,s),t.ViewPort.SetCurrent(i[2],i[3])}}},t.Element.symbol.prototype=new t.Element.RenderedElementBase,t.Element.style=function(e){this.base=t.Element.ElementBase,this.base(e);for(var e=e.childNodes[0].nodeValue+(e.childNodes.length>1?e.childNodes[1].nodeValue:""),e=e.replace(/(\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,""),e=t.compressSpaces(e),e=e.split("}"),i=0;i<e.length;i++)if(""!=t.trim(e[i]))for(var n=e[i].split("{"),s=n[0].split(","),n=n[1].split(";"),a=0;a<s.length;a++){var r=t.trim(s[a]);if(""!=r){for(var o={},h=0;h<n.length;h++){var l=n[h].indexOf(":"),u=n[h].substr(0,l),l=n[h].substr(l+1,n[h].length-l);null!=u&&null!=l&&(o[t.trim(u)]=new t.Property(t.trim(u),t.trim(l)))}if(t.Styles[r]=o,"@font-face"==r)for(r=o["font-family"].value.replace(/"/g,""),o=o.src.value.split(","),h=0;h<o.length;h++)if(o[h].indexOf('format("svg")')>0)for(u=o[h].indexOf("url"),l=o[h].indexOf(")",u),u=o[h].substr(u+5,l-u-6),u=t.parseXml(t.ajax(u)).getElementsByTagName("font"),l=0;l<u.length;l++){var f=t.CreateElement(u[l]);t.Definitions[r]=f}}}},t.Element.style.prototype=new t.Element.ElementBase,t.Element.use=function(e){this.base=t.Element.RenderedElementBase,this.base(e),this.baseSetContext=this.setContext,this.setContext=function(t){this.baseSetContext(t),this.attribute("x").hasValue()&&t.translate(this.attribute("x").Length.toPixels("x"),0),this.attribute("y").hasValue()&&t.translate(0,this.attribute("y").Length.toPixels("y"))},this.getDefinition=function(){var t=this.attribute("xlink:href").Definition.getDefinition();return this.attribute("width").hasValue()&&(t.attribute("width",!0).value=this.attribute("width").value),this.attribute("height").hasValue()&&(t.attribute("height",!0).value=this.attribute("height").value),t},this.path=function(t){var e=this.getDefinition();null!=e&&e.path(t)},this.renderChildren=function(t){var e=this.getDefinition();null!=e&&e.render(t)}},t.Element.use.prototype=new t.Element.RenderedElementBase,t.Element.mask=function(e){this.base=t.Element.ElementBase,this.base(e),this.apply=function(t,e){var i=this.attribute("x").Length.toPixels("x"),n=this.attribute("y").Length.toPixels("y"),s=this.attribute("width").Length.toPixels("x"),a=this.attribute("height").Length.toPixels("y"),r=e.attribute("mask").value;e.attribute("mask").value="";var o=document.createElement("canvas");o.width=i+s,o.height=n+a;var h=o.getContext("2d");this.renderChildren(h);var l=document.createElement("canvas");l.width=i+s,l.height=n+a;var u=l.getContext("2d");e.render(u),u.globalCompositeOperation="destination-in",u.fillStyle=h.createPattern(o,"no-repeat"),u.fillRect(0,0,i+s,n+a),t.fillStyle=u.createPattern(l,"no-repeat"),t.fillRect(0,0,i+s,n+a),e.attribute("mask").value=r},this.render=function(){}},t.Element.mask.prototype=new t.Element.ElementBase,t.Element.clipPath=function(e){this.base=t.Element.ElementBase,this.base(e),this.apply=function(t){for(var e=0;e<this.children.length;e++)this.children[e].path&&(this.children[e].path(t),t.clip())},this.render=function(){}},t.Element.clipPath.prototype=new t.Element.ElementBase,t.Element.filter=function(e){this.base=t.Element.ElementBase,this.base(e),this.apply=function(t,e){var i=e.getBoundingBox(),n=this.attribute("x").Length.toPixels("x"),s=this.attribute("y").Length.toPixels("y");0!=n&&0!=s||(n=i.x1,s=i.y1);var a=this.attribute("width").Length.toPixels("x"),r=this.attribute("height").Length.toPixels("y");0!=a&&0!=r||(a=i.width(),r=i.height()),i=e.style("filter").value,e.style("filter").value="";var o=.2*a,h=.2*r,l=document.createElement("canvas");l.width=a+2*o,l.height=r+2*h;var u=l.getContext("2d");u.translate(-n+o,-s+h),e.render(u);for(var f=0;f<this.children.length;f++)this.children[f].apply(u,0,0,a+2*o,r+2*h);t.drawImage(l,0,0,a+2*o,r+2*h,n-o,s-h,a+2*o,r+2*h),e.style("filter",!0).value=i},this.render=function(){}},t.Element.filter.prototype=new t.Element.ElementBase,t.Element.feGaussianBlur=function(e){function i(t,e,i,n,s){for(var a=0;a<s;a++)for(var r=0;r<n;r++)for(var o=t[a*n*4+4*r+3]/255,h=0;h<4;h++){for(var l=i[0]*(0==o?255:t[a*n*4+4*r+h])*(0==o||3==h?1:o),u=1;u<i.length;u++){var f,d=Math.max(r-u,0),c=t[a*n*4+4*d+3]/255,d=Math.min(r+u,n-1),d=t[a*n*4+4*d+3]/255,m=i[u];0==c?f=255:(f=Math.max(r-u,0),f=t[a*n*4+4*f+h]),c=f*(0==c||3==h?1:c),0==d?f=255:(f=Math.min(r+u,n-1),f=t[a*n*4+4*f+h]),l+=m*(c+f*(0==d||3==h?1:d))}e[r*s*4+4*a+h]=l}}this.base=t.Element.ElementBase,this.base(e),this.apply=function(t,e,n,s,a){var n=this.attribute("stdDeviation").numValue(),e=t.getImageData(0,0,s,a),n=Math.max(n,.01),r=Math.ceil(4*n)+1;mask=[];for(var o=0;o<r;o++)mask[o]=Math.exp(-.5*(o/n)*(o/n));for(n=mask,r=0,o=1;o<n.length;o++)r+=Math.abs(n[o]);for(r=2*r+Math.abs(n[0]),o=0;o<n.length;o++)n[o]/=r;tmp=[],i(e.data,tmp,n,s,a),i(tmp,e.data,n,a,s),t.clearRect(0,0,s,a),t.putImageData(e,0,0)}},t.Element.filter.prototype=new t.Element.feGaussianBlur,t.Element.title=function(){},t.Element.title.prototype=new t.Element.ElementBase,t.Element.desc=function(){},t.Element.desc.prototype=new t.Element.ElementBase,t.Element.MISSING=function(t){console.log("ERROR: Element '"+t.nodeName+"' not yet implemented.")},t.Element.MISSING.prototype=new t.Element.ElementBase,t.CreateElement=function(e){var i=e.nodeName.replace(/^[^:]+:/,""),i=i.replace(/\-/g,""),n=null,n="undefined"!=typeof t.Element[i]?new t.Element[i](e):new t.Element.MISSING(e);return n.type=e.nodeName,n},t.load=function(e,i){t.loadXml(e,t.ajax(i))},t.loadXml=function(e,i){t.loadXmlDoc(e,t.parseXml(i))},t.loadXmlDoc=function(e,i){t.init(e);var n=function(t){for(var i=e.canvas;i;)t.x-=i.offsetLeft,t.y-=i.offsetTop,i=i.offsetParent;return window.scrollX&&(t.x+=window.scrollX),window.scrollY&&(t.y+=window.scrollY),t};1!=t.opts.ignoreMouse&&(e.canvas.onclick=function(e){e=n(new t.Point(null!=e?e.clientX:event.clientX,null!=e?e.clientY:event.clientY)),t.Mouse.onclick(e.x,e.y)},e.canvas.onmousemove=function(e){e=n(new t.Point(null!=e?e.clientX:event.clientX,null!=e?e.clientY:event.clientY)),t.Mouse.onmousemove(e.x,e.y)});var s=t.CreateElement(i.documentElement),a=s.root=!0,r=function(){t.ViewPort.Clear(),e.canvas.parentNode&&t.ViewPort.SetCurrent(e.canvas.parentNode.clientWidth,e.canvas.parentNode.clientHeight),1!=t.opts.ignoreDimensions&&(s.style("width").hasValue()&&(e.canvas.width=s.style("width").Length.toPixels("x"),e.canvas.style.width=e.canvas.width+"px"),s.style("height").hasValue()&&(e.canvas.height=s.style("height").Length.toPixels("y"),e.canvas.style.height=e.canvas.height+"px"));var i=e.canvas.clientWidth||e.canvas.width,n=e.canvas.clientHeight||e.canvas.height;if(t.ViewPort.SetCurrent(i,n),null!=t.opts&&null!=t.opts.offsetX&&(s.attribute("x",!0).value=t.opts.offsetX),null!=t.opts&&null!=t.opts.offsetY&&(s.attribute("y",!0).value=t.opts.offsetY),null!=t.opts&&null!=t.opts.scaleWidth&&null!=t.opts.scaleHeight){var r=1,o=1;s.attribute("width").hasValue()&&(r=s.attribute("width").Length.toPixels("x")/t.opts.scaleWidth),s.attribute("height").hasValue()&&(o=s.attribute("height").Length.toPixels("y")/t.opts.scaleHeight),s.attribute("width",!0).value=t.opts.scaleWidth,s.attribute("height",!0).value=t.opts.scaleHeight,s.attribute("viewBox",!0).value="0 0 "+i*r+" "+n*o,s.attribute("preserveAspectRatio",!0).value="none"}1!=t.opts.ignoreClear&&e.clearRect(0,0,i,n),s.render(e),a&&(a=!1,null!=t.opts&&"function"==typeof t.opts.renderCallback&&t.opts.renderCallback())},o=!0;t.ImagesLoaded()&&(o=!1,r()),t.intervalID=setInterval(function(){var e=!1;if(o&&t.ImagesLoaded()&&(o=!1,e=!0),1!=t.opts.ignoreMouse&&(e|=t.Mouse.hasEvents()),1!=t.opts.ignoreAnimation)for(var i=0;i<t.Animations.length;i++)e|=t.Animations[i].update(1e3/t.FRAMERATE);null!=t.opts&&"function"==typeof t.opts.forceRedraw&&1==t.opts.forceRedraw()&&(e=!0),e&&(r(),t.Mouse.runEvents())},1e3/t.FRAMERATE)},t.stop=function(){t.intervalID&&clearInterval(t.intervalID)},t.Mouse=new function(){this.events=[],this.hasEvents=function(){return 0!=this.events.length},this.onclick=function(t,e){this.events.push({type:"onclick",x:t,y:e,run:function(t){t.onclick&&t.onclick()}})},this.onmousemove=function(t,e){this.events.push({type:"onmousemove",x:t,y:e,run:function(t){t.onmousemove&&t.onmousemove()}})},this.eventElements=[],this.checkPath=function(t,e){for(var i=0;i<this.events.length;i++){var n=this.events[i];e.isPointInPath&&e.isPointInPath(n.x,n.y)&&(this.eventElements[i]=t)}},this.checkBoundingBox=function(t,e){for(var i=0;i<this.events.length;i++){var n=this.events[i];e.isPointInBox(n.x,n.y)&&(this.eventElements[i]=t)}},this.runEvents=function(){t.ctx.canvas.style.cursor="";for(var e=0;e<this.events.length;e++)for(var i=this.events[e],n=this.eventElements[e];n;)i.run(n),n=n.parent;this.events=[],this.eventElements=[]}},t}this.canvg=function(e,i,n){if(null==e&&null==i&&null==n)for(var i=document.getElementsByTagName("svg"),s=0;s<i.length;s++){e=i[s],n=document.createElement("canvas"),n.width=e.clientWidth,n.height=e.clientHeight,e.parentNode.insertBefore(n,e),e.parentNode.removeChild(e);var a=document.createElement("div");a.appendChild(e),canvg(n,a.innerHTML)}else n=n||{},"string"==typeof e&&(e=document.getElementById(e)),null==e.svg?(s=t(),e.svg=s):(s=e.svg,s.stop()),s.opts=n,e=e.getContext("2d"),"undefined"!=typeof i.documentElement?s.loadXmlDoc(e,i):"<"==i.substr(0,1)?s.loadXml(e,i):s.load(e,i)}}(),CanvasRenderingContext2D&&(CanvasRenderingContext2D.prototype.drawSvg=function(t,e,i,n,s){canvg(this.canvas,t,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:e,offsetY:i,scaleWidth:n,scaleHeight:s})}),function(t){var e=t.css,i=t.CanVGRenderer,n=t.SVGRenderer,s=t.extend,a=t.merge,r=t.addEvent,o=t.createElement,h=t.discardElement;s(i.prototype,n.prototype),s(i.prototype,{create:function(t,e,i,n){this.setContainer(e,i,n),this.configure(t)},setContainer:function(t,e,i){var n=t.style,s=t.parentNode,a=n.left,n=n.top,r=t.offsetWidth,h=t.offsetHeight,l={visibility:"hidden",position:"absolute"};this.init.apply(this,[t,e,i]),this.canvas=o("canvas",{width:r,height:h},{position:"relative",left:a,top:n},t),this.ttLine=o("div",null,l,s),this.ttDiv=o("div",null,l,s),this.ttTimer=void 0,this.hiddenSvg=t=o("div",{width:r,height:h},{visibility:"hidden",left:a,top:n},s),t.appendChild(this.box)},configure:function(t){var i=this,n=t.options.tooltip,s=n.borderWidth,o=i.ttDiv,h=n.style,l=i.ttLine,u=parseInt(h.padding,10),h=a(h,{padding:u+"px","background-color":n.backgroundColor,"border-style":"solid","border-width":s+"px","border-radius":n.borderRadius+"px"});n.shadow&&(h=a(h,{"box-shadow":"1px 1px 3px gray","-webkit-box-shadow":"1px 1px 3px gray"})),e(o,h),e(l,{"border-left":"1px solid darkgray"}),r(t,"tooltipRefresh",function(n){var s,a=t.container,r=a.offsetLeft,a=a.offsetTop;o.innerHTML=n.text,s=t.tooltip.getPosition(o.offsetWidth,o.offsetHeight,{plotX:n.x,plotY:n.y}),e(o,{visibility:"visible",left:s.x+"px",top:s.y+"px","border-color":n.borderColor}),e(l,{visibility:"visible",left:r+n.x+"px",top:a+t.plotTop+"px",height:t.plotHeight+"px"}),void 0!==i.ttTimer&&clearTimeout(i.ttTimer),i.ttTimer=setTimeout(function(){e(o,{visibility:"hidden"}),e(l,{visibility:"hidden"})},3e3)})},destroy:function(){return h(this.canvas),void 0!==this.ttTimer&&clearTimeout(this.ttTimer),h(this.ttLine),h(this.ttDiv),h(this.hiddenSvg),n.prototype.destroy.apply(this)},color:function(t,e,i){return t&&t.linearGradient&&(t=t.stops[t.stops.length-1][1]),n.prototype.color.call(this,t,e,i)},draw:function(){window.canvg(this.canvas,this.hiddenSvg.innerHTML)}})}(Highcharts);