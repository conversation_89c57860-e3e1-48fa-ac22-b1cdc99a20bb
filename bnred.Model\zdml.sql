-- #############################################################################
-- # Enum Dictionary Table Definition and Data Inserts
-- #############################################################################

PRINT 'Starting creation and population of WMS_EnumDictionary...';
GO

-- Drop table if it exists (optional, for re-running the script)
IF OBJECT_ID('dbo.WMS_EnumDictionary', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_EnumDictionary;
GO

CREATE TABLE WMS_EnumDictionary (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    EnumTypeName NVARCHAR(255) NOT NULL,    -- C# Enum Type Name (e.g., "AlertType", "MaterialStatus")
    EnumItemValue INT NOT NULL,             -- Integer value of the enum item
    EnumValueName NVARCHAR(255) NOT NULL,    -- C# Enum Field Name (e.g., "Inventory", "Active")
    EnumItemDescription NVARCHAR(1000) NULL, -- Description from [Display(Name="...")] or /// summary
    TenantId UNIQUEIDENTIFIER NULL,         -- Tenant ID, if applicable
    CreateTime DATETIME2 DEFAULT GETDATE(),  -- Record creation time
    CreateBy UNIQUEIDENTIFIER NULL,         -- ID of the user who created the record
    UpdateTime DATETIME2 NULL,              -- Record last update time
    UpdateBy UNIQUEIDENTIFIER NULL,         -- ID of the user who last updated the record
    IsSystem BIT DEFAULT 1                  -- Indicates if the enum is a system enum
);
GO

-- Add MS_Description for WMS_EnumDictionary table and its columns
EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', @value=N'存储系统中所有C#枚举类型的定义、值和描述，方便查询和维护。', 
    @level0type=N'SCHEMA',@level0name=N'dbo', 
    @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'C#中枚举类型的名称 (例如: AlertType, MaterialStatus)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumTypeName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'枚举成员对应的整数值', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumItemValue';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'C#中枚举的成员名称 (例如: Inventory, Active)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumValueName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'枚举成员的描述，通常来自[Display(Name="...")]特性或XML注释', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumItemDescription';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID (如果适用)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'TenantId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'记录创建时间', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建记录的用户ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'记录最后更新时间', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后更新记录的用户ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否为系统枚举', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'IsSystem';
GO

CREATE UNIQUE INDEX UX_EnumDictionary_TypeName_ValueName_ItemValue ON WMS_EnumDictionary (EnumTypeName, EnumValueName, EnumItemValue);
CREATE INDEX IX_EnumDictionary_TypeName_ItemValue ON WMS_EnumDictionary (EnumTypeName, EnumItemValue);
GO

PRINT 'WMS_EnumDictionary table definition created successfully.';
GO

-- #############################################################################
-- # Data Inserts from WMS_EnumDictionary_Data_Generated.sql
-- #############################################################################
-- Inserting enums from bnred.Model.WMS.Enums.AlertEnums

-- Enum: AlertType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('AlertType', 0, 'Inventory', '库存预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 1, 'Expiry', '效期预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 2, 'Equipment', '设备预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 3, 'Capacity', '容量预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 4, 'Task', '任务预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertType', 5, 'Quality', '质量预警', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: AlertLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('AlertLevel', 0, 'Info', '提示级别', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertLevel', 1, 'Warning', '警告级别', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertLevel', 2, 'Critical', '严重级别', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AlertLevel', 3, 'Emergency', '紧急级别', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.AnalysisEnums

-- Enum: CostAnomalyLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnomalyLevel', 0, 'Normal', '正常', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 1, 'Minor', '轻微', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 2, 'Moderate', '中等', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 3, 'Severe', '严重', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyLevel', 4, 'Critical', '极端', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnalysisType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnalysisType', 0, 'Material', '物料成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 1, 'Inventory', '库存成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 2, 'Warehousing', '仓储成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 3, 'Operation', '运营成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnalysisType', 4, 'Comprehensive', '综合成本分析', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostTrend
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostTrend', 0, 'Stable', '稳定', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 1, 'SlowlyRising', '缓慢上升', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 2, 'RapidlyRising', '迅速上升', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 3, 'SlowlyDecreasing', '缓慢下降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 4, 'RapidlyDecreasing', '迅速下降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostTrend', 5, 'Fluctuating', '波动', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.BarcodeEnums

-- Enum: BarcodeType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BarcodeType', 0, 'Material', '物料条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 1, 'Location', '库位条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 2, 'Container', '容器条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 3, 'Pallet', '托盘条码', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeType', 4, 'Package', '包装条码', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: BarcodeStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BarcodeStatus', 0, 'Generated', '已生成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeStatus', 1, 'Used', '已使用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeStatus', 2, 'Voided', '已作废', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BarcodeStatus', 3, 'Expired', '已失效', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.BatchEnums

-- Enum: BatchQualityStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BatchQualityStatus', 0, 'Pending', 'Pending', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 1, 'Qualified', 'Qualified', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 2, 'Unqualified', 'Unqualified', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 3, 'ReInspection', 'ReInspection', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchQualityStatus', 4, 'Scrapped', 'Scrapped', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: BatchStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('BatchStatus', 0, 'PendingIn', 'PendingIn', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 1, 'InStock', 'InStock', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 2, 'PendingOut', 'PendingOut', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 3, 'OutStock', 'OutStock', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 4, 'Frozen', 'Frozen', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('BatchStatus', 5, 'Scrapped', 'Scrapped', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.ContainerEnums

-- Enum: ContainerType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ContainerType', 0, 'Box', '周转箱', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 1, 'Pallet', '托盘', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 2, 'Rack', '料架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 3, 'Cage', '笼车', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerType', 4, 'Shelf', '货架', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ContainerStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ContainerStatus', 0, 'Empty', '空闲中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerStatus', 1, 'InUse', '使用中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerStatus', 2, 'Maintenance', '维修中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerStatus', 3, 'Scrapped', '已报废', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ContainerSpecification
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ContainerSpecification', 0, 'Small', '小型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 1, 'Medium', '中型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 2, 'Large', '大型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 3, 'ExtraLarge', '特大型', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ContainerSpecification', 4, 'Custom', '定制型', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.Enums.CostAnalysisType

-- Enum: CostAnalysisType (from bnred.Model.Enums)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LegacyCostAnalysisType', 1, 'Daily', '按日分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 2, 'Weekly', '按周分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 3, 'Monthly', '按月分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 4, 'Quarterly', '按季度分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 5, 'Yearly', '按年分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 6, 'ByCategory', '按类别分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 7, 'ByRegion', '按区域分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 8, 'BySupplier', '按供应商分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 9, 'ByCostCenter', '按成本中心分析', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnalysisType', 10, 'ByBatch', '按批次分析', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Analysis.CostAnomalyEnums

-- Enum: CostAnomalyType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnomalyType', 0, 'SuddenIncrease', '成本突增', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 1, 'SuddenDecrease', '成本突降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 2, 'ContinuousIncrease', '持续上涨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 3, 'ContinuousDecrease', '持续下跌', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 4, 'AbnormalFluctuation', '异常波动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 5, 'OverBudget', '超出预算', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyType', 99, 'Other', '其他异常', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyLevel (from bnred.Model.Analysis)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LegacyCostAnomalyLevel', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnomalyLevel', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyCostAnomalyLevel', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostAnomalyStatus', 0, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyStatus', 1, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyStatus', 2, 'Processed', '已处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostAnomalyStatus', 3, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.CostAnomalyLevel

-- Enum: CostAnomalyLevel (from bnred.Model root)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RootCostAnomalyLevel', 0, 'Normal', '正常', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyLevel', 1, 'Warning', '警告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyLevel', 2, 'Critical', '严重', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.CostAnomalyStatus

-- Enum: CostAnomalyStatus (from bnred.Model root)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RootCostAnomalyStatus', 1, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 2, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 3, 'Processed', '已处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 4, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RootCostAnomalyStatus', 5, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Enums.CostAnomalyType

-- Enum: CostAnomalyType (from bnred.Model.Enums)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EnumsCostAnomalyType', 1, 'CostSpike', '成本突增', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 2, 'CostDrop', '成本突降', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 3, 'PersistentlyHigh', '持续偏高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 4, 'PersistentlyLow', '持续偏低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 5, 'AbnormalFluctuation', '异常波动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 6, 'CalculationError', '计算错误', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 7, 'DataMissing', '数据缺失', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 8, 'ExceedLimit', '超出限制', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 9, 'Inconsistency', '不一致', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyType', 10, 'Unclassified', '未分类异常', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyLevel (from bnred.Model.Enums.CostAnomalyType.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EnumsCostAnomalyLevel', 1, 'Low', '低风险', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyLevel', 2, 'Medium', '中等风险', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyLevel', 3, 'High', '高风险', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyLevel', 4, 'Critical', '严重风险', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostAnomalyStatus (from bnred.Model.Enums.CostAnomalyType.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EnumsCostAnomalyStatus', 1, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 2, 'InProgress', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 3, 'Resolved', '已解决', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 4, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 5, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EnumsCostAnomalyStatus', 6, 'NeedsFollowUp', '需要跟进', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.CostEnums

-- Enum: CostType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostType', 0, 'Purchase', '采购成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 1, 'Storage', '仓储成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 2, 'Transportation', '运输成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 3, 'Labor', '人工成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 4, 'Management', '管理成本', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostType', 5, 'Equipment', '设备成本', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CostMethod
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostMethod', 0, 'MovingAverage', '移动平均法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 1, 'FIFO', '先进先出法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 2, 'LIFO', '后进先出法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 3, 'WeightedAverage', '加权平均法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostMethod', 4, 'StandardCost', '标准成本法', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.CultureEnums

-- Enum: SupportedCulture
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SupportedCulture', 0, 'zh_CN', '简体中文', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 1, 'en_US', 'English', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 2, 'zh_TW', '繁體中文', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 3, 'ja_JP', '日本語', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 4, 'ko_KR', '한국어', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupportedCulture', 5, 'fr_FR', 'Français', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.DefaultSettings

-- Enum: DefaultSettings
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DefaultSettings', 0, 'System', 'System', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DefaultSettings', 1, 'Custom', 'Custom', NULL, GETDATE(), NULL, NULL, NULL, 1); 


-- Inserting enums from bnred.Model.DocumentType

-- Enum: LegacyDocumentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LegacyDocumentType', 1, 'PurchaseIn', '采购入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 2, 'SalesOut', '销售出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 3, 'StockAdjustment', '库存调整单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 4, 'StockTaking', '盘点单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 5, 'OtherIn', '其他入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 6, 'OtherOut', '其他出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 7, 'ProductionIn', '生产入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 8, 'ProductionOut', '生产领料', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 9, 'ReturnIn', '退货入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LegacyDocumentType', 10, 'ReturnOut', '退货出库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.DocumentTypeEnum
-- Enum: DocumentTypeEnum
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DocumentTypeEnum', 0, 'InboundOrder', '入库单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 1, 'OutboundOrder', '出库单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 2, 'TransferOrder', '移库单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 3, 'AllocationOrder', '调拨单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 4, 'StockTakingOrder', '盘点单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 5, 'DamageOrder', '报损单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 6, 'OverflowOrder', '报溢单', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentTypeEnum', 99, 'Other', '其他文档', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.DocumentTypeEnums
-- Enum: DocumentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DocumentType', 0, 'PurchaseInbound', '采购入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 1, 'ProductionInbound', '生产入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 2, 'ReturnInbound', '退货入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 3, 'TransferInbound', '调拨入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 4, 'CountingInbound', '盘盈入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 5, 'OtherInbound', '其他入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 10, 'SalesOutbound', '销售出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 11, 'ProductionOutbound', '生产领料出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 12, 'ReturnOutbound', '退货出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 13, 'TransferOutbound', '调拨出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 14, 'CountingOutbound', '盘亏出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 15, 'OtherOutbound', '其他出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 20, 'Adjustment', '库存调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 21, 'Movement', '库存移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 22, 'StockTaking', '库存盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 23, 'QualityCheck', '质量检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DocumentType', 99, 'Other', '其他单据', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.DurationUnit

-- Enum: DurationUnit
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DurationUnit', 0, 'Seconds', '秒', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 1, 'Minutes', '分钟', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 2, 'Hours', '小时', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 3, 'Days', '天', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 4, 'Weeks', '周', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 5, 'Months', '月', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DurationUnit', 6, 'Years', '年', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.EquipmentEnums

-- Enum: EquipmentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EquipmentType', 0, 'Forklift', '叉车', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 1, 'Trolley', '手推车', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 2, 'Conveyor', '输送机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 3, 'Stacker', '堆垛机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 4, 'Lift', '升降机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 5, 'Scanner', '扫码器', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentType', 6, 'Printer', '打印机', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: EquipmentStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('EquipmentStatus', 0, 'Running', '正常运行', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 1, 'Idle', '空闲待命', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 2, 'Maintenance', '维护保养', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 3, 'Malfunction', '故障停用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('EquipmentStatus', 4, 'Scrapped', '已报废', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: MaintenanceType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MaintenanceType', 0, 'Daily', '日常保养', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 1, 'Periodic', '定期维护', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 2, 'Repair', '故障维修', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 3, 'Annual', '年度大修', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaintenanceType', 4, 'Emergency', '紧急维修', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.GlobalEnums

-- Enum: ActiveStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ActiveStatus', 0, 'Inactive', '未激活', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ActiveStatus', 1, 'Active', '已激活', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ApprovalStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ApprovalStatus', 0, 'Pending', '待审批', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ApprovalStatus', 1, 'Approved', '已批准', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ApprovalStatus', 2, 'Rejected', '已拒绝', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ApprovalStatus', 3, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PriorityLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PriorityLevel', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PriorityLevel', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PriorityLevel', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PriorityLevel', 3, 'Urgent', '紧急', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InboundEnums

-- Enum: InboundStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InboundStatus', 0, 'PendingReceive', '待收货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 1, 'Receiving', '收货中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 2, 'PendingCheck', '待检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 3, 'PendingPutaway', '待上架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 4, 'PuttingAway', '上架中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 5, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundStatus', 6, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InboundType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InboundType', 0, 'Purchase', '采购入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 1, 'Production', '生产入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 2, 'Return', '退货入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 3, 'Transfer', '调拨入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundType', 4, 'StockGain', '盘盈入库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InboundDocumentType (Matches WMS.Enums.DocumentType, but scoped here)
-- This was previously trying to match a non-existent WMS.Enums.DocumentType.
-- Based on current files, it seems like this was intended to be a distinct enum.
-- If it should map to the new global 'DocumentType', that would require different values and names.
-- Keeping it as its own distinct enum type for now.
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InboundDocumentType', 0, 'ASN', 'ASN (Advanced Shipment Notice)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 1, 'PO', 'PO (Purchase Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 2, 'STO', 'STO (Stock Transfer Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 3, 'RMA', 'RMA (Return Material Authorization)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InboundDocumentType', 4, 'WO', 'WO (Work Order)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InventoryEnums

-- Enum: InventoryStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryStatus', 0, 'Available', '可用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 1, 'Hold', '冻结', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 2, 'Damaged', '损坏', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 3, 'Reserved', '预留', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 4, 'InTransit', '在途', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryStatus', 5, 'Expired', '过期', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InventoryAdjustmentType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryAdjustmentType', 0, 'StockIn', '入库调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 1, 'StockOut', '出库调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 2, 'CycleCount', '盘点调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 3, 'Damage', '损坏调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 4, 'Loss', '丢失调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentType', 99, 'Other', '其他调整', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InventoryMovementType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryMovementType', 0, 'Receiving', '收货移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 1, 'Putaway', '上架移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 2, 'Picking', '拣货移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 3, 'Replenishment', '补货移动', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 4, 'Transfer', '库内转移', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryMovementType', 5, 'Adjustment', '调整移动', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InventoryLedgerEnums

-- Enum: LedgerTransactionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LedgerTransactionType', 0, 'Inbound', '入库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 1, 'Outbound', '出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 2, 'Adjustment', '调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 3, 'Transfer', '转移', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LedgerTransactionType', 4, 'Stocktake', '盘点', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.InventoryReportGroupBy
-- Enum: InventoryReportGroupBy
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryReportGroupBy', 1, 'Warehouse', '仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryReportGroupBy', 2, 'Location', '库位', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryReportGroupBy', 3, 'Material', '物料', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryReportGroupBy', 4, 'Batch', '批次', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.KPIEnums
-- Enum: KPIType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('KPIType', 0, 'ReceivingEfficiency', '收货效率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 1, 'PutAwayEfficiency', '上架效率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 2, 'PickingEfficiency', '拣货效率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 3, 'InventoryAccuracy', '库存准确率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 4, 'OrderFulfillment', '订单完成率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 5, 'InventoryTurnover', '库存周转率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 6, 'ErrorRate', '差错率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIType', 7, 'SpaceUtilization', '空间利用率', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: KPIPeriod
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('KPIPeriod', 0, 'Daily', '每日', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIPeriod', 1, 'Weekly', '每周', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIPeriod', 2, 'Monthly', '每月', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIPeriod', 3, 'Quarterly', '每季', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('KPIPeriod', 4, 'Yearly', '每年', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.LedgerEnums
-- Enum: CostCalculationMethod
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CostCalculationMethod', 0, 'AverageCost', '平均成本法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostCalculationMethod', 1, 'FIFO', '先进先出法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostCalculationMethod', 2, 'LIFO', '后进先出法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostCalculationMethod', 3, 'WeightedAverage', '加权平均法', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CostCalculationMethod', 4, 'StandardCost', '标准成本法', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.LabelEnums

-- Enum: LabelType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LabelType', 0, 'Material', '物料标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 1, 'Location', '库位标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 2, 'Container', '容器标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 3, 'Shipping', '发货标签', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelType', 4, 'Receiving', '收货标签', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: LabelFormat
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LabelFormat', 0, 'ZPL', 'ZPL (Zebra Programming Language)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelFormat', 1, 'PDF', 'PDF (Portable Document Format)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelFormat', 2, 'Image', '图片格式 (PNG, JPG)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LabelFormat', 3, 'Text', '纯文本格式', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.LocationEnums

-- Enum: LocationType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LocationType', 0, 'Storage', '存储区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 1, 'Receiving', '收货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 2, 'Shipping', '发货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 3, 'Picking', '拣货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 4, 'Staging', '暂存区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 5, 'QualityCheck', '质检区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 6, 'Defective', '不合格品区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationType', 7, 'Return', '退货区', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: LocationStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('LocationStatus', 0, 'Empty', '空', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationStatus', 1, 'Occupied', '占用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationStatus', 2, 'Reserved', '预留', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('LocationStatus', 3, 'Unavailable', '不可用', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.MaterialEnums

-- Enum: MaterialType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MaterialType', 0, 'RawMaterial', '原材料', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 1, 'FinishedGood', '成品', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 2, 'SemiFinishedGood', '半成品', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 3, 'SparePart', '备件', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 4, 'Consumable', '消耗品', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialType', 5, 'PackagingMaterial', '包装材料', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: MaterialStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MaterialStatus', 0, 'Active', '启用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialStatus', 1, 'Inactive', '停用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialStatus', 2, 'Obsolete', '废弃', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MaterialStatus', 3, 'PendingApproval', '待审核', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: UnitOfMeasure
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('UnitOfMeasure', 0, 'EA', '个 (Each)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 1, 'KG', '千克 (Kilogram)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 2, 'M', '米 (Meter)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 3, 'L', '升 (Liter)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 4, 'BOX', '箱 (Box)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitOfMeasure', 5, 'PALLET', '托盘 (Pallet)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Notification.NotificationEnums

-- Enum: NotificationType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('NotificationType', 0, 'System', '系统通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 1, 'Alert', '预警通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 2, 'TaskAssignment', '任务分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 3, 'ApprovalRequest', '审批请求', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationType', 4, 'Information', '信息通知', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: NotificationStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('NotificationStatus', 0, 'Unread', '未读', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationStatus', 1, 'Read', '已读', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationStatus', 2, 'Archived', '已归档', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationStatus', 3, 'Deleted', '已删除', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: NotificationChannel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('NotificationChannel', 0, 'InApp', '应用内通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationChannel', 1, 'Email', '邮件通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationChannel', 2, 'SMS', '短信通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('NotificationChannel', 3, 'Push', '推送通知', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.OutboundEnums

-- Enum: OutboundStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('OutboundStatus', 0, 'PendingAllocation', '待配货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 1, 'Allocated', '已配货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 2, 'PendingPicking', '待拣货', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 3, 'Picking', '拣货中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 4, 'PendingPacking', '待包装', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 5, 'Packing', '包装中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 6, 'PendingShipping', '待发运', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 7, 'Shipped', '已发运', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 8, 'Delivered', '已送达', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundStatus', 9, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: OutboundType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('OutboundType', 0, 'Sales', '销售出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 1, 'Production', '生产领料出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 2, 'ReturnToSupplier', '退供出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 3, 'Transfer', '调拨出库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundType', 4, 'StockLoss', '盘亏出库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: OutboundDocumentType (Matches WMS.Enums.DocumentType, but scoped here)
-- This was previously trying to match a non-existent WMS.Enums.DocumentType.
-- Based on current files, it seems like this was intended to be a distinct enum.
-- If it should map to the new global 'DocumentType', that would require different values and names.
-- Keeping it as its own distinct enum type for now.
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('OutboundDocumentType', 0, 'SO', 'SO (Sales Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundDocumentType', 1, 'STO', 'STO (Stock Transfer Order)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundDocumentType', 2, 'RTV', 'RTV (Return to Vendor)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('OutboundDocumentType', 3, 'WO', 'WO (Work Order/Material Issue)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.PackageEnums
-- Enum: PackageType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PackageType', 0, 'Box', '箱', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageType', 1, 'Pallet', '托盘', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageType', 2, 'Bag', '袋', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageType', 3, 'Roll', '卷', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageType', 4, 'Other', '其他', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PackageStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PackageStatus', 0, 'Open', '开放', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageStatus', 1, 'Closed', '关闭', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageStatus', 2, 'Shipped', '已发运', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PackageStatus', 3, 'Received', '已接收', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Enums.PermissionEnums

-- Enum: PermissionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PermissionType', 1, 'Menu', '菜单权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 2, 'Button', '按钮权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 3, 'Api', 'API权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 4, 'Data', '数据权限', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionType', 5, 'Field', '字段权限', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PermissionScope
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PermissionScope', 1, 'Global', '全局范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 2, 'Role', '角色范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 3, 'User', '用户范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 4, 'Department', '部门范围', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PermissionScope', 5, 'Custom', '自定义范围', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.PlanEnums

-- Enum: PlanType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PlanType', 0, 'Inbound', '入库计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 1, 'Outbound', '出库计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 2, 'Production', '生产计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 3, 'Replenishment', '补货计划', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanType', 4, 'CycleCount', '盘点计划', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PlanStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PlanStatus', 0, 'Draft', '草稿', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 1, 'PendingApproval', '待审批', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 2, 'Approved', '已审批', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 3, 'InProgress', '执行中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 4, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PlanStatus', 6, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.PrintEnums
-- Enum: PrintTaskStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PrintTaskStatus', 0, 'Pending', '待打印', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrintTaskStatus', 1, 'Printing', '打印中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrintTaskStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrintTaskStatus', 3, 'Failed', '打印失败', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrintTaskStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PrintType (Distinct from PrinterType)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PrintType', 0, 'Label', '标签打印', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrintType', 1, 'Report', '报表打印', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrintType', 2, 'Document', '单据打印', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.PrinterEnums

-- Enum: PrinterType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PrinterType', 0, 'Label', '标签打印机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterType', 1, 'Document', '文档打印机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterType', 2, 'Network', '网络打印机', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterType', 3, 'Local', '本地打印机', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: PrinterStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('PrinterStatus', 0, 'Online', '在线', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterStatus', 1, 'Offline', '离线', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterStatus', 2, 'Error', '错误', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('PrinterStatus', 3, 'Busy', '繁忙', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.QualityEnums

-- Enum: QualityInspectionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityInspectionType', 0, 'Receiving', '收货检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 1, 'InProcess', '过程检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 2, 'Shipping', '发货检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 3, 'Storage', '存储检验', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityInspectionType', 4, 'Return', '退货检验', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: QualityResult
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityResult', 0, 'Pass', '合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityResult', 1, 'Fail', '不合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityResult', 2, 'ConditionalPass', '让步接收', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: QualityStatus (Distinct from QualityInspectionStatus to avoid conflict with QualityStatusEnums.cs, if any)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityStatus', 0, 'Pending', '待检', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatus', 1, 'Qualified', '合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatus', 2, 'Unqualified', '不合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatus', 3, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: DefectLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DefectLevel', 0, 'Critical', '致命缺陷', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DefectLevel', 1, 'Major', '主要缺陷', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DefectLevel', 2, 'Minor', '次要缺陷', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.ReportEnums

-- Enum: ReportType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ReportType', 0, 'InventoryStatus', '库存状态报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 1, 'InboundPerformance', '入库绩效报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 2, 'OutboundPerformance', '出库绩效报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 3, 'StorageUtilization', '库容利用率报告', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportType', 4, 'CycleCountAccuracy', '盘点准确率报告', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ReportFormat
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ReportFormat', 0, 'PDF', 'PDF格式', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFormat', 1, 'Excel', 'Excel格式', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFormat', 2, 'CSV', 'CSV格式', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFormat', 3, 'HTML', 'HTML格式', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ReportFrequency
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ReportFrequency', 0, 'Daily', '每日', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 1, 'Weekly', '每周', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 2, 'Monthly', '每月', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 3, 'Quarterly', '每季度', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 4, 'Annually', '每年', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ReportFrequency', 5, 'OnDemand', '按需', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.Enums.RoleType

-- Enum: RoleType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RoleType', 1, 'SystemAdmin', '系统管理员', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 2, 'TenantAdmin', '租户管理员', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 3, 'WarehouseManager', '仓库经理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 4, 'Operator', '操作员', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RoleType', 5, 'Viewer', '查看员', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.RuleEnums

-- Enum: RuleType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RuleType', 0, 'Putaway', '上架规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 1, 'Picking', '拣货规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 2, 'Replenishment', '补货规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 3, 'Allocation', '分配规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleType', 4, 'Rotation', '周转规则 (FIFO/LIFO)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: RuleConditionOperator
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RuleConditionOperator', 0, 'Equals', '等于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 1, 'NotEquals', '不等于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 2, 'GreaterThan', '大于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 3, 'LessThan', '小于', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 4, 'Contains', '包含', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 5, 'StartsWith', '开头是', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleConditionOperator', 6, 'EndsWith', '结尾是', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.ShippingEnums

-- Enum: ShipmentStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ShipmentStatus', 0, 'Pending', '待发运', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 1, 'InTransit', '运输中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 2, 'Delivered', '已送达', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 3, 'Delayed', '已延迟', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShipmentStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: CarrierType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('CarrierType', 0, 'ThirdPartyLogistics', '第三方物流', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CarrierType', 1, 'OwnFleet', '自有车队', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CarrierType', 2, 'Express', '快递公司', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('CarrierType', 3, 'Postal', '邮政', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.StockTakingEnums

-- Enum: StockTakingStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StockTakingStatus', 0, 'Pending', '待盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 1, 'InProgress', '盘点中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 3, 'Auditing', '审核中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 4, 'Adjusted', '已调整', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: StockTakingType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StockTakingType', 0, 'Full', '全盘', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingType', 1, 'CycleCount', '循环盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingType', 2, 'ByLocation', '按库位盘点', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StockTakingType', 3, 'ByMaterial', '按物料盘点', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.StrategyEnums

-- Enum: StrategyType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StrategyType', 0, 'Putaway', '上架策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyType', 1, 'Picking', '拣货策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyType', 2, 'Replenishment', '补货策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyType', 3, 'Rotation', '周转策略 (如FIFO, FEFO)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: StrategyStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StrategyStatus', 0, 'Active', '激活', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyStatus', 1, 'Inactive', '未激活', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyStatus', 2, 'Draft', '草稿', NULL, GETDATE(), NULL, NULL, NULL, 1);


-- Inserting enums from bnred.Model.WMS.Enums.TaskEnums

-- Enum: TaskType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskType', 0, 'Putaway', '上架任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 1, 'Picking', '拣货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 2, 'Replenishment', '补货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 3, 'CycleCount', '盘点任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 4, 'Movement', '移库任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskType', 5, 'QualityInspection', '质检任务', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskStatus', 0, 'Pending', '待分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 1, 'Assigned', '已分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 2, 'InProgress', '执行中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 3, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskStatus', 5, 'OnHold', '已暂停', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskPriority
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskPriority', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskPriority', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskPriority', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskPriority', 3, 'Urgent', '紧急', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.TransactionEnums

-- Enum: TransactionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransactionType', 0, 'Inbound', '入库事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 1, 'Outbound', '出库事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 2, 'Adjustment', '调整事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 3, 'Transfer', '转移事务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionType', 4, 'Stocktake', '盘点事务', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TransactionStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransactionStatus', 0, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 1, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 3, 'Failed', '已失败', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransactionStatus', 4, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.TransferEnums

-- Enum: TransferType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransferType', 0, 'WarehouseToWarehouse', '仓库间调拨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferType', 1, 'LocationToLocation', '库位间调拨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferType', 2, 'StockToStock', '库存状态间调拨', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TransferStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TransferStatus', 0, 'Pending', '待调拨', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 1, 'InProgress', '调拨中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 2, 'Shipped', '已发运 (出库方)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 3, 'Received', '已接收 (入库方)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 4, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TransferStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

    -- Inserting enums from bnred.Model.Enums.UserType

-- Enum: UserType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('UserType', 1, 'Internal', '内部用户', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UserType', 2, 'External', '外部用户', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UserType', 3, 'System', '系统用户', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UserType', 4, 'ServiceAccount', '服务账户', NULL, GETDATE(), NULL, NULL, NULL, 1);

    -- Enum: WarehouseType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WarehouseType', 0, 'RawMaterial', '原材料仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 1, 'FinishedProduct', '成品仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 2, 'SemiFinished', '半成品仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 3, 'Transit', '周转仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 4, 'Return', '退货仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 5, 'Defective', '不良品仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 6, 'Virtual', '虚拟仓库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseType', 7, 'General', '一般仓库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: WarehouseStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WarehouseStatus', 0, 'Operating', '正常运营', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseStatus', 1, 'Suspended', '暂停使用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseStatus', 2, 'Maintenance', '维护中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseStatus', 3, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: AreaType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('AreaType', 0, 'Storage', '存储区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 1, 'Receiving', '收货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 2, 'Shipping', '发货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 3, 'Picking', '拣选区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 4, 'Staging', '暂存区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 5, 'Return', '退货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 6, 'QualityControl', '质检区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 7, 'Packaging', '包装区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaType', 99, 'Other', '其他区域', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: AreaStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('AreaStatus', 0, 'Normal', '正常', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaStatus', 1, 'Maintenance', '维护中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaStatus', 2, 'Full', '已满', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaStatus', 3, 'Locked', '已锁定', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('AreaStatus', 4, 'Disabled', '已禁用', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum Type: InventoryAlertLevel
-- Source File: bnred.Model/WMS/Analysis/WarehouseAnalytics.cs
-- Note: Column list and values corrected to align with standard format.
-- TenantCode is assumed to be covered by TenantId (UNIQUEIDENTIFIER).
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES 
(N'InventoryAlertLevel', 0, N'Normal', N'正常', NULL, GETDATE(), NULL, NULL, NULL, 1),
(N'InventoryAlertLevel', 1, N'Warning', N'警告', NULL, GETDATE(), NULL, NULL, NULL, 1),
(N'InventoryAlertLevel', 2, N'Low', N'库存不足', NULL, GETDATE(), NULL, NULL, NULL, 1),
(N'InventoryAlertLevel', 3, N'High', N'库存过高', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

PRINT 'Population of WMS_EnumDictionary completed.';
GO 

-- #############################################################################
-- # Enum Category Table Definition and Data Inserts
-- #############################################################################

PRINT 'Starting creation and population of WMS_EnumCategory...';
GO

-- Drop table if it exists (optional, for re-running the script)
IF OBJECT_ID('dbo.WMS_EnumCategory', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_EnumCategory;
GO

CREATE TABLE WMS_EnumCategory (
    ID INT IDENTITY(1,1) PRIMARY KEY,         -- 自增主键ID
    CategoryName NVARCHAR(255) UNIQUE NOT NULL, -- 枚举类型的名称 (例如: "AlertType")
    CategoryDescription NVARCHAR(1000) NULL,  -- 枚举分类的描述
    TenantId UNIQUEIDENTIFIER NULL,           -- 租户ID
    CreateTime DATETIME2 DEFAULT GETDATE(),   -- 创建时间
    CreateBy UNIQUEIDENTIFIER NULL,           -- 创建人
    UpdateTime DATETIME2 NULL,                -- 更新时间
    UpdateBy UNIQUEIDENTIFIER NULL,           -- 更新人
    IsSystem BIT DEFAULT 1                    -- 是否系统分类
);
GO

-- Add MS_Description for WMS_EnumCategory table and its columns
EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', @value=N'存储系统中所有枚举类型的分类信息，作为 WMS_EnumDictionary 的目录。', 
    @level0type=N'SCHEMA',@level0name=N'dbo', 
    @level1type=N'TABLE',@level1name=N'WMS_EnumCategory';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'枚举类型的唯一名称 (例如: AlertType)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'CategoryName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'枚举分类的描述信息', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'CategoryDescription';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID (如果适用)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'TenantId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'记录创建时间', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建记录的用户ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'记录最后更新时间', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后更新记录的用户ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否为系统分类', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumCategory', @level2type=N'COLUMN',@level2name=N'IsSystem';
GO

PRINT 'WMS_EnumCategory table definition created successfully.';
GO

-- #############################################################################
-- # Populate WMS_EnumCategory from WMS_EnumDictionary
-- #############################################################################

PRINT 'Populating WMS_EnumCategory with distinct EnumTypeNames from WMS_EnumDictionary...';
GO

INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription, -- 生成一个基础描述
    NULL, -- 假设系统枚举不直接关联特定租户，或按需调整
    NULL, -- 假设由系统进程创建
    NULL, -- 假设由系统进程创建
    1     -- 标记为系统分类
FROM WMS_EnumDictionary;
GO

PRINT 'Population of WMS_EnumCategory completed.';
GO 

-- Inserting enums from bnred.Model.WMS.Enums.MovementEnums
-- Enum: MovementType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MovementType', 0, 'InternalTransfer', '内部移库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementType', 1, 'Replenishment', '补货移库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementType', 2, 'Consolidation', '集货移库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementType', 3, 'Deconsolidation', '分货移库', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementType', 4, 'StockAdjustment', '库存调整移库', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: MovementStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MovementStatus', 0, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementStatus', 1, 'InProgress', '进行中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementStatus', 2, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MovementStatus', 3, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Inserting enums from bnred.Model.WMS.Enums.QualityStatusEnums

-- Enum: QualityStatus (from QualityStatusEnums.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('QualityStatusEnums_QualityStatus', 0, 'Pending', '待检', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatusEnums_QualityStatus', 1, 'Qualified', '合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatusEnums_QualityStatus', 2, 'Unqualified', '不合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatusEnums_QualityStatus', 3, 'PartiallyQualified', '部分合格', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('QualityStatusEnums_QualityStatus', 4, 'Exempted', '免检', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with the new EnumTypeName
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    'QualityStatusEnums_QualityStatus', 
    N'枚举类型: QualityStatusEnums_QualityStatus' AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName = 'QualityStatusEnums_QualityStatus' AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = 'QualityStatusEnums_QualityStatus');
GO

-- Inserting enums from bnred.Model.WMS.Enums.WMSEnums

-- Enum: DustProofLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('DustProofLevel', 0, 'None', '无要求', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DustProofLevel', 1, 'Level1', '一级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DustProofLevel', 2, 'Level2', '二级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('DustProofLevel', 3, 'Level3', '三级', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: MoistureProofLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('MoistureProofLevel', 0, 'None', '无要求', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MoistureProofLevel', 1, 'Level1', '一级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MoistureProofLevel', 2, 'Level2', '二级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('MoistureProofLevel', 3, 'Level3', '三级', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: InventoryAdjustmentReason
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('InventoryAdjustmentReason', 0, 'Gain', '盘盈', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentReason', 1, 'Loss', '盘亏', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentReason', 2, 'Damage', '报损', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('InventoryAdjustmentReason', 3, 'Other', '其他', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: WarehouseAreaType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WarehouseAreaType', 0, 'Storage', '存储区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseAreaType', 1, 'Receiving', '收货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseAreaType', 2, 'Shipping', '发货区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseAreaType', 3, 'QualityCheck', '待检区', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WarehouseAreaType', 4, 'Return', '退货区', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('DustProofLevel', 'MoistureProofLevel', 'InventoryAdjustmentReason', 'WarehouseAreaType')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.NotificationEnums

-- Enum: WMSNotificationType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WMSNotificationType', 0, 'System', '系统通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationType', 1, 'Alert', '预警通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationType', 2, 'Task', '任务通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationType', 3, 'Message', '消息通知', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationType', 4, 'Other', '其他通知', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: WMSNotificationPriority
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WMSNotificationPriority', 0, 'Low', '低', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationPriority', 1, 'Medium', '中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationPriority', 2, 'High', '高', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationPriority', 3, 'Urgent', '紧急', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: WMSNotificationStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('WMSNotificationStatus', 0, 'Unread', '未读', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationStatus', 1, 'Read', '已读', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationStatus', 2, 'Processed', '已处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationStatus', 3, 'Ignored', '已忽略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('WMSNotificationStatus', 4, 'Expired', '已过期', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('WMSNotificationType', 'WMSNotificationPriority', 'WMSNotificationStatus')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.RuleEnums

-- Enum: RuleType (from RuleEnums.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('RuleEnums_RuleType', 0, 'PutAway', '上架规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleEnums_RuleType', 1, 'Picking', '拣货规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleEnums_RuleType', 2, 'Replenishment', '补货规则', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleEnums_RuleType', 3, 'LocationAssignment', '库位分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleEnums_RuleType', 4, 'StockAlert', '库存预警', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleEnums_RuleType', 5, 'BatchManagement', '批次管理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('RuleEnums_RuleType', 99, 'Other', '其他规则', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with the new EnumTypeName
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    'RuleEnums_RuleType', 
    N'枚举类型: RuleEnums_RuleType' AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName = 'RuleEnums_RuleType' AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = 'RuleEnums_RuleType');
GO

-- Inserting enums from bnred.Model.WMS.Enums.ShelfEnums

-- Enum: ShelfType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ShelfType', 0, 'Standard', '标准货架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfType', 1, 'Heavy', '重型货架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfType', 2, 'Flow', '流利式货架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfType', 3, 'Shuttle', '穿梭式货架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfType', 4, 'Cantilever', '悬臂式货架', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfType', 5, 'Mezzanine', '阁楼式货架', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: ShelfStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('ShelfStatus', 0, 'Normal', '正常使用', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfStatus', 1, 'Maintenance', '维护中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfStatus', 2, 'Full', '已满载', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfStatus', 3, 'Locked', '已锁定', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('ShelfStatus', 4, 'Disabled', '已停用', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('ShelfType', 'ShelfStatus')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.StrategyEnums

-- Enum: StrategyType (from StrategyEnums.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('StrategyEnums_StrategyType', 0, 'PutAway', '上架策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 1, 'Picking', '拣货策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 2, 'Replenishment', '补货策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 3, 'LocationAssignment', '库位分配策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 4, 'StockAdjustment', '库存调整策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 5, 'StockTaking', '盘点策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 6, 'Wave', '波次策略', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('StrategyEnums_StrategyType', 99, 'Other', '其他策略', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with the new EnumTypeName
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    'StrategyEnums_StrategyType', 
    N'枚举类型: StrategyEnums_StrategyType' AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName = 'StrategyEnums_StrategyType' AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = 'StrategyEnums_StrategyType');
GO

-- Inserting enums from bnred.Model.WMS.Enums.SupplierEnums

-- Enum: SupplierType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SupplierType', 0, 'Manufacturer', '生产厂商', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierType', 1, 'Agent', '代理商', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierType', 2, 'Trader', '贸易商', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierType', 3, 'ServiceProvider', '服务商', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: SupplierLevel
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SupplierLevel', 0, 'Strategic', '战略供应商', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierLevel', 1, 'Core', '核心供应商', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierLevel', 2, 'Regular', '普通供应商', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierLevel', 3, 'Temporary', '临时供应商', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: SupplierStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SupplierStatus', 0, 'Active', '正常合作', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierStatus', 1, 'Suspended', '合作暂停', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierStatus', 2, 'Probation', '观察期', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierStatus', 3, 'Terminated', '已终止', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SupplierStatus', 4, 'Blacklisted', '黑名单', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('SupplierType', 'SupplierLevel', 'SupplierStatus')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.SystemEnums

-- Enum: SystemParameterType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SystemParameterType', 0, 'Basic', '基础参数', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemParameterType', 1, 'Business', '业务参数', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemParameterType', 2, 'Interface', '接口参数', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemParameterType', 3, 'Print', '打印参数', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemParameterType', 4, 'Message', '消息参数', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemParameterType', 5, 'Report', '报表参数', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: SystemLogType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('SystemLogType', 0, 'Operation', '操作日志', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemLogType', 1, 'Login', '登录日志', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemLogType', 2, 'Exception', '异常日志', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemLogType', 3, 'Security', '安全日志', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('SystemLogType', 4, 'Performance', '性能日志', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('SystemParameterType', 'SystemLogType')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.TaskEnums

-- Enum: TaskType (from TaskEnums.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskEnums_TaskType', 0, 'Receiving', '收货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskType', 1, 'PutAway', '上架任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskType', 2, 'Picking', '拣货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskType', 3, 'Replenishment', '补货任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskType', 4, 'Moving', '移库任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskType', 5, 'StockTaking', '盘点任务', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskType', 6, 'Checking', '复核任务', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskStatus (from TaskEnums.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskEnums_TaskStatus', 0, 'PendingAssignment', '待分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskStatus', 1, 'Assigned', '已分配', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskStatus', 2, 'Processing', '进行中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskStatus', 3, 'Paused', '已暂停', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskStatus', 4, 'Completed', '已完成', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskStatus', 5, 'Cancelled', '已取消', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskStatus', 6, 'Exception', '异常', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskPriority (from TaskEnums.cs)
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskEnums_TaskPriority', 0, 'Low', '低优先级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskPriority', 1, 'Normal', '普通优先级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskPriority', 2, 'High', '高优先级', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskEnums_TaskPriority', 3, 'Urgent', '紧急优先级', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('TaskEnums_TaskType', 'TaskEnums_TaskStatus', 'TaskEnums_TaskPriority')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.TaskExceptionEnums

-- Enum: TaskExceptionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskExceptionType', 0, 'InsufficientStock', '库存不足', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionType', 1, 'LocationNotFound', '库位不存在', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionType', 2, 'BarcodeError', '条码错误', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionType', 3, 'EquipmentFailure', '设备故障', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionType', 4, 'StaffAbsent', '人员缺失', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionType', 99, 'Other', '其他', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: TaskExceptionStatus
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TaskExceptionStatus', 0, 'Pending', '待处理', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionStatus', 1, 'Processing', '处理中', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionStatus', 2, 'Resolved', '已解决', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TaskExceptionStatus', 3, 'Closed', '已关闭', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('TaskExceptionType', 'TaskExceptionStatus')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO

-- Inserting enums from bnred.Model.WMS.Enums.TimeGranularity (actually bnred.Model.TimeGranularity)

-- Enum: TimeGranularity
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('TimeGranularity', 1, 'Daily', '按日', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TimeGranularity', 2, 'Weekly', '按周', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TimeGranularity', 3, 'Monthly', '按月', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TimeGranularity', 4, 'Quarterly', '按季度', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('TimeGranularity', 5, 'Yearly', '按年', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with the new EnumTypeName
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    'TimeGranularity', 
    N'枚举类型: TimeGranularity' AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName = 'TimeGranularity' AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = 'TimeGranularity');
GO

-- Inserting enums from bnred.Model.Enums.UnitType

-- Enum: UnitType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('UnitType', 0, 'EA', '个 (Each)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitType', 1, 'KG', '千克 (Kilogram)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitType', 2, 'M', '米 (Meter)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitType', 3, 'L', '升 (Liter)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitType', 4, 'BOX', '箱 (Box)', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitType', 5, 'PALLET', '托盘 (Pallet)', NULL, GETDATE(), NULL, NULL, NULL, 1);

-- Enum: UnitConversionType
INSERT INTO WMS_EnumDictionary (EnumTypeName, EnumItemValue, EnumValueName, EnumItemDescription, TenantId, CreateTime, CreateBy, UpdateTime, UpdateBy, IsSystem)
VALUES
    ('UnitConversionType', 0, 'ConversionFactor', '转换因子', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitConversionType', 1, 'ConversionRate', '转换率', NULL, GETDATE(), NULL, NULL, NULL, 1),
    ('UnitConversionType', 2, 'ConversionEquation', '转换方程', NULL, GETDATE(), NULL, NULL, NULL, 1);
GO

-- Update WMS_EnumCategory with new EnumTypeNames
INSERT INTO WMS_EnumCategory (CategoryName, CategoryDescription, TenantId, CreateBy, UpdateBy, IsSystem)
SELECT DISTINCT 
    EnumTypeName, 
    N'枚举类型: ' + EnumTypeName AS CategoryDescription,
    NULL,
    NULL,
    NULL,
    1
FROM WMS_EnumDictionary
WHERE EnumTypeName IN ('UnitType', 'UnitConversionType')
  AND NOT EXISTS (SELECT 1 FROM WMS_EnumCategory WHERE CategoryName = WMS_EnumDictionary.EnumTypeName);
GO