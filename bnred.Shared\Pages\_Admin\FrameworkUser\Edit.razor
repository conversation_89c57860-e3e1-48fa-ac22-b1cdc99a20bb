@page "/_Admin/FrameworkUser/Edit/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Tab IsBorderCard="true">
        <TabItem Text="@WtmBlazor.Localizer["_Admin.BasicInfo"]">
            <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
                <BootstrapInput @bind-Value="@Model.Entity.ITCode" />
                <Switch @bind-Value="@Model.Entity.IsValid" />
                <BootstrapInput @bind-Value="@Model.Entity.Name" />
                <Select @bind-Value="@Model.Entity.Gender" />
                <WTUploadImage @bind-Value="@Model.Entity.PhotoId" />
            </Row>
            <Row>
            </Row>
            <CheckboxList @bind-Value="Model.SelectedRolesCodes" Items="@AllRoles" />
            <CheckboxList @bind-Value="Model.SelectedGroupCodes" Items="@AllGroups" />
        </TabItem>
        <TabItem Text="@WtmBlazor.Localizer["_Admin.AdditionInfo"]">
            <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
                <BootstrapInput @bind-Value="@Model.Entity.CellPhone" />
                <BootstrapInput @bind-Value="@Model.Entity.HomePhone" />
                <BootstrapInput @bind-Value="@Model.Entity.Email" />
                <BootstrapInput @bind-Value="@Model.Entity.ZipCode" />
            </Row>
            <Row>
                <BootstrapInput @bind-Value="@Model.Entity.Address" />
            </Row>
        </TabItem>
    </Tab>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>
@code {
    private FrameworkUserVM Model = null;
    private List<SelectedItem> AllRoles = new List<SelectedItem>();
    private List<SelectedItem> AllGroups = new List<SelectedItem>();
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    protected override async Task OnInitializedAsync()
    {
        AllRoles = await WtmBlazor.Api.CallItemsApi("/api/_FrameworkUser/GetFrameworkRoles");
        AllGroups = await WtmBlazor.Api.CallItemsApi("/api/_FrameworkUser/GetFrameworkGroups");
        var rv = await WtmBlazor.Api.CallAPI<FrameworkUserVM>($"/api/_FrameworkUser/{id}");
        Model = rv.Data;
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_frameworkuser/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
