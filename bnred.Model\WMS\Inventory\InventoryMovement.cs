using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;
namespace bnred.Model.WMS.Inventory
{
    /// <summary>
    /// 库存移动记录
    /// 记录物料在不同库位间的移动历史
    /// </summary>
    [Table("WMS_InventoryMovements")]
    public class InventoryMovement : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 移动单号
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "移动单号")]
        public string MovementNo { get; set; }

        /// <summary>
        /// 移动类型
        /// </summary>
        [Required]
        [Display(Name = "移动类型")]
        public MovementType MovementType { get; set; }

        /// <summary>
        /// 移动状态
        /// </summary>
        [Required]
        public MovementStatus Status { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }
        
        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string? BatchId { get; set; }
        
        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 源仓库ID
        /// </summary>
        [Required]
        [Display(Name = "来源仓库")]
        [StringLength(50)]
        public string SourceWarehouseId { get; set; }
        
        /// <summary>
        /// 源仓库
        /// </summary>
        [Display(Name = "来源仓库")]
        public Warehouse.Warehouse SourceWarehouse { get; set; }

        /// <summary>
        /// 源库位ID
        /// </summary>
        [Required]
        [Display(Name = "来源库位")]
        [StringLength(50)]
        public string SourceLocationId { get; set; }
        
        /// <summary>
        /// 源库位
        /// </summary>
        [Display(Name = "来源库位")]
        public Location SourceLocation { get; set; }

        /// <summary>
        /// 目标仓库ID
        /// </summary>
        [Required]
        [Display(Name = "目标仓库")]
        [StringLength(50)]
        public string TargetWarehouseId { get; set; }
        
        /// <summary>
        /// 目标仓库
        /// </summary>
        [Display(Name = "目标仓库")]
        public Warehouse.Warehouse TargetWarehouse { get; set; }

        /// <summary>
        /// 目标库位ID
        /// </summary>
        [Required]
        [Display(Name = "目标库位")]
        [StringLength(50)]
        public string TargetLocationId { get; set; }
        
        /// <summary>
        /// 目标库位
        /// </summary>
        [Display(Name = "目标库位")]
        public Location TargetLocation { get; set; }

        /// <summary>
        /// 计划移动数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        [Display(Name = "移动数量")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际移动数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? ActualQuantity { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        [Required]
        [Display(Name = "计划开始时间")]
        public DateTime PlanStartTime { get; set; }

        /// <summary>
        /// 计划完成时间
        /// </summary>
        [Required]
        [Display(Name = "计划完成时间")]
        public DateTime PlanEndTime { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        [Display(Name = "实际开始时间")]
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完成时间
        /// </summary>
        [Display(Name = "实际完成时间")]
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        [Required]
        [Display(Name = "操作人")]
   
        public Guid? OperatorId { get; set; }
        
 
        public FrameworkUser Operator { get; set; }

        /// <summary>
        /// 关联单据号
        /// </summary>
        [StringLength(50)]
        [Display(Name = "关联单据号")]
        public string RelatedOrderNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}