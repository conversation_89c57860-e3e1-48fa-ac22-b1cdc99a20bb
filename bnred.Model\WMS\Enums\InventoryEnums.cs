using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums.Inventory
{
    /// <summary>
    /// 库存变动类型枚举
    /// 用于标识库存变动的不同类型
    /// </summary>
    public enum InventoryTransactionType
    {
        /// <summary>
        /// 入库
        /// </summary>
        [Display(Name = "入库")]
        Inbound = 0,

        /// <summary>
        /// 出库
        /// </summary>
        [Display(Name = "出库")]
        Outbound = 1,

        /// <summary>
        /// 移库
        /// </summary>
        [Display(Name = "移库")]
        Transfer = 2,

        /// <summary>
        /// 盘点调整
        /// </summary>
        [Display(Name = "盘点调整")]
        StockTaking = 3,

        /// <summary>
        /// 报损
        /// </summary>
        [Display(Name = "报损")]
        Damage = 4,

        /// <summary>
        /// 报溢
        /// </summary>
        [Display(Name = "报溢")]
        Overflow = 5,

        /// <summary>
        /// 退货入库
        /// </summary>
        [Display(Name = "退货入库")]
        ReturnInbound = 6,

        /// <summary>
        /// 退货出库
        /// </summary>
        [Display(Name = "退货出库")]
        ReturnOutbound = 7,

        /// <summary>
        /// 其他
        /// </summary>
        [Display(Name = "其他")]
        Other = 8
    }

    /// <summary>
    /// 库存状态枚举
    /// 用于标识库存的不同状态
    /// </summary>
    public enum InventoryStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 锁定
        /// </summary>
        [Display(Name = "锁定")]
        Locked = 1,

        /// <summary>
        /// 待检
        /// </summary>
        [Display(Name = "待检")]
        PendingInspection = 2,

        /// <summary>
        /// 不合格
        /// </summary>
        [Display(Name = "不合格")]
        Defective = 3,

        /// <summary>
        /// 待处理
        /// </summary>
        [Display(Name = "待处理")]
        Pending = 4,

        /// <summary>
        /// 已分配
        /// </summary>
        [Display(Name = "已分配")]
        Allocated = 5,

        /// <summary>
        /// 已过期
        /// </summary>
        [Display(Name = "已过期")]
        Expired = 6
    }

    /// <summary>
    /// 盘点类型枚举
    /// 用于标识不同的盘点类型
    /// </summary>
    public enum StockTakingType
    {
        /// <summary>
        /// 全面盘点
        /// </summary>
        [Display(Name = "全面盘点")]
        Full = 0,

        /// <summary>
        /// 抽样盘点
        /// </summary>
        [Display(Name = "抽样盘点")]
        Sample = 1,

        /// <summary>
        /// 动态盘点
        /// </summary>
        [Display(Name = "动态盘点")]
        Dynamic = 2,

        /// <summary>
        /// 周期盘点
        /// </summary>
        [Display(Name = "周期盘点")]
        Periodic = 3,

        /// <summary>
        /// ABC盘点
        /// </summary>
        [Display(Name = "ABC盘点")]
        ABC = 4
    }

    /// <summary>
    /// 盘点状态枚举
    /// 用于标识盘点的不同状态
    /// </summary>
    public enum StockTakingStatus
    {
        /// <summary>
        /// 待盘点
        /// </summary>
        [Display(Name = "待盘点")]
        Pending = 0,

        /// <summary>
        /// 盘点中
        /// </summary>
        [Display(Name = "盘点中")]
        InProgress = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 2,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 3,

        /// <summary>
        /// 已调整
        /// </summary>
        [Display(Name = "已调整")]
        Adjusted = 4
    }

    /// <summary>
    /// 库存调整类型枚举
    /// 用于标识不同的库存调整类型
    /// </summary>
    public enum InventoryAdjustmentType
    {
        /// <summary>
        /// 盘盈
        /// </summary>
        [Display(Name = "盘盈")]
        Gain = 0,

        /// <summary>
        /// 盘亏
        /// </summary>
        [Display(Name = "盘亏")]
        Loss = 1,

        /// <summary>
        /// 报损
        /// </summary>
        [Display(Name = "报损")]
        Damage = 2,

        /// <summary>
        /// 报溢
        /// </summary>
        [Display(Name = "报溢")]
        Overflow = 3,

        /// <summary>
        /// 其他
        /// </summary>
        [Display(Name = "其他")]
        Other = 4
    }

    /// <summary>
    /// 库存预警类型枚举
    /// 用于标识库存预警的类型
    /// </summary>
    public enum InventoryAlertType
    {
        /// <summary>
        /// 最小库存
        /// 低于最小库存预警
        /// </summary>
        [Display(Name = "最小库存")]
        MinStock = 0,

        /// <summary>
        /// 最大库存
        /// 超过最大库存预警
        /// </summary>
        [Display(Name = "最大库存")]
        MaxStock = 1,

        /// <summary>
        /// 安全库存
        /// 低于安全库存预警
        /// </summary>
        [Display(Name = "安全库存")]
        SafetyStock = 2,

        /// <summary>
        /// 积压库存
        /// 超过积压预警天数
        /// </summary>
        [Display(Name = "积压库存")]
        StagnantStock = 3,

        /// <summary>
        /// 呆滞库存
        /// 超过呆滞预警天数
        /// </summary>
        [Display(Name = "呆滞库存")]
        DeadStock = 4
    }

    /// <summary>
    /// 调整类型枚举
    /// 用于标识不同的调整类型
    /// </summary>
    public enum AdjustmentType
    {
        /// <summary>
        /// 盘盈
        /// </summary>
        [Display(Name = "盘盈")]
        Gain = 0,

        /// <summary>
        /// 盘亏
        /// </summary>
        [Display(Name = "盘亏")]
        Loss = 1,

        /// <summary>
        /// 报损
        /// </summary>
        [Display(Name = "报损")]
        Damage = 2,

        /// <summary>
        /// 报溢
        /// </summary>
        [Display(Name = "报溢")]
        Overflow = 3,

        /// <summary>
        /// 其他
        /// </summary>
        [Display(Name = "其他")]
        Other = 4
    }

    /// <summary>
    /// 审核状态枚举
    /// </summary>
    public enum AuditStatus
    {
        /// <summary>
        /// 待审核
        /// </summary>
        [Display(Name = "待审核")]
        Pending = 0,

        /// <summary>
        /// 已审核
        /// </summary>
        [Display(Name = "已审核")]
        Approved = 1,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Display(Name = "已拒绝")]
        Rejected = 2,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 3
    }

    /// <summary>
    /// 业务类型枚举
    /// </summary>
    public enum BusinessType
    {
        /// <summary>
        /// 采购入库
        /// </summary>
        [Display(Name = "采购入库")]
        PurchaseInbound = 0,

        /// <summary>
        /// 销售出库
        /// </summary>
        [Display(Name = "销售出库")]
        SalesOutbound = 1,

        /// <summary>
        /// 库存调整
        /// </summary>
        [Display(Name = "库存调整")]
        Adjustment = 2,

        /// <summary>
        /// 库存盘点
        /// </summary>
        [Display(Name = "库存盘点")]
        StockTaking = 3,

        /// <summary>
        /// 库存调拨
        /// </summary>
        [Display(Name = "库存调拨")]
        Transfer = 4,

        /// <summary>
        /// 其他入库
        /// </summary>
        [Display(Name = "其他入库")]
        OtherInbound = 5,

        /// <summary>
        /// 其他出库
        /// </summary>
        [Display(Name = "其他出库")]
        OtherOutbound = 6
    }

    /// <summary>
    /// 盘点明细状态枚举
    /// </summary>
    public enum StockTakingDetailStatus
    {
        /// <summary>
        /// 待盘点
        /// </summary>
        [Display(Name = "待盘点")]
        Pending = 0,

        /// <summary>
        /// 已盘点
        /// </summary>
        [Display(Name = "已盘点")]
        Counted = 1,

        /// <summary>
        /// 盘盈
        /// </summary>
        [Display(Name = "盘盈")]
        Gain = 2,

        /// <summary>
        /// 盘亏
        /// </summary>
        [Display(Name = "盘亏")]
        Loss = 3,

        /// <summary>
        /// 已调整
        /// </summary>
        [Display(Name = "已调整")]
        Adjusted = 4
    }

    /// <summary>
    /// 预警状态枚举
    /// </summary>
    public enum AlertStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        [Display(Name = "待处理")]
        Pending = 0,

        /// <summary>
        /// 已处理
        /// </summary>
        [Display(Name = "已处理")]
        Processed = 1,

        /// <summary>
        /// 已忽略
        /// </summary>
        [Display(Name = "已忽略")]
        Ignored = 2,

        /// <summary>
        /// 已过期
        /// </summary>
        [Display(Name = "已过期")]
        Expired = 3
    }

    /// <summary>
    /// 库位状态枚举
    /// </summary>
    public enum LocationStatus
    {
        /// <summary>
        /// 空闲
        /// </summary>
        [Display(Name = "空闲")]
        Empty = 0,

        /// <summary>
        /// 占用
        /// </summary>
        [Display(Name = "占用")]
        Occupied = 1,

        /// <summary>
        /// 锁定
        /// </summary>
        [Display(Name = "锁定")]
        Locked = 2,

        /// <summary>
        /// 禁用
        /// </summary>
        [Display(Name = "禁用")]
        Disabled = 3,

        /// <summary>
        /// 故障
        /// </summary>
        [Display(Name = "故障")]
        Faulty = 4
    }
}