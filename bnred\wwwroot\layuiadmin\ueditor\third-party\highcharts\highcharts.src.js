/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function(){function t(t,e){var i;t||(t={});for(i in e)t[i]=e[i];return t}function e(){var t,e=arguments.length,i={},n=function(t,e){var i,o;"object"!=typeof t&&(t={});for(o in e)e.hasOwnProperty(o)&&(i=e[o],i&&"object"==typeof i&&"[object Array]"!==Object.prototype.toString.call(i)&&"number"!=typeof i.nodeType?t[o]=n(t[o]||{},i):t[o]=e[o]);return t};for(t=0;t<e;t++)i=n(i,arguments[t]);return i}function i(){for(var t=0,e=arguments,i=e.length,n={};t<i;t++)n[e[t++]]=e[t];return n}function n(t,e){return parseInt(t,e||10)}function o(t){return"string"==typeof t}function r(t){return"object"==typeof t}function s(t){return"[object Array]"===Object.prototype.toString.call(t)}function a(t){return"number"==typeof t}function l(t){return mt.log(t)/mt.LN10}function h(t){return mt.pow(10,t)}function c(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}}function d(t){return t!==Z&&null!==t}function p(t,e,i){var n,s,a="setAttribute";if(o(e))d(i)?t[a](e,i):t&&t.getAttribute&&(s=t.getAttribute(e));else if(d(e)&&r(e))for(n in e)t[a](n,e[n]);return s}function u(t){return s(t)?t:[t]}function f(){var t,e,i=arguments,n=i.length;for(t=0;t<n;t++)if(e=i[t],"undefined"!=typeof e&&null!==e)return e}function g(e,i){Mt&&i&&i.opacity!==Z&&(i.filter="alpha(opacity="+100*i.opacity+")"),t(e.style,i)}function m(e,i,n,o,r){var s=ft.createElement(e);return i&&t(s,i),r&&g(s,{padding:0,border:Jt,margin:0}),n&&g(s,n),o&&o.appendChild(s),s}function y(e,i){var n=function(){};return n.prototype=new e,t(n.prototype,i),n}function x(t,e,i,o){var r=q.lang,s=+t||0,a=e===-1?(s.toString().split(".")[1]||"").length:isNaN(e=wt(e))?2:e,l=void 0===i?r.decimalPoint:i,h=void 0===o?r.thousandsSep:o,c=s<0?"-":"",d=String(n(s=wt(s).toFixed(a))),p=d.length>3?d.length%3:0;return c+(p?d.substr(0,p)+h:"")+d.substr(p).replace(/(\d{3})(?=\d)/g,"$1"+h)+(a?l+wt(s-d).toFixed(a).slice(2):"")}function v(t,e){return new Array((e||2)+1-String(t).length).join(0)+t}function b(t,e,i){var n=t[e];t[e]=function(){var t=Array.prototype.slice.call(arguments);return t.unshift(n),i.apply(this,t)}}function k(t,e){var i,n=/f$/,o=/\.([0-9])/,r=q.lang;return n.test(t)?(i=t.match(o),i=i?i[1]:-1,e=x(e,i,r.decimalPoint,t.indexOf(",")>-1?r.thousandsSep:"")):e=J(t,e),e}function w(t,e){for(var i,n,o,r,s,a,l,h="{",c=!1,d=[];(l=t.indexOf(h))!==-1;){if(i=t.slice(0,l),c){for(n=i.split(":"),o=n.shift().split("."),s=o.length,a=e,r=0;r<s;r++)a=a[o[r]];n.length&&(a=k(n.join(":"),a)),d.push(a)}else d.push(i);t=t.slice(l+1),c=!c,h=c?"}":"{"}return d.push(t),d.join("")}function T(t){return mt.pow(10,xt(mt.log(t)/mt.LN10))}function S(t,e,i,n){var o,r;for(i=f(i,1),o=t/i,e||(e=[1,2,2.5,5,10],n&&n.allowDecimals===!1&&(1===i?e=[1,2,5,10]:i<=.1&&(e=[1/i]))),r=0;r<e.length&&(t=e[r],!(o<=(e[r]+(e[r+1]||e[r]))/2));r++);return t*=i}function P(t,e){var i,n,o=e||[[re,[1,2,5,10,20,25,50,100,200,500]],[se,[1,2,5,10,15,30]],[ae,[1,2,5,10,15,30]],[le,[1,2,3,4,6,8,12]],[he,[1,2]],[ce,[1,2]],[de,[1,2,3,4,6]],[pe,null]],r=o[o.length-1],s=et[r[0]],a=r[1];for(n=0;n<o.length;n++)if(r=o[n],s=et[r[0]],a=r[1],o[n+1]){var l=(s*a[a.length-1]+et[o[n+1][0]])/2;if(t<=l)break}return s===et[pe]&&t<5*s&&(a=[1,2,5]),i=S(t/s,a,r[0]===pe?T(t/s):1),{unitRange:s,count:i,unitName:r[0]}}function A(e,i,n,o){var r,s,a=[],l={},h=q.global.useUTC,c=new Date(i),p=e.unitRange,u=e.count;if(d(i)){p>=et[se]&&(c.setMilliseconds(0),c.setSeconds(p>=et[ae]?0:u*xt(c.getSeconds()/u))),p>=et[ae]&&c[ht](p>=et[le]?0:u*xt(c[nt]()/u)),p>=et[le]&&c[ct](p>=et[he]?0:u*xt(c[ot]()/u)),p>=et[he]&&c[dt](p>=et[de]?1:u*xt(c[st]()/u)),p>=et[de]&&(c[pt](p>=et[pe]?0:u*xt(c[at]()/u)),s=c[lt]()),p>=et[pe]&&(s-=s%u,c[ut](s)),p===et[ce]&&c[dt](c[st]()-c[rt]()+f(o,1)),r=1,s=c[lt]();for(var g=c.getTime(),m=c[at](),y=c[st](),x=h?0:(864e5+60*c.getTimezoneOffset()*1e3)%864e5;g<n;)a.push(g),p===et[pe]?g=it(s+r*u,0):p===et[de]?g=it(s,m+r*u):h||p!==et[he]&&p!==et[ce]?g+=p*u:g=it(s,m,y+r*u*(p===et[he]?1:7)),r++;a.push(g),be(ke(a,function(t){return p<=et[le]&&t%et[he]===x}),function(t){l[t]=he})}return a.info=t(e,{higherRanks:l,totalRange:p*u}),a}function L(){this.color=0,this.symbol=0}function C(t,e){var i,n,o=t.length;for(n=0;n<o;n++)t[n].ss_i=n;for(t.sort(function(t,n){return i=e(t,n),0===i?t.ss_i-n.ss_i:i}),n=0;n<o;n++)delete t[n].ss_i}function M(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i}function D(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i}function I(t,e){var i;for(i in t)t[i]&&t[i]!==e&&t[i].destroy&&t[i].destroy(),delete t[i]}function z(t){$||($=m(jt)),t&&$.appendChild(t),$.innerHTML=""}function B(t,e){var i="Highcharts error #"+t+": www.highcharts.com/errors/"+t;if(e)throw i;gt.console&&console.log(i)}function O(t){return parseFloat(t.toPrecision(14))}function R(t,e){Q=f(t,e.animation)}function H(){var t=q.global.useUTC,e=t?"getUTC":"get",i=t?"setUTC":"set";it=t?Date.UTC:function(t,e,i,n,o,r){return new Date(t,e,f(i,1),f(n,0),f(o,0),f(r,0)).getTime()},nt=e+"Minutes",ot=e+"Hours",rt=e+"Day",st=e+"Date",at=e+"Month",lt=e+"FullYear",ht=i+"Minutes",ct=i+"Hours",dt=i+"Date",pt=i+"Month",ut=i+"FullYear"}function X(t){return q=e(q,t),H(),q}function W(){return q}function Y(){}function E(t,e,i,n){this.axis=t,this.pos=e,this.type=i||"",this.isNew=!0,i||n||this.addLabel()}function G(t,e){this.axis=t,e&&(this.options=e,this.id=e.id)}function N(t,e,i,n,o,r){var s=t.chart.inverted;this.axis=t,this.isNegative=i,this.options=e,this.x=n,this.total=null,this.points={},this.stack=o,this.percent="percent"===r,this.alignOptions={align:e.align||(s?i?"left":"right":"center"),verticalAlign:e.verticalAlign||(s?"middle":i?"bottom":"top"),y:f(e.y,s?4:i?14:-6),x:f(e.x,s?i?-6:6:0)},this.textAlign=e.textAlign||(s?i?"right":"left":"center")}function F(){this.init.apply(this,arguments)}function V(){this.init.apply(this,arguments)}function j(t,e){this.init(t,e)}function _(t,e){this.init(t,e)}function U(){this.init.apply(this,arguments)}var Z,K,$,q,J,Q,tt,et,it,nt,ot,rt,st,at,lt,ht,ct,dt,pt,ut,ft=document,gt=window,mt=Math,yt=mt.round,xt=mt.floor,vt=mt.ceil,bt=mt.max,kt=mt.min,wt=mt.abs,Tt=mt.cos,St=mt.sin,Pt=mt.PI,At=2*Pt/360,Lt=navigator.userAgent,Ct=gt.opera,Mt=/msie/i.test(Lt)&&!Ct,Dt=8===ft.documentMode,It=/AppleWebKit/.test(Lt),zt=/Firefox/.test(Lt),Bt=/(Mobile|Android|Windows Phone)/.test(Lt),Ot="http://www.w3.org/2000/svg",Rt=!!ft.createElementNS&&!!ft.createElementNS(Ot,"svg").createSVGRect,Ht=zt&&parseInt(Lt.split("Firefox/")[1],10)<4,Xt=!Rt&&!Mt&&!!ft.createElement("canvas").getContext,Wt=ft.documentElement.ontouchstart!==Z,Yt={},Et=0,Gt=function(){},Nt=[],Ft="Highcharts",Vt="3.0.6",jt="div",_t="absolute",Ut="relative",Zt="hidden",Kt="highcharts-",$t="visible",qt="px",Jt="none",Qt="M",te="L",ee="rgba(192,192,192,"+(Rt?1e-4:.002)+")",ie="",ne="hover",oe="select",re="millisecond",se="second",ae="minute",le="hour",he="day",ce="week",de="month",pe="year",ue="stroke-width",fe={};gt.Highcharts=gt.Highcharts?B(16,!0):{},J=function(e,i,n){if(!d(i)||isNaN(i))return"Invalid date";e=f(e,"%Y-%m-%d %H:%M:%S");var o,r=new Date(i),s=r[ot](),a=r[rt](),l=r[st](),h=r[at](),c=r[lt](),p=q.lang,u=p.weekdays,g=t({a:u[a].substr(0,3),A:u[a],d:v(l),e:l,b:p.shortMonths[h],B:p.months[h],m:v(h+1),y:c.toString().substr(2,2),Y:c,H:v(s),I:v(s%12||12),l:s%12||12,M:v(r[nt]()),p:s<12?"AM":"PM",P:s<12?"am":"pm",S:v(r.getSeconds()),L:v(yt(i%1e3),3)},Highcharts.dateFormats);for(o in g)for(;e.indexOf("%"+o)!==-1;)e=e.replace("%"+o,"function"==typeof g[o]?g[o](i):g[o]);return n?e.substr(0,1).toUpperCase()+e.substr(1):e},L.prototype={wrapColor:function(t){this.color>=t&&(this.color=0)},wrapSymbol:function(t){this.symbol>=t&&(this.symbol=0)}},et=i(re,1,se,1e3,ae,6e4,le,36e5,he,864e5,ce,6048e5,de,26784e5,pe,31556952e3),tt={init:function(t,e,i){e=e||"";var n,o,r,s,a,l=t.shift,h=e.indexOf("C")>-1,c=h?7:3,d=e.split(" "),p=[].concat(i),u=function(t){for(r=t.length;r--;)t[r]===Qt&&t.splice(r+1,0,t[r+1],t[r+2],t[r+1],t[r+2])};if(h&&(u(d),u(p)),t.isArea&&(s=d.splice(d.length-6,6),a=p.splice(p.length-6,6)),l<=p.length/c&&d.length===p.length)for(;l--;)p=[].concat(p).splice(0,c).concat(p);if(t.shift=0,d.length)for(n=p.length;d.length<n;)o=[].concat(d).splice(d.length-c,c),h&&(o[c-6]=o[c-2],o[c-5]=o[c-1]),d=d.concat(o);return s&&(d=d.concat(s),p=p.concat(a)),[d,p]},step:function(t,e,i,n){var o,r=[],s=t.length;if(1===i)r=n;else if(s===e.length&&i<1)for(;s--;)o=parseFloat(t[s]),r[s]=isNaN(o)?t[s]:i*parseFloat(e[s]-o)+o;else r=e;return r}},function(e){gt.HighchartsAdapter=gt.HighchartsAdapter||e&&{init:function(t){var i,n=e.fx,r=n.step,s=e.Tween,a=s&&s.propHooks,l=e.cssHooks.opacity;e.extend(e.easing,{easeOutQuad:function(t,e,i,n,o){return-n*(e/=o)*(e-2)+i}}),e.each(["cur","_default","width","height","opacity"],function(t,e){var i,o,l=r;"cur"===e?l=n.prototype:"_default"===e&&s&&(l=a[e],e="set"),i=l[e],i&&(l[e]=function(n){if(n=t?n:this,"align"!==n.prop)return o=n.elem,o.attr?o.attr(n.prop,"cur"===e?Z:n.now):i.apply(this,arguments)})}),b(l,"get",function(t,e,i){return e.attr?e.opacity||0:t.call(this,e,i)}),i=function(e){var i,n=e.elem;e.started||(i=t.init(n,n.d,n.toD),e.start=i[0],e.end=i[1],e.started=!0),n.attr("d",t.step(e.start,e.end,e.pos,n.toD))},s?a.d={set:i}:r.d=i,this.each=Array.prototype.forEach?function(t,e){return Array.prototype.forEach.call(t,e)}:function(t,e){for(var i=0,n=t.length;i<n;i++)if(e.call(t[i],t[i],i,t)===!1)return i},e.fn.highcharts=function(){var t,e,i,n="Chart",r=arguments;return o(r[0])&&(n=r[0],r=Array.prototype.slice.call(r,1)),t=r[0],t!==Z&&(t.chart=t.chart||{},t.chart.renderTo=this[0],i=new Highcharts[n](t,r[1]),e=this),t===Z&&(e=Nt[p(this[0],"data-highcharts-chart")]),e}},getScript:e.getScript,inArray:e.inArray,adapterRun:function(t,i){return e(t)[i]()},grep:e.grep,map:function(t,e){for(var i=[],n=0,o=t.length;n<o;n++)i[n]=e.call(t[n],t[n],n,t);return i},offset:function(t){return e(t).offset()},addEvent:function(t,i,n){e(t).bind(i,n)},removeEvent:function(t,i,n){var o=ft.removeEventListener?"removeEventListener":"detachEvent";ft[o]&&t&&!t[o]&&(t[o]=function(){}),e(t).unbind(i,n)},fireEvent:function(i,n,o,r){var s,a=e.Event(n),l="detached"+n;!Mt&&o&&(delete o.layerX,delete o.layerY),t(a,o),i[n]&&(i[l]=i[n],i[n]=null),e.each(["preventDefault","stopPropagation"],function(t,e){var i=a[e];a[e]=function(){try{i.call(a)}catch(t){"preventDefault"===e&&(s=!0)}}}),e(i).trigger(a),i[l]&&(i[n]=i[l],i[l]=null),!r||a.isDefaultPrevented()||s||r(a)},washMouseEvent:function(t){var e=t.originalEvent||t;return e.pageX===Z&&(e.pageX=t.pageX,e.pageY=t.pageY),e},animate:function(t,i,n){var o=e(t);t.style||(t.style={}),i.d&&(t.toD=i.d,i.d=1),o.stop(),i.opacity!==Z&&t.attr&&(i.opacity+="px"),o.animate(i,n)},stop:function(t){e(t).stop()}}}(gt.jQuery);var ge=gt.HighchartsAdapter,me=ge||{};ge&&ge.init.call(ge,tt);var ye=me.adapterRun,xe=me.getScript,ve=me.inArray,be=me.each,ke=me.grep,we=me.offset,Te=me.map,Se=me.addEvent,Pe=me.removeEvent,Ae=me.fireEvent,Le=me.washMouseEvent,Ce=me.animate,Me=me.stop,De={enabled:!0,x:0,y:15,style:{color:"#666",cursor:"default",fontSize:"11px",lineHeight:"14px"}};q={colors:["#2f7ed8","#0d233a","#8bbc21","#910000","#1aadce","#492970","#f28f43","#77a1e5","#c42525","#a6c96a"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{loading:"Loading...",months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],decimalPoint:".",numericSymbols:["k","M","G","T","P","E"],resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1",thousandsSep:","},global:{useUTC:!0,canvasToolsURL:"http://code.highcharts.com/3.0.6/modules/canvas-tools.js",VMLRadialGradientURL:"http://code.highcharts.com/3.0.6/gfx/vml-radial-gradient.png"},chart:{borderColor:"#4572A7",borderRadius:5,defaultSeriesType:"line",ignoreHiddenSeries:!0,spacing:[10,10,15,10],style:{fontFamily:'"Lucida Grande", "Lucida Sans Unicode", Verdana, Arial, Helvetica, sans-serif',fontSize:"12px"},backgroundColor:"#FFFFFF",plotBorderColor:"#C0C0C0",resetZoomButton:{theme:{zIndex:20},position:{align:"right",x:-10,y:10}}},title:{text:"Chart title",align:"center",margin:15,style:{color:"#274b6d",fontSize:"16px"}},subtitle:{text:"",align:"center",style:{color:"#4d759e"}},plotOptions:{line:{allowPointSelect:!1,showCheckbox:!1,animation:{duration:1e3},events:{},lineWidth:2,marker:{enabled:!0,lineWidth:0,radius:4,lineColor:"#FFFFFF",states:{hover:{enabled:!0},select:{fillColor:"#FFFFFF",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:e(De,{align:"center",enabled:!1,formatter:function(){return null===this.y?"":x(this.y,-1)},verticalAlign:"bottom",y:0}),cropThreshold:300,pointRange:0,showInLegend:!0,states:{hover:{marker:{}},select:{marker:{}}},stickyTracking:!0}},labels:{style:{position:_t,color:"#3E576F"}},legend:{enabled:!0,align:"center",layout:"horizontal",labelFormatter:function(){return this.name},borderWidth:1,borderColor:"#909090",borderRadius:5,navigation:{activeColor:"#274b6d",inactiveColor:"#CCC"},shadow:!1,itemStyle:{cursor:"pointer",color:"#274b6d",fontSize:"12px"},itemHoverStyle:{color:"#000"},itemHiddenStyle:{color:"#CCC"},itemCheckboxStyle:{position:_t,width:"13px",height:"13px"},symbolWidth:16,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:Ut,top:"1em"},style:{position:_t,backgroundColor:"white",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:Rt,backgroundColor:"rgba(255, 255, 255, .85)",borderWidth:1,borderRadius:3,dateTimeLabelFormats:{millisecond:"%A, %b %e, %H:%M:%S.%L",second:"%A, %b %e, %H:%M:%S",minute:"%A, %b %e, %H:%M",hour:"%A, %b %e, %H:%M",day:"%A, %b %e, %Y",week:"Week from %A, %b %e, %Y",month:"%B %Y",year:"%Y"},headerFormat:'<span style="font-size: 10px">{point.key}</span><br/>',pointFormat:'<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b><br/>',shadow:!0,snap:Bt?25:10,style:{color:"#333333",cursor:"default",fontSize:"12px",padding:"8px",whiteSpace:"nowrap"}},credits:{enabled:!0,text:"Highcharts.com",href:"http://www.highcharts.com",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#909090",fontSize:"9px"}}};var Ie=q.plotOptions,ze=Ie.line;H();var Be=function(t){function i(t){t&&t.stops?h=Te(t.stops,function(t){return Be(t[1])}):(l=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/.exec(t),l?c=[n(l[1]),n(l[2]),n(l[3]),parseFloat(l[4],10)]:(l=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(t),l?c=[n(l[1],16),n(l[2],16),n(l[3],16),1]:(l=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(t),l&&(c=[n(l[1]),n(l[2]),n(l[3]),1]))))}function o(i){var n;return h?(n=e(t),n.stops=[].concat(n.stops),be(h,function(t,e){n.stops[e]=[n.stops[e][0],t.get(i)]})):n=c&&!isNaN(c[0])?"rgb"===i?"rgb("+c[0]+","+c[1]+","+c[2]+")":"a"===i?c[3]:"rgba("+c.join(",")+")":t,n}function r(t){if(h)be(h,function(e){e.brighten(t)});else if(a(t)&&0!==t){var e;for(e=0;e<3;e++)c[e]+=n(255*t),c[e]<0&&(c[e]=0),c[e]>255&&(c[e]=255)}return this}function s(t){return c[3]=t,this}var l,h,c=[];return i(t),{get:o,brighten:r,rgba:c,setOpacity:s}};Y.prototype={init:function(t,e){var i=this;i.element="span"===e?m(e):ft.createElementNS(Ot,e),i.renderer=t,i.attrSetters={}},opacity:1,animate:function(t,i,n){var o=f(i,Q,!0);Me(this),o?(o=e(o),n&&(o.complete=n),Ce(this,t,o)):(this.attr(t),n&&n())},attr:function(t,e){var i,r,s,a,l,h,c,u,g,m=this,y=m.element,x=y.nodeName.toLowerCase(),v=m.renderer,b=m.attrSetters,k=m.shadows,w=m;if(o(t)&&d(e)&&(i=t,t={},t[i]=e),o(t))i=t,"circle"===x?i={x:"cx",y:"cy"}[i]||i:"strokeWidth"===i&&(i="stroke-width"),w=p(y,i)||m[i]||0,"d"!==i&&"visibility"!==i&&"fill"!==i&&(w=parseFloat(w));else{for(i in t)if(h=!1,r=t[i],s=b[i]&&b[i].call(m,r,i),s!==!1){if(s!==Z&&(r=s),"d"===i)r&&r.join&&(r=r.join(" ")),/(NaN| {2}|^$)/.test(r)&&(r="M 0 0");else if("x"===i&&"text"===x)for(a=0;a<y.childNodes.length;a++)l=y.childNodes[a],p(l,"x")===p(y,"x")&&p(l,"x",r);else if(!m.rotation||"x"!==i&&"y"!==i)if("fill"===i)r=v.color(r,y,i);else if("circle"!==x||"x"!==i&&"y"!==i)if("rect"===x&&"r"===i)p(y,{rx:r,ry:r}),h=!0;else if("translateX"===i||"translateY"===i||"rotation"===i||"verticalAlign"===i||"scaleX"===i||"scaleY"===i)g=!0,h=!0;else if("stroke"===i)r=v.color(r,y,i);else if("dashstyle"===i){if(i="stroke-dasharray",r=r&&r.toLowerCase(),"solid"===r)r=Jt;else if(r){for(r=r.replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(","),a=r.length;a--;)r[a]=n(r[a])*f(t["stroke-width"],m["stroke-width"]);r=r.join(",")}}else"width"===i?r=n(r):"align"===i?(i="text-anchor",r={left:"start",center:"middle",right:"end"}[r]):"title"===i&&(c=y.getElementsByTagName("title")[0],c||(c=ft.createElementNS(Ot,"title"),y.appendChild(c)),c.textContent=r);else i={x:"cx",y:"cy"}[i]||i;else g=!0;if("strokeWidth"===i&&(i="stroke-width"),"stroke-width"!==i&&"stroke"!==i||(m[i]=r,m.stroke&&m["stroke-width"]?(p(y,"stroke",m.stroke),p(y,"stroke-width",m["stroke-width"]),m.hasStroke=!0):"stroke-width"===i&&0===r&&m.hasStroke&&(y.removeAttribute("stroke"),m.hasStroke=!1),h=!0),m.symbolName&&/^(x|y|width|height|r|start|end|innerR|anchorX|anchorY)/.test(i)&&(u||(m.symbolAttr(t),u=!0),h=!0),k&&/^(width|height|visibility|x|y|d|transform|cx|cy|r)$/.test(i))for(a=k.length;a--;)p(k[a],i,"height"===i?bt(r-(k[a].cutHeight||0),0):r);("width"===i||"height"===i)&&"rect"===x&&r<0&&(r=0),m[i]=r,"text"===i?(r!==m.textStr&&delete m.bBox,m.textStr=r,m.added&&v.buildText(m)):h||p(y,i,r)}g&&m.updateTransform()}return w},addClass:function(t){var e=this.element,i=p(e,"class")||"";return i.indexOf(t)===-1&&p(e,"class",i+" "+t),this},symbolAttr:function(t){var e=this;be(["x","y","r","start","end","width","height","innerR","anchorX","anchorY"],function(i){e[i]=f(t[i],e[i])}),e.attr({d:e.renderer.symbols[e.symbolName](e.x,e.y,e.width,e.height,e)})},clip:function(t){return this.attr("clip-path",t?"url("+this.renderer.url+"#"+t.id+")":Jt)},crisp:function(t,e,i,n,o){var r,s,a=this,l={},h={};t=t||a.strokeWidth||a.attr&&a.attr("stroke-width")||0,s=yt(t)%2/2,h.x=xt(e||a.x||0)+s,h.y=xt(i||a.y||0)+s,h.width=xt((n||a.width||0)-2*s),h.height=xt((o||a.height||0)-2*s),h.strokeWidth=t;for(r in h)a[r]!==h[r]&&(a[r]=l[r]=h[r]);return l},css:function(e){var i,n=this,o=n.element,r=e&&e.width&&"text"===o.nodeName.toLowerCase(),s="",a=function(t,e){return"-"+e.toLowerCase()};if(e&&e.color&&(e.fill=e.color),e=t(n.styles,e),n.styles=e,Xt&&r&&delete e.width,Mt&&!Rt)r&&delete e.width,g(n.element,e);else{for(i in e)s+=i.replace(/([A-Z])/g,a)+":"+e[i]+";";p(o,"style",s)}return r&&n.added&&n.renderer.buildText(n),n},on:function(t,e){var i=this,n=i.element;return Wt&&"click"===t?(n.ontouchstart=function(t){i.touchEventFired=Date.now(),t.preventDefault(),e.call(n,t)},n.onclick=function(t){(Lt.indexOf("Android")===-1||Date.now()-(i.touchEventFired||0)>1100)&&e.call(n,t)}):n["on"+t]=e,this},setRadialReference:function(t){return this.element.radialReference=t,this},translate:function(t,e){return this.attr({translateX:t,translateY:e})},invert:function(){var t=this;return t.inverted=!0,t.updateTransform(),t},htmlCss:function(e){var i=this,n=i.element,o=e&&"SPAN"===n.tagName&&e.width;return o&&(delete e.width,i.textWidth=o,i.updateTransform()),i.styles=t(i.styles,e),g(i.element,e),i},htmlGetBBox:function(){var t=this,e=t.element,i=t.bBox;return i||("text"===e.nodeName&&(e.style.position=_t),i=t.bBox={x:e.offsetLeft,y:e.offsetTop,width:e.offsetWidth,height:e.offsetHeight}),i},htmlUpdateTransform:function(){if(!this.added)return void(this.alignOnAdd=!0);var t=this,e=t.renderer,i=t.element,o=t.translateX||0,r=t.translateY||0,s=t.x||0,a=t.y||0,l=t.textAlign||"left",h={left:0,center:.5,right:1}[l],c=l&&"left"!==l,p=t.shadows;if(g(i,{marginLeft:o,marginTop:r}),p&&be(p,function(t){g(t,{marginLeft:o+1,marginTop:r+1})}),t.inverted&&be(i.childNodes,function(t){e.invertChild(t,i)}),"SPAN"===i.tagName){var u,m,y,x,v=t.rotation,b=0,k=1,w=0,T=n(t.textWidth),S=t.xCorr||0,P=t.yCorr||0,A=[v,l,i.innerHTML,t.textWidth].join(",");A!==t.cTT&&(d(v)&&(b=v*At,k=Tt(b),w=St(b),t.setSpanRotation(v,w,k)),u=f(t.elemWidth,i.offsetWidth),m=f(t.elemHeight,i.offsetHeight),u>T&&/[ \-]/.test(i.textContent||i.innerText)&&(g(i,{width:T+qt,display:"block",whiteSpace:"normal"}),u=T),y=e.fontMetrics(i.style.fontSize).b,S=k<0&&-u,P=w<0&&-m,x=k*w<0,S+=w*y*(x?1-h:h),P-=k*y*(v?x?h:1-h:1),c&&(S-=u*h*(k<0?-1:1),v&&(P-=m*h*(w<0?-1:1)),g(i,{textAlign:l})),t.xCorr=S,t.yCorr=P),g(i,{left:s+S+qt,top:a+P+qt}),It&&(m=i.offsetHeight),t.cTT=A}},setSpanRotation:function(t){var e={},i=Mt?"-ms-transform":It?"-webkit-transform":zt?"MozTransform":Ct?"-o-transform":"";e[i]=e.transform="rotate("+t+"deg)",g(this.element,e)},updateTransform:function(){var t,e=this,i=e.translateX||0,n=e.translateY||0,o=e.scaleX,r=e.scaleY,s=e.inverted,a=e.rotation;s&&(i+=e.attr("width"),n+=e.attr("height")),t=["translate("+i+","+n+")"],s?t.push("rotate(90) scale(-1,1)"):a&&t.push("rotate("+a+" "+(e.x||0)+" "+(e.y||0)+")"),(d(o)||d(r))&&t.push("scale("+f(o,1)+" "+f(r,1)+")"),t.length&&p(e.element,"transform",t.join(" "))},toFront:function(){var t=this.element;return t.parentNode.appendChild(t),this},align:function(t,e,i){var n,r,s,a,l,h={},d=this.renderer,p=d.alignedObjects;return t?(this.alignOptions=t,this.alignByTranslate=e,i&&!o(i)||(this.alignTo=l=i||"renderer",c(p,this),p.push(this),i=null)):(t=this.alignOptions,e=this.alignByTranslate,l=this.alignTo),i=f(i,d[l],d),n=t.align,r=t.verticalAlign,s=(i.x||0)+(t.x||0),a=(i.y||0)+(t.y||0),"right"!==n&&"center"!==n||(s+=(i.width-(t.width||0))/{right:1,center:2}[n]),h[e?"translateX":"x"]=yt(s),"bottom"!==r&&"middle"!==r||(a+=(i.height-(t.height||0))/({bottom:1,middle:2}[r]||1)),h[e?"translateY":"y"]=yt(a),this[this.placed?"animate":"attr"](h),this.placed=!0,this.alignAttr=h,this},getBBox:function(){var e,i,n=this,o=n.bBox,r=n.renderer,s=n.rotation,a=n.element,l=n.styles,h=s*At;if(!o){if(a.namespaceURI===Ot||r.forExport){try{o=a.getBBox?t({},a.getBBox()):{width:a.offsetWidth,height:a.offsetHeight}}catch(c){}(!o||o.width<0)&&(o={width:0,height:0})}else o=n.htmlGetBBox();r.isSVG&&(e=o.width,i=o.height,Mt&&l&&"11px"===l.fontSize&&"22.7"===i.toPrecision(3)&&(o.height=i=14),s&&(o.width=wt(i*St(h))+wt(e*Tt(h)),o.height=wt(i*Tt(h))+wt(e*St(h)))),n.bBox=o}return o},show:function(){return this.attr({visibility:$t})},hide:function(){return this.attr({visibility:Zt})},fadeOut:function(t){var e=this;e.animate({opacity:0},{duration:t||150,complete:function(){e.hide()}})},add:function(t){var e,i,o,r,s=this.renderer,a=t||s,l=a.element||s.box,h=l.childNodes,c=this.element,u=p(c,"zIndex");if(t&&(this.parentGroup=t),this.parentInverted=t&&t.inverted,void 0!==this.textStr&&s.buildText(this),u&&(a.handleZ=!0,u=n(u)),a.handleZ)for(o=0;o<h.length;o++)if(e=h[o],i=p(e,"zIndex"),e!==c&&(n(i)>u||!d(u)&&d(i))){l.insertBefore(c,e),r=!0;break}return r||l.appendChild(c),this.added=!0,Ae(this,"add"),this},safeRemoveChild:function(t){var e=t.parentNode;e&&e.removeChild(t)},destroy:function(){var t,e,i,n=this,o=n.element||{},r=n.shadows,s=n.renderer.isSVG&&"SPAN"===o.nodeName&&o.parentNode;if(o.onclick=o.onmouseout=o.onmouseover=o.onmousemove=o.point=null,Me(n),n.clipPath&&(n.clipPath=n.clipPath.destroy()),n.stops){for(i=0;i<n.stops.length;i++)n.stops[i]=n.stops[i].destroy();n.stops=null}for(n.safeRemoveChild(o),r&&be(r,function(t){n.safeRemoveChild(t)});s&&0===s.childNodes.length;)t=s.parentNode,n.safeRemoveChild(s),s=t;n.alignTo&&c(n.renderer.alignedObjects,n);for(e in n)delete n[e];return null},shadow:function(t,e,i){var n,o,r,s,a,l,h=[],c=this.element;if(t){for(s=f(t.width,3),a=(t.opacity||.15)/s,l=this.parentInverted?"(-1,-1)":"("+f(t.offsetX,1)+", "+f(t.offsetY,1)+")",n=1;n<=s;n++)o=c.cloneNode(0),r=2*s+1-2*n,p(o,{isShadow:"true",stroke:t.color||"black","stroke-opacity":a*n,"stroke-width":r,transform:"translate"+l,fill:Jt}),i&&(p(o,"height",bt(p(o,"height")-r,0)),o.cutHeight=r),e?e.element.appendChild(o):c.parentNode.insertBefore(o,c),h.push(o);this.shadows=h}return this}};var Oe=function(){this.init.apply(this,arguments)};Oe.prototype={Element:Y,init:function(t,e,i,n){var o,r,s,a=this,l=location;o=a.createElement("svg").attr({version:"1.1"}),r=o.element,t.appendChild(r),t.innerHTML.indexOf("xmlns")===-1&&p(r,"xmlns",Ot),a.isSVG=!0,a.box=r,a.boxWrapper=o,a.alignedObjects=[],a.url=(zt||It)&&ft.getElementsByTagName("base").length?l.href.replace(/#.*?$/,"").replace(/([\('\)])/g,"\\$1").replace(/ /g,"%20"):"",s=this.createElement("desc").add(),s.element.appendChild(ft.createTextNode("Created with "+Ft+" "+Vt)),a.defs=this.createElement("defs").add(),a.forExport=n,a.gradients={},a.setSize(e,i,!1);var h,c;zt&&t.getBoundingClientRect&&(a.subPixelFix=h=function(){g(t,{left:0,top:0}),c=t.getBoundingClientRect(),g(t,{left:vt(c.left)-c.left+qt,top:vt(c.top)-c.top+qt})},h(),Se(gt,"resize",h))},isHidden:function(){return!this.boxWrapper.getBBox().width},destroy:function(){var t=this,e=t.defs;return t.box=null,t.boxWrapper=t.boxWrapper.destroy(),I(t.gradients||{}),t.gradients=null,e&&(t.defs=e.destroy()),t.subPixelFix&&Pe(gt,"resize",t.subPixelFix),t.alignedObjects=null,null},createElement:function(t){var e=new this.Element;return e.init(this,t),e},draw:function(){},buildText:function(t){for(var e=t.element,i=this,o=i.forExport,r=f(t.textStr,"").toString().replace(/<(b|strong)>/g,'<span style="font-weight:bold">').replace(/<(i|em)>/g,'<span style="font-style:italic">').replace(/<a/g,"<span").replace(/<\/(b|strong|i|em|a)>/g,"</span>").split(/<br.*?>/g),s=e.childNodes,a=/style="([^"]+)"/,l=/href="(http[^"]+)"/,h=p(e,"x"),c=t.styles,d=c&&c.width&&n(c.width),u=c&&c.lineHeight,m=s.length;m--;)e.removeChild(s[m]);d&&!t.added&&this.box.appendChild(e),""===r[r.length-1]&&r.pop(),be(r,function(r,s){var f,m=0;r=r.replace(/<span/g,"|||<span").replace(/<\/span>/g,"</span>|||"),f=r.split("|||"),be(f,function(r){if(""!==r||1===f.length){var y,x={},v=ft.createElementNS(Ot,"tspan");if(a.test(r)&&(y=r.match(a)[1].replace(/(;| |^)color([ :])/,"$1fill$2"),p(v,"style",y)),l.test(r)&&!o&&(p(v,"onclick",'location.href="'+r.match(l)[1]+'"'),g(v,{cursor:"pointer"})),r=(r.replace(/<(.|\n)*?>/g,"")||" ").replace(/&lt;/g,"<").replace(/&gt;/g,">")," "!==r&&(v.appendChild(ft.createTextNode(r)),m?x.dx=0:x.x=h,p(v,x),!m&&s&&(!Rt&&o&&g(v,{display:"block"}),p(v,"dy",u||i.fontMetrics(/px$/.test(v.style.fontSize)?v.style.fontSize:c.fontSize).h,It&&v.offsetHeight)),e.appendChild(v),m++,d))for(var b,k,w,T=r.replace(/([^\^])-/g,"$1- ").split(" "),S=t._clipHeight,P=[],A=n(u||16),L=1;T.length||P.length;)delete t.bBox,w=t.getBBox(),k=w.width,b=k>d,b&&1!==T.length?(v.removeChild(v.firstChild),P.unshift(T.pop())):(T=P,P=[],T.length&&(L++,S&&L*A>S?(T=["..."],t.attr("title",t.textStr)):(v=ft.createElementNS(Ot,"tspan"),p(v,{dy:A,x:h}),y&&p(v,"style",y),e.appendChild(v),k>d&&(d=k)))),T.length&&v.appendChild(ft.createTextNode(T.join(" ").replace(/- /g,"-")))}})})},button:function(i,n,o,r,s,a,l,h){var c,d,p,u,f,g,m=this.label(i,n,o,null,null,null,null,null,"button"),y=0,x="style",v={x1:0,y1:0,x2:0,y2:1};return s=e({"stroke-width":1,stroke:"#CCCCCC",fill:{linearGradient:v,stops:[[0,"#FEFEFE"],[1,"#F6F6F6"]]},r:2,padding:5,style:{color:"black"}},s),p=s[x],delete s[x],a=e(s,{stroke:"#68A",fill:{linearGradient:v,stops:[[0,"#FFF"],[1,"#ACF"]]}},a),u=a[x],delete a[x],l=e(s,{stroke:"#68A",fill:{linearGradient:v,stops:[[0,"#9BD"],[1,"#CDF"]]}},l),f=l[x],delete l[x],h=e(s,{style:{color:"#CCC"}},h),g=h[x],delete h[x],Se(m.element,Mt?"mouseover":"mouseenter",function(){3!==y&&m.attr(a).css(u)}),Se(m.element,Mt?"mouseout":"mouseleave",function(){3!==y&&(c=[s,a,l][y],d=[p,u,f][y],m.attr(c).css(d))}),m.setState=function(t){m.state=y=t,t?2===t?m.attr(l).css(f):3===t&&m.attr(h).css(g):m.attr(s).css(p)},m.on("click",function(){3!==y&&r.call(m)}).attr(s).css(t({cursor:"default"},p))},crispLine:function(t,e){return t[1]===t[4]&&(t[1]=t[4]=yt(t[1])-e%2/2),t[2]===t[5]&&(t[2]=t[5]=yt(t[2])+e%2/2),t},path:function(e){var i={fill:Jt};return s(e)?i.d=e:r(e)&&t(i,e),this.createElement("path").attr(i)},circle:function(t,e,i){var n=r(t)?t:{x:t,y:e,r:i};return this.createElement("circle").attr(n)},arc:function(t,e,i,n,o,s){var a;return r(t)&&(e=t.y,i=t.r,n=t.innerR,o=t.start,s=t.end,t=t.x),a=this.symbol("arc",t||0,e||0,i||0,i||0,{innerR:n||0,start:o||0,end:s||0}),a.r=i,a},rect:function(t,e,i,n,o,s){o=r(t)?t.r:o;var a=this.createElement("rect").attr({rx:o,ry:o,fill:Jt});return a.attr(r(t)?t:a.crisp(s,t,e,bt(i,0),bt(n,0)))},setSize:function(t,e,i){var n=this,o=n.alignedObjects,r=o.length;for(n.width=t,n.height=e,n.boxWrapper[f(i,!0)?"animate":"attr"]({width:t,height:e});r--;)o[r].align()},g:function(t){var e=this.createElement("g");return d(t)?e.attr({"class":Kt+t}):e},image:function(e,i,n,o,r){var s,a={preserveAspectRatio:Jt};return arguments.length>1&&t(a,{x:i,y:n,width:o,height:r}),s=this.createElement("image").attr(a),s.element.setAttributeNS?s.element.setAttributeNS("http://www.w3.org/1999/xlink","href",e):s.element.setAttribute("hc-svg-href",e),s},symbol:function(e,i,n,o,r,s){var a,l,h,c,d,p=this.symbols[e],u=p&&p(yt(i),yt(n),o,r,s),f=/^url\((.*?)\)$/;return u?(a=this.path(u),t(a,{symbolName:e,x:i,y:n,width:o,height:r}),s&&t(a,s)):f.test(e)&&(d=function(t,e){t.element&&(t.attr({width:e[0],height:e[1]}),t.alignByTranslate||t.translate(yt((o-e[0])/2),yt((r-e[1])/2)))},h=e.match(f)[1],c=Yt[h],a=this.image(h).attr({x:i,y:n}),a.isImg=!0,c?d(a,c):(a.attr({width:0,height:0}),l=m("img",{onload:function(){d(a,Yt[h]=[this.width,this.height])},src:h}))),a},symbols:{circle:function(t,e,i,n){var o=.166*i;return[Qt,t+i/2,e,"C",t+i+o,e,t+i+o,e+n,t+i/2,e+n,"C",t-o,e+n,t-o,e,t+i/2,e,"Z"]},square:function(t,e,i,n){return[Qt,t,e,te,t+i,e,t+i,e+n,t,e+n,"Z"]},triangle:function(t,e,i,n){return[Qt,t+i/2,e,te,t+i,e+n,t,e+n,"Z"]},"triangle-down":function(t,e,i,n){return[Qt,t,e,te,t+i,e,t+i/2,e+n,"Z"]},diamond:function(t,e,i,n){return[Qt,t+i/2,e,te,t+i,e+n/2,t+i/2,e+n,t,e+n/2,"Z"]},arc:function(t,e,i,n,o){var r=o.start,s=o.r||i||n,a=o.end-.001,l=o.innerR,h=o.open,c=Tt(r),d=St(r),p=Tt(a),u=St(a),f=o.end-r<Pt?0:1;return[Qt,t+s*c,e+s*d,"A",s,s,0,f,1,t+s*p,e+s*u,h?Qt:te,t+l*p,e+l*u,"A",l,l,0,f,0,t+l*c,e+l*d,h?"":"Z"]}},clipRect:function(t,e,i,n){var o,r=Kt+Et++,s=this.createElement("clipPath").attr({id:r}).add(this.defs);return o=this.rect(t,e,i,n,0).add(s),o.id=r,o.clipPath=s,o},color:function(t,i,n){var o,r,a,l,h,c,u,f,g,m,y,x=this,v=/^rgba/,b=[];if(t&&t.linearGradient?r="linearGradient":t&&t.radialGradient&&(r="radialGradient"),r){a=t[r],l=x.gradients,c=t.stops,g=i.radialReference,s(a)&&(t[r]=a={x1:a[0],y1:a[1],x2:a[2],y2:a[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===r&&g&&!d(a.gradientUnits)&&(a=e(a,{cx:g[0]-g[2]/2+a.cx*g[2],cy:g[1]-g[2]/2+a.cy*g[2],r:a.r*g[2],gradientUnits:"userSpaceOnUse"}));for(m in a)"id"!==m&&b.push(m,a[m]);for(m in c)b.push(c[m]);return b=b.join(","),l[b]?y=l[b].id:(a.id=y=Kt+Et++,l[b]=h=x.createElement(r).attr(a).add(x.defs),h.stops=[],be(c,function(t){var e;v.test(t[1])?(o=Be(t[1]),u=o.get("rgb"),f=o.get("a")):(u=t[1],f=1),e=x.createElement("stop").attr({offset:t[0],"stop-color":u,"stop-opacity":f}).add(h),h.stops.push(e)})),"url("+x.url+"#"+y+")"}return v.test(t)?(o=Be(t),p(i,n+"-opacity",o.get("a")),o.get("rgb")):(i.removeAttribute(n+"-opacity"),t)},text:function(t,e,i,n){var o,r=this,s=q.chart.style,a=Xt||!Rt&&r.forExport;return n&&!r.forExport?r.html(t,e,i):(e=yt(f(e,0)),i=yt(f(i,0)),o=r.createElement("text").attr({x:e,y:i,text:t}).css({fontFamily:s.fontFamily,fontSize:s.fontSize}),a&&o.css({position:_t}),o.x=e,o.y=i,o)},html:function(e,i,n){var o=q.chart.style,r=this.createElement("span"),s=r.attrSetters,a=r.element,l=r.renderer;return s.text=function(t){return t!==a.innerHTML&&delete this.bBox,a.innerHTML=t,
!1},s.x=s.y=s.align=function(t,e){return"align"===e&&(e="textAlign"),r[e]=t,r.htmlUpdateTransform(),!1},r.attr({text:e,x:yt(i),y:yt(n)}).css({position:_t,whiteSpace:"nowrap",fontFamily:o.fontFamily,fontSize:o.fontSize}),r.css=r.htmlCss,l.isSVG&&(r.add=function(e){var i,n,o=l.box.parentNode,s=[];if(e){if(i=e.div,!i){for(n=e;n;)s.push(n),n=n.parentGroup;be(s.reverse(),function(e){var n;i=e.div=e.div||m(jt,{className:p(e.element,"class")},{position:_t,left:(e.translateX||0)+qt,top:(e.translateY||0)+qt},i||o),n=i.style,t(e.attrSetters,{translateX:function(t){n.left=t+qt},translateY:function(t){n.top=t+qt},visibility:function(t,e){n[e]=t}})})}}else i=o;return i.appendChild(a),r.added=!0,r.alignOnAdd&&r.htmlUpdateTransform(),r}),r},fontMetrics:function(t){t=n(t||11);var e=t<24?t+4:yt(1.2*t),i=yt(.8*e);return{h:e,b:i}},label:function(i,n,o,r,s,a,l,h,c){function p(){var t,i,n=A.element.style;y=(void 0===x||void 0===v||P.styles.textAlign)&&A.getBBox(),P.width=(x||y.width||0)+2*C+M,P.height=(v||y.height||0)+2*C,w=C+S.fontMetrics(n&&n.fontSize).b,T&&(m||(t=yt(-L*C),i=h?-w:0,P.box=m=r?S.symbol(r,t,i,P.width,P.height):S.rect(t,i,P.width,P.height,0,I[ue]),m.add(P)),m.isImg||m.attr(e({width:P.width,height:P.height},I)),I=null)}function u(){var t,e=P.styles,i=e&&e.textAlign,n=M+C*(1-L);t=h?0:w,!d(x)||"center"!==i&&"right"!==i||(n+={center:.5,right:1}[i]*(x-y.width)),n===A.x&&t===A.y||A.attr({x:n,y:t}),A.x=n,A.y=t}function f(t,e){m?m.attr(t,e):I[t]=e}function g(){A.add(P),P.attr({text:i,x:n,y:o}),m&&d(s)&&P.attr({anchorX:s,anchorY:a})}var m,y,x,v,b,k,w,T,S=this,P=S.g(c),A=S.text("",0,0,l).attr({zIndex:1}),L=0,C=3,M=0,D=0,I={},z=P.attrSetters;Se(P,"add",g),z.width=function(t){return x=t,!1},z.height=function(t){return v=t,!1},z.padding=function(t){return d(t)&&t!==C&&(C=t,u()),!1},z.paddingLeft=function(t){return d(t)&&t!==M&&(M=t,u()),!1},z.align=function(t){return L={left:0,center:.5,right:1}[t],!1},z.text=function(t,e){return A.attr(e,t),p(),u(),!1},z[ue]=function(t,e){return T=!0,D=t%2/2,f(e,t),!1},z.stroke=z.fill=z.r=function(t,e){return"fill"===e&&(T=!0),f(e,t),!1},z.anchorX=function(t,e){return s=t,f(e,t+D-b),!1},z.anchorY=function(t,e){return a=t,f(e,t-k),!1},z.x=function(t){return P.x=t,t-=L*((x||y.width)+C),b=yt(t),P.attr("translateX",b),!1},z.y=function(t){return k=P.y=yt(t),P.attr("translateY",k),!1};var B=P.css;return t(P,{css:function(t){if(t){var i={};t=e(t),be(["fontSize","fontWeight","fontFamily","color","lineHeight","width","textDecoration","textShadow"],function(e){t[e]!==Z&&(i[e]=t[e],delete t[e])}),A.css(i)}return B.call(P,t)},getBBox:function(){return{width:y.width+2*C,height:y.height+2*C,x:y.x-C,y:y.y-C}},shadow:function(t){return m&&m.shadow(t),P},destroy:function(){Pe(P,"add",g),Pe(P.element,"mouseenter"),Pe(P.element,"mouseleave"),A&&(A=A.destroy()),m&&(m=m.destroy()),Y.prototype.destroy.call(P),P=S=p=u=f=g=null}})}},K=Oe;var Re,He;if(!Rt&&!Xt){Highcharts.VMLElement=He={init:function(t,e){var i=this,n=["<",e,' filled="f" stroked="f"'],o=["position: ",_t,";"],r=e===jt;("shape"===e||r)&&o.push("left:0;top:0;width:1px;height:1px;"),o.push("visibility: ",r?Zt:$t),n.push(' style="',o.join(""),'"/>'),e&&(n=r||"span"===e||"img"===e?n.join(""):t.prepVML(n),i.element=m(n)),i.renderer=t,i.attrSetters={}},add:function(t){var e=this,i=e.renderer,n=e.element,o=i.box,r=t&&t.inverted,s=t?t.element||t:o;return r&&i.invertChild(n,s),s.appendChild(n),e.added=!0,e.alignOnAdd&&!e.deferUpdateTransform&&e.updateTransform(),Ae(e,"add"),e},updateTransform:Y.prototype.htmlUpdateTransform,setSpanRotation:function(t,e,i){g(this.element,{filter:t?["progid:DXImageTransform.Microsoft.Matrix(M11=",i,", M12=",-e,", M21=",e,", M22=",i,", sizingMethod='auto expand')"].join(""):Jt})},pathToVML:function(t){for(var e,i=t.length,n=[];i--;)a(t[i])?n[i]=yt(10*t[i])-5:"Z"===t[i]?n[i]="x":(n[i]=t[i],!t.isArc||"wa"!==t[i]&&"at"!==t[i]||(e="wa"===t[i]?1:-1,n[i+5]===n[i+7]&&(n[i+7]-=e),n[i+6]===n[i+8]&&(n[i+8]-=e)));return n.join(" ")||"x"},attr:function(t,e){var i,n,r,s,l,h,c=this,u=c.element||{},f=u.style,g=u.nodeName,y=c.renderer,x=c.symbolName,v=c.shadows,b=c.attrSetters,k=c;if(o(t)&&d(e)&&(i=t,t={},t[i]=e),o(t))i=t,k="strokeWidth"===i||"stroke-width"===i?c.strokeweight:c[i];else for(i in t)if(n=t[i],h=!1,s=b[i]&&b[i].call(c,n,i),s!==!1&&null!==n){if(s!==Z&&(n=s),x&&/^(x|y|r|start|end|width|height|innerR|anchorX|anchorY)/.test(i))l||(c.symbolAttr(t),l=!0),h=!0;else if("d"===i){if(n=n||[],c.d=n.join(" "),u.path=n=c.pathToVML(n),v)for(r=v.length;r--;)v[r].path=v[r].cutOff?this.cutOffPath(n,v[r].cutOff):n;h=!0}else if("visibility"===i){if(v)for(r=v.length;r--;)v[r].style[i]=n;"DIV"===g&&(n=n===Zt?"-999em":0,Dt||(f[i]=n?$t:Zt),i="top"),f[i]=n,h=!0}else if("zIndex"===i)n&&(f[i]=n),h=!0;else if(ve(i,["x","y","width","height"])!==-1)c[i]=n,"x"===i||"y"===i?i={x:"left",y:"top"}[i]:n=bt(0,n),c.updateClipping?(c[i]=n,c.updateClipping()):f[i]=n,h=!0;else if("class"===i&&"DIV"===g)u.className=n;else if("stroke"===i)n=y.color(n,u,i),i="strokecolor";else if("stroke-width"===i||"strokeWidth"===i)u.stroked=!!n,i="strokeweight",c[i]=n,a(n)&&(n+=qt);else if("dashstyle"===i){var w=u.getElementsByTagName("stroke")[0]||m(y.prepVML(["<stroke/>"]),null,null,u);w[i]=n||"solid",c.dashstyle=n,h=!0}else"fill"===i?"SPAN"===g?f.color=n:"IMG"!==g&&(u.filled=n!==Jt,n=y.color(n,u,i,c),i="fillcolor"):"opacity"===i?h=!0:"shape"===g&&"rotation"===i?(c[i]=u.style[i]=n,u.style.left=-yt(St(n*At)+1)+qt,u.style.top=yt(Tt(n*At))+qt):"translateX"===i||"translateY"===i||"rotation"===i?(c[i]=n,c.updateTransform(),h=!0):"text"===i&&(this.bBox=null,u.innerHTML=n,h=!0);h||(Dt?u[i]=n:p(u,i,n))}return k},clip:function(t){var e,i,n=this;return t?(e=t.members,c(e,n),e.push(n),n.destroyClip=function(){c(e,n)},i=t.getCSS(n)):(n.destroyClip&&n.destroyClip(),i={clip:Dt?"inherit":"rect(auto)"}),n.css(i)},css:Y.prototype.htmlCss,safeRemoveChild:function(t){t.parentNode&&z(t)},destroy:function(){return this.destroyClip&&this.destroyClip(),Y.prototype.destroy.apply(this)},on:function(t,e){return this.element["on"+t]=function(){var t=gt.event;t.target=t.srcElement,e(t)},this},cutOffPath:function(t,e){var i;return t=t.split(/[ ,]/),i=t.length,9!==i&&11!==i||(t[i-4]=t[i-2]=n(t[i-2])-10*e),t.join(" ")},shadow:function(t,e,i){var o,r,s,a,l,h,c,d=[],p=this.element,u=this.renderer,g=p.style,y=p.path;if(y&&"string"!=typeof y.value&&(y="x"),l=y,t){for(h=f(t.width,3),c=(t.opacity||.15)/h,o=1;o<=3;o++)a=2*h+1-2*o,i&&(l=this.cutOffPath(y.value,a+.5)),s=['<shape isShadow="true" strokeweight="',a,'" filled="false" path="',l,'" coordsize="10 10" style="',p.style.cssText,'" />'],r=m(u.prepVML(s),null,{left:n(g.left)+f(t.offsetX,1),top:n(g.top)+f(t.offsetY,1)}),i&&(r.cutOff=a+1),s=['<stroke color="',t.color||"black",'" opacity="',c*o,'"/>'],m(u.prepVML(s),null,null,r),e?e.element.appendChild(r):p.parentNode.insertBefore(r,p),d.push(r);this.shadows=d}return this}},He=y(Y,He);var Xe={Element:He,isIE8:Lt.indexOf("MSIE 8.0")>-1,init:function(t,e,i){var n,o,r=this;r.alignedObjects=[],n=r.createElement(jt),o=n.element,o.style.position=Ut,t.appendChild(n.element),r.isVML=!0,r.box=o,r.boxWrapper=n,r.setSize(e,i,!1),ft.namespaces.hcv||(ft.namespaces.add("hcv","urn:schemas-microsoft-com:vml"),(ft.styleSheets.length?ft.styleSheets[0]:ft.createStyleSheet()).cssText+="hcv\\:fill, hcv\\:path, hcv\\:shape, hcv\\:stroke{ behavior:url(#default#VML); display: inline-block; } ")},isHidden:function(){return!this.box.offsetWidth},clipRect:function(e,i,n,o){var s=this.createElement(),a=r(e);return t(s,{members:[],left:(a?e.x:e)+1,top:(a?e.y:i)+1,width:(a?e.width:n)-1,height:(a?e.height:o)-1,getCSS:function(e){var i=e.element,n=i.nodeName,o="shape"===n,r=e.inverted,s=this,a=s.top-(o?i.offsetTop:0),l=s.left,h=l+s.width,c=a+s.height,d={clip:"rect("+yt(r?l:a)+"px,"+yt(r?c:h)+"px,"+yt(r?h:c)+"px,"+yt(r?a:l)+"px)"};return!r&&Dt&&"DIV"===n&&t(d,{width:h+qt,height:c+qt}),d},updateClipping:function(){be(s.members,function(t){t.css(s.getCSS(t))})}})},color:function(t,e,i,n){var o,r,s,a=this,l=/^rgba/,h=Jt;if(t&&t.linearGradient?s="gradient":t&&t.radialGradient&&(s="pattern"),s){var c,d,p,u,f,g,y,x,v,b,k,w,T=t.linearGradient||t.radialGradient,S="",P=t.stops,A=[],L=function(){r=['<fill colors="'+A.join(",")+'" opacity="',x,'" o:opacity2="',y,'" type="',s,'" ',S,'focus="100%" method="any" />'],m(a.prepVML(r),null,null,e)};if(k=P[0],w=P[P.length-1],k[0]>0&&P.unshift([0,k[1]]),w[0]<1&&P.push([1,w[1]]),be(P,function(t,e){l.test(t[1])?(o=Be(t[1]),c=o.get("rgb"),d=o.get("a")):(c=t[1],d=1),A.push(100*t[0]+"% "+c),e?(x=d,v=c):(y=d,b=c)}),"fill"===i)if("gradient"===s)p=T.x1||T[0]||0,u=T.y1||T[1]||0,f=T.x2||T[2]||0,g=T.y2||T[3]||0,S='angle="'+(90-180*mt.atan((g-u)/(f-p))/Pt)+'"',L();else{var C,M=T.r,D=2*M,I=2*M,z=T.cx,B=T.cy,O=e.radialReference,R=function(){O&&(C=n.getBBox(),z+=(O[0]-C.x)/C.width-.5,B+=(O[1]-C.y)/C.height-.5,D*=O[2]/C.width,I*=O[2]/C.height),S='src="'+q.global.VMLRadialGradientURL+'" size="'+D+","+I+'" origin="0.5,0.5" position="'+z+","+B+'" color2="'+b+'" ',L()};n.added?R():Se(n,"add",R),h=v}else h=c}else if(l.test(t)&&"IMG"!==e.tagName)o=Be(t),r=["<",i,' opacity="',o.get("a"),'"/>'],m(this.prepVML(r),null,null,e),h=o.get("rgb");else{var H=e.getElementsByTagName(i);H.length&&(H[0].opacity=1,H[0].type="solid"),h=t}return h},prepVML:function(t){var e="display:inline-block;behavior:url(#default#VML);",i=this.isIE8;return t=t.join(""),i?(t=t.replace("/>",' xmlns="urn:schemas-microsoft-com:vml" />'),t=t.indexOf('style="')===-1?t.replace("/>",' style="'+e+'" />'):t.replace('style="','style="'+e)):t=t.replace("<","<hcv:"),t},text:Oe.prototype.html,path:function(e){var i={coordsize:"10 10"};return s(e)?i.d=e:r(e)&&t(i,e),this.createElement("shape").attr(i)},circle:function(t,e,i){var n=this.symbol("circle");return r(t)&&(i=t.r,e=t.y,t=t.x),n.isCircle=!0,n.r=i,n.attr({x:t,y:e})},g:function(t){var e,i;return t&&(i={className:Kt+t,"class":Kt+t}),e=this.createElement(jt).attr(i)},image:function(t,e,i,n,o){var r=this.createElement("img").attr({src:t});return arguments.length>1&&r.attr({x:e,y:i,width:n,height:o}),r},rect:function(t,e,i,n,o,s){var a=this.symbol("rect");return a.r=r(t)?t.r:o,a.attr(r(t)?t:a.crisp(s,t,e,bt(i,0),bt(n,0)))},invertChild:function(t,e){var i=e.style;g(t,{flip:"x",left:n(i.width)-1,top:n(i.height)-1,rotation:-90})},symbols:{arc:function(t,e,i,n,o){var r,s=o.start,a=o.end,l=o.r||i||n,h=o.innerR,c=Tt(s),d=St(s),p=Tt(a),u=St(a);return a-s===0?["x"]:(r=["wa",t-l,e-l,t+l,e+l,t+l*c,e+l*d,t+l*p,e+l*u],o.open&&!h&&r.push("e",Qt,t,e),r.push("at",t-h,e-h,t+h,e+h,t+h*p,e+h*u,t+h*c,e+h*d,"x","e"),r.isArc=!0,r)},circle:function(t,e,i,n,o){return o&&(i=n=2*o.r),o&&o.isCircle&&(t-=i/2,e-=n/2),["wa",t,e,t+i,e+n,t+i,e+n/2,t+i,e+n/2,"e"]},rect:function(t,e,i,n,o){var r,s,a=t+i,l=e+n;return d(o)&&o.r?(s=kt(o.r,i,n),r=[Qt,t+s,e,te,a-s,e,"wa",a-2*s,e,a,e+2*s,a-s,e,a,e+s,te,a,l-s,"wa",a-2*s,l-2*s,a,l,a,l-s,a-s,l,te,t+s,l,"wa",t,l-2*s,t+2*s,l,t+s,l,t,l-s,te,t,e+s,"wa",t,e,t+2*s,e+2*s,t,e+s,t+s,e,"x","e"]):r=Oe.prototype.symbols.square.apply(0,arguments),r}}};Highcharts.VMLRenderer=Re=function(){this.init.apply(this,arguments)},Re.prototype=e(Oe.prototype,Xe),K=Re}var We,Ye;Xt&&(Highcharts.CanVGRenderer=We=function(){Ot="http://www.w3.org/1999/xhtml"},We.prototype.symbols={},Ye=function(){function t(){var t,i=e.length;for(t=0;t<i;t++)e[t]();e=[]}var e=[];return{push:function(i,n){0===e.length&&xe(n,t),e.push(i)}}}(),K=We),E.prototype={addLabel:function(){var e,i,n,o,r=this,s=r.axis,l=s.options,c=s.chart,p=s.horiz,u=s.categories,g=s.series[0]&&s.series[0].names,m=r.pos,y=l.labels,x=s.tickPositions,v=p&&u&&!y.step&&!y.staggerLines&&!y.rotation&&c.plotWidth/x.length||!p&&(c.margin[3]||.33*c.chartWidth),b=m===x[0],k=m===x[x.length-1],w=u?f(u[m],g&&g[m],m):m,T=r.label,S=x.info;s.isDatetimeAxis&&S&&(o=l.dateTimeLabelFormats[S.higherRanks[m]||S.unitName]),r.isFirst=b,r.isLast=k,e=s.labelFormatter.call({axis:s,chart:c,isFirst:b,isLast:k,dateTimeLabelFormat:o,value:s.isLog?O(h(w)):w}),i=v&&{width:bt(1,yt(v-2*(y.padding||10)))+qt},i=t(i,y.style),d(T)?T&&T.attr({text:e}).css(i):(n={align:s.labelAlign},a(y.rotation)&&(n.rotation=y.rotation),v&&y.ellipsis&&(n._clipHeight=s.len/x.length),r.label=d(e)&&y.enabled?c.renderer.text(e,0,0,y.useHTML).attr(n).css(i).add(s.labelGroup):null)},getLabelSize:function(){var t=this.label,e=this.axis;return t?(this.labelBBox=t.getBBox())[e.horiz?"height":"width"]:0},getLabelSides:function(){var t=this.labelBBox,e=this.axis,i=e.options,n=i.labels,o=t.width,r=o*{left:0,center:.5,right:1}[e.labelAlign]-n.x;return[-r,o-r]},handleOverflow:function(t,e){var i=!0,n=this.axis,o=n.chart,r=this.isFirst,s=this.isLast,a=e.x,l=n.reversed,h=n.tickPositions;if(r||s){var c=this.getLabelSides(),d=c[0],p=c[1],u=o.plotLeft,f=u+n.len,g=n.ticks[h[t+(r?1:-1)]],m=g&&g.label.xy&&g.label.xy.x+g.getLabelSides()[r?0:1];r&&!l||s&&l?a+d<u&&(a=u-d,g&&a+p>m&&(i=!1)):a+p>f&&(a=f-p,g&&a+d<m&&(i=!1)),e.x=a}return i},getPosition:function(t,e,i,n){var o=this.axis,r=o.chart,s=n&&r.oldChartHeight||r.chartHeight;return{x:t?o.translate(e+i,null,null,n)+o.transB:o.left+o.offset+(o.opposite?(n&&r.oldChartWidth||r.chartWidth)-o.right-o.left:0),y:t?s-o.bottom+o.offset-(o.opposite?o.height:0):s-o.translate(e+i,null,null,n)-o.transB}},getLabelPosition:function(t,e,i,n,o,r,s,a){var l=this.axis,h=l.transA,c=l.reversed,p=l.staggerLines,u=l.chart.renderer.fontMetrics(o.style.fontSize).b,f=o.rotation;return t=t+o.x-(r&&n?r*h*(c?-1:1):0),e=e+o.y-(r&&!n?r*h*(c?1:-1):0),f&&2===l.side&&(e-=u-u*Tt(f*At)),d(o.y)||f||(e+=u-i.getBBox().height/2),p&&(e+=s/(a||1)%p*(l.labelOffset/p)),{x:t,y:e}},getMarkPath:function(t,e,i,n,o,r){return r.crispLine([Qt,t,e,te,t+(o?0:-i),e+(o?i:0)],n)},render:function(t,e,i){var n,o,r,s=this,a=s.axis,l=a.options,h=a.chart,c=h.renderer,d=a.horiz,p=s.type,u=s.label,g=s.pos,m=l.labels,y=s.gridLine,x=p?p+"Grid":"grid",v=p?p+"Tick":"tick",b=l[x+"LineWidth"],k=l[x+"LineColor"],w=l[x+"LineDashStyle"],T=l[v+"Length"],S=l[v+"Width"]||0,P=l[v+"Color"],A=l[v+"Position"],L=s.mark,C=m.step,M=!0,D=a.tickmarkOffset,I=s.getPosition(d,g,D,e),z=I.x,B=I.y,O=d&&z===a.pos+a.len||!d&&B===a.pos?-1:1,R=a.staggerLines;this.isActive=!0,b&&(n=a.getPlotLinePath(g+D,b*O,e,!0),y===Z&&(r={stroke:k,"stroke-width":b},w&&(r.dashstyle=w),p||(r.zIndex=1),e&&(r.opacity=0),s.gridLine=y=b?c.path(n).attr(r).add(a.gridGroup):null),!e&&y&&n&&y[s.isNew?"attr":"animate"]({d:n,opacity:i})),S&&T&&("inside"===A&&(T=-T),a.opposite&&(T=-T),o=s.getMarkPath(z,B,T,S*O,d,c),L?L.animate({d:o,opacity:i}):s.mark=c.path(o).attr({stroke:P,"stroke-width":S,opacity:i}).add(a.axisGroup)),u&&!isNaN(z)&&(u.xy=I=s.getLabelPosition(z,B,u,d,m,D,t,C),s.isFirst&&!s.isLast&&!f(l.showFirstLabel,1)||s.isLast&&!s.isFirst&&!f(l.showLastLabel,1)?M=!1:R||!d||"justify"!==m.overflow||s.handleOverflow(t,I)||(M=!1),C&&t%C&&(M=!1),M&&!isNaN(I.y)?(I.opacity=i,u[s.isNew?"attr":"animate"](I),s.isNew=!1):u.attr("y",-9999))},destroy:function(){I(this,this.axis)}},G.prototype={render:function(){var t,i,n,o,r,s,a,h=this,c=h.axis,p=c.horiz,u=(c.pointRange||0)/2,g=h.options,m=g.label,y=h.label,x=g.width,v=g.to,b=g.from,k=d(b)&&d(v),w=g.value,T=g.dashStyle,S=h.svgElem,P=[],A=g.color,L=g.zIndex,C=g.events,I=c.chart.renderer;if(c.isLog&&(b=l(b),v=l(v),w=l(w)),x)P=c.getPlotLinePath(w,x),a={stroke:A,"stroke-width":x},T&&(a.dashstyle=T);else{if(!k)return;b=bt(b,c.min-u),v=kt(v,c.max+u),P=c.getPlotBandPath(b,v,g),a={fill:A},g.borderWidth&&(a.stroke=g.borderColor,a["stroke-width"]=g.borderWidth)}if(d(L)&&(a.zIndex=L),S)P?S.animate({d:P},null,S.onGetPath):(S.hide(),S.onGetPath=function(){S.show()});else if(P&&P.length&&(h.svgElem=S=I.path(P).attr(a).add(),C)){t=function(t){S.on(t,function(e){C[t].apply(h,[e])})};for(i in C)t(i)}return m&&d(m.text)&&P&&P.length&&c.width>0&&c.height>0?(m=e({align:p&&k&&"center",x:p?!k&&4:10,verticalAlign:!p&&k&&"middle",y:p?k?16:10:k?6:-4,rotation:p&&!k&&90},m),y||(h.label=y=I.text(m.text,0,0,m.useHTML).attr({align:m.textAlign||m.align,rotation:m.rotation,zIndex:L}).css(m.style).add()),n=[P[1],P[4],f(P[6],P[1])],o=[P[2],P[5],f(P[7],P[2])],r=M(n),s=M(o),y.align(m,!1,{x:r,y:s,width:D(n)-r,height:D(o)-s}),y.show()):y&&y.hide(),h},destroy:function(){c(this.axis.plotLinesAndBands,this),delete this.axis,I(this)}},N.prototype={destroy:function(){I(this,this.axis)},render:function(t){var e=this.options,i=e.format,n=i?w(i,this):e.formatter.call(this);this.label?this.label.attr({text:n,visibility:Zt}):this.label=this.axis.chart.renderer.text(n,0,0,e.useHTML).css(e.style).attr({align:this.textAlign,rotation:e.rotation,visibility:Zt}).add(t)},setOffset:function(t,e){var i,n=this,o=n.axis,r=o.chart,s=r.inverted,a=this.isNegative,l=o.translate(this.percent?100:this.total,0,0,0,1),h=o.translate(0),c=wt(l-h),d=r.xAxis[0].translate(this.x)+t,p=r.plotHeight,u={x:s?a?l:l-c:d,y:s?p-d-e:a?p-l-c:p-l,width:s?c:e,height:s?e:c},f=this.label;f&&(f.align(this.alignOptions,null,u),i=f.alignAttr,f.attr({visibility:this.options.crop===!1||r.isInsidePlot(i.x,i.y)?Rt?"inherit":$t:Zt}))}},F.prototype={defaultOptions:{dateTimeLabelFormats:{millisecond:"%H:%M:%S.%L",second:"%H:%M:%S",minute:"%H:%M",hour:"%H:%M",day:"%e. %b",week:"%e. %b",month:"%b '%y",year:"%Y"},endOnTick:!1,gridLineColor:"#C0C0C0",labels:De,lineColor:"#C0D0E0",lineWidth:1,minPadding:.01,maxPadding:.01,minorGridLineColor:"#E0E0E0",minorGridLineWidth:1,minorTickColor:"#A0A0A0",minorTickLength:2,minorTickPosition:"outside",startOfWeek:1,startOnTick:!1,tickColor:"#C0D0E0",tickLength:5,tickmarkPlacement:"between",tickPixelInterval:100,tickPosition:"outside",tickWidth:1,title:{align:"middle",style:{color:"#4d759e",fontWeight:"bold"}},type:"linear"},defaultYAxisOptions:{endOnTick:!0,gridLineWidth:1,tickPixelInterval:72,showLastLabel:!0,labels:{x:-8,y:3},lineWidth:0,maxPadding:.05,minPadding:.05,startOnTick:!0,tickWidth:0,title:{rotation:270,text:"Values"},stackLabels:{enabled:!1,formatter:function(){return x(this.total,-1)},style:De.style}},defaultLeftAxisOptions:{labels:{x:-8,y:null},title:{rotation:270}},defaultRightAxisOptions:{labels:{x:8,y:null},title:{rotation:90}},defaultBottomAxisOptions:{labels:{x:0,y:14},title:{rotation:0}},defaultTopAxisOptions:{labels:{x:0,y:-5},title:{rotation:0}},init:function(t,e){var i=e.isX,n=this;n.horiz=t.inverted?!i:i,n.isXAxis=i,n.xOrY=i?"x":"y",n.opposite=e.opposite,n.side=n.horiz?n.opposite?0:2:n.opposite?1:3,n.setOptions(e);var o=this.options,r=o.type,s="datetime"===r;n.labelFormatter=o.labels.formatter||n.defaultLabelFormatter,n.userOptions=e,n.minPixelPadding=0,n.chart=t,n.reversed=o.reversed,n.zoomEnabled=o.zoomEnabled!==!1,n.categories=o.categories||"category"===r,n.isLog="logarithmic"===r,n.isDatetimeAxis=s,n.isLinked=d(o.linkedTo),n.tickmarkOffset=n.categories&&"between"===o.tickmarkPlacement?.5:0,n.ticks={},n.minorTicks={},n.plotLinesAndBands=[],n.alternateBands={},n.len=0,n.minRange=n.userMinRange=o.minRange||o.maxZoom,n.range=o.range,n.offset=o.offset||0,n.stacks={},n.oldStacks={},n.stackExtremes={},n.max=null,n.min=null;var a,c=n.options.events;ve(n,t.axes)===-1&&(t.axes.push(n),t[i?"xAxis":"yAxis"].push(n)),n.series=n.series||[],t.inverted&&i&&n.reversed===Z&&(n.reversed=!0),n.removePlotBand=n.removePlotBandOrLine,n.removePlotLine=n.removePlotBandOrLine;for(a in c)Se(n,a,c[a]);n.isLog&&(n.val2lin=l,n.lin2val=h)},setOptions:function(t){this.options=e(this.defaultOptions,this.isXAxis?{}:this.defaultYAxisOptions,[this.defaultTopAxisOptions,this.defaultRightAxisOptions,this.defaultBottomAxisOptions,this.defaultLeftAxisOptions][this.side],e(q[this.isXAxis?"xAxis":"yAxis"],t))},update:function(i,n){var o=this.chart;i=o.options[this.xOrY+"Axis"][this.options.index]=e(this.userOptions,i),this.destroy(!0),this._addedPlotLB=this.userMin=this.userMax=Z,this.init(o,t(i,{events:Z})),o.isDirtyBox=!0,f(n,!0)&&o.redraw()},remove:function(t){var e=this.chart,i=this.xOrY+"Axis";be(this.series,function(t){t.remove(!1)}),c(e.axes,this),c(e[i],this),e.options[i].splice(this.options.index,1),be(e[i],function(t,e){t.options.index=e}),this.destroy(),e.isDirtyBox=!0,f(t,!0)&&e.redraw()},defaultLabelFormatter:function(){var t,e,i=this.axis,n=this.value,o=i.categories,r=this.dateTimeLabelFormat,s=q.lang.numericSymbols,a=s&&s.length,l=i.options.labels.format,h=i.isLog?n:i.tickInterval;if(l)e=w(l,this);else if(o)e=n;else if(r)e=J(r,n);else if(a&&h>=1e3)for(;a--&&e===Z;)t=Math.pow(1e3,a+1),h>=t&&null!==s[a]&&(e=x(n/t,-1)+s[a]);return e===Z&&(e=n>=1e3?x(n,0):x(n,-1)),e},getSeriesExtremes:function(){var t=this,e=t.chart;t.hasVisibleSeries=!1,t.dataMin=t.dataMax=null,t.stackExtremes={},t.buildStacks(),be(t.series,function(i){if(i.visible||!e.options.chart.ignoreHiddenSeries){var n,o,r,s=i.options,a=s.threshold;t.hasVisibleSeries=!0,t.isLog&&a<=0&&(a=null),t.isXAxis?(n=i.xData,n.length&&(t.dataMin=kt(f(t.dataMin,n[0]),M(n)),t.dataMax=bt(f(t.dataMax,n[0]),D(n)))):(i.getExtremes(),r=i.dataMax,o=i.dataMin,d(o)&&d(r)&&(t.dataMin=kt(f(t.dataMin,o),o),t.dataMax=bt(f(t.dataMax,r),r)),d(a)&&(t.dataMin>=a?(t.dataMin=a,t.ignoreMinPadding=!0):t.dataMax<a&&(t.dataMax=a,t.ignoreMaxPadding=!0)))}})},translate:function(t,e,i,n,o,r){var s,l=this,h=l.len,c=1,d=0,p=n?l.oldTransA:l.transA,u=n?l.oldMin:l.min,f=l.minPixelPadding,g=(l.options.ordinal||l.isLog&&o)&&l.lin2val;return p||(p=l.transA),i&&(c*=-1,d=h),l.reversed&&(c*=-1,d-=c*h),e?(t=t*c+d,t-=f,s=t/p+u,g&&(s=l.lin2val(s))):(g&&(t=l.val2lin(t)),"between"===r&&(r=.5),s=c*(t-u)*p+d+c*f+(a(r)?p*r*l.pointRange:0)),s},toPixels:function(t,e){return this.translate(t,!1,!this.horiz,null,!0)+(e?0:this.pos)},toValue:function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,null,!0)},getPlotLinePath:function(t,e,i,n){var o,r,s,a,l,h=this,c=h.chart,d=h.left,p=h.top,u=h.translate(t,null,null,i),f=i&&c.oldChartHeight||c.chartHeight,g=i&&c.oldChartWidth||c.chartWidth,m=h.transB;return o=s=yt(u+m),r=a=yt(f-u-m),isNaN(u)?l=!0:h.horiz?(r=p,a=f-h.bottom,(o<d||o>d+h.width)&&(l=!0)):(o=d,s=g-h.right,(r<p||r>p+h.height)&&(l=!0)),l&&!n?null:c.renderer.crispLine([Qt,o,r,te,s,a],e||0)},getPlotBandPath:function(t,e){var i=this.getPlotLinePath(e),n=this.getPlotLinePath(t);return n&&i?n.push(i[4],i[5],i[1],i[2]):n=null,n},getLinearTickPositions:function(t,e,i){var n,o,r=O(xt(e/t)*t),s=O(vt(i/t)*t),a=[];for(n=r;n<=s&&(a.push(n),n=O(n+t),n!==o);)o=n;return a},getLogTickPositions:function(t,e,i,n){var o=this,r=o.options,s=o.len,a=[];if(n||(o._minorAutoInterval=null),t>=.5)t=yt(t),a=o.getLinearTickPositions(t,e,i);else if(t>=.08){var c,d,p,u,g,m,y,x=xt(e);for(c=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],d=x;d<i+1&&!y;d++)for(u=c.length,p=0;p<u&&!y;p++)g=l(h(d)*c[p]),g>e&&(!n||m<=i)&&a.push(m),m>i&&(y=!0),m=g}else{var v=h(e),b=h(i),k=r[n?"minorTickInterval":"tickInterval"],w="auto"===k?null:k,P=r.tickPixelInterval/(n?5:1),A=n?s/o.tickPositions.length:s;t=f(w,o._minorAutoInterval,(b-v)*P/(A||1)),t=S(t,null,T(t)),a=Te(o.getLinearTickPositions(t,v,b),l),n||(o._minorAutoInterval=t/5)}return n||(o.tickInterval=t),a},getMinorTickPositions:function(){var t,e,i,n=this,o=n.options,r=n.tickPositions,s=n.minorTickInterval,a=[];if(n.isLog)for(i=r.length,e=1;e<i;e++)a=a.concat(n.getLogTickPositions(s,r[e-1],r[e],!0));else if(n.isDatetimeAxis&&"auto"===o.minorTickInterval)a=a.concat(A(P(s),n.min,n.max,o.startOfWeek)),a[0]<n.min&&a.shift();else for(t=n.min+(r[0]-n.min)%s;t<=n.max;t+=s)a.push(t);return a},adjustForMinRange:function(){var t,e,i,n,o,r,s,a,l=this,h=l.options,c=l.min,p=l.max,u=l.dataMax-l.dataMin>=l.minRange;if(l.isXAxis&&l.minRange===Z&&!l.isLog&&(d(h.min)||d(h.max)?l.minRange=null:(be(l.series,function(t){for(o=t.xData,r=t.xIncrement?1:o.length-1,i=r;i>0;i--)n=o[i]-o[i-1],(e===Z||n<e)&&(e=n)}),l.minRange=kt(5*e,l.dataMax-l.dataMin))),p-c<l.minRange){var g=l.minRange;t=(g-p+c)/2,s=[c-t,f(h.min,c-t)],u&&(s[2]=l.dataMin),c=D(s),a=[c+g,f(h.max,c+g)],u&&(a[2]=l.dataMax),p=M(a),p-c<g&&(s[0]=p-g,s[1]=f(h.min,p-g),c=D(s))}l.min=c,l.max=p},setAxisTranslation:function(t){var e,i,n=this,r=n.max-n.min,s=0,a=0,l=0,h=n.linkedParent,c=n.transA;n.isXAxis&&(h?(a=h.minPointOffset,l=h.pointRangePadding):be(n.series,function(t){var i=t.pointRange,n=t.options.pointPlacement,h=t.closestPointRange;i>r&&(i=0),s=bt(s,i),a=bt(a,o(n)?0:i/2),l=bt(l,"on"===n?0:i),!t.noSharedTooltip&&d(h)&&(e=d(e)?kt(e,h):h)}),i=n.ordinalSlope&&e?n.ordinalSlope/e:1,n.minPointOffset=a*=i,n.pointRangePadding=l*=i,n.pointRange=kt(s,r),n.closestPointRange=e),t&&(n.oldTransA=c),n.translationSlope=n.transA=c=n.len/(r+l||1),n.transB=n.horiz?n.left:n.bottom,n.minPixelPadding=c*a},setTickPositions:function(t){var e,i,n,o,r=this,s=r.chart,a=r.options,h=r.isLog,c=r.isDatetimeAxis,p=r.isXAxis,u=r.isLinked,g=r.options.tickPositioner,m=a.maxPadding,y=a.minPadding,x=a.tickInterval,v=a.minTickInterval,b=a.tickPixelInterval,k=r.categories;if(u?(r.linkedParent=s[p?"xAxis":"yAxis"][a.linkedTo],i=r.linkedParent.getExtremes(),r.min=f(i.min,i.dataMin),r.max=f(i.max,i.dataMax),a.type!==r.linkedParent.options.type&&B(11,1)):(r.min=f(r.userMin,a.min,r.dataMin),r.max=f(r.userMax,a.max,r.dataMax)),h&&(!t&&kt(r.min,f(r.dataMin,r.min))<=0&&B(10,1),r.min=O(l(r.min)),r.max=O(l(r.max))),r.range&&(r.userMin=r.min=bt(r.min,r.max-r.range),r.userMax=r.max,t&&(r.range=null)),r.beforePadding&&r.beforePadding(),r.adjustForMinRange(),k||r.usePercentage||u||!d(r.min)||!d(r.max)||(e=r.max-r.min,e&&(d(a.min)||d(r.userMin)||!y||!(r.dataMin<0)&&r.ignoreMinPadding||(r.min-=e*y),d(a.max)||d(r.userMax)||!m||!(r.dataMax>0)&&r.ignoreMaxPadding||(r.max+=e*m))),r.min===r.max||void 0===r.min||void 0===r.max?r.tickInterval=1:u&&!x&&b===r.linkedParent.options.tickPixelInterval?r.tickInterval=r.linkedParent.tickInterval:(r.tickInterval=f(x,k?1:(r.max-r.min)*b/bt(r.len,b)),!d(x)&&r.len<b&&!this.isRadial&&(o=!0,r.tickInterval/=4)),p&&!t&&be(r.series,function(t){t.processData(r.min!==r.oldMin||r.max!==r.oldMax)}),r.setAxisTranslation(!0),r.beforeSetTickPositions&&r.beforeSetTickPositions(),r.postProcessTickInterval&&(r.tickInterval=r.postProcessTickInterval(r.tickInterval)),r.pointRange&&(r.tickInterval=bt(r.pointRange,r.tickInterval)),!x&&r.tickInterval<v&&(r.tickInterval=v),c||h||x||(r.tickInterval=S(r.tickInterval,null,T(r.tickInterval),a)),r.minorTickInterval="auto"===a.minorTickInterval&&r.tickInterval?r.tickInterval/5:a.minorTickInterval,r.tickPositions=n=a.tickPositions?[].concat(a.tickPositions):g&&g.apply(r,[r.min,r.max]),n||(!r.ordinalPositions&&(r.max-r.min)/r.tickInterval>bt(2*r.len,200)&&B(19,!0),n=c?(r.getNonLinearTimeTicks||A)(P(r.tickInterval,a.units),r.min,r.max,a.startOfWeek,r.ordinalPositions,r.closestPointRange,!0):h?r.getLogTickPositions(r.tickInterval,r.min,r.max):r.getLinearTickPositions(r.tickInterval,r.min,r.max),o&&n.splice(1,n.length-2),r.tickPositions=n),!u){var w,L=n[0],C=n[n.length-1],M=r.minPointOffset||0;a.startOnTick?r.min=L:r.min-M>L&&n.shift(),a.endOnTick?r.max=C:r.max+M<C&&n.pop(),1===n.length&&(w=.001,r.min-=w,r.max+=w)}},setMaxTicks:function(){var t=this.chart,e=t.maxTicks||{},i=this.tickPositions,n=this._maxTicksKey=[this.xOrY,this.pos,this.len].join("-");!this.isLinked&&!this.isDatetimeAxis&&i&&i.length>(e[n]||0)&&this.options.alignTicks!==!1&&(e[n]=i.length),t.maxTicks=e},adjustTickAmount:function(){var t=this,e=t.chart,i=t._maxTicksKey,n=t.tickPositions,o=e.maxTicks;if(o&&o[i]&&!t.isDatetimeAxis&&!t.categories&&!t.isLinked&&t.options.alignTicks!==!1){var r,s=t.tickAmount,a=n.length;if(t.tickAmount=r=o[i],a<r){for(;n.length<r;)n.push(O(n[n.length-1]+t.tickInterval));t.transA*=(a-1)/(r-1),t.max=n[n.length-1]}d(s)&&r!==s&&(t.isDirty=!0)}},setScale:function(){var t,e,i,n,o=this,r=o.stacks;if(o.oldMin=o.min,o.oldMax=o.max,o.oldAxisLength=o.len,o.setAxisSize(),n=o.len!==o.oldAxisLength,be(o.series,function(t){(t.isDirtyData||t.isDirty||t.xAxis.isDirty)&&(i=!0)}),n||i||o.isLinked||o.forceRedraw||o.userMin!==o.oldUserMin||o.userMax!==o.oldUserMax){if(!o.isXAxis)for(t in r)delete r[t];o.forceRedraw=!1,o.getSeriesExtremes(),o.setTickPositions(),o.oldUserMin=o.userMin,o.oldUserMax=o.userMax,o.isDirty||(o.isDirty=n||o.min!==o.oldMin||o.max!==o.oldMax)}else if(!o.isXAxis){o.oldStacks&&(r=o.stacks=o.oldStacks);for(t in r)for(e in r[t])r[t][e].cum=r[t][e].total}o.setMaxTicks()},setExtremes:function(e,i,n,o,r){var s=this,a=s.chart;n=f(n,!0),r=t(r,{min:e,max:i}),Ae(s,"setExtremes",r,function(){s.userMin=e,s.userMax=i,s.eventArgs=r,s.isDirtyExtremes=!0,n&&a.redraw(o)})},zoom:function(t,e){return this.allowZoomOutside||(d(this.dataMin)&&t<=this.dataMin&&(t=Z),d(this.dataMax)&&e>=this.dataMax&&(e=Z)),this.displayBtn=t!==Z||e!==Z,this.setExtremes(t,e,!1,Z,{trigger:"zoom"}),!0},setAxisSize:function(){var t,e,i,n,o=this.chart,r=this.options,s=r.offsetLeft||0,a=r.offsetRight||0,l=this.horiz;this.left=n=f(r.left,o.plotLeft+s),this.top=i=f(r.top,o.plotTop),this.width=t=f(r.width,o.plotWidth-s+a),this.height=e=f(r.height,o.plotHeight),this.bottom=o.chartHeight-e-i,this.right=o.chartWidth-t-n,this.len=bt(l?t:e,0),this.pos=l?n:i},getExtremes:function(){var t=this,e=t.isLog;return{min:e?O(h(t.min)):t.min,max:e?O(h(t.max)):t.max,dataMin:t.dataMin,dataMax:t.dataMax,userMin:t.userMin,userMax:t.userMax}},getThreshold:function(t){var e=this,i=e.isLog,n=i?h(e.min):e.min,o=i?h(e.max):e.max;return n>t||null===t?t=n:o<t&&(t=o),e.translate(t,0,1,0,1)},addPlotBand:function(t){this.addPlotBandOrLine(t,"plotBands")},addPlotLine:function(t){this.addPlotBandOrLine(t,"plotLines")},addPlotBandOrLine:function(t,e){var i=new G(this,t).render(),n=this.userOptions;return i&&(e&&(n[e]=n[e]||[],n[e].push(t)),this.plotLinesAndBands.push(i)),i},autoLabelAlign:function(t){var e,i=(f(t,0)-90*this.side+720)%360;return e=i>15&&i<165?"right":i>195&&i<345?"left":"center"},getOffset:function(){var t,e,i,n,o,r,s,a,l,h,c,p,u,g=this,m=g.chart,y=m.renderer,x=g.options,v=g.tickPositions,b=g.ticks,k=g.horiz,w=g.side,T=m.inverted?[1,0,3,2][w]:w,S=0,P=0,A=x.title,L=x.labels,C=0,M=m.axisOffset,D=m.clipOffset,I=[-1,1,1,-1][w],z=1,B=f(L.maxStaggerLines,5);if(g.hasData=t=g.hasVisibleSeries||d(g.min)&&d(g.max)&&!!v,g.showAxis=e=t||f(x.showEmpty,!0),g.staggerLines=g.horiz&&L.staggerLines,g.axisGroup||(g.gridGroup=y.g("grid").attr({zIndex:x.gridZIndex||1}).add(),g.axisGroup=y.g("axis").attr({zIndex:x.zIndex||2}).add(),g.labelGroup=y.g("axis-labels").attr({zIndex:L.zIndex||7}).add()),t||g.isLinked){if(g.labelAlign=f(L.align||g.autoLabelAlign(L.rotation)),be(v,function(t){b[t]?b[t].addLabel():b[t]=new E(g,t)}),g.horiz&&!g.staggerLines&&B&&!L.rotation){for(r=g.reversed?[].concat(v).reverse():v;z<B;){for(s=[],a=!1,o=0;o<r.length;o++)l=r[o],h=b[l].label&&b[l].label.getBBox(),p=h?h.width:0,u=o%z,p&&(c=g.translate(l),s[u]!==Z&&c<s[u]&&(a=!0),s[u]=c+p);if(!a)break;z++}z>1&&(g.staggerLines=z)}be(v,function(t){0!==w&&2!==w&&{1:"left",3:"right"}[w]!==g.labelAlign||(C=bt(b[t].getLabelSize(),C))}),g.staggerLines&&(C*=g.staggerLines,g.labelOffset=C)}else for(n in b)b[n].destroy(),delete b[n];A&&A.text&&A.enabled!==!1&&(g.axisTitle||(g.axisTitle=y.text(A.text,0,0,A.useHTML).attr({zIndex:7,rotation:A.rotation||0,align:A.textAlign||{low:"left",middle:"center",high:"right"}[A.align]}).css(A.style).add(g.axisGroup),g.axisTitle.isNew=!0),e&&(S=g.axisTitle.getBBox()[k?"height":"width"],P=f(A.margin,k?5:10),i=A.offset),g.axisTitle[e?"show":"hide"]()),g.offset=I*f(x.offset,M[w]),g.axisTitleMargin=f(i,C+P+(2!==w&&C&&I*x.labels[k?"y":"x"])),M[w]=bt(M[w],g.axisTitleMargin+S+I*g.offset),D[T]=bt(D[T],2*xt(x.lineWidth/2))},getLinePath:function(t){var e=this.chart,i=this.opposite,n=this.offset,o=this.horiz,r=this.left+(i?this.width:0)+n,s=e.chartHeight-this.bottom-(i?this.height:0)+n;return i&&(t*=-1),e.renderer.crispLine([Qt,o?this.left:r,o?s:this.top,te,o?e.chartWidth-this.right:r,o?s:e.chartHeight-this.bottom],t)},getTitlePosition:function(){var t=this.horiz,e=this.left,i=this.top,o=this.len,r=this.options.title,s=t?e:i,a=this.opposite,l=this.offset,h=n(r.style.fontSize||12),c={low:s+(t?0:o),middle:s+o/2,high:s+(t?o:0)}[r.align],d=(t?i+this.height:e)+(t?1:-1)*(a?-1:1)*this.axisTitleMargin+(2===this.side?h:0);return{x:t?c:d+(a?this.width:0)+l+(r.x||0),y:t?d-(a?this.height:0)+l:c+(r.y||0)}},render:function(){
var t,e,i,n=this,o=n.chart,r=o.renderer,s=n.options,a=n.isLog,l=n.isLinked,c=n.tickPositions,p=n.axisTitle,u=n.stacks,f=n.ticks,g=n.minorTicks,m=n.alternateBands,y=s.stackLabels,x=s.alternateGridColor,v=n.tickmarkOffset,b=s.lineWidth,k=o.hasRendered,w=k&&d(n.oldMin)&&!isNaN(n.oldMin),T=n.hasData,S=n.showAxis;if(be([f,g,m],function(t){var e;for(e in t)t[e].isActive=!1}),(T||l)&&(n.minorTickInterval&&!n.categories&&be(n.getMinorTickPositions(),function(t){g[t]||(g[t]=new E(n,t,"minor")),w&&g[t].isNew&&g[t].render(null,!0),g[t].render(null,!1,1)}),c.length&&(be(c.slice(1).concat([c[0]]),function(t,e){e=e===c.length-1?0:e+1,(!l||t>=n.min&&t<=n.max)&&(f[t]||(f[t]=new E(n,t)),w&&f[t].isNew&&f[t].render(e,!0),f[t].render(e,!1,1))}),v&&0===n.min&&(f[-1]||(f[-1]=new E(n,(-1),null,(!0))),f[-1].render(-1))),x&&be(c,function(t,o){o%2===0&&t<n.max&&(m[t]||(m[t]=new G(n)),e=t+v,i=c[o+1]!==Z?c[o+1]+v:n.max,m[t].options={from:a?h(e):e,to:a?h(i):i,color:x},m[t].render(),m[t].isActive=!0)}),n._addedPlotLB||(be((s.plotLines||[]).concat(s.plotBands||[]),function(t){n.addPlotBandOrLine(t)}),n._addedPlotLB=!0)),be([f,g,m],function(t){var e,i,n=[],r=Q?Q.duration||500:0,s=function(){for(i=n.length;i--;)t[n[i]]&&!t[n[i]].isActive&&(t[n[i]].destroy(),delete t[n[i]])};for(e in t)t[e].isActive||(t[e].render(e,!1,0),t[e].isActive=!1,n.push(e));t!==m&&o.hasRendered&&r?r&&setTimeout(s,r):s()}),b&&(t=n.getLinePath(b),n.axisLine?n.axisLine.animate({d:t}):n.axisLine=r.path(t).attr({stroke:s.lineColor,"stroke-width":b,zIndex:7}).add(n.axisGroup),n.axisLine[S?"show":"hide"]()),p&&S&&(p[p.isNew?"attr":"animate"](n.getTitlePosition()),p.isNew=!1),y&&y.enabled){var P,A,L,C=n.stackTotalGroup;C||(n.stackTotalGroup=C=r.g("stack-labels").attr({visibility:$t,zIndex:6}).add()),C.translate(o.plotLeft,o.plotTop);for(P in u){A=u[P];for(L in A)A[L].render(C)}}n.isDirty=!1},removePlotBandOrLine:function(t){for(var e=this.plotLinesAndBands,i=this.options,n=this.userOptions,o=e.length;o--;)e[o].id===t&&e[o].destroy();be([i.plotLines||[],n.plotLines||[],i.plotBands||[],n.plotBands||[]],function(e){for(o=e.length;o--;)e[o].id===t&&c(e,e[o])})},setTitle:function(t,e){this.update({title:t},e)},redraw:function(){var t=this,e=t.chart,i=e.pointer;i.reset&&i.reset(!0),t.render(),be(t.plotLinesAndBands,function(t){t.render()}),be(t.series,function(t){t.isDirty=!0})},buildStacks:function(){var t=this.series,e=t.length;if(!this.isXAxis){for(;e--;)t[e].setStackedPoints();if(this.usePercentage)for(e=0;e<t.length;e++)t[e].setPercentStacks()}},setCategories:function(t,e){this.update({categories:t},e)},destroy:function(t){var e,i,n=this,o=n.stacks,r=n.plotLinesAndBands;t||Pe(n);for(e in o)I(o[e]),o[e]=null;for(be([n.ticks,n.minorTicks,n.alternateBands],function(t){I(t)}),i=r.length;i--;)r[i].destroy();be(["stackTotalGroup","axisLine","axisGroup","gridGroup","labelGroup","axisTitle"],function(t){n[t]&&(n[t]=n[t].destroy())})}},V.prototype={init:function(t,e){var i=e.borderWidth,o=e.style,r=n(o.padding);this.chart=t,this.options=e,this.crosshairs=[],this.now={x:0,y:0},this.isHidden=!0,this.label=t.renderer.label("",0,0,e.shape,null,null,e.useHTML,null,"tooltip").attr({padding:r,fill:e.backgroundColor,"stroke-width":i,r:e.borderRadius,zIndex:8}).css(o).css({padding:0}).add().attr({y:-999}),Xt||this.label.shadow(e.shadow),this.shared=e.shared},destroy:function(){be(this.crosshairs,function(t){t&&t.destroy()}),this.label&&(this.label=this.label.destroy()),clearTimeout(this.hideTimer),clearTimeout(this.tooltipTimeout)},move:function(e,i,n,o){var r=this,s=r.now,a=r.options.animation!==!1&&!r.isHidden;t(s,{x:a?(2*s.x+e)/3:e,y:a?(s.y+i)/2:i,anchorX:a?(2*s.anchorX+n)/3:n,anchorY:a?(s.anchorY+o)/2:o}),r.label.attr(s),a&&(wt(e-s.x)>1||wt(i-s.y)>1)&&(clearTimeout(this.tooltipTimeout),this.tooltipTimeout=setTimeout(function(){r&&r.move(e,i,n,o)},32))},hide:function(){var t,e=this;clearTimeout(this.hideTimer),this.isHidden||(t=this.chart.hoverPoints,this.hideTimer=setTimeout(function(){e.label.fadeOut(),e.isHidden=!0},f(this.options.hideDelay,500)),t&&be(t,function(t){t.setState()}),this.chart.hoverPoints=null)},hideCrosshairs:function(){be(this.crosshairs,function(t){t&&t.hide()})},getAnchor:function(t,e){var i,n,o=this.chart,r=o.inverted,s=o.plotTop,a=0,l=0;return t=u(t),i=t[0].tooltipPos,this.followPointer&&e&&(e.chartX===Z&&(e=o.pointer.normalize(e)),i=[e.chartX-o.plotLeft,e.chartY-s]),i||(be(t,function(t){n=t.series.yAxis,a+=t.plotX,l+=(t.plotLow?(t.plotLow+t.plotHigh)/2:t.plotY)+(!r&&n?n.top-s:0)}),a/=t.length,l/=t.length,i=[r?o.plotWidth-l:a,this.shared&&!r&&t.length>1&&e?e.chartY-s:r?o.plotHeight-a:l]),Te(i,yt)},getPosition:function(t,e,i){var n,o=this.chart,r=o.plotLeft,s=o.plotTop,a=o.plotWidth,l=o.plotHeight,h=f(this.options.distance,12),c=i.plotX,d=i.plotY,p=c+r+(o.inverted?h:-t-h),u=d-e+s+15;return p<7&&(p=r+bt(c,0)+h),p+t>r+a&&(p-=p+t-(r+a),u=d-e+s-h,n=!0),u<s+5&&(u=s+5,n&&d>=u&&d<=u+e&&(u=d+s+h)),u+e>s+l&&(u=bt(s,s+l-e-h)),{x:p,y:u}},defaultFormatter:function(t){var e,i=this.points||u(this),n=i[0].series;return e=[n.tooltipHeaderFormatter(i[0])],be(i,function(t){n=t.series,e.push(n.tooltipFormatter&&n.tooltipFormatter(t)||t.point.tooltipFormatter(n.tooltipOptions.pointFormat))}),e.push(t.options.footerFormat||""),e.join("")},refresh:function(t,e){var i,n,o,r,s,a,h=this,c=h.chart,d=h.label,p=h.options,g={},m=[],y=p.formatter||h.defaultFormatter,x=c.hoverPoints,v=p.crosshairs,b=h.shared;if(clearTimeout(this.hideTimer),h.followPointer=u(t)[0].series.tooltipOptions.followPointer,o=h.getAnchor(t,e),i=o[0],n=o[1],!b||t.series&&t.series.noSharedTooltip?g=t.getLabelConfig():(c.hoverPoints=t,x&&be(x,function(t){t.setState()}),be(t,function(t){t.setState(ne),m.push(t.getLabelConfig())}),g={x:t[0].category,y:t[0].y},g.points=m,t=t[0]),r=y.call(g,h),a=t.series,r===!1?this.hide():(h.isHidden&&(Me(d),d.attr("opacity",1).show()),d.attr({text:r}),s=p.borderColor||t.color||a.color||"#606060",d.attr({stroke:s}),h.updatePosition({plotX:i,plotY:n}),this.isHidden=!1),v){v=u(v);for(var k,w,T,S,P,A=v.length;A--;)P=t.series,T=P[A?"yAxis":"xAxis"],v[A]&&T&&(S=A?f(t.stackY,t.y):t.x,T.isLog&&(S=l(S)),1===A&&P.modifyValue&&(S=P.modifyValue(S)),k=T.getPlotLinePath(S,1),h.crosshairs[A]?h.crosshairs[A].attr({d:k,visibility:$t}):(w={"stroke-width":v[A].width||1,stroke:v[A].color||"#C0C0C0",zIndex:v[A].zIndex||2},v[A].dashStyle&&(w.dashstyle=v[A].dashStyle),h.crosshairs[A]=c.renderer.path(k).attr(w).add()))}Ae(c,"tooltipRefresh",{text:r,x:i+c.plotLeft,y:n+c.plotTop,borderColor:s})},updatePosition:function(t){var e=this.chart,i=this.label,n=(this.options.positioner||this.getPosition).call(this,i.width,i.height,t);this.move(yt(n.x),yt(n.y),t.plotX+e.plotLeft,t.plotY+e.plotTop)}},j.prototype={init:function(t,e){var i,n,o=e.chart,r=o.events,s=Xt?"":o.zoomType,a=t.inverted;this.options=e,this.chart=t,this.zoomX=i=/x/.test(s),this.zoomY=n=/y/.test(s),this.zoomHor=i&&!a||n&&a,this.zoomVert=n&&!a||i&&a,this.runChartClick=r&&!!r.click,this.pinchDown=[],this.lastValidTouch={},e.tooltip.enabled&&(t.tooltip=new V(t,e.tooltip)),this.setDOMEvents()},normalize:function(e,i){var n,o,r;return e=e||gt.event,e.target||(e.target=e.srcElement),e=Le(e),r=e.touches?e.touches.item(0):e,i||(this.chartPosition=i=we(this.chart.container)),r.pageX===Z?(n=bt(e.x,e.clientX-i.left),o=e.y):(n=r.pageX-i.left,o=r.pageY-i.top),t(e,{chartX:yt(n),chartY:yt(o)})},getCoordinates:function(t){var e={xAxis:[],yAxis:[]};return be(this.chart.axes,function(i){e[i.isXAxis?"xAxis":"yAxis"].push({axis:i,value:i.toValue(t[i.horiz?"chartX":"chartY"])})}),e},getIndex:function(t){var e=this.chart;return e.inverted?e.plotHeight+e.plotTop-t.chartY:t.chartX-e.plotLeft},runPointActions:function(t){var e,i,n,o,r,s=this,a=s.chart,l=a.series,h=a.tooltip,c=a.hoverPoint,d=a.hoverSeries,p=a.chartWidth,u=s.getIndex(t);if(h&&s.options.tooltip.shared&&(!d||!d.noSharedTooltip)){for(i=[],n=l.length,o=0;o<n;o++)l[o].visible&&l[o].options.enableMouseTracking!==!1&&!l[o].noSharedTooltip&&l[o].tooltipPoints.length&&(e=l[o].tooltipPoints[u],e&&e.series&&(e._dist=wt(u-e.clientX),p=kt(p,e._dist),i.push(e)));for(n=i.length;n--;)i[n]._dist>p&&i.splice(n,1);i.length&&i[0].clientX!==s.hoverX&&(h.refresh(i,t),s.hoverX=i[0].clientX)}d&&d.tracker?(e=d.tooltipPoints[u],e&&e!==c&&e.onMouseOver(t)):h&&h.followPointer&&!h.isHidden&&(r=h.getAnchor([{}],t),h.updatePosition({plotX:r[0],plotY:r[1]}))},reset:function(t){var e=this,i=e.chart,n=i.hoverSeries,o=i.hoverPoint,r=i.tooltip,s=r&&r.shared?i.hoverPoints:o;t=t&&r&&s,t&&u(s)[0].plotX===Z&&(t=!1),t?r.refresh(s):(o&&o.onMouseOut(),n&&n.onMouseOut(),r&&(r.hide(),r.hideCrosshairs()),e.hoverX=null)},scaleGroups:function(t,e){var i,n=this.chart;be(n.series,function(o){i=t||o.getPlotBox(),o.xAxis&&o.xAxis.zoomEnabled&&(o.group.attr(i),o.markerGroup&&(o.markerGroup.attr(i),o.markerGroup.clip(e?n.clipRect:null)),o.dataLabelsGroup&&o.dataLabelsGroup.attr(i))}),n.clipRect.attr(e||n.clipBox)},pinchTranslateDirection:function(t,e,i,n,o,r,s){var a,l,h,c,d,p,u=this.chart,f=t?"x":"y",g=t?"X":"Y",m="chart"+g,y=t?"width":"height",x=u["plot"+(t?"Left":"Top")],v=1,b=u.inverted,k=u.bounds[t?"h":"v"],w=1===e.length,T=e[0][m],S=i[0][m],P=!w&&e[1][m],A=!w&&i[1][m],L=function(){!w&&wt(T-P)>20&&(v=wt(S-A)/wt(T-P)),h=(x-S)/v+T,a=u["plot"+(t?"Width":"Height")]/v};L(),l=h,l<k.min?(l=k.min,c=!0):l+a>k.max&&(l=k.max-a,c=!0),c?(S-=.8*(S-s[f][0]),w||(A-=.8*(A-s[f][1])),L()):s[f]=[S,A],b||(r[f]=h-x,r[y]=a),p=b?t?"scaleY":"scaleX":"scale"+g,d=b?1/v:v,o[y]=a,o[f]=l,n[p]=v,n["translate"+g]=d*x+(S-d*T)},pinch:function(e){var i=this,n=i.chart,o=i.pinchDown,r=n.tooltip&&n.tooltip.options.followTouchMove,s=e.touches,a=s.length,l=i.lastValidTouch,h=i.zoomHor||i.pinchHor,c=i.zoomVert||i.pinchVert,d=h||c,p=i.selectionMarker,u={},f=1===a&&(i.inClass(e.target,Kt+"tracker")&&n.runTrackerClick||n.runChartClick),g={};!d&&!r||f||e.preventDefault(),Te(s,function(t){return i.normalize(t)}),"touchstart"===e.type?(be(s,function(t,e){o[e]={chartX:t.chartX,chartY:t.chartY}}),l.x=[o[0].chartX,o[1]&&o[1].chartX],l.y=[o[0].chartY,o[1]&&o[1].chartY],be(n.axes,function(t){if(t.zoomEnabled){var e=n.bounds[t.horiz?"h":"v"],i=t.minPixelPadding,o=t.toPixels(t.dataMin),r=t.toPixels(t.dataMax),s=kt(o,r),a=bt(o,r);e.min=kt(t.pos,s-i),e.max=bt(t.pos+t.len,a+i)}})):o.length&&(p||(i.selectionMarker=p=t({destroy:Gt},n.plotBox)),h&&i.pinchTranslateDirection(!0,o,s,u,p,g,l),c&&i.pinchTranslateDirection(!1,o,s,u,p,g,l),i.hasPinched=d,i.scaleGroups(u,g),!d&&r&&1===a&&this.runPointActions(i.normalize(e)))},dragStart:function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=this.mouseDownX=t.chartX,e.mouseDownY=this.mouseDownY=t.chartY},drag:function(t){var e,i,n=this.chart,o=n.options.chart,r=t.chartX,s=t.chartY,a=this.zoomHor,l=this.zoomVert,h=n.plotLeft,c=n.plotTop,d=n.plotWidth,p=n.plotHeight,u=this.mouseDownX,f=this.mouseDownY;r<h?r=h:r>h+d&&(r=h+d),s<c?s=c:s>c+p&&(s=c+p),this.hasDragged=Math.sqrt(Math.pow(u-r,2)+Math.pow(f-s,2)),this.hasDragged>10&&(e=n.isInsidePlot(u-h,f-c),n.hasCartesianSeries&&(this.zoomX||this.zoomY)&&e&&(this.selectionMarker||(this.selectionMarker=n.renderer.rect(h,c,a?1:d,l?1:p,0).attr({fill:o.selectionMarkerFill||"rgba(69,114,167,0.25)",zIndex:7}).add())),this.selectionMarker&&a&&(i=r-u,this.selectionMarker.attr({width:wt(i),x:(i>0?0:i)+u})),this.selectionMarker&&l&&(i=s-f,this.selectionMarker.attr({height:wt(i),y:(i>0?0:i)+f})),e&&!this.selectionMarker&&o.panning&&n.pan(t,o.panning))},drop:function(e){var i=this.chart,n=this.hasPinched;if(this.selectionMarker){var o,r={xAxis:[],yAxis:[],originalEvent:e.originalEvent||e},s=this.selectionMarker,a=s.x,l=s.y;(this.hasDragged||n)&&(be(i.axes,function(t){if(t.zoomEnabled){var e=t.horiz,i=t.toValue(e?a:l),n=t.toValue(e?a+s.width:l+s.height);isNaN(i)||isNaN(n)||(r[t.xOrY+"Axis"].push({axis:t,min:kt(i,n),max:bt(i,n)}),o=!0)}}),o&&Ae(i,"selection",r,function(e){i.zoom(t(e,n?{animation:!1}:null))})),this.selectionMarker=this.selectionMarker.destroy(),n&&this.scaleGroups()}i&&(g(i.container,{cursor:i._cursor}),i.cancelClick=this.hasDragged>10,i.mouseIsDown=this.hasDragged=this.hasPinched=!1,this.pinchDown=[])},onContainerMouseDown:function(t){t=this.normalize(t),t.preventDefault&&t.preventDefault(),this.dragStart(t)},onDocumentMouseUp:function(t){this.drop(t)},onDocumentMouseMove:function(t){var e=this.chart,i=this.chartPosition,n=e.hoverSeries;t=this.normalize(t,i),i&&n&&!this.inClass(t.target,"highcharts-tracker")&&!e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)&&this.reset()},onContainerMouseLeave:function(){this.reset(),this.chartPosition=null},onContainerMouseMove:function(t){var e=this.chart;t=this.normalize(t),t.returnValue=!1,"mousedown"===e.mouseIsDown&&this.drag(t),!this.inClass(t.target,"highcharts-tracker")&&!e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)||e.openMenu||this.runPointActions(t)},inClass:function(t,e){for(var i;t;){if(i=p(t,"class")){if(i.indexOf(e)!==-1)return!0;if(i.indexOf(Kt+"container")!==-1)return!1}t=t.parentNode}},onTrackerMouseOut:function(t){var e=this.chart.hoverSeries;!e||e.options.stickyTracking||this.inClass(t.toElement||t.relatedTarget,Kt+"tooltip")||e.onMouseOut()},onContainerClick:function(e){var i,n,o,r=this.chart,s=r.hoverPoint,a=r.plotLeft,l=r.plotTop,h=r.inverted;e=this.normalize(e),e.cancelBubble=!0,r.cancelClick||(s&&this.inClass(e.target,Kt+"tracker")?(i=this.chartPosition,n=s.plotX,o=s.plotY,t(s,{pageX:i.left+a+(h?r.plotWidth-o:n),pageY:i.top+l+(h?r.plotHeight-n:o)}),Ae(s.series,"click",t(e,{point:s})),r.hoverPoint&&s.firePointEvent("click",e)):(t(e,this.getCoordinates(e)),r.isInsidePlot(e.chartX-a,e.chartY-l)&&Ae(r,"click",e)))},onContainerTouchStart:function(t){var e=this.chart;1===t.touches.length?(t=this.normalize(t),e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)?(this.runPointActions(t),this.pinch(t)):this.reset()):2===t.touches.length&&this.pinch(t)},onContainerTouchMove:function(t){1!==t.touches.length&&2!==t.touches.length||this.pinch(t)},onDocumentTouchEnd:function(t){this.drop(t)},setDOMEvents:function(){var t,e=this,i=e.chart.container;this._events=t=[[i,"onmousedown","onContainerMouseDown"],[i,"onmousemove","onContainerMouseMove"],[i,"onclick","onContainerClick"],[i,"mouseleave","onContainerMouseLeave"],[ft,"mousemove","onDocumentMouseMove"],[ft,"mouseup","onDocumentMouseUp"]],Wt&&t.push([i,"ontouchstart","onContainerTouchStart"],[i,"ontouchmove","onContainerTouchMove"],[ft,"touchend","onDocumentTouchEnd"]),be(t,function(t){e["_"+t[2]]=function(i){e[t[2]](i)},0===t[1].indexOf("on")?t[0][t[1]]=e["_"+t[2]]:Se(t[0],t[1],e["_"+t[2]])})},destroy:function(){var t=this;be(t._events,function(e){0===e[1].indexOf("on")?e[0][e[1]]=null:Pe(e[0],e[1],t["_"+e[2]])}),delete t._events,clearInterval(t.tooltipTimeout)}},_.prototype={init:function(t,i){var o=this,r=i.itemStyle,s=f(i.padding,8),a=i.itemMarginTop||0;this.options=i,i.enabled&&(o.baseline=n(r.fontSize)+3+a,o.itemStyle=r,o.itemHiddenStyle=e(r,i.itemHiddenStyle),o.itemMarginTop=a,o.padding=s,o.initialItemX=s,o.initialItemY=s-5,o.maxItemWidth=0,o.chart=t,o.itemHeight=0,o.lastLineHeight=0,o.render(),Se(o.chart,"endResize",function(){o.positionCheckboxes()}))},colorizeItem:function(t,e){var i,n,o=this,r=o.options,s=t.legendItem,a=t.legendLine,l=t.legendSymbol,h=o.itemHiddenStyle.color,c=e?r.itemStyle.color:h,d=e?t.color:h,p=t.options&&t.options.marker,u={stroke:d,fill:d};if(s&&s.css({fill:c,color:c}),a&&a.attr({stroke:d}),l){if(p&&l.isMarker){p=t.convertAttribs(p);for(i in p)n=p[i],n!==Z&&(u[i]=n)}l.attr(u)}},positionItem:function(t){var e=this,i=e.options,n=i.symbolPadding,o=!i.rtl,r=t._legendItemPos,s=r[0],a=r[1],l=t.checkbox;t.legendGroup&&t.legendGroup.translate(o?s:e.legendWidth-s-2*n-4,a),l&&(l.x=s,l.y=a)},destroyItem:function(t){var e=t.checkbox;be(["legendItem","legendLine","legendSymbol","legendGroup"],function(e){t[e]&&(t[e]=t[e].destroy())}),e&&z(t.checkbox)},destroy:function(){var t=this,e=t.group,i=t.box;i&&(t.box=i.destroy()),e&&(t.group=e.destroy())},positionCheckboxes:function(t){var e,i=this.group.alignAttr,n=this.clipHeight||this.legendHeight;i&&(e=i.translateY,be(this.allItems,function(o){var r,s=o.checkbox;s&&(r=e+s.y+(t||0)+3,g(s,{left:i.translateX+o.legendItemWidth+s.x-20+qt,top:r+qt,display:r>e-6&&r<e+n-6?"":Jt}))}))},renderTitle:function(){var t,e=this.options,i=this.padding,n=e.title,o=0;n.text&&(this.title||(this.title=this.chart.renderer.label(n.text,i-3,i-4,null,null,null,null,null,"legend-title").attr({zIndex:1}).css(n.style).add(this.group)),t=this.title.getBBox(),o=t.height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:o})),this.titleHeight=o},renderItem:function(t){var i,n,o,r=this,s=r.chart,a=s.renderer,l=r.options,h="horizontal"===l.layout,c=l.symbolWidth,d=l.symbolPadding,p=r.itemStyle,u=r.itemHiddenStyle,g=r.padding,y=h?f(l.itemDistance,8):0,x=!l.rtl,v=l.width,b=l.itemMarginBottom||0,k=r.itemMarginTop,T=r.initialItemX,S=t.legendItem,P=t.series||t,A=P.options,L=A.showCheckbox,C=l.useHTML;S||(t.legendGroup=a.g("legend-item").attr({zIndex:1}).add(r.scrollGroup),P.drawLegendSymbol(r,t),t.legendItem=S=a.text(l.labelFormat?w(l.labelFormat,t):l.labelFormatter.call(t),x?c+d:-d,r.baseline,C).css(e(t.visible?p:u)).attr({align:x?"left":"right",zIndex:2}).add(t.legendGroup),(C?S:t.legendGroup).on("mouseover",function(){t.setState(ne),S.css(r.options.itemHoverStyle)}).on("mouseout",function(){S.css(t.visible?p:u),t.setState()}).on("click",function(e){var i="legendItemClick",n=function(){t.setVisible()};e={browserEvent:e},t.firePointEvent?t.firePointEvent(i,e,n):Ae(t,i,e,n)}),r.colorizeItem(t,t.visible),A&&L&&(t.checkbox=m("input",{type:"checkbox",checked:t.selected,defaultChecked:t.selected},l.itemCheckboxStyle,s.container),Se(t.checkbox,"click",function(e){var i=e.target;Ae(t,"checkboxClick",{checked:i.checked},function(){t.select()})}))),n=S.getBBox(),o=t.legendItemWidth=l.itemWidth||c+d+n.width+y+(L?20:0),r.itemHeight=i=n.height,h&&r.itemX-T+o>(v||s.chartWidth-2*g-T)&&(r.itemX=T,r.itemY+=k+r.lastLineHeight+b,r.lastLineHeight=0),r.maxItemWidth=bt(r.maxItemWidth,o),r.lastItemY=k+r.itemY+b,r.lastLineHeight=bt(i,r.lastLineHeight),t._legendItemPos=[r.itemX,r.itemY],h?r.itemX+=o:(r.itemY+=k+i+b,r.lastLineHeight=i),r.offsetWidth=v||bt((h?r.itemX-T-y:o)+g,r.offsetWidth)},render:function(){var e,i,n,o,r=this,s=r.chart,a=s.renderer,l=r.group,h=r.box,c=r.options,p=r.padding,u=c.borderWidth,f=c.backgroundColor;r.itemX=r.initialItemX,r.itemY=r.initialItemY,r.offsetWidth=0,r.lastItemY=0,l||(r.group=l=a.g("legend").attr({zIndex:7}).add(),r.contentGroup=a.g().attr({zIndex:1}).add(l),r.scrollGroup=a.g().add(r.contentGroup)),r.renderTitle(),e=[],be(s.series,function(t){var i=t.options;i.showInLegend&&!d(i.linkedTo)&&(e=e.concat(t.legendItems||("point"===i.legendType?t.data:t)))}),C(e,function(t,e){return(t.options&&t.options.legendIndex||0)-(e.options&&e.options.legendIndex||0)}),c.reversed&&e.reverse(),r.allItems=e,r.display=i=!!e.length,be(e,function(t){r.renderItem(t)}),n=c.width||r.offsetWidth,o=r.lastItemY+r.lastLineHeight+r.titleHeight,o=r.handleOverflow(o),(u||f)&&(n+=p,o+=p,h?n>0&&o>0&&(h[h.isNew?"attr":"animate"](h.crisp(null,null,null,n,o)),h.isNew=!1):(r.box=h=a.rect(0,0,n,o,c.borderRadius,u||0).attr({stroke:c.borderColor,"stroke-width":u||0,fill:f||Jt}).add(l).shadow(c.shadow),h.isNew=!0),h[i?"show":"hide"]()),r.legendWidth=n,r.legendHeight=o,be(e,function(t){r.positionItem(t)}),i&&l.align(t({width:n,height:o},c),!0,"spacingBox"),s.isResizing||this.positionCheckboxes()},handleOverflow:function(t){var e,i,n=this,o=this.chart,r=o.renderer,s=this.options,a=s.y,l="top"===s.verticalAlign,h=o.spacingBox.height+(l?-a:a)-this.padding,c=s.maxHeight,d=this.clipRect,p=s.navigation,u=f(p.animation,!0),g=p.arrowSize||12,m=this.nav;return"horizontal"===s.layout&&(h/=2),c&&(h=kt(h,c)),t>h&&!s.useHTML?(this.clipHeight=i=h-20-this.titleHeight,this.pageCount=e=vt(t/i),this.currentPage=f(this.currentPage,1),this.fullHeight=t,d||(d=n.clipRect=r.clipRect(0,0,9999,0),n.contentGroup.clip(d)),d.attr({height:i}),m||(this.nav=m=r.g().attr({zIndex:1}).add(this.group),this.up=r.symbol("triangle",0,0,g,g).on("click",function(){n.scroll(-1,u)}).add(m),this.pager=r.text("",15,10).css(p.style).add(m),this.down=r.symbol("triangle-down",0,0,g,g).on("click",function(){n.scroll(1,u)}).add(m)),n.scroll(0),t=h):m&&(d.attr({height:o.chartHeight}),m.hide(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},scroll:function(t,e){var i,n=this.pageCount,o=this.currentPage+t,r=this.clipHeight,s=this.options.navigation,a=s.activeColor,l=s.inactiveColor,h=this.pager,c=this.padding;o>n&&(o=n),o>0&&(e!==Z&&R(e,this.chart),this.nav.attr({translateX:c,translateY:r+7+this.titleHeight,visibility:$t}),this.up.attr({fill:1===o?l:a}).css({cursor:1===o?"default":"pointer"}),h.attr({text:o+"/"+this.pageCount}),this.down.attr({x:18+this.pager.getBBox().width,fill:o===n?l:a}).css({cursor:o===n?"default":"pointer"}),i=-kt(r*(o-1),this.fullHeight-r+c)+1,this.scrollGroup.animate({translateY:i}),h.attr({text:o+"/"+n}),this.currentPage=o,this.positionCheckboxes(i))}},/Trident.*?11\.0/.test(Lt)&&b(_.prototype,"positionItem",function(t,e){var i=this;setTimeout(function(){t.call(i,e)})}),U.prototype={init:function(t,i){var n,o=t.series;t.series=null,n=e(q,t),n.series=t.series=o;var r=n.chart;this.margin=this.splashArray("margin",r),this.spacing=this.splashArray("spacing",r);var s=r.events;this.bounds={h:{},v:{}},this.callback=i,this.isResizing=0,this.options=n,this.axes=[],this.series=[],this.hasCartesianSeries=r.showAxes;var a,l=this;if(l.index=Nt.length,Nt.push(l),r.reflow!==!1&&Se(l,"load",function(){l.initReflow()}),s)for(a in s)Se(l,a,s[a]);l.xAxis=[],l.yAxis=[],l.animation=!Xt&&f(r.animation,!0),l.pointCount=0,l.counters=new L,l.firstRender()},initSeries:function(t){var e,i=this,n=i.options.chart,o=t.type||n.type||n.defaultSeriesType,r=fe[o];return r||B(17,!0),e=new r,e.init(this,t),e},addSeries:function(t,e,i){var n,o=this;return t&&(e=f(e,!0),Ae(o,"addSeries",{options:t},function(){n=o.initSeries(t),o.isDirtyLegend=!0,o.linkSeries(),e&&o.redraw(i)})),n},addAxis:function(t,i,n,o){var r,s=i?"xAxis":"yAxis",a=this.options;r=new F(this,e(t,{index:this[s].length,isX:i})),a[s]=u(a[s]||{}),a[s].push(t),f(n,!0)&&this.redraw(o)},isInsidePlot:function(t,e,i){var n=i?e:t,o=i?t:e;return n>=0&&n<=this.plotWidth&&o>=0&&o<=this.plotHeight},adjustTickAmounts:function(){this.options.chart.alignTicks!==!1&&be(this.axes,function(t){t.adjustTickAmount()}),this.maxTicks=null},redraw:function(e){var i,n,o,r=this,s=r.axes,a=r.series,l=r.pointer,h=r.legend,c=r.isDirtyLegend,d=r.isDirtyBox,p=a.length,u=p,f=r.renderer,g=f.isHidden(),m=[];for(R(e,r),g&&r.cloneRenderTo(),r.layOutTitles();u--;)if(o=a[u],o.options.stacking&&(i=!0,o.isDirty)){n=!0;break}if(n)for(u=p;u--;)o=a[u],o.options.stacking&&(o.isDirty=!0);be(a,function(t){t.isDirty&&"point"===t.options.legendType&&(c=!0)}),c&&h.options.enabled&&(h.render(),r.isDirtyLegend=!1),i&&r.getStacks(),r.hasCartesianSeries&&(r.isResizing||(r.maxTicks=null,be(s,function(t){t.setScale()})),r.adjustTickAmounts(),r.getMargins(),be(s,function(t){t.isDirty&&(d=!0)}),be(s,function(e){e.isDirtyExtremes&&(e.isDirtyExtremes=!1,m.push(function(){Ae(e,"afterSetExtremes",t(e.eventArgs,e.getExtremes())),delete e.eventArgs})),(d||i)&&e.redraw()})),d&&r.drawChartBox(),be(a,function(t){t.isDirty&&t.visible&&(!t.isCartesian||t.xAxis)&&t.redraw()}),l&&l.reset&&l.reset(!0),f.draw(),Ae(r,"redraw"),g&&r.cloneRenderTo(!0),be(m,function(t){t.call()})},showLoading:function(e){var i=this,n=i.options,o=i.loadingDiv,r=n.loading;o||(i.loadingDiv=o=m(jt,{className:Kt+"loading"},t(r.style,{zIndex:10,display:Jt}),i.container),i.loadingSpan=m("span",null,r.labelStyle,o)),i.loadingSpan.innerHTML=e||n.lang.loading,i.loadingShown||(g(o,{opacity:0,display:"",left:i.plotLeft+qt,top:i.plotTop+qt,width:i.plotWidth+qt,height:i.plotHeight+qt}),Ce(o,{opacity:r.style.opacity},{duration:r.showDuration||0}),i.loadingShown=!0)},hideLoading:function(){var t=this.options,e=this.loadingDiv;e&&Ce(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){g(e,{display:Jt})}}),this.loadingShown=!1},get:function(t){var e,i,n,o=this,r=o.axes,s=o.series;for(e=0;e<r.length;e++)if(r[e].options.id===t)return r[e];for(e=0;e<s.length;e++)if(s[e].options.id===t)return s[e];for(e=0;e<s.length;e++)for(n=s[e].points||[],i=0;i<n.length;i++)if(n[i].id===t)return n[i];return null},getAxes:function(){var t,e,i=this,n=this.options,o=n.xAxis=u(n.xAxis||{}),r=n.yAxis=u(n.yAxis||{});be(o,function(t,e){t.index=e,t.isX=!0}),be(r,function(t,e){t.index=e}),t=o.concat(r),be(t,function(t){e=new F(i,t)}),i.adjustTickAmounts()},getSelectedPoints:function(){var t=[];return be(this.series,function(e){t=t.concat(ke(e.points||[],function(t){return t.selected}))}),t},getSelectedSeries:function(){return ke(this.series,function(t){return t.selected})},getStacks:function(){var t=this;be(t.yAxis,function(t){t.stacks&&t.hasVisibleSeries&&(t.oldStacks=t.stacks)}),be(t.series,function(e){!e.options.stacking||e.visible!==!0&&t.options.chart.ignoreHiddenSeries!==!1||(e.stackKey=e.type+f(e.options.stack,""))})},showResetZoom:function(){var t=this,e=q.lang,i=t.options.chart.resetZoomButton,n=i.theme,o=n.states,r="chart"===i.relativeTo?null:"plotBox";this.resetZoomButton=t.renderer.button(e.resetZoom,null,null,function(){t.zoomOut()},n,o&&o.hover).attr({align:i.position.align,title:e.resetZoomTitle}).add().align(i.position,!1,r)},zoomOut:function(){var t=this;Ae(t,"selection",{resetSelection:!0},function(){t.zoom()})},zoom:function(t){var e,i,n=this,o=n.pointer,s=!1;!t||t.resetSelection?be(n.axes,function(t){e=t.zoom()}):be(t.xAxis.concat(t.yAxis),function(t){var i=t.axis,n=i.isXAxis;(o[n?"zoomX":"zoomY"]||o[n?"pinchX":"pinchY"])&&(e=i.zoom(t.min,t.max),i.displayBtn&&(s=!0))}),i=n.resetZoomButton,s&&!i?n.showResetZoom():!s&&r(i)&&(n.resetZoomButton=i.destroy()),e&&n.redraw(f(n.options.chart.animation,t&&t.animation,n.pointCount<100))},pan:function(t,e){var i,n=this,o=n.hoverPoints;o&&be(o,function(t){t.setState()}),be("xy"===e?[1,0]:[1],function(e){var o=t[e?"chartX":"chartY"],r=n[e?"xAxis":"yAxis"][0],s=n[e?"mouseDownX":"mouseDownY"],a=(r.pointRange||0)/2,l=r.getExtremes(),h=r.toValue(s-o,!0)+a,c=r.toValue(s+n[e?"plotWidth":"plotHeight"]-o,!0)-a;r.series.length&&h>kt(l.dataMin,l.min)&&c<bt(l.dataMax,l.max)&&(r.setExtremes(h,c,!1,!1,{trigger:"pan"}),i=!0),n[e?"mouseDownX":"mouseDownY"]=o}),i&&n.redraw(!1),g(n.container,{cursor:"move"})},setTitle:function(t,i){var n,o,r=this,s=r.options;n=s.title=e(s.title,t),o=s.subtitle=e(s.subtitle,i),be([["title",t,n],["subtitle",i,o]],function(t){var e=t[0],i=r[e],n=t[1],o=t[2];i&&n&&(r[e]=i=i.destroy()),o&&o.text&&!i&&(r[e]=r.renderer.text(o.text,0,0,o.useHTML).attr({align:o.align,"class":Kt+e,zIndex:o.zIndex||4}).css(o.style).add())}),r.layOutTitles()},layOutTitles:function(){var e=0,i=this.title,n=this.subtitle,o=this.options,r=o.title,s=o.subtitle,a=this.spacingBox.width-44;i&&(i.css({width:(r.width||a)+qt}).align(t({y:15},r),!1,"spacingBox"),r.floating||r.verticalAlign||(e=i.getBBox().height,e>=18&&e<=25&&(e=15))),n&&(n.css({width:(s.width||a)+qt}).align(t({y:e+r.margin},s),!1,"spacingBox"),s.floating||s.verticalAlign||(e=vt(e+n.getBBox().height))),this.titleOffset=e},getChartSize:function(){var t=this,e=t.options.chart,i=t.renderToClone||t.renderTo;t.containerWidth=ye(i,"width"),t.containerHeight=ye(i,"height"),t.chartWidth=bt(0,e.width||t.containerWidth||600),t.chartHeight=bt(0,f(e.height,t.containerHeight>19?t.containerHeight:400))},cloneRenderTo:function(t){var e=this.renderToClone,i=this.container;t?e&&(this.renderTo.appendChild(i),z(e),delete this.renderToClone):(i&&i.parentNode===this.renderTo&&this.renderTo.removeChild(i),this.renderToClone=e=this.renderTo.cloneNode(0),g(e,{position:_t,top:"-9999px",display:"block"}),ft.body.appendChild(e),i&&e.appendChild(i))},getContainer:function(){var e,i,r,s,a,l,h=this,c=h.options.chart,d="data-highcharts-chart";h.renderTo=s=c.renderTo,l=Kt+Et++,o(s)&&(h.renderTo=s=ft.getElementById(s)),s||B(13,!0),a=n(p(s,d)),!isNaN(a)&&Nt[a]&&Nt[a].destroy(),p(s,d,h.index),s.innerHTML="",s.offsetWidth||h.cloneRenderTo(),h.getChartSize(),i=h.chartWidth,r=h.chartHeight,h.container=e=m(jt,{className:Kt+"container"+(c.className?" "+c.className:""),id:l},t({position:Ut,overflow:Zt,width:i+qt,height:r+qt,textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)"},c.style),h.renderToClone||s),h._cursor=e.style.cursor,h.renderer=c.forExport?new Oe(e,i,r,(!0)):new K(e,i,r),Xt&&h.renderer.create(h,e,i,r)},getMargins:function(){var t,e=this,i=e.spacing,n=e.legend,o=e.margin,r=e.options.legend,s=f(r.margin,10),a=r.x,l=r.y,h=r.align,c=r.verticalAlign,p=e.titleOffset;e.resetMargins(),t=e.axisOffset,p&&!d(o[0])&&(e.plotTop=bt(e.plotTop,p+e.options.title.margin+i[0])),n.display&&!r.floating&&("right"===h?d(o[1])||(e.marginRight=bt(e.marginRight,n.legendWidth-a+s+i[1])):"left"===h?d(o[3])||(e.plotLeft=bt(e.plotLeft,n.legendWidth+a+s+i[3])):"top"===c?d(o[0])||(e.plotTop=bt(e.plotTop,n.legendHeight+l+s+i[0])):"bottom"===c&&(d(o[2])||(e.marginBottom=bt(e.marginBottom,n.legendHeight-l+s+i[2])))),e.extraBottomMargin&&(e.marginBottom+=e.extraBottomMargin),e.extraTopMargin&&(e.plotTop+=e.extraTopMargin),e.hasCartesianSeries&&be(e.axes,function(t){t.getOffset()}),d(o[3])||(e.plotLeft+=t[3]),d(o[0])||(e.plotTop+=t[0]),d(o[2])||(e.marginBottom+=t[2]),d(o[1])||(e.marginRight+=t[1]),e.setChartSize()},initReflow:function(){function t(t){var r=n.width||ye(o,"width"),s=n.height||ye(o,"height"),a=t?t.target:gt;i.hasUserSize||!r||!s||a!==gt&&a!==ft||(r===i.containerWidth&&s===i.containerHeight||(clearTimeout(e),i.reflowTimeout=e=setTimeout(function(){i.container&&(i.setSize(r,s,!1),i.hasUserSize=null)},100)),i.containerWidth=r,i.containerHeight=s)}var e,i=this,n=i.options.chart,o=i.renderTo;i.reflow=t,Se(gt,"resize",t),Se(i,"destroy",function(){Pe(gt,"resize",t)})},setSize:function(t,e,i){var n,o,r,s=this;s.isResizing+=1,r=function(){s&&Ae(s,"endResize",null,function(){s.isResizing-=1})},R(i,s),s.oldChartHeight=s.chartHeight,s.oldChartWidth=s.chartWidth,d(t)&&(s.chartWidth=n=bt(0,yt(t)),s.hasUserSize=!!n),d(e)&&(s.chartHeight=o=bt(0,yt(e))),g(s.container,{width:n+qt,height:o+qt}),s.setChartSize(!0),s.renderer.setSize(n,o,i),s.maxTicks=null,be(s.axes,function(t){t.isDirty=!0,t.setScale()}),be(s.series,function(t){t.isDirty=!0}),s.isDirtyLegend=!0,s.isDirtyBox=!0,s.getMargins(),s.redraw(i),s.oldChartHeight=null,Ae(s,"resize"),Q===!1?r():setTimeout(r,Q&&Q.duration||500)},setChartSize:function(t){var e,i,n,o,r,s,a,l=this,h=l.inverted,c=l.renderer,d=l.chartWidth,p=l.chartHeight,u=l.options.chart,f=l.spacing,g=l.clipOffset;l.plotLeft=n=yt(l.plotLeft),l.plotTop=o=yt(l.plotTop),l.plotWidth=r=bt(0,yt(d-n-l.marginRight)),l.plotHeight=s=bt(0,yt(p-o-l.marginBottom)),l.plotSizeX=h?s:r,l.plotSizeY=h?r:s,l.plotBorderWidth=u.plotBorderWidth||0,l.spacingBox=c.spacingBox={x:f[3],y:f[0],width:d-f[3]-f[1],height:p-f[0]-f[2]},l.plotBox=c.plotBox={x:n,y:o,width:r,height:s},a=2*xt(l.plotBorderWidth/2),e=vt(bt(a,g[3])/2),i=vt(bt(a,g[0])/2),l.clipBox={x:e,y:i,width:xt(l.plotSizeX-bt(a,g[1])/2-e),height:xt(l.plotSizeY-bt(a,g[2])/2-i)},t||be(l.axes,function(t){t.setAxisSize(),t.setAxisTranslation()})},resetMargins:function(){var t=this,e=t.spacing,i=t.margin;t.plotTop=f(i[0],e[0]),t.marginRight=f(i[1],e[1]),t.marginBottom=f(i[2],e[2]),t.plotLeft=f(i[3],e[3]),t.axisOffset=[0,0,0,0],t.clipOffset=[0,0,0,0]},drawChartBox:function(){var t,e,i=this,n=i.options.chart,o=i.renderer,r=i.chartWidth,s=i.chartHeight,a=i.chartBackground,l=i.plotBackground,h=i.plotBorder,c=i.plotBGImage,d=n.borderWidth||0,p=n.backgroundColor,u=n.plotBackgroundColor,f=n.plotBackgroundImage,g=n.plotBorderWidth||0,m=i.plotLeft,y=i.plotTop,x=i.plotWidth,v=i.plotHeight,b=i.plotBox,k=i.clipRect,w=i.clipBox;t=d+(n.shadow?8:0),(d||p)&&(a?a.animate(a.crisp(null,null,null,r-t,s-t)):(e={fill:p||Jt},d&&(e.stroke=n.borderColor,e["stroke-width"]=d),i.chartBackground=o.rect(t/2,t/2,r-t,s-t,n.borderRadius,d).attr(e).add().shadow(n.shadow))),
u&&(l?l.animate(b):i.plotBackground=o.rect(m,y,x,v,0).attr({fill:u}).add().shadow(n.plotShadow)),f&&(c?c.animate(b):i.plotBGImage=o.image(f,m,y,x,v).add()),k?k.animate({width:w.width,height:w.height}):i.clipRect=o.clipRect(w),g&&(h?h.animate(h.crisp(null,m,y,x,v)):i.plotBorder=o.rect(m,y,x,v,0,-g).attr({stroke:n.plotBorderColor,"stroke-width":g,zIndex:1}).add()),i.isDirtyBox=!1},propFromSeries:function(){var t,e,i,n=this,o=n.options.chart,r=n.options.series;be(["inverted","angular","polar"],function(s){for(t=fe[o.type||o.defaultSeriesType],i=n[s]||o[s]||t&&t.prototype[s],e=r&&r.length;!i&&e--;)t=fe[r[e].type],t&&t.prototype[s]&&(i=!0);n[s]=i})},linkSeries:function(){var t=this,e=t.series;be(e,function(t){t.linkedSeries.length=0}),be(e,function(e){var i=e.options.linkedTo;o(i)&&(i=":previous"===i?t.series[e.index-1]:t.get(i),i&&(i.linkedSeries.push(e),e.linkedParent=i))})},render:function(){var e,i=this,o=i.axes,r=i.renderer,s=i.options,a=s.labels,l=s.credits;i.setTitle(),i.legend=new _(i,s.legend),i.getStacks(),be(o,function(t){t.setScale()}),i.getMargins(),i.maxTicks=null,be(o,function(t){t.setTickPositions(!0),t.setMaxTicks()}),i.adjustTickAmounts(),i.getMargins(),i.drawChartBox(),i.hasCartesianSeries&&be(o,function(t){t.render()}),i.seriesGroup||(i.seriesGroup=r.g("series-group").attr({zIndex:3}).add()),be(i.series,function(t){t.translate(),t.setTooltipPoints(),t.render()}),a.items&&be(a.items,function(e){var o=t(a.style,e.style),s=n(o.left)+i.plotLeft,l=n(o.top)+i.plotTop+12;delete o.left,delete o.top,r.text(e.html,s,l).attr({zIndex:2}).css(o).add()}),l.enabled&&!i.credits&&(e=l.href,i.credits=r.text(l.text,0,0).on("click",function(){e&&(location.href=e)}).attr({align:l.position.align,zIndex:8}).css(l.style).add().align(l.position)),i.hasRendered=!0},destroy:function(){var t,e=this,i=e.axes,n=e.series,o=e.container,r=o&&o.parentNode;for(Ae(e,"destroy"),Nt[e.index]=Z,e.renderTo.removeAttribute("data-highcharts-chart"),Pe(e),t=i.length;t--;)i[t]=i[t].destroy();for(t=n.length;t--;)n[t]=n[t].destroy();be(["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","scroller","rangeSelector","legend","resetZoomButton","tooltip","renderer"],function(t){var i=e[t];i&&i.destroy&&(e[t]=i.destroy())}),o&&(o.innerHTML="",Pe(o),r&&z(o));for(t in e)delete e[t]},isReadyToRender:function(){var t=this;return!(!Rt&&gt==gt.top&&"complete"!==ft.readyState||Xt&&!gt.canvg)||(Xt?Ye.push(function(){t.firstRender()},t.options.global.canvasToolsURL):ft.attachEvent("onreadystatechange",function(){ft.detachEvent("onreadystatechange",t.firstRender),"complete"===ft.readyState&&t.firstRender()}),!1)},firstRender:function(){var t=this,e=t.options,i=t.callback;t.isReadyToRender()&&(t.getContainer(),Ae(t,"init"),t.resetMargins(),t.setChartSize(),t.propFromSeries(),t.getAxes(),be(e.series||[],function(e){t.initSeries(e)}),t.linkSeries(),Ae(t,"beforeRender"),t.pointer=new j(t,e),t.render(),t.renderer.draw(),i&&i.apply(t,[t]),be(t.callbacks,function(e){e.apply(t,[t])}),t.cloneRenderTo(!0),Ae(t,"load"))},splashArray:function(t,e){var i=e[t],n=r(i)?i:[i,i,i,i];return[f(e[t+"Top"],n[0]),f(e[t+"Right"],n[1]),f(e[t+"Bottom"],n[2]),f(e[t+"Left"],n[3])]}},U.prototype.callbacks=[];var Ee=function(){};Ee.prototype={init:function(t,e,i){var n,o=this;return o.series=t,o.applyOptions(e,i),o.pointAttr={},t.options.colorByPoint&&(n=t.options.colors||t.chart.options.colors,o.color=o.color||n[t.colorCounter++],t.colorCounter===n.length&&(t.colorCounter=0)),t.chart.pointCount++,o},applyOptions:function(e,i){var n=this,o=n.series,r=o.pointValKey;return e=Ee.prototype.optionsToObject.call(this,e),t(n,e),n.options=n.options?t(n.options,e):e,r&&(n.y=n[r]),n.x===Z&&o&&(n.x=i===Z?o.autoIncrement():i),n},optionsToObject:function(t){var e,i,n=this.series,o=n.pointArrayMap||["y"],r=o.length,a=0,l=0;if("number"==typeof t||null===t)e={y:t};else if(s(t))for(e={},t.length>r&&(i=typeof t[0],"string"===i?e.name=t[0]:"number"===i&&(e.x=t[0]),a++);l<r;)e[o[l++]]=t[a++];else"object"==typeof t&&(e=t,t.dataLabels&&(n._hasPointLabels=!0),t.marker&&(n._hasPointMarkers=!0));return e},destroy:function(){var t,e=this,i=e.series,n=i.chart,o=n.hoverPoints;n.pointCount--,o&&(e.setState(),c(o,e),o.length||(n.hoverPoints=null)),e===n.hoverPoint&&e.onMouseOut(),(e.graphic||e.dataLabel)&&(Pe(e),e.destroyElements()),e.legendItem&&n.legend.destroyItem(e);for(t in e)e[t]=null},destroyElements:function(){for(var t,e=this,i=["graphic","dataLabel","dataLabelUpper","group","connector","shadowGroup"],n=6;n--;)t=i[n],e[t]&&(e[t]=e[t].destroy())},getLabelConfig:function(){var t=this;return{x:t.category,y:t.y,key:t.name||t.category,series:t.series,point:t,percentage:t.percentage,total:t.total||t.stackTotal}},select:function(t,e){var i=this,n=i.series,o=n.chart;t=f(t,!i.selected),i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,n.options.data[ve(i,n.data)]=i.options,i.setState(t&&oe),e||be(o.getSelectedPoints(),function(t){t.selected&&t!==i&&(t.selected=t.options.selected=!1,n.options.data[ve(t,n.data)]=t.options,t.setState(ie),t.firePointEvent("unselect"))})})},onMouseOver:function(t){var e=this,i=e.series,n=i.chart,o=n.tooltip,r=n.hoverPoint;r&&r!==e&&r.onMouseOut(),e.firePointEvent("mouseOver"),!o||o.shared&&!i.noSharedTooltip||o.refresh(e,t),e.setState(ne),n.hoverPoint=e},onMouseOut:function(){var t=this.series.chart,e=t.hoverPoints;e&&ve(this,e)!==-1||(this.firePointEvent("mouseOut"),this.setState(),t.hoverPoint=null)},tooltipFormatter:function(t){var e=this.series,i=e.tooltipOptions,n=f(i.valueDecimals,""),o=i.valuePrefix||"",r=i.valueSuffix||"";return be(e.pointArrayMap||["y"],function(e){e="{point."+e,(o||r)&&(t=t.replace(e+"}",o+e+"}"+r)),t=t.replace(e+"}",e+":,."+n+"f}")}),w(t,{point:this,series:this.series})},update:function(t,e,i){var n,o=this,s=o.series,a=o.graphic,l=s.data,h=s.chart,c=s.options;e=f(e,!0),o.firePointEvent("update",{options:t},function(){o.applyOptions(t),r(t)&&(s.getAttribs(),a&&(t.marker&&t.marker.symbol?o.graphic=a.destroy():a.attr(o.pointAttr[o.state||""]))),n=ve(o,l),s.xData[n]=o.x,s.yData[n]=s.toYData?s.toYData(o):o.y,s.zData[n]=o.z,c.data[n]=o.options,s.isDirty=s.isDirtyData=!0,!s.fixedBox&&s.hasCartesianSeries&&(h.isDirtyBox=!0),"point"===c.legendType&&h.legend.destroyItem(o),e&&h.redraw(i)})},remove:function(t,e){var i,n=this,o=n.series,r=o.points,s=o.chart,a=o.data;R(e,s),t=f(t,!0),n.firePointEvent("remove",null,function(){i=ve(n,a),a.length===r.length&&r.splice(i,1),a.splice(i,1),o.options.data.splice(i,1),o.xData.splice(i,1),o.yData.splice(i,1),o.zData.splice(i,1),n.destroy(),o.isDirty=!0,o.isDirtyData=!0,t&&s.redraw()})},firePointEvent:function(t,e,i){var n=this,o=this.series,r=o.options;(r.point.events[t]||n.options&&n.options.events&&n.options.events[t])&&this.importEvents(),"click"===t&&r.allowPointSelect&&(i=function(t){n.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),Ae(this,t,e,i)},importEvents:function(){if(!this.hasImportedEvents){var t,i=this,n=e(i.series.options.point,i.options),o=n.events;i.events=o;for(t in o)Se(i,t,o[t]);this.hasImportedEvents=!0}},setState:function(t){var i,n,o=this,r=o.plotX,s=o.plotY,a=o.series,l=a.options.states,h=Ie[a.type].marker&&a.options.marker,c=h&&!h.enabled,d=h&&h.states[t],p=d&&d.enabled===!1,u=a.stateMarkerGraphic,f=o.marker||{},g=a.chart,m=o.pointAttr;t=t||ie,t===o.state||o.selected&&t!==oe||l[t]&&l[t].enabled===!1||t&&(p||c&&!d.enabled)||(o.graphic?(i=h&&o.graphic.symbolName&&m[t].r,o.graphic.attr(e(m[t],i?{x:r-i,y:s-i,width:2*i,height:2*i}:{}))):(t&&d&&(i=d.radius,n=f.symbol||a.symbol,u&&u.currentSymbol!==n&&(u=u.destroy()),u?u.attr({x:r-i,y:s-i}):(a.stateMarkerGraphic=u=g.renderer.symbol(n,r-i,s-i,2*i,2*i).attr(m[t]).add(a.markerGroup),u.currentSymbol=n)),u&&u[t&&g.isInsidePlot(r,s)?"show":"hide"]()),o.state=t)}};var Ge=function(){};Ge.prototype={isCartesian:!0,type:"line",pointClass:Ee,sorted:!0,requireSorting:!0,pointAttrToOptions:{stroke:"lineColor","stroke-width":"lineWidth",fill:"fillColor",r:"radius"},colorCounter:0,init:function(e,i){var n,o,r=this,s=e.series;r.chart=e,r.options=i=r.setOptions(i),r.linkedSeries=[],r.bindAxes(),t(r,{name:i.name,state:ie,pointAttr:{},visible:i.visible!==!1,selected:i.selected===!0}),Xt&&(i.animation=!1),o=i.events;for(n in o)Se(r,n,o[n]);(o&&o.click||i.point&&i.point.events&&i.point.events.click||i.allowPointSelect)&&(e.runTrackerClick=!0),r.getColor(),r.getSymbol(),r.setData(i.data,!1),r.isCartesian&&(e.hasCartesianSeries=!0),s.push(r),r._i=s.length-1,C(s,function(t,e){return f(t.options.index,t._i)-f(e.options.index,t._i)}),be(s,function(t,e){t.index=e,t.name=t.name||"Series "+(e+1)})},bindAxes:function(){var t,e=this,i=e.options,n=e.chart;e.isCartesian&&be(["xAxis","yAxis"],function(o){be(n[o],function(n){t=n.options,(i[o]===t.index||i[o]!==Z&&i[o]===t.id||i[o]===Z&&0===t.index)&&(n.series.push(e),e[o]=n,n.isDirty=!0)}),e[o]||B(18,!0)})},autoIncrement:function(){var t=this,e=t.options,i=t.xIncrement;return i=f(i,e.pointStart,0),t.pointInterval=f(t.pointInterval,e.pointInterval,1),t.xIncrement=i+t.pointInterval,i},getSegments:function(){var t,e=this,i=-1,n=[],o=e.points,r=o.length;if(r)if(e.options.connectNulls){for(t=r;t--;)null===o[t].y&&o.splice(t,1);o.length&&(n=[o])}else be(o,function(t,e){null===t.y?(e>i+1&&n.push(o.slice(i+1,e)),i=e):e===r-1&&n.push(o.slice(i+1,e+1))});e.segments=n},setOptions:function(t){var i,n=this.chart,o=n.options,r=o.plotOptions,s=r[this.type];return this.userOptions=t,i=e(s,r.series,t),this.tooltipOptions=e(o.tooltip,i.tooltip),null===s.marker&&delete i.marker,i},getColor:function(){var t,e,i=this.options,n=this.userOptions,o=this.chart.options.colors,r=this.chart.counters;t=i.color||Ie[this.type].color,t||i.colorByPoint||(d(n._colorIndex)?e=n._colorIndex:(n._colorIndex=r.color,e=r.color++),t=o[e]),this.color=t,r.wrapColor(o.length)},getSymbol:function(){var t,e=this,i=e.userOptions,n=e.options.marker,o=e.chart,r=o.options.symbols,s=o.counters;e.symbol=n.symbol,e.symbol||(d(i._symbolIndex)?t=i._symbolIndex:(i._symbolIndex=s.symbol,t=s.symbol++),e.symbol=r[t]),/^url/.test(e.symbol)&&(n.radius=0),s.wrapSymbol(r.length)},drawLegendSymbol:function(t){var e,i,n,o=this.options,r=o.marker,s=t.options,a=s.symbolWidth,l=this.chart.renderer,h=this.legendGroup,c=t.baseline-yt(.3*l.fontMetrics(s.itemStyle.fontSize).b);o.lineWidth&&(n={"stroke-width":o.lineWidth},o.dashStyle&&(n.dashstyle=o.dashStyle),this.legendLine=l.path([Qt,0,c,te,a,c]).attr(n).add(h)),r&&r.enabled&&(e=r.radius,this.legendSymbol=i=l.symbol(this.symbol,a/2-e,c-e,2*e,2*e).add(h),i.isMarker=!0)},addPoint:function(t,e,i,n){var o,r,s,a,l=this,h=l.options,c=l.data,d=l.graph,p=l.area,u=l.chart,g=l.xData,m=l.yData,y=l.zData,x=l.names,v=d&&d.shift||0,b=h.data;if(R(n,u),i&&be([d,p,l.graphNeg,l.areaNeg],function(t){t&&(t.shift=v+1)}),p&&(p.isArea=!0),e=f(e,!0),o={series:l},l.pointClass.prototype.applyOptions.apply(o,[t]),s=o.x,a=g.length,l.requireSorting&&s<g[a-1])for(r=!0;a&&g[a-1]>s;)a--;g.splice(a,0,s),m.splice(a,0,l.toYData?l.toYData(o):o.y),y.splice(a,0,o.z),x&&(x[s]=o.name),b.splice(a,0,t),r&&(l.data.splice(a,0,null),l.processData()),"point"===h.legendType&&l.generatePoints(),i&&(c[0]&&c[0].remove?c[0].remove(!1):(c.shift(),g.shift(),m.shift(),y.shift(),b.shift())),l.isDirty=!0,l.isDirtyData=!0,e&&(l.getAttribs(),u.redraw())},setData:function(t,e){var i,n=this,r=n.points,l=n.options,h=n.chart,c=null,d=n.xAxis,p=d&&d.categories&&!d.categories.length?[]:null;n.xIncrement=null,n.pointRange=d&&d.categories?1:l.pointRange,n.colorCounter=0;var u,g=[],m=[],y=[],x=t?t.length:[],v=f(l.turboThreshold,1e3),b=n.pointArrayMap,k=b&&b.length,w=!!n.toYData;if(v&&x>v){for(i=0;null===c&&i<x;)c=t[i],i++;if(a(c)){var T=f(l.pointStart,0),S=f(l.pointInterval,1);for(i=0;i<x;i++)g[i]=T,m[i]=t[i],T+=S;n.xIncrement=T}else if(s(c))if(k)for(i=0;i<x;i++)u=t[i],g[i]=u[0],m[i]=u.slice(1,k+1);else for(i=0;i<x;i++)u=t[i],g[i]=u[0],m[i]=u[1];else B(12)}else for(i=0;i<x;i++)t[i]!==Z&&(u={series:n},n.pointClass.prototype.applyOptions.apply(u,[t[i]]),g[i]=u.x,m[i]=w?n.toYData(u):u.y,y[i]=u.z,p&&u.name&&(p[u.x]=u.name));for(o(m[0])&&B(14,!0),n.data=[],n.options.data=t,n.xData=g,n.yData=m,n.zData=y,n.names=p,i=r&&r.length||0;i--;)r[i]&&r[i].destroy&&r[i].destroy();d&&(d.minRange=d.userMinRange),n.isDirty=n.isDirtyData=h.isDirtyBox=!0,f(e,!0)&&h.redraw(!1)},remove:function(t,e){var i=this,n=i.chart;t=f(t,!0),i.isRemoving||(i.isRemoving=!0,Ae(i,"remove",null,function(){i.destroy(),n.isDirtyLegend=n.isDirtyBox=!0,n.linkSeries(),t&&n.redraw(e)})),i.isRemoving=!1},processData:function(t){var e,i,n,o,r,s=this,a=s.xData,l=s.yData,h=a.length,c=0,d=s.xAxis,p=s.options,u=p.cropThreshold,f=s.isCartesian;if(f&&!s.isDirty&&!d.isDirty&&!s.yAxis.isDirty&&!t)return!1;if(f&&s.sorted&&(!u||h>u||s.forceCrop)){var g=d.min,m=d.max;a[h-1]<g||a[0]>m?(a=[],l=[]):(a[0]<g||a[h-1]>m)&&(e=this.cropData(s.xData,s.yData,g,m),a=e.xData,l=e.yData,c=e.start,i=!0)}for(r=a.length-1;r>=0;r--)n=a[r]-a[r-1],n>0&&(o===Z||n<o)?o=n:n<0&&s.requireSorting&&B(15);s.cropped=i,s.cropStart=c,s.processedXData=a,s.processedYData=l,null===p.pointRange&&(s.pointRange=o||1),s.closestPointRange=o},cropData:function(t,e,i,n){var o,r=t.length,s=0,a=r,l=f(this.cropShoulder,1);for(o=0;o<r;o++)if(t[o]>=i){s=bt(0,o-l);break}for(;o<r;o++)if(t[o]>n){a=o+l;break}return{xData:t.slice(s,a),yData:e.slice(s,a),start:s,end:a}},generatePoints:function(){var t,e,i,n,o=this,r=o.options,s=r.data,a=o.data,l=o.processedXData,h=o.processedYData,c=o.pointClass,d=l.length,p=o.cropStart||0,f=o.hasGroupedData,g=[];if(!a&&!f){var m=[];m.length=s.length,a=o.data=m}for(n=0;n<d;n++)e=p+n,f?g[n]=(new c).init(o,[l[n]].concat(u(h[n]))):(a[e]?i=a[e]:s[e]!==Z&&(a[e]=i=(new c).init(o,s[e],l[n])),g[n]=i);if(a&&(d!==(t=a.length)||f))for(n=0;n<t;n++)n!==p||f||(n+=d),a[n]&&(a[n].destroyElements(),a[n].plotX=Z);o.data=a,o.points=g},setStackedPoints:function(){if(this.options.stacking&&(this.visible===!0||this.chart.options.chart.ignoreHiddenSeries===!1)){var t,e,i,n,o,r,s,a=this,l=a.processedXData,h=a.processedYData,c=[],d=h.length,p=a.options,u=p.threshold,f=p.stack,g=p.stacking,m=a.stackKey,y="-"+m,x=a.negStacks,v=a.yAxis,b=v.stacks,k=v.oldStacks;for(o=0;o<d;o++)r=l[o],s=h[o],t=x&&s<u,n=t?y:m,b[n]||(b[n]={}),b[n][r]||(k[n]&&k[n][r]?(b[n][r]=k[n][r],b[n][r].total=null):b[n][r]=new N(v,v.options.stackLabels,t,r,f,g)),e=b[n][r],e.points[a.index]=[e.cum||0],"percent"===g?(i=t?m:y,x&&b[i]&&b[i][r]?(i=b[i][r],e.total=i.total=bt(i.total,e.total)+wt(s)||0):e.total+=wt(s)||0):e.total+=s||0,e.cum=(e.cum||0)+(s||0),e.points[a.index].push(e.cum),c[o]=e.cum;"percent"===g&&(v.usePercentage=!0),this.stackedYData=c,v.oldStacks={}}},setPercentStacks:function(){var t=this,e=t.stackKey,i=t.yAxis.stacks;be([e,"-"+e],function(e){for(var n,o,r,s,a=t.xData.length;a--;)n=t.xData[a],o=i[e]&&i[e][n],r=o&&o.points[t.index],r&&(s=o.total?100/o.total:0,r[0]=O(r[0]*s),r[1]=O(r[1]*s),t.stackedYData[a]=r[1])})},getExtremes:function(){var t,e,i,n,o,r,s,a,l=this.xAxis,h=this.yAxis,c=this.processedXData,d=this.stackedYData||this.processedYData,p=d.length,u=[],g=0,m=l.getExtremes(),y=m.min,x=m.max;for(s=0;s<p;s++)if(o=c[s],r=d[s],t=null!==r&&r!==Z&&(!h.isLog||r.length||r>0),e=this.getExtremesFromAll||this.cropped||(c[s+1]||o)>=y&&(c[s-1]||o)<=x,t&&e)if(a=r.length)for(;a--;)null!==r[a]&&(u[g++]=r[a]);else u[g++]=r;this.dataMin=f(i,M(u)),this.dataMax=f(n,D(u))},translate:function(){this.processedXData||this.processData(),this.generatePoints();var t,e=this,i=e.options,n=i.stacking,o=e.xAxis,r=o.categories,s=e.yAxis,l=e.points,h=l.length,c=!!e.modifyValue,p=i.pointPlacement,u="between"===p||a(p),g=i.threshold;for(t=0;t<h;t++){var m,y,x=l[t],v=x.x,b=x.y,k=x.low,w=s.stacks[(e.negStacks&&b<g?"-":"")+e.stackKey];s.isLog&&b<=0&&(x.y=b=null),x.plotX=o.translate(v,0,0,0,1,p,"flags"===this.type),n&&e.visible&&w&&w[v]&&(m=w[v],y=m.points[e.index],k=y[0],b=y[1],0===k&&(k=f(g,s.min)),s.isLog&&k<=0&&(k=null),x.percentage="percent"===n&&b,x.total=x.stackTotal=m.total,x.stackY=b,m.setOffset(e.pointXOffset||0,e.barW||0)),x.yBottom=d(k)?s.translate(k,0,1,0,1):null,c&&(b=e.modifyValue(b,x)),x.plotY="number"==typeof b&&b!==1/0?s.translate(b,0,1,0,1):Z,x.clientX=u?o.translate(v,0,0,0,1):x.plotX,x.negative=x.y<(g||0),x.category=r&&r[x.x]!==Z?r[x.x]:x.x}e.getSegments()},setTooltipPoints:function(t){var e,i,n,o,r,s,a,l=this,h=[],c=l.xAxis,d=c&&c.getExtremes(),p=c?c.tooltipLen||c.len:l.chart.plotSizeX,u=[];if(l.options.enableMouseTracking!==!1){for(t&&(l.tooltipPoints=null),be(l.segments||l.points,function(t){h=h.concat(t)}),c&&c.reversed&&(h=h.reverse()),l.orderTooltipPoints&&l.orderTooltipPoints(h),e=h.length,a=0;a<e;a++)if(o=h[a],r=o.x,r>=d.min&&r<=d.max)for(s=h[a+1],i=n===Z?0:n+1,n=h[a+1]?kt(bt(0,xt((o.clientX+(s?s.wrappedClientX||s.clientX:p))/2)),p):p;i>=0&&i<=n;)u[i++]=o;l.tooltipPoints=u}},tooltipHeaderFormatter:function(t){var e,i=this,n=i.tooltipOptions,o=n.xDateFormat,r=n.dateTimeLabelFormats,s=i.xAxis,l=s&&"datetime"===s.options.type,h=n.headerFormat,c=s&&s.closestPointRange;if(l&&!o)if(c){for(e in et)if(et[e]>=c){o=r[e];break}}else o=r.day;return l&&o&&a(t.key)&&(h=h.replace("{point.key}","{point.key:"+o+"}")),w(h,{point:t,series:i})},onMouseOver:function(){var t=this,e=t.chart,i=e.hoverSeries;i&&i!==t&&i.onMouseOut(),t.options.events.mouseOver&&Ae(t,"mouseOver"),t.setState(ne),e.hoverSeries=t},onMouseOut:function(){var t=this,e=t.options,i=t.chart,n=i.tooltip,o=i.hoverPoint;o&&o.onMouseOut(),t&&e.events.mouseOut&&Ae(t,"mouseOut"),!n||e.stickyTracking||n.shared&&!t.noSharedTooltip||n.hide(),t.setState(),i.hoverSeries=null},animate:function(e){var i,n,o,s=this,a=s.chart,l=a.renderer,h=s.options.animation,c=a.clipBox,d=a.inverted;h&&!r(h)&&(h=Ie[s.type].animation),o="_sharedClip"+h.duration+h.easing,e?(i=a[o],n=a[o+"m"],i||(a[o]=i=l.clipRect(t(c,{width:0})),a[o+"m"]=n=l.clipRect(-99,d?-a.plotLeft:-a.plotTop,99,d?a.chartWidth:a.chartHeight)),s.group.clip(i),s.markerGroup.clip(n),s.sharedClipKey=o):(i=a[o],i&&(i.animate({width:a.plotSizeX},h),a[o+"m"].animate({width:a.plotSizeX+99},h)),s.animate=null,s.animationTimeout=setTimeout(function(){s.afterAnimate()},h.duration))},afterAnimate:function(){var t=this.chart,e=this.sharedClipKey,i=this.group;i&&this.options.clip!==!1&&(i.clip(t.clipRect),this.markerGroup.clip()),setTimeout(function(){e&&t[e]&&(t[e]=t[e].destroy(),t[e+"m"]=t[e+"m"].destroy())},100)},drawPoints:function(){var e,i,n,o,r,s,a,l,h,c,d,p,u=this,g=u.points,m=u.chart,y=u.options,x=y.marker,v=u.markerGroup;if(x.enabled||u._hasPointMarkers)for(o=g.length;o--;)r=g[o],i=xt(r.plotX),n=r.plotY,h=r.graphic,c=r.marker||{},d=x.enabled&&c.enabled===Z||c.enabled,p=m.isInsidePlot(yt(i),n,m.inverted),d&&n!==Z&&!isNaN(n)&&null!==r.y?(e=r.pointAttr[r.selected?oe:ie],s=e.r,a=f(c.symbol,u.symbol),l=0===a.indexOf("url"),h?h.attr({visibility:p?Rt?"inherit":$t:Zt}).animate(t({x:i-s,y:n-s},h.symbolName?{width:2*s,height:2*s}:{})):p&&(s>0||l)&&(r.graphic=h=m.renderer.symbol(a,i-s,n-s,2*s,2*s).attr(e).add(v))):h&&(r.graphic=h.destroy())},convertAttribs:function(t,e,i,n){var o,r,s=this.pointAttrToOptions,a={};t=t||{},e=e||{},i=i||{},n=n||{};for(o in s)r=s[o],a[o]=f(t[r],e[o],i[o],n[o]);return a},getAttribs:function(){var e,i,n,o,r,s,a=this,l=a.options,h=Ie[a.type].marker?l.marker:l,c=h.states,p=c[ne],u=a.color,f={stroke:u,fill:u},g=a.points||[],m=[],y=a.pointAttrToOptions,x=l.negativeColor,v=h.lineColor;for(l.marker?(p.radius=p.radius||h.radius+2,p.lineWidth=p.lineWidth||h.lineWidth+1):p.color=p.color||Be(p.color||u).brighten(p.brightness).get(),m[ie]=a.convertAttribs(h,f),be([ne,oe],function(t){m[t]=a.convertAttribs(c[t],m[ie])}),a.pointAttr=m,i=g.length;i--;){if(n=g[i],h=n.options&&n.options.marker||n.options,h&&h.enabled===!1&&(h.radius=0),n.negative&&x&&(n.color=n.fillColor=x),r=l.colorByPoint||n.color,n.options)for(s in y)d(h[y[s]])&&(r=!0);r?(h=h||{},o=[],c=h.states||{},e=c[ne]=c[ne]||{},l.marker||(e.color=Be(e.color||n.color).brighten(e.brightness||p.brightness).get()),o[ie]=a.convertAttribs(t({color:n.color,fillColor:n.color,lineColor:null===v?n.color:Z},h),m[ie]),o[ne]=a.convertAttribs(c[ne],m[ne],o[ie]),o[oe]=a.convertAttribs(c[oe],m[oe],o[ie])):o=m,n.pointAttr=o}},update:function(i,n){var o,r=this.chart,s=this.userOptions,a=this.type,l=fe[a].prototype;i=e(s,{animation:!1,index:this.index,pointStart:this.xData[0]},{data:this.options.data},i),this.remove(!1);for(o in l)l.hasOwnProperty(o)&&(this[o]=Z);t(this,fe[i.type||a].prototype),this.init(r,i),f(n,!0)&&r.redraw(!1)},destroy:function(){var t,e,i,n,o,r=this,s=r.chart,a=/AppleWebKit\/533/.test(Lt),l=r.data||[];for(Ae(r,"destroy"),Pe(r),be(["xAxis","yAxis"],function(t){o=r[t],o&&(c(o.series,r),o.isDirty=o.forceRedraw=!0,o.stacks={})}),r.legendItem&&r.chart.legend.destroyItem(r),e=l.length;e--;)i=l[e],i&&i.destroy&&i.destroy();r.points=null,clearTimeout(r.animationTimeout),be(["area","graph","dataLabelsGroup","group","markerGroup","tracker","graphNeg","areaNeg","posClip","negClip"],function(e){r[e]&&(t=a&&"group"===e?"hide":"destroy",r[e][t]())}),s.hoverSeries===r&&(s.hoverSeries=null),c(s.series,r);for(n in r)delete r[n]},drawDataLabels:function(){var t,i,n,o,r=this,s=r.options,a=s.dataLabels,l=r.points;(a.enabled||r._hasPointLabels)&&(r.dlProcessOptions&&r.dlProcessOptions(a),o=r.plotGroup("dataLabelsGroup","data-labels",r.visible?$t:Zt,a.zIndex||6),i=a,be(l,function(s){var l,h,c,p,u,g=s.dataLabel,m=s.connector,y=!0;if(t=s.options&&s.options.dataLabels,l=f(t&&t.enabled,i.enabled),g&&!l)s.dataLabel=g.destroy();else if(l){if(a=e(i,t),u=a.rotation,h=s.getLabelConfig(),n=a.format?w(a.format,h):a.formatter.call(h,a),a.style.color=f(a.color,a.style.color,r.color,"black"),g)d(n)?(g.attr({text:n}),y=!1):(s.dataLabel=g=g.destroy(),m&&(s.connector=m.destroy()));else if(d(n)){c={fill:a.backgroundColor,stroke:a.borderColor,"stroke-width":a.borderWidth,r:a.borderRadius||0,rotation:u,padding:a.padding,zIndex:1};for(p in c)c[p]===Z&&delete c[p];g=s.dataLabel=r.chart.renderer[u?"text":"label"](n,0,-999,null,null,null,a.useHTML).attr(c).css(a.style).add(o).shadow(a.shadow)}g&&r.alignDataLabel(s,g,a,null,y)}}))},alignDataLabel:function(e,i,n,o,r){var s,a=this.chart,l=a.inverted,h=f(e.plotX,-999),c=f(e.plotY,-999),d=i.getBBox(),p=this.visible&&a.isInsidePlot(e.plotX,e.plotY,l);p&&(o=t({x:l?a.plotWidth-c:h,y:yt(l?a.plotHeight-h:c),width:0,height:0},o),t(n,{width:d.width,height:d.height}),n.rotation?(s={align:n.align,x:o.x+n.x+o.width/2,y:o.y+n.y+o.height/2},i[r?"attr":"animate"](s)):(i.align(n,null,o),s=i.alignAttr,"justify"===f(n.overflow,"justify")?this.justifyDataLabel(i,n,s,d,o,r):f(n.crop,!0)&&(p=a.isInsidePlot(s.x,s.y)&&a.isInsidePlot(s.x+d.width,s.y+d.height)))),p||i.attr({y:-999})},justifyDataLabel:function(t,e,i,n,o,r){var s,a,l=this.chart,h=e.align,c=e.verticalAlign;s=i.x,s<0&&("right"===h?e.align="left":e.x=-s,a=!0),s=i.x+n.width,s>l.plotWidth&&("left"===h?e.align="right":e.x=l.plotWidth-s,a=!0),s=i.y,s<0&&("bottom"===c?e.verticalAlign="top":e.y=-s,a=!0),s=i.y+n.height,s>l.plotHeight&&("top"===c?e.verticalAlign="bottom":e.y=l.plotHeight-s,a=!0),a&&(t.placed=!r,t.align(e,null,o))},getSegmentPath:function(t){var e=this,i=[],n=e.options.step;return be(t,function(o,r){var s,a=o.plotX,l=o.plotY;e.getPointSpline?i.push.apply(i,e.getPointSpline(t,o,r)):(i.push(r?te:Qt),n&&r&&(s=t[r-1],"right"===n?i.push(s.plotX,l):"center"===n?i.push((s.plotX+a)/2,s.plotY,(s.plotX+a)/2,l):i.push(a,s.plotY)),i.push(o.plotX,o.plotY))}),i},getGraphPath:function(){var t,e=this,i=[],n=[];return be(e.segments,function(o){t=e.getSegmentPath(o),o.length>1?i=i.concat(t):n.push(o[0])}),e.singlePoints=n,e.graphPath=i,i},drawGraph:function(){var t=this,e=this.options,i=[["graph",e.lineColor||this.color]],n=e.lineWidth,o=e.dashStyle,r=this.getGraphPath(),s=e.negativeColor;s&&i.push(["graphNeg",s]),be(i,function(i,s){var a,l=i[0],h=t[l];h?(Me(h),h.animate({d:r})):n&&r.length&&(a={stroke:i[1],"stroke-width":n,zIndex:1},o?a.dashstyle=o:a["stroke-linecap"]=a["stroke-linejoin"]="round",t[l]=t.chart.renderer.path(r).attr(a).add(t.group).shadow(!s&&e.shadow))})},clipNeg:function(){var t,e,i,n,o,r=this.options,s=this.chart,a=s.renderer,l=r.negativeColor||r.negativeFillColor,h=this.graph,c=this.area,d=this.posClip,p=this.negClip,u=s.chartWidth,f=s.chartHeight,g=bt(u,f),m=this.yAxis;l&&(h||c)&&(t=yt(m.toPixels(r.threshold||0,!0)),n={x:0,y:0,width:g,height:t},o={x:0,y:t,width:g,height:g},s.inverted&&(n.height=o.y=s.plotWidth-t,a.isVML&&(n={x:s.plotWidth-t-s.plotLeft,y:0,width:u,height:f},o={x:t+s.plotLeft-u,y:0,width:s.plotLeft+t,height:u})),m.reversed?(e=o,i=n):(e=n,i=o),d?(d.animate(e),p.animate(i)):(this.posClip=d=a.clipRect(e),this.negClip=p=a.clipRect(i),h&&this.graphNeg&&(h.clip(d),this.graphNeg.clip(p)),c&&(c.clip(d),this.areaNeg.clip(p))))},invertGroups:function(){function t(){var t={width:e.yAxis.len,height:e.xAxis.len};be(["group","markerGroup"],function(i){e[i]&&e[i].attr(t).invert()})}var e=this,i=e.chart;e.xAxis&&(Se(i,"resize",t),Se(e,"destroy",function(){Pe(i,"resize",t)}),t(),e.invertGroups=t)},plotGroup:function(t,e,i,n,o){var r=this[t],s=!r;return s&&(this[t]=r=this.chart.renderer.g(e).attr({visibility:i,zIndex:n||.1}).add(o)),r[s?"attr":"animate"](this.getPlotBox()),r},getPlotBox:function(){return{translateX:this.xAxis?this.xAxis.left:this.chart.plotLeft,translateY:this.yAxis?this.yAxis.top:this.chart.plotTop,scaleX:1,scaleY:1}},render:function(){var t,e=this,i=e.chart,n=e.options,o=n.animation,r=o&&!!e.animate&&i.renderer.isSVG,s=e.visible?$t:Zt,a=n.zIndex,l=e.hasRendered,h=i.seriesGroup;t=e.plotGroup("group","series",s,a,h),e.markerGroup=e.plotGroup("markerGroup","markers",s,a,h),r&&e.animate(!0),e.getAttribs(),t.inverted=!!e.isCartesian&&i.inverted,e.drawGraph&&(e.drawGraph(),e.clipNeg()),e.drawDataLabels(),e.drawPoints(),e.options.enableMouseTracking!==!1&&e.drawTracker(),i.inverted&&e.invertGroups(),n.clip===!1||e.sharedClipKey||l||t.clip(i.clipRect),r?e.animate():l||e.afterAnimate(),e.isDirty=e.isDirtyData=!1,e.hasRendered=!0},redraw:function(){var t=this,e=t.chart,i=t.isDirtyData,n=t.group,o=t.xAxis,r=t.yAxis;n&&(e.inverted&&n.attr({width:e.plotWidth,height:e.plotHeight}),n.animate({translateX:f(o&&o.left,e.plotLeft),translateY:f(r&&r.top,e.plotTop)})),t.translate(),t.setTooltipPoints(!0),t.render(),i&&Ae(t,"updatedData")},setState:function(t){var e,i=this,n=i.options,o=i.graph,r=i.graphNeg,s=n.states,a=n.lineWidth;if(t=t||ie,i.state!==t){if(i.state=t,s[t]&&s[t].enabled===!1)return;t&&(a=s[t].lineWidth||a+1),o&&!o.dashstyle&&(e={"stroke-width":a},o.attr(e),r&&r.attr(e))}},setVisible:function(t,e){var i,n=this,o=n.chart,r=n.legendItem,s=o.options.chart.ignoreHiddenSeries,a=n.visible;n.visible=t=n.userOptions.visible=t===Z?!a:t,i=t?"show":"hide",be(["group","dataLabelsGroup","markerGroup","tracker"],function(t){n[t]&&n[t][i]()}),o.hoverSeries===n&&n.onMouseOut(),r&&o.legend.colorizeItem(n,t),n.isDirty=!0,n.options.stacking&&be(o.series,function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),be(n.linkedSeries,function(e){e.setVisible(t,!1)}),s&&(o.isDirtyBox=!0),e!==!1&&o.redraw(),Ae(n,i)},show:function(){this.setVisible(!0)},hide:function(){this.setVisible(!1)},select:function(t){var e=this;e.selected=t=t===Z?!e.selected:t,e.checkbox&&(e.checkbox.checked=t),Ae(e,t?"select":"unselect")},drawTracker:function(){var t,e,i=this,n=i.options,o=n.trackByArea,r=[].concat(o?i.areaPath:i.graphPath),s=r.length,a=i.chart,l=a.pointer,h=a.renderer,c=a.options.tooltip.snap,d=i.tracker,p=n.cursor,u=p&&{cursor:p},f=i.singlePoints,g=function(){a.hoverSeries!==i&&i.onMouseOver()};if(s&&!o)for(e=s+1;e--;)r[e]===Qt&&r.splice(e+1,0,r[e+1]-c,r[e+2],te),(e&&r[e]===Qt||e===s)&&r.splice(e,0,te,r[e-2]+c,r[e-1]);for(e=0;e<f.length;e++)t=f[e],r.push(Qt,t.plotX-c,t.plotY,te,t.plotX+c,t.plotY);d?d.attr({d:r}):(i.tracker=h.path(r).attr({"stroke-linejoin":"round",visibility:i.visible?$t:Zt,stroke:ee,fill:o?ee:Jt,"stroke-width":n.lineWidth+(o?0:2*c),zIndex:2}).add(i.group),be([i.tracker,i.markerGroup],function(t){t.addClass(Kt+"tracker").on("mouseover",g).on("mouseout",function(t){l.onTrackerMouseOut(t)}).css(u),Wt&&t.on("touchstart",g)}))}};var Ne=y(Ge);fe.line=Ne,Ie.area=e(ze,{threshold:0});var Fe=y(Ge,{type:"area",getSegments:function(){var t,e,i,n,o,r=[],s=[],a=[],l=this.xAxis,h=this.yAxis,c=h.stacks[this.stackKey],d={},p=this.points,u=this.options.connectNulls;if(this.options.stacking&&!this.cropped){for(n=0;n<p.length;n++)d[p[n].x]=p[n];for(o in c)a.push(+o);a.sort(function(t,e){return t-e}),be(a,function(n){(!u||d[n]&&null!==d[n].y)&&(d[n]?s.push(d[n]):(t=l.translate(n),i=c[n].percent?c[n].total?100*c[n].cum/c[n].total:0:c[n].cum,e=h.toPixels(i,!0),s.push({y:null,plotX:t,clientX:t,plotY:e,yBottom:e,onMouseOver:Gt})))}),s.length&&r.push(s)}else Ge.prototype.getSegments.call(this),r=this.segments;this.segments=r},getSegmentPath:function(t){var e,i,n=Ge.prototype.getSegmentPath.call(this,t),o=[].concat(n),r=this.options,s=n.length,a=this.yAxis.getThreshold(r.threshold);if(3===s&&o.push(te,n[1],n[2]),r.stacking&&!this.closedStacks)for(e=t.length-1;e>=0;e--)i=f(t[e].yBottom,a),e<t.length-1&&r.step&&o.push(t[e+1].plotX,i),o.push(t[e].plotX,i);else this.closeSegment(o,t,a);return this.areaPath=this.areaPath.concat(o),n},closeSegment:function(t,e,i){t.push(te,e[e.length-1].plotX,i,te,e[0].plotX,i)},drawGraph:function(){this.areaPath=[],Ge.prototype.drawGraph.apply(this);var t=this,e=this.areaPath,i=this.options,n=i.negativeColor,o=i.negativeFillColor,r=[["area",this.color,i.fillColor]];(n||o)&&r.push(["areaNeg",n,o]),be(r,function(n){var o=n[0],r=t[o];r?r.animate({d:e}):t[o]=t.chart.renderer.path(e).attr({fill:f(n[2],Be(n[1]).setOpacity(f(i.fillOpacity,.75)).get()),zIndex:0}).add(t.group)})},drawLegendSymbol:function(t,e){e.legendSymbol=this.chart.renderer.rect(0,t.baseline-11,t.options.symbolWidth,12,2).attr({zIndex:3}).add(e.legendGroup)}});fe.area=Fe,Ie.spline=e(ze);var Ve=y(Ge,{type:"spline",getPointSpline:function(t,e,i){var n,o,r,s,a,l=1.5,h=l+1,c=e.plotX,d=e.plotY,p=t[i-1],u=t[i+1];if(p&&u){var f,g=p.plotX,m=p.plotY,y=u.plotX,x=u.plotY;n=(l*c+g)/h,o=(l*d+m)/h,r=(l*c+y)/h,s=(l*d+x)/h,f=(s-o)*(r-c)/(r-n)+d-s,o+=f,s+=f,o>m&&o>d?(o=bt(m,d),s=2*d-o):o<m&&o<d&&(o=kt(m,d),s=2*d-o),s>x&&s>d?(s=bt(x,d),o=2*d-s):s<x&&s<d&&(s=kt(x,d),o=2*d-s),e.rightContX=r,e.rightContY=s}return i?(a=["C",p.rightContX||p.plotX,p.rightContY||p.plotY,n||c,o||d,c,d],p.rightContX=p.rightContY=null):a=[Qt,c,d],a}});fe.spline=Ve,Ie.areaspline=e(Ie.area);var je=Fe.prototype,_e=y(Ve,{type:"areaspline",closedStacks:!0,getSegmentPath:je.getSegmentPath,closeSegment:je.closeSegment,drawGraph:je.drawGraph,drawLegendSymbol:je.drawLegendSymbol});fe.areaspline=_e,Ie.column=e(ze,{borderColor:"#FFFFFF",borderWidth:1,borderRadius:0,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{brightness:.1,shadow:!1},select:{color:"#C0C0C0",borderColor:"#000000",shadow:!1}},dataLabels:{align:null,verticalAlign:null,y:null},stickyTracking:!1,threshold:0});var Ue=y(Ge,{type:"column",pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color",r:"borderRadius"},cropShoulder:0,trackerGroups:["group","dataLabelsGroup"],negStacks:!0,init:function(){Ge.prototype.init.apply(this,arguments);var t=this,e=t.chart;e.hasRendered&&be(e.series,function(e){e.type===t.type&&(e.isDirty=!0)})},getColumnMetrics:function(){var t,e,i=this,n=i.options,o=i.xAxis,r=i.yAxis,s=o.reversed,a={},l=0;n.grouping===!1?l=1:be(i.chart.series,function(n){var o=n.options,s=n.yAxis;n.type===i.type&&n.visible&&r.len===s.len&&r.pos===s.pos&&(o.stacking?(t=n.stackKey,a[t]===Z&&(a[t]=l++),e=a[t]):o.grouping!==!1&&(e=l++),n.columnIndex=e)});var h=kt(wt(o.transA)*(o.ordinalSlope||n.pointRange||o.closestPointRange||1),o.len),c=h*n.groupPadding,p=h-2*c,u=p/l,g=n.pointWidth,m=d(g)?(u-g)/2:u*n.pointPadding,y=f(g,u-2*m),x=(s?l-(i.columnIndex||0):i.columnIndex)||0,v=m+(c+x*u-h/2)*(s?-1:1);return i.columnMetrics={width:y,offset:v}},translate:function(){var t=this,e=t.chart,i=t.options,n=i.borderWidth,o=t.yAxis,r=i.threshold,s=t.translatedThreshold=o.getThreshold(r),a=f(i.minPointLength,5),l=t.getColumnMetrics(),h=l.width,c=t.barW=vt(bt(h,1+2*n)),d=t.pointXOffset=l.offset,p=-(n%2?.5:0),u=n%2?.5:1;
e.renderer.isVML&&e.inverted&&(u+=1),Ge.prototype.translate.apply(t),be(t.points,function(t){var e,i,n,r,l=f(t.yBottom,s),g=kt(bt(-999-l,t.plotY),o.len+999+l),m=t.plotX+d,y=c,x=kt(g,l),v=bt(g,l)-x;wt(v)<a&&a&&(v=a,x=yt(wt(x-s)>a?l-a:s-(o.translate(t.y,0,1,0,1)<=s?a:0))),t.barX=m,t.pointWidth=h,r=wt(m)<.5,e=yt(m+y)+p,m=yt(m)+p,y=e-m,n=wt(x)<.5,i=yt(x+v)+u,x=yt(x)+u,v=i-x,r&&(m+=1,y-=1),n&&(x-=1,v+=1),t.shapeType="rect",t.shapeArgs={x:m,y:x,width:y,height:v}})},getSymbol:Gt,drawLegendSymbol:Fe.prototype.drawLegendSymbol,drawGraph:Gt,drawPoints:function(){var t,i=this,n=i.options,o=i.chart.renderer;be(i.points,function(r){var s=r.plotY,a=r.graphic;s===Z||isNaN(s)||null===r.y?a&&(r.graphic=a.destroy()):(t=r.shapeArgs,a?(Me(a),a.animate(e(t))):r.graphic=a=o[r.shapeType](t).attr(r.pointAttr[r.selected?oe:ie]).add(i.group).shadow(n.shadow,null,n.stacking&&!n.borderRadius))})},drawTracker:function(){var t=this,e=t.chart,i=e.pointer,n=t.options.cursor,o=n&&{cursor:n},r=function(i){var n,o=i.target;for(e.hoverSeries!==t&&t.onMouseOver();o&&!n;)n=o.point,o=o.parentNode;n!==Z&&n!==e.hoverPoint&&n.onMouseOver(i)};be(t.points,function(t){t.graphic&&(t.graphic.element.point=t),t.dataLabel&&(t.dataLabel.element.point=t)}),t._hasTracking||(be(t.trackerGroups,function(e){t[e]&&(t[e].addClass(Kt+"tracker").on("mouseover",r).on("mouseout",function(t){i.onTrackerMouseOut(t)}).css(o),Wt&&t[e].on("touchstart",r))}),t._hasTracking=!0)},alignDataLabel:function(t,i,n,o,r){var s=this.chart,a=s.inverted,l=t.dlBox||t.shapeArgs,h=t.below||t.plotY>f(this.translatedThreshold,s.plotSizeY),c=f(n.inside,!!this.options.stacking);l&&(o=e(l),a&&(o={x:s.plotWidth-o.y-o.height,y:s.plotHeight-o.x-o.width,width:o.height,height:o.width}),c||(a?(o.x+=h?0:o.width,o.width=0):(o.y+=h?o.height:0,o.height=0))),n.align=f(n.align,!a||c?"center":h?"right":"left"),n.verticalAlign=f(n.verticalAlign,a||c?"middle":h?"top":"bottom"),Ge.prototype.alignDataLabel.call(this,t,i,n,o,r)},animate:function(t){var e,i=this,n=this.yAxis,o=i.options,r=this.chart.inverted,s={};Rt&&(t?(s.scaleY=.001,e=kt(n.pos+n.len,bt(n.pos,n.toPixels(o.threshold))),r?s.translateX=e-n.len:s.translateY=e,i.group.attr(s)):(s.scaleY=1,s[r?"translateX":"translateY"]=n.pos,i.group.animate(s,i.options.animation),i.animate=null))},remove:function(){var t=this,e=t.chart;e.hasRendered&&be(e.series,function(e){e.type===t.type&&(e.isDirty=!0)}),Ge.prototype.remove.apply(t,arguments)}});fe.column=Ue,Ie.bar=e(Ie.column);var Ze=y(Ue,{type:"bar",inverted:!0});fe.bar=Ze,Ie.scatter=e(ze,{lineWidth:0,tooltip:{headerFormat:'<span style="font-size: 10px; color:{series.color}">{series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>",followPointer:!0},stickyTracking:!1});var Ke=y(Ge,{type:"scatter",sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["markerGroup"],drawTracker:Ue.prototype.drawTracker,setTooltipPoints:Gt});fe.scatter=Ke,Ie.pie=e(ze,{borderColor:"#FFFFFF",borderWidth:1,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{distance:30,enabled:!0,formatter:function(){return this.point.name}},ignoreHiddenPoint:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,states:{hover:{brightness:.1,shadow:!1}},stickyTracking:!1,tooltip:{followPointer:!0}});var $e=y(Ee,{init:function(){Ee.prototype.init.apply(this,arguments);var e,i=this;return i.y<0&&(i.y=null),t(i,{visible:i.visible!==!1,name:f(i.name,"Slice")}),e=function(t){i.slice("select"===t.type)},Se(i,"select",e),Se(i,"unselect",e),i},setVisible:function(t){var e,i=this,n=i.series,o=n.chart;i.visible=i.options.visible=t=t===Z?!i.visible:t,n.options.data[ve(i,n.data)]=i.options,e=t?"show":"hide",be(["graphic","dataLabel","connector","shadowGroup"],function(t){i[t]&&i[t][e]()}),i.legendItem&&o.legend.colorizeItem(i,t),!n.isDirty&&n.options.ignoreHiddenPoint&&(n.isDirty=!0,o.redraw())},slice:function(t,e,i){var n,o=this,r=o.series,s=r.chart;R(i,s),e=f(e,!0),o.sliced=o.options.sliced=t=d(t)?t:!o.sliced,r.options.data[ve(o,r.data)]=o.options,n=t?o.slicedTranslation:{translateX:0,translateY:0},o.graphic.animate(n),o.shadowGroup&&o.shadowGroup.animate(n)}}),qe={type:"pie",isCartesian:!1,pointClass:$e,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","dataLabelsGroup"],pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color"},getColor:Gt,animate:function(t){var e=this,i=e.points,n=e.startAngleRad;t||(be(i,function(t){var i=t.graphic,o=t.shapeArgs;i&&(i.attr({r:e.center[3]/2,start:n,end:n}),i.animate({r:o.r,start:o.start,end:o.end},e.options.animation))}),e.animate=null)},setData:function(t,e){Ge.prototype.setData.call(this,t,!1),this.processData(),this.generatePoints(),f(e,!0)&&this.chart.redraw()},generatePoints:function(){var t,e,i,n,o=0,r=this.options.ignoreHiddenPoint;for(Ge.prototype.generatePoints.call(this),e=this.points,i=e.length,t=0;t<i;t++)n=e[t],o+=r&&!n.visible?0:n.y;for(this.total=o,t=0;t<i;t++)n=e[t],n.percentage=o>0?n.y/o*100:0,n.total=o},getCenter:function(){var t,e,i=this.options,o=this.chart,r=2*(i.slicedOffset||0),s=o.plotWidth-2*r,a=o.plotHeight-2*r,l=i.center,h=[f(l[0],"50%"),f(l[1],"50%"),i.size||"100%",i.innerSize||0],c=kt(s,a);return Te(h,function(i,o){return e=/%$/.test(i),t=o<2||2===o&&e,(e?[s,a,c,c][o]*n(i)/100:i)+(t?r:0)})},translate:function(t){this.generatePoints();var e,i,n,o,r,s,a,l=this,h=0,c=1e3,d=l.options,p=d.slicedOffset,u=p+d.borderWidth,f=d.startAngle||0,g=l.startAngleRad=Pt/180*(f-90),m=l.endAngleRad=Pt/180*((d.endAngle||f+360)-90),y=m-g,x=l.points,v=d.dataLabels.distance,b=d.ignoreHiddenPoint,k=x.length;for(t||(l.center=t=l.getCenter()),l.getX=function(e,i){return n=mt.asin((e-t[1])/(t[2]/2+v)),t[0]+(i?-1:1)*(Tt(n)*(t[2]/2+v))},s=0;s<k;s++)a=x[s],e=g+h*y,b&&!a.visible||(h+=a.percentage/100),i=g+h*y,a.shapeType="arc",a.shapeArgs={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:yt(e*c)/c,end:yt(i*c)/c},n=(i+e)/2,n>.75*y&&(n-=2*Pt),a.slicedTranslation={translateX:yt(Tt(n)*p),translateY:yt(St(n)*p)},o=Tt(n)*t[2]/2,r=St(n)*t[2]/2,a.tooltipPos=[t[0]+.7*o,t[1]+.7*r],a.half=n<-Pt/2||n>Pt/2?1:0,a.angle=n,u=kt(u,v/2),a.labelPos=[t[0]+o+Tt(n)*v,t[1]+r+St(n)*v,t[0]+o+Tt(n)*u,t[1]+r+St(n)*u,t[0]+o,t[1]+r,v<0?"center":a.half?"right":"left",n]},setTooltipPoints:Gt,drawGraph:null,drawPoints:function(){var e,i,n,o,r=this,s=r.chart,a=s.renderer,l=r.options.shadow;l&&!r.shadowGroup&&(r.shadowGroup=a.g("shadow").add(r.group)),be(r.points,function(s){i=s.graphic,o=s.shapeArgs,n=s.shadowGroup,l&&!n&&(n=s.shadowGroup=a.g("shadow").add(r.shadowGroup)),e=s.sliced?s.slicedTranslation:{translateX:0,translateY:0},n&&n.attr(e),i?i.animate(t(o,e)):s.graphic=i=a.arc(o).setRadialReference(r.center).attr(s.pointAttr[s.selected?oe:ie]).attr({"stroke-linejoin":"round"}).attr(e).add(r.group).shadow(l,n),s.visible===!1&&s.setVisible(!1)})},sortByAngle:function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},drawDataLabels:function(){var t,e,i,n,o,r,s,a,l,h,c,d,p,u=this,g=u.data,m=u.chart,y=u.options.dataLabels,x=f(y.connectorPadding,10),v=f(y.connectorWidth,1),b=m.plotWidth,k=m.plotHeight,w=f(y.softConnector,!0),T=y.distance,S=u.center,P=S[2]/2,A=S[1],L=T>0,C=[[],[]],M=[0,0,0,0],I=function(t,e){return e.y-t.y};if(u.visible&&(y.enabled||u._hasPointLabels)){for(Ge.prototype.drawDataLabels.apply(u),be(g,function(t){t.dataLabel&&C[t.half].push(t)}),d=0;!s&&g[d];)s=g[d]&&g[d].dataLabel&&(g[d].dataLabel.getBBox().height||21),d++;for(d=2;d--;){var z,B,O,R=[],H=[],X=C[d],W=X.length;if(u.sortByAngle(X,d-.5),T>0){for(B=A-P-T;B<=A+P+T;B+=s)R.push(B);if(z=R.length,W>z){for(c=[].concat(X),c.sort(I),p=W;p--;)c[p].rank=p;for(p=W;p--;)X[p].rank>=z&&X.splice(p,1);W=X.length}for(p=0;p<W;p++){t=X[p],r=t.labelPos;var Y,E,G=9999;for(E=0;E<z;E++)Y=wt(R[E]-r[1]),Y<G&&(G=Y,O=E);if(O<p&&null!==R[p])O=p;else if(z<W-p+O&&null!==R[p])for(O=z-W+p;null===R[O];)O++;else for(;null===R[O];)O++;H.push({i:O,y:R[O]}),R[O]=null}H.sort(I)}for(p=0;p<W;p++){var N,F;t=X[p],r=t.labelPos,n=t.dataLabel,h=t.visible===!1?Zt:$t,F=r[1],T>0?(N=H.pop(),O=N.i,l=N.y,(F>l&&null!==R[O+1]||F<l&&null!==R[O-1])&&(l=F)):l=F,a=y.justify?S[0]+(d?-1:1)*(P+T):u.getX(0===O||O===R.length-1?F:l,d),n._attr={visibility:h,align:r[6]},n._pos={x:a+y.x+({left:x,right:-x}[r[6]]||0),y:l+y.y-10},n.connX=a,n.connY=l,null===this.options.size&&(o=n.width,a-o<x?M[3]=bt(yt(o-a+x),M[3]):a+o>b-x&&(M[1]=bt(yt(a+o-b+x),M[1])),l-s/2<0?M[0]=bt(yt(-l+s/2),M[0]):l+s/2>k&&(M[2]=bt(yt(l+s/2-k),M[2])))}}(0===D(M)||this.verifyDataLabelOverflow(M))&&(this.placeDataLabels(),L&&v&&be(this.points,function(t){e=t.connector,r=t.labelPos,n=t.dataLabel,n&&n._pos?(h=n._attr.visibility,a=n.connX,l=n.connY,i=w?[Qt,a+("left"===r[6]?5:-5),l,"C",a,l,2*r[2]-r[4],2*r[3]-r[5],r[2],r[3],te,r[4],r[5]]:[Qt,a+("left"===r[6]?5:-5),l,te,r[2],r[3],te,r[4],r[5]],e?(e.animate({d:i}),e.attr("visibility",h)):t.connector=e=u.chart.renderer.path(i).attr({"stroke-width":v,stroke:y.connectorColor||t.color||"#606060",visibility:h}).add(u.group)):e&&(t.connector=e.destroy())}))}},verifyDataLabelOverflow:function(t){var e,i=this.center,n=this.options,o=n.center,r=n.minSize||80,s=r;return null!==o[0]?s=bt(i[2]-bt(t[1],t[3]),r):(s=bt(i[2]-t[1]-t[3],r),i[0]+=(t[3]-t[1])/2),null!==o[1]?s=bt(kt(s,i[2]-bt(t[0],t[2])),r):(s=bt(kt(s,i[2]-t[0]-t[2]),r),i[1]+=(t[0]-t[2])/2),s<i[2]?(i[2]=s,this.translate(i),be(this.points,function(t){t.dataLabel&&(t.dataLabel._pos=null)}),this.drawDataLabels()):e=!0,e},placeDataLabels:function(){be(this.points,function(t){var e,i=t.dataLabel;i&&(e=i._pos,e?(i.attr(i._attr),i[i.moved?"animate":"attr"](e),i.moved=!0):i&&i.attr({y:-999}))})},alignDataLabel:Gt,drawTracker:Ue.prototype.drawTracker,drawLegendSymbol:Fe.prototype.drawLegendSymbol,getSymbol:Gt};qe=y(Ge,qe),fe.pie=qe,t(Highcharts,{Axis:F,Chart:U,Color:Be,Legend:_,Pointer:j,Point:Ee,Tick:E,Tooltip:V,Renderer:K,Series:Ge,SVGElement:Y,SVGRenderer:Oe,arrayMin:M,arrayMax:D,charts:Nt,dateFormat:J,format:w,pathAnim:tt,getOptions:W,hasBidiBug:Ht,isTouchDevice:Bt,numberFormat:x,seriesTypes:fe,setOptions:X,addEvent:Se,removeEvent:Pe,createElement:m,discardElement:z,css:g,each:be,extend:t,map:Te,merge:e,pick:f,splat:u,extendClass:y,pInt:n,wrap:b,svg:Rt,canvas:Xt,vml:!Rt&&!Xt,product:Ft,version:Vt})}();