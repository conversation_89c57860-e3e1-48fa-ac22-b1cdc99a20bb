using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Outbound
{
    /// <summary>
    /// 出库单
    /// 记录物料出库的基本信息
    /// </summary>
    [Table("Outbounds")]
    public class Outbound : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 出库单号
        /// </summary>
        [Display(Name = "出库单号")]
        [Required(ErrorMessage = "出库单号是必填项")]
        [StringLength(50)]
        public string OutboundNo { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        [Display(Name = "出库类型")]
        public DocumentType Type { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Display(Name = "物料")]
        public string MaterialID { get; set; }
 
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        public string? BatchId { get; set; }
 
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Display(Name = "仓库")]
        public string WarehouseId { get; set; }

 
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        [Display(Name = "位置")]
        public Guid? LocationId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Display(Name = "数量")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Display(Name = "单位")]
        [StringLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Display(Name = "单价")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [Display(Name = "总金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        [Display(Name = "客户")]
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Display(Name = "单据日期")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 出库单状态枚举
    /// </summary>
    public enum OutboundStatus
    {
        /// <summary>
        /// 待审核 - 出库单已创建但未审核
        /// </summary>
        [Display(Name = "待审核")]
        Pending = 0,

        /// <summary>
        /// 已审核 - 出库单已通过审核
        /// </summary>
        [Display(Name = "已审核")]
        Approved = 1,

        /// <summary>
        /// 已拒绝 - 出库单审核未通过
        /// </summary>
        [Display(Name = "已拒绝")]
        Rejected = 2,

        /// <summary>
        /// 已取消 - 出库单已被取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 3,

        /// <summary>
        /// 已完成 - 出库单已完成出库操作
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 4
    }
}