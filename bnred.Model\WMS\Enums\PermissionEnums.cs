using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 权限类型枚举
    /// 用于标识不同的权限类型
    /// </summary>
    public enum PermissionType
    {
        /// <summary>
        /// 菜单权限
        /// 系统菜单的访问权限
        /// </summary>
        [Display(Name = "菜单权限")]
        Menu = 0,

        /// <summary>
        /// 功能权限
        /// 系统功能的操作权限
        /// </summary>
        [Display(Name = "功能权限")]
        Function = 1,

        /// <summary>
        /// 数据权限
        /// 业务数据的访问权限
        /// </summary>
        [Display(Name = "数据权限")]
        Data = 2,

        /// <summary>
        /// 字段权限
        /// 数据字段的访问权限
        /// </summary>
        [Display(Name = "字段权限")]
        Field = 3,

        /// <summary>
        /// 按钮权限
        /// 界面按钮的操作权限
        /// </summary>
        [Display(Name = "按钮权限")]
        Button = 4
    }

    /// <summary>
    /// 权限��作枚举
    /// 用于标识权限的操作类型
    /// </summary>
    public enum PermissionOperation
    {
        /// <summary>
        /// 查看权限
        /// 允许查看数据
        /// </summary>
        [Display(Name = "查看权限")]
        View = 0,

        /// <summary>
        /// 新增权限
        /// 允许新增数据
        /// </summary>
        [Display(Name = "新增权限")]
        Add = 1,

        /// <summary>
        /// 修改权限
        /// 允许修改数据
        /// </summary>
        [Display(Name = "修改权限")]
        Edit = 2,

        /// <summary>
        /// 删除权限
        /// 允许删除数据
        /// </summary>
        [Display(Name = "删除权限")]
        Delete = 3,

        /// <summary>
        /// 导入权限
        /// 允许导入数据
        /// </summary>
        [Display(Name = "导入权限")]
        Import = 4,

        /// <summary>
        /// 导出权限
        /// 允许导出数据
        /// </summary>
        [Display(Name = "导出权限")]
        Export = 5,

        /// <summary>
        /// 审核权限
        /// 允许审核数据
        /// </summary>
        [Display(Name = "审核权限")]
        Audit = 6,

        /// <summary>
        /// 打印权限
        /// ��许打印数据
        /// </summary>
        [Display(Name = "打印权限")]
        Print = 7
    }
} 