using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Analysis
{
    /// <summary>
    /// 成本分布数据类
    /// 用于分析成本的分布情况
    /// </summary>
    [Table("WMS_CostDistributionData")]
    public class CostDistributionData : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 物料ID
        /// </summary>
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 总数量
        /// 库存总数量
        /// </summary>
        public decimal TotalQuantity { get; set; }

        /// <summary>
        /// 总成本
        /// 库存总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 平均单位成本
        /// 平均每单位的成本
        /// </summary>
        public decimal AverageUnitCost { get; set; }

        /// <summary>
        /// 最低单位成本
        /// 最低的单位成本
        /// </summary>
        public decimal MinUnitCost { get; set; }

        /// <summary>
        /// 最高单位成本
        /// 最高的单位成本
        /// </summary>
        public decimal MaxUnitCost { get; set; }

        /// <summary>
        /// 标准差
        /// 成本分布的标准差
        /// </summary>
        public decimal StandardDeviation { get; set; }

        /// <summary>
        /// 成本占比
        /// 在总成本中的占比
        /// </summary>
        public decimal CostPercentage { get; set; }

        /// <summary>
        /// 物料类别
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }
} 