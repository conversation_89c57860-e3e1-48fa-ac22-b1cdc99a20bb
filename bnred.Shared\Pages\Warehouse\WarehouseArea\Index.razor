﻿@page "/Warehouse/WarehouseArea"
@using bnred.ViewModel.Warehouse.WarehouseAreaVMs;
@inherits BasePage
@attribute [ActionDescription("仓库区域", "bnred.Controllers,WarehouseArea")]

<WTSearchPanel OnSearch="@DoSearch">
    <ValidateForm Model="@SearchModel">
        <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">

            <BootstrapInput @bind-Value="@SearchModel.ID"  />
            <BootstrapInput @bind-Value="@SearchModel.Code"  />
            <BootstrapInput @bind-Value="@SearchModel.Name"  />
            <Select @bind-Value="@SearchModel.WarehouseId" Items="@AllWarehouses" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.Type"  PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <Select @bind-Value="@SearchModel.Status"  PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInputNumber @bind-Value="@SearchModel.Area"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.Volume"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.MaxCapacity"  />
            <BootstrapInputNumber @bind-Value="@SearchModel.CurrentCapacity"  />
            <Select @bind-Value="@SearchModel.ManagerId" Items="@AllFrameworkUsers" PlaceHolder="@WtmBlazor.Localizer["Sys.All"]"/>
            <BootstrapInput @bind-Value="@SearchModel.Description"  />
            <BootstrapInput @bind-Value="@SearchModel.Remark"  />
        </Row>
    </ValidateForm>
</WTSearchPanel>



<Table @ref="dataTable" TItem="WarehouseArea_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowDefaultButtons="false" ShowExtendEditButton="false" ShowExtendDeleteButton="false" style="margin-top:10px;">
    <TableColumns>

        <TableColumn @bind-Field="@context.ID"  />
        <TableColumn @bind-Field="@context.Code"  />
        <TableColumn @bind-Field="@context.Name"  />
        <TableColumn @bind-Field="@context.Name_view"  />
        <TableColumn @bind-Field="@context.Type"  />
        <TableColumn @bind-Field="@context.Status"  />
        <TableColumn @bind-Field="@context.Area"  />
        <TableColumn @bind-Field="@context.Volume"  />
        <TableColumn @bind-Field="@context.MaxCapacity"  />
        <TableColumn @bind-Field="@context.CurrentCapacity"  />
        <TableColumn @bind-Field="@context.Name_view2"  />
        <TableColumn @bind-Field="@context.Description"  />
        <TableColumn @bind-Field="@context.Remark"  />
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/WarehouseArea/Add"))
        {
            <TableToolbarButton TItem="WarehouseArea_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/WarehouseArea/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="WarehouseArea_View" Color="Color.Primary"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.BatchDelete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        
        @if (IsAccessable("/api/WarehouseArea/Import"))
        {
            <TableToolbarButton TItem="WarehouseArea_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
        }
        @if (IsAccessable("/api/WarehouseArea/ExportExcel"))
        {
            <TableToolbarButton TItem="WarehouseArea_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
    </TableToolbarTemplate>
    <RowButtonTemplate>
        <div style="padding-right:10px;">
            @if (IsAccessable("/api/WarehouseArea/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/WarehouseArea/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/WarehouseArea/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
        </div>
    </RowButtonTemplate>
</Table>

@code{

    private WarehouseAreaSearcher SearchModel = new WarehouseAreaSearcher();
    private Table<WarehouseArea_View> dataTable;

    private List<SelectedItem> AllWarehouses = new List<SelectedItem>();

    private List<SelectedItem> AllFrameworkUsers = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllWarehouses = await WtmBlazor.Api.CallItemsApi("/api/WarehouseArea/GetWarehouses", placeholder: WtmBlazor.Localizer["Sys.All"]);

        AllFrameworkUsers = await WtmBlazor.Api.CallItemsApi("/api/WarehouseArea/GetFrameworkUsers", placeholder: WtmBlazor.Localizer["Sys.All"]);

        await base.OnInitializedAsync();
    }

    private async Task<QueryData<WarehouseArea_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<WarehouseArea_View>("/api/WarehouseArea/Search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<WarehouseArea_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(WarehouseArea_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(WarehouseArea_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/WarehouseArea/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(WarehouseArea_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/WarehouseArea/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<WarehouseArea_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/WarehouseArea/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/WarehouseArea/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<WarehouseArea_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
