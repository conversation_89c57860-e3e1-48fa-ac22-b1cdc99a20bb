@page "/_Admin/FrameworkMenu"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkMenuVMs;
@inherits BasePage
@attribute [ActionDescription("MenuKey.MenuMangement", "WalkingTec.Mvvm.Admin.Api,FrameworkMenu")]


<Table @ref="dataTable" TItem="FrameworkMenu_ListView" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowExtendEditButton="false" ShowExtendDeleteButton="false" ShowDefaultButtons="false" style="margin-top:10px;" TreeNodeConverter="(x)=>WtmBlazor.DefaultTreeConverter(x)" IsTree="true" AllowResizing="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.PageName" Width="180" />
        <TableColumn @bind-Field="@context.ModuleName" />
        <TableColumn @bind-Field="@context.ShowOnMenu" ComponentType="@typeof(Switch)"/>
        <TableColumn @bind-Field="@context.FolderOnly"   ComponentType="@typeof(Switch)"/>
        <TableColumn @bind-Field="@context.IsPublic"   ComponentType="@typeof(Switch)"/>
        <TableColumn @bind-Field="@context.TenantAllowed"   ComponentType="@typeof(Switch)"/>
        <TableColumn @bind-Field="@context.DisplayOrder" Sortable="true"  />
        <TableColumn @bind-Field="@context.Icon">
            <Template Context="value">
                <span><i class="@value.Value"></i></span>
            </Template>
        </TableColumn>
    </TableColumns>
    <TableToolbarTemplate>
        @if (IsAccessable("/api/_frameworkmenu/Add"))
        {
            <TableToolbarButton TItem="FrameworkMenu_ListView" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
        }
        @if (IsAccessable("/api/_frameworkmenu/BatchDelete"))
        {
            <TableToolbarPopConfirmButton TItem="FrameworkMenu_ListView" Color="Color.Danger"
                                      Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
        }
        @if (IsAccessable("/api/_frameworkmenu/ExportExcel"))
        {
            <TableToolbarButton TItem="FrameworkMenu_ListView" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
        }
        <TableToolbarButton TItem="FrameworkMenu_ListView" Color="Color.Primary" Icon="fa fa-fw fa-refresh" Text="@WtmBlazor.Localizer["Sys.Refresh"]" OnClickCallback="@OnRefresh" IsAsync="true" />

    </TableToolbarTemplate>
    <RowButtonTemplate>
            @if (IsAccessable("/api/_frameworkmenu/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/_frameworkmenu/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="() => OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/_frameworkmenu/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
    </RowButtonTemplate>
</Table>

@code{

    private FrameworkMenuSearcher SearchModel = new FrameworkMenuSearcher();
    private Table<FrameworkMenu_ListView> dataTable;



    private async Task<QueryData<FrameworkMenu_ListView>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearchTree<FrameworkMenu_ListView>("/api/_frameworkmenu/Search", SearchModel, opts);
    }

    private async Task<IEnumerable<FrameworkMenu_ListView>> OnTreeExpand(FrameworkMenu_ListView data)
    {
        return await Task.FromResult(data.Children);
    }

    private async Task DoSearch(IEnumerable<FrameworkMenu_ListView> data)
    {
        await dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<FrameworkMenu_ListView> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(FrameworkMenu_ListView item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnDetailsClick(FrameworkMenu_ListView item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/_frameworkmenu/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(FrameworkMenu_ListView item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/_frameworkmenu/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<FrameworkMenu_ListView> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/_frameworkmenu/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/_frameworkmenu/ExportExcel", SearchModel);
        }
    }

    private async Task OnRefresh(IEnumerable<FrameworkMenu_ListView> items)
    {
        var msg = await WtmBlazor.Api.CallAPI($"/api/_frameworkmenu/RefreshMenu");
        await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], msg.Data);
    }

}
