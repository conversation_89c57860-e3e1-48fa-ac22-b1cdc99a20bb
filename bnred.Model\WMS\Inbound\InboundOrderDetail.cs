using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Warehouse;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Inbound
{
    /// <summary>
    /// 入库单明细
    /// </summary>
    [Table("WMS_InboundOrderDetails")]
    public class InboundOrderDetail : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 入库单ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InboundOrderId { get; set; }

        /// <summary>
        /// 入库单
        /// </summary>
        [Display(Name = "入库单")]
        public InboundOrder InboundOrder { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public Location Location { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal PlanQuantity { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        [Display(Name = "实际数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? ActualQuantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Display(Name = "单价")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [Display(Name = "总金额")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        [Display(Name = "生产日期")]
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 过期日期
        /// </summary>
        [Display(Name = "过期日期")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 质检状态
        /// </summary>
        [Required]
        public QualityStatus QualityStatus { get; set; }

        /// <summary>
        /// 质检备注
        /// </summary>
        [StringLength(500)]
        [Display(Name = "质检备注")]
        public string QualityRemark { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        [Required(ErrorMessage = "行号不能为空")]
        [Display(Name = "行号")]
        public int LineNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(200, ErrorMessage = "备注最多200个字符")]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}