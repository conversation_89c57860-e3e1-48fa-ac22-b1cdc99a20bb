{"Files": [{"Id": "D:\\work\\wms2025\\bnred.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "PackagePath": "staticwebassets\\_framework\\blazor.webassembly.js"}, {"Id": "D:\\work\\wms2025\\bnred.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\bnred.Client.styles.css", "PackagePath": "staticwebassets\\bnred.Client.styles.css"}, {"Id": "D:\\work\\wms2025\\bnred.Client\\wwwroot\\appsettings.json", "PackagePath": "staticwebassets\\appsettings.json"}, {"Id": "D:\\work\\wms2025\\bnred.Client\\wwwroot\\fav.ico", "PackagePath": "staticwebassets\\fav.ico"}, {"Id": "D:\\work\\wms2025\\bnred.Client\\wwwroot\\index.html", "PackagePath": "staticwebassets\\index.html"}, {"Id": "D:\\work\\wms2025\\bnred.Client\\wwwroot\\manifest.json", "PackagePath": "staticwebassets\\manifest.json"}, {"Id": "D:\\work\\wms2025\\bnred.Client\\wwwroot\\pwaicon.png", "PackagePath": "staticwebassets\\pwaicon.png"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.bnred.Client.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.bnred.Client.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.bnred.Client.props", "PackagePath": "build\\bnred.Client.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.bnred.Client.props", "PackagePath": "buildMultiTargeting\\bnred.Client.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.bnred.Client.props", "PackagePath": "buildTransitive\\bnred.Client.props"}], "ElementsToRemove": []}