using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Batch
{
    /// <summary>
    /// 批次信息
    /// 用于管理物料的批次信息
    /// </summary>
    [Table("Batches")]
    public class Batch : BasePoco, IBasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 批次编号
        /// </summary>
        [Display(Name = "批次编号")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string BatchNumber { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Display(Name = "物料")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Display(Name = "供应商")]
        [StringLength(50)]
        public string SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [Display(Name = "供应商")]
        public Model.Supplier.Supplier Supplier { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        [Display(Name = "生产日期")]
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 到期日期
        /// </summary>
        [Display(Name = "到期日期")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 入库日期
        /// </summary>
        [Display(Name = "入库日期")]
        public DateTime ReceiptDate { get; set; }

        /// <summary>
        /// 批次状态
        /// </summary>
        [Display(Name = "批次状态")]
        public BatchStatus Status { get; set; }

        /// <summary>
        /// 质检状态
        /// </summary>
        [Display(Name = "质检状态")]
        public BatchQualityStatus QualityStatus { get; set; }

        /// <summary>
        /// 质检日期
        /// </summary>
        [Display(Name = "质检日期")]
        public DateTime? QualityCheckDate { get; set; }

        /// <summary>
        /// 质检人员ID
        /// </summary>
        [Display(Name = "质检人员")]
     
        public Guid? QualityCheckUserId { get; set; }
 
        public FrameworkUser QualityCheckUser { get; set; }

        /// <summary>
        /// 质检备注
        /// </summary>
        [Display(Name = "质检备注")]
        [StringLength(500)]
        public string QualityRemark { get; set; }

        /// <summary>
        /// 成本价
        /// </summary>
        [Display(Name = "成本价")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Cost { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 批次状态枚举
    /// </summary>
    public enum BatchStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 锁定
        /// </summary>
        [Display(Name = "锁定")]
        Locked = 1,

        /// <summary>
        /// 耗尽
        /// </summary>
        [Display(Name = "耗尽")]
        Depleted = 2,

        /// <summary>
        /// 过期
        /// </summary>
        [Display(Name = "过期")]
        Expired = 3,

        /// <summary>
        /// 报废
        /// </summary>
        [Display(Name = "报废")]
        Scrapped = 4
    }

    /// <summary>
    /// 批次质检状态枚举
    /// 用于标识批次特有的质检状态
    /// </summary>
    public enum BatchQualityStatus
    {
        /// <summary>
        /// 待检
        /// </summary>
        [Display(Name = "待检")]
        Pending = 0,

        /// <summary>
        /// 合格
        /// </summary>
        [Display(Name = "合格")]
        Qualified = 1,

        /// <summary>
        /// 不合格
        /// </summary>
        [Display(Name = "不合格")]
        Unqualified = 2,

        /// <summary>
        /// 部分合格
        /// </summary>
        [Display(Name = "部分合格")]
        PartiallyQualified = 3,

        /// <summary>
        /// 免检
        /// </summary>
        [Display(Name = "免检")]
        Exempted = 4
    }
}