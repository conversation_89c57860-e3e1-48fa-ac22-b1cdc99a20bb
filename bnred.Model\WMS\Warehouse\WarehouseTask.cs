using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库任务
    /// 用于记录仓库内的各类作业任务
    /// </summary>
    [Table("WMS_WarehouseTasks")]
    public class WarehouseTask : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 任务编号
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "任务编号")]
        [StringLength(50, ErrorMessage = "{0}最多输入{1}个字符")]
        public string TaskNo { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "任务类型")]
        public TaskType TaskType { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "任务状态")]
        public Enums.TaskStatus TaskStatus { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "仓库ID")]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse Warehouse { get; set; }

        /// <summary>
        /// 关联业务单号
        /// </summary>
        [Display(Name = "关联业务单号")]
        [StringLength(50, ErrorMessage = "{0}最多输入{1}个字符")]
        public string RelatedOrderNo { get; set; }

        /// <summary>
        /// 关联业务单据ID
        /// </summary>
        [Display(Name = "关联业务单据ID")]
        [StringLength(50)]
        public string RelatedOrderId { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        [Display(Name = "计划开始时间")]
        public DateTime? PlannedStartTime { get; set; }

        /// <summary>
        /// 计划完成时间
        /// </summary>
        [Display(Name = "计划完成时间")]
        public DateTime? PlannedEndTime { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        [Display(Name = "实际开始时间")]
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完成时间
        /// </summary>
        [Display(Name = "实际完成时间")]
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 分配人ID
        /// </summary>
        [Display(Name = "分配人ID")]
 
        public Guid? AssignedById { get; set; }

 
        public FrameworkUser AssignedBy { get; set; }

        /// <summary>
        /// 执行人ID
        /// </summary>
        [Display(Name = "执行人ID")]
 
        public Guid? ExecutorId { get; set; }

 
        public FrameworkUser Executor { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "优先级")]
        public TaskPriority Priority { get; set; } = TaskPriority.Normal;

        /// <summary>
        /// 异常标记
        /// </summary>
        [Display(Name = "异常标记")]
        public bool HasException { get; set; } = false;

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        public string Remark { get; set; }

        /// <summary>
        /// 任务明细列表
        /// </summary>
        [Display(Name = "任务明细列表")]
        public List<WarehouseTaskDetail> Details { get; set; }
    }
} 