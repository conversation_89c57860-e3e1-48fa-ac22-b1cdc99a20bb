using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 包装类型枚举
    /// 用于标识不同的包装类型
    /// </summary>
    public enum PackageType
    {
        /// <summary>
        /// 纸箱
        /// 使用纸箱进行包装
        /// </summary>
        [Display(Name = "纸箱")]
        Carton = 0,

        /// <summary>
        /// 木箱
        /// 使用木箱进行包装
        /// </summary>
        [Display(Name = "木箱")]
        WoodenBox = 1,

        /// <summary>
        /// 托盘
        /// 使用托盘进行包装
        /// </summary>
        [Display(Name = "托盘")]
        Pallet = 2,

        /// <summary>
        /// 袋装
        /// 使用袋子进行包装
        /// </summary>
        [Display(Name = "袋装")]
        Bag = 3,

        /// <summary>
        /// 桶装
        /// 使用桶进行包装
        /// </summary>
        [Display(Name = "桶装")]
        Drum = 4,

        /// <summary>
        /// 散装
        /// 无包装的散装状态
        /// </summary>
        [Display(Name = "散装")]
        Bulk = 5
    }

    /// <summary>
    /// 包装规格枚举
    /// 用于标识包装的规格等级
    /// </summary>
    public enum PackageLevel
    {
        /// <summary>
        /// 小包装
        /// 最小单位包装
        /// </summary>
        [Display(Name = "小包装")]
        Small = 0,

        /// <summary>
        /// 中包装
        /// 中等单位包装
        /// </summary>
        [Display(Name = "中包装")]
        Medium = 1,

        /// <summary>
        /// 大包装
        /// 最大单位包装
        /// </summary>
        [Display(Name = "大包装")]
        Large = 2,

        /// <summary>
        /// 混合包装
        /// 多种规格混合包装
        /// </summary>
        [Display(Name = "混合包装")]
        Mixed = 3
    }
} 