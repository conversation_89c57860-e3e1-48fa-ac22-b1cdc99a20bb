using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_LocationLocalizations")]
    public class LocationLocalization : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string LocationId { get; set; }
        public Location Location { get; set; }

        [Required]
        [StringLength(10)]
        public string Culture { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 