-- ==================================================================================================================================================================
-- 表: WMS_Inventory (库存信息)
-- 描述: 存储当前库存状态
-- ==================================================================================================================================================================
CREATE TABLE WMS_Inventory (
    ID NVARCHAR(50) PRIMARY KEY NOT NULL,  -- 主键ID
    WarehouseId NVARCHAR(50) NOT NULL,      -- 仓库ID
    LocationId NVARCHAR(50) NOT NULL,       -- 库位ID
    MaterialId NVARCHAR(50) NOT NULL,       -- 物料ID
    BatchId NVARCHAR(50) NULL,              -- 批次ID
    Quantity DECIMAL(18,4) NOT NULL,        -- 数量
    AllocatedQuantity DECIMAL(18,4) NULL,   -- 分配数量
    AvailableQuantity DECIMAL(18,4) NULL,   -- 可用数量
    Unit NVARCHAR(20) NOT NULL,             -- 单位
    Status INT NOT NULL,                    -- 库存状态 (枚举: InventoryStatus)
    LastMovementTime DATETIME2 NULL,        -- 上次移动时间
    InboundTime DATETIME2 NOT NULL,         -- 入库时间
    ProductionDate DATETIME2 NULL,          -- 生产日期
    ExpiryDate DATETIME2 NULL,              -- 到期日期
    Cost DECIMAL(18,4) NULL,                -- 成本
    OwnerId NVARCHAR(50) NULL,              -- 所有者ID
    Remark NVARCHAR(500) NULL,              -- 备注
    CreateTime DATETIME2 NULL,              -- 创建时间
    CreateBy NVARCHAR(50) NULL,             -- 创建人
    UpdateTime DATETIME2 NULL,              -- 更新时间
    UpdateBy NVARCHAR(50) NULL,             -- 更新人
    TenantId NVARCHAR(50) NULL,             -- 租户ID

    CONSTRAINT FK_Inventory_Warehouse FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID),
    CONSTRAINT FK_Inventory_Location FOREIGN KEY (LocationId) REFERENCES WMS_Locations(ID),
    CONSTRAINT FK_Inventory_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID), -- 假设物料表为 WMS_Materials
    CONSTRAINT FK_Inventory_Batch FOREIGN KEY (BatchId) REFERENCES WMS_Batches(ID)          -- 假设批次表为 WMS_Batches
);

-- 添加 WMS_Inventory 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'库存信息; 存储当前库存状态',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'WMS_Inventory';

-- 添加 WMS_Inventory 列注释
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'ID';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'WarehouseId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'LocationId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'MaterialId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'BatchId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'Quantity';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'分配数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'AllocatedQuantity';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'可用数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'AvailableQuantity';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'单位', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'Unit';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'库存状态 (枚举: InventoryStatus)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'Status';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'上次移动时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'LastMovementTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'入库时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'InboundTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'生产日期', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'ProductionDate';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'到期日期', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'ExpiryDate';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'成本', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'Cost';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'所有者ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'OwnerId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'Remark';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'CreateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'CreateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'UpdateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'UpdateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_Inventory', @level2type=N'COLUMN', @level2name=N'TenantId';

-- 创建唯一索引
CREATE UNIQUE INDEX IX_Inventory_Material_Batch_Warehouse_Location ON WMS_Inventory (MaterialId, BatchId, WarehouseId, LocationId);


-- ==================================================================================================================================================================
-- 表: WMS_InventoryLedgers (库存台账)
-- 描述: 记录库存变动的完整历史
-- ==================================================================================================================================================================
CREATE TABLE WMS_InventoryLedgers (
    ID NVARCHAR(50) PRIMARY KEY NOT NULL,  -- 主键ID
    LedgerNo NVARCHAR(50) NOT NULL,         -- 台账编号
    MaterialId NVARCHAR(50) NOT NULL,       -- 物料ID
    BatchId NVARCHAR(50) NULL,              -- 批次ID
    WarehouseId NVARCHAR(50) NOT NULL,      -- 仓库ID
    LocationId NVARCHAR(50) NOT NULL,       -- 库位ID
    MovementType INT NOT NULL,              -- 变动类型 (枚举: MovementType, 定义在 InventoryMovement 模型中, 但此处用作通用类型)
    QuantityBefore DECIMAL(18,4) NOT NULL,  -- 变动前数量
    QuantityChange DECIMAL(18,4) NOT NULL,  -- 变动数量
    QuantityAfter DECIMAL(18,4) NOT NULL,   -- 变动后数量
    Unit NVARCHAR(20) NOT NULL,             -- 单位
    OperatorId UNIQUEIDENTIFIER NULL,       -- 操作人ID (原为 Guid?, 改为 UNIQUEIDENTIFIER)
    OperationTime DATETIME2 NOT NULL,       -- 操作时间
    RelatedOrderNo NVARCHAR(50) NULL,       -- 关联单据号
    Remark NVARCHAR(500) NULL,              -- 备注
    CreateTime DATETIME2 NULL,              -- 创建时间
    CreateBy NVARCHAR(50) NULL,             -- 创建人
    UpdateTime DATETIME2 NULL,              -- 更新时间
    UpdateBy NVARCHAR(50) NULL,             -- 更新人
    TenantId NVARCHAR(50) NULL,             -- 租户ID

    CONSTRAINT FK_Ledger_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID),
    CONSTRAINT FK_Ledger_Batch FOREIGN KEY (BatchId) REFERENCES WMS_Batches(ID),
    CONSTRAINT FK_Ledger_Warehouse FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID),
    CONSTRAINT FK_Ledger_Location FOREIGN KEY (LocationId) REFERENCES WMS_Locations(ID),
    CONSTRAINT FK_Ledger_Operator FOREIGN KEY (OperatorId) REFERENCES WMS_FrameworkUsers(ID) -- 假设用户表为 WMS_FrameworkUsers 且主键为 UNIQUEIDENTIFIER
);

-- 添加 WMS_InventoryLedgers 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'库存台账; 记录库存变动的完整历史',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'WMS_InventoryLedgers';

-- 添加 WMS_InventoryLedgers 列注释
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'ID';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'台账编号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'LedgerNo';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'MaterialId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'BatchId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'WarehouseId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'LocationId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'变动类型 (枚举: MovementType)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'MovementType';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'变动前数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'QuantityBefore';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'变动数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'QuantityChange';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'变动后数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'QuantityAfter';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'单位', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'Unit';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'操作人ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'OperatorId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'操作时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'OperationTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'关联单据号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'RelatedOrderNo';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'Remark';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'CreateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'CreateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'UpdateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'UpdateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryLedgers', @level2type=N'COLUMN', @level2name=N'TenantId';


-- ==================================================================================================================================================================
-- 表: WMS_InventoryAdjustments (库存调整记录)
-- 描述: 记录手动调整库存的历史
-- ==================================================================================================================================================================
CREATE TABLE WMS_InventoryAdjustments (
    ID NVARCHAR(50) PRIMARY KEY NOT NULL,  -- 主键ID
    AdjustmentNo NVARCHAR(50) NOT NULL,     -- 调整单号
    MaterialId NVARCHAR(50) NOT NULL,       -- 物料ID
    BatchId NVARCHAR(50) NULL,              -- 批次ID
    WarehouseId NVARCHAR(50) NOT NULL,      -- 仓库ID
    LocationId NVARCHAR(50) NOT NULL,       -- 库位ID
    QuantityBefore DECIMAL(18,4) NOT NULL,  -- 调整前数量
    QuantityAfter DECIMAL(18,4) NOT NULL,   -- 调整后数量
    Difference DECIMAL(18,4) NOT NULL,      -- 调整数量差值
    AdjustmentType INT NOT NULL,            -- 调整类型 (枚举: AdjustmentType)
    AdjusterId UNIQUEIDENTIFIER NULL,       -- 调整人ID (原为 Guid?, 改为 UNIQUEIDENTIFIER)
    AdjustmentTime DATETIME2 NOT NULL,      -- 调整时间
    Reason NVARCHAR(500) NOT NULL,          -- 调整原因
    Remark NVARCHAR(500) NULL,              -- 备注
    CreateTime DATETIME2 NULL,              -- 创建时间
    CreateBy NVARCHAR(50) NULL,             -- 创建人
    UpdateTime DATETIME2 NULL,              -- 更新时间
    UpdateBy NVARCHAR(50) NULL,             -- 更新人
    TenantId NVARCHAR(50) NULL,             -- 租户ID

    CONSTRAINT FK_Adjustment_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID),
    CONSTRAINT FK_Adjustment_Batch FOREIGN KEY (BatchId) REFERENCES WMS_Batches(ID),
    CONSTRAINT FK_Adjustment_Warehouse FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID),
    CONSTRAINT FK_Adjustment_Location FOREIGN KEY (LocationId) REFERENCES WMS_Locations(ID),
    CONSTRAINT FK_Adjustment_Adjuster FOREIGN KEY (AdjusterId) REFERENCES WMS_FrameworkUsers(ID)
);

-- 添加 WMS_InventoryAdjustments 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'库存调整记录; 记录手动调整库存的历史',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'WMS_InventoryAdjustments';

-- 添加 WMS_InventoryAdjustments 列注释
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'ID';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整单号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'AdjustmentNo';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'MaterialId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'BatchId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'WarehouseId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'LocationId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整前数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'QuantityBefore';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整后数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'QuantityAfter';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整数量差值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'Difference';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整类型 (枚举: AdjustmentType)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'AdjustmentType';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整人ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'AdjusterId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'AdjustmentTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'调整原因', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'Reason';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'Remark';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'CreateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'CreateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'UpdateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'UpdateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryAdjustments', @level2type=N'COLUMN', @level2name=N'TenantId';


-- ==================================================================================================================================================================
-- 表: WMS_InventoryMovements (库存移动记录)
-- 描述: 记录物料在不同库位间的移动历史
-- ==================================================================================================================================================================
CREATE TABLE WMS_InventoryMovements (
    ID NVARCHAR(50) PRIMARY KEY NOT NULL,  -- 主键ID
    MovementNo NVARCHAR(50) NOT NULL,       -- 移动单号
    MovementType INT NOT NULL,              -- 移动类型 (枚举: MovementType)
    Status INT NOT NULL,                    -- 移动状态 (枚举: MovementStatus)
    MaterialId NVARCHAR(50) NOT NULL,       -- 物料ID
    BatchId NVARCHAR(50) NULL,              -- 批次ID
    SourceWarehouseId NVARCHAR(50) NOT NULL,  -- 源仓库ID
    SourceLocationId NVARCHAR(50) NOT NULL,   -- 源库位ID
    TargetWarehouseId NVARCHAR(50) NOT NULL,  -- 目标仓库ID
    TargetLocationId NVARCHAR(50) NOT NULL,   -- 目标库位ID
    PlanQuantity DECIMAL(18,4) NOT NULL,    -- 计划移动数量
    ActualQuantity DECIMAL(18,4) NULL,      -- 实际移动数量
    PlanStartTime DATETIME2 NOT NULL,       -- 计划开始时间
    PlanEndTime DATETIME2 NOT NULL,         -- 计划完成时间
    ActualStartTime DATETIME2 NULL,         -- 实际开始时间
    ActualEndTime DATETIME2 NULL,           -- 实际完成时间
    OperatorId UNIQUEIDENTIFIER NULL,       -- 操作人ID (原为 Guid?)
    RelatedOrderNo NVARCHAR(50) NULL,       -- 关联单据号
    Remark NVARCHAR(500) NULL,              -- 备注
    CreateTime DATETIME2 NULL,              -- 创建时间
    CreateBy NVARCHAR(50) NULL,             -- 创建人
    UpdateTime DATETIME2 NULL,              -- 更新时间
    UpdateBy NVARCHAR(50) NULL,             -- 更新人
    TenantId NVARCHAR(50) NULL,             -- 租户ID

    CONSTRAINT FK_Movement_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID),
    CONSTRAINT FK_Movement_Batch FOREIGN KEY (BatchId) REFERENCES WMS_Batches(ID),
    CONSTRAINT FK_Movement_SourceWarehouse FOREIGN KEY (SourceWarehouseId) REFERENCES WMS_Warehouses(ID),
    CONSTRAINT FK_Movement_SourceLocation FOREIGN KEY (SourceLocationId) REFERENCES WMS_Locations(ID),
    CONSTRAINT FK_Movement_TargetWarehouse FOREIGN KEY (TargetWarehouseId) REFERENCES WMS_Warehouses(ID),
    CONSTRAINT FK_Movement_TargetLocation FOREIGN KEY (TargetLocationId) REFERENCES WMS_Locations(ID),
    CONSTRAINT FK_Movement_Operator FOREIGN KEY (OperatorId) REFERENCES WMS_FrameworkUsers(ID)
);

-- 添加 WMS_InventoryMovements 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'库存移动记录; 记录物料在不同库位间的移动历史',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'WMS_InventoryMovements';

-- 添加 WMS_InventoryMovements 列注释
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'ID';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'移动单号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'MovementNo';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'移动类型 (枚举: MovementType)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'MovementType';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'移动状态 (枚举: MovementStatus)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'Status';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'MaterialId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'BatchId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'源仓库ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'SourceWarehouseId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'源库位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'SourceLocationId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'目标仓库ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'TargetWarehouseId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'目标库位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'TargetLocationId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'计划移动数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'PlanQuantity';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'实际移动数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'ActualQuantity';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'计划开始时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'PlanStartTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'计划完成时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'PlanEndTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'实际开始时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'ActualStartTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'实际完成时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'ActualEndTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'操作人ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'OperatorId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'关联单据号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'RelatedOrderNo';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'Remark';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'CreateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'CreateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'UpdateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'UpdateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventoryMovements', @level2type=N'COLUMN', @level2name=N'TenantId';


-- ==================================================================================================================================================================
-- 表: WMS_InventorySnapshots (库存快照)
-- 描述: 记录特定时间点的库存状态，用于历史数据分析
-- ==================================================================================================================================================================
CREATE TABLE WMS_InventorySnapshots (
    ID NVARCHAR(50) PRIMARY KEY NOT NULL,  -- 主键ID
    SnapshotTime DATETIME2 NOT NULL,        -- 快照时间
    MaterialId NVARCHAR(50) NOT NULL,       -- 物料ID
    BatchId NVARCHAR(50) NULL,              -- 批次ID
    WarehouseId NVARCHAR(50) NOT NULL,      -- 仓库ID
    LocationId NVARCHAR(50) NOT NULL,       -- 库位ID
    Quantity DECIMAL(18,4) NOT NULL,        -- 数量
    Unit NVARCHAR(20) NOT NULL,             -- 单位
    Status INT NOT NULL,                    -- 库存状态 (枚举: InventoryStatus)
    Remark NVARCHAR(500) NULL,              -- 备注
    CreateTime DATETIME2 NULL,              -- 创建时间
    CreateBy NVARCHAR(50) NULL,             -- 创建人
    UpdateTime DATETIME2 NULL,              -- 更新时间
    UpdateBy NVARCHAR(50) NULL,             -- 更新人
    TenantId NVARCHAR(50) NULL,             -- 租户ID

    CONSTRAINT FK_Snapshot_Material FOREIGN KEY (MaterialId) REFERENCES WMS_Materials(ID),
    CONSTRAINT FK_Snapshot_Batch FOREIGN KEY (BatchId) REFERENCES WMS_Batches(ID),
    CONSTRAINT FK_Snapshot_Warehouse FOREIGN KEY (WarehouseId) REFERENCES WMS_Warehouses(ID),
    CONSTRAINT FK_Snapshot_Location FOREIGN KEY (LocationId) REFERENCES WMS_Locations(ID)
);

-- 添加 WMS_InventorySnapshots 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'库存快照; 记录特定时间点的库存状态，用于历史数据分析',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'WMS_InventorySnapshots';

-- 添加 WMS_InventorySnapshots 列注释
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'ID';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'快照时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'SnapshotTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'MaterialId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'BatchId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'WarehouseId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'LocationId';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'数量', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'Quantity';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'单位', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'Unit';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'库存状态 (枚举: InventoryStatus)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'Status';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'Remark';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'CreateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'创建人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'CreateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'UpdateTime';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'更新人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'UpdateBy';
EXEC sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'WMS_InventorySnapshots', @level2type=N'COLUMN', @level2name=N'TenantId';


</code_block_to_apply_changes_from> 