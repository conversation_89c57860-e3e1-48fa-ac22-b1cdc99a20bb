using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 移库类型
    /// </summary>
    public enum TransferType
    {
        /// <summary>
        /// 库内移库（同一仓库内的库位间移动）
        /// </summary>
        [Display(Name = "库内移库")]
        Internal = 0,

        /// <summary>
        /// 跨库移库（不同仓库间的移动）
        /// </summary>
        [Display(Name = "跨库移库")]
        External = 1,

        /// <summary>
        /// 整批移库（整个批次的移动）
        /// </summary>
        [Display(Name = "整批移库")]
        Batch = 2,

        /// <summary>
        /// 部分移库（批次的部分移动）
        /// </summary>
        [Display(Name = "部分移库")]
        Partial = 3,

        /// <summary>
        /// 补货移库（从存储区域到拣货区域）
        /// </summary>
        [Display(Name = "补货移库")]
        Replenishment = 4,

        /// <summary>
        /// 库位整理（库存整理优化）
        /// </summary>
        [Display(Name = "库位整理")]
        Reorganization = 5
    }

    /// <summary>
    /// 移库单状态
    /// </summary>
    public enum TransferStatus
    {
        /// <summary>
        /// 草稿（初始创建未确认）
        /// </summary>
        [Display(Name = "草稿")]
        Draft = 0,

        /// <summary>
        /// 待执行（已确认等待执行）
        /// </summary>
        [Display(Name = "待执行")]
        Pending = 1,

        /// <summary>
        /// 执行中（正在进行移库作业）
        /// </summary>
        [Display(Name = "执行中")]
        InProcess = 2,

        /// <summary>
        /// 已完成（全部移库完成）
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 3,

        /// <summary>
        /// 部分完成（部分移库完成）
        /// </summary>
        [Display(Name = "部分完成")]
        PartiallyCompleted = 4,

        /// <summary>
        /// 已取消（移库单被取消）
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 5,

        /// <summary>
        /// 异常（移库过程中出现异常）
        /// </summary>
        [Display(Name = "异常")]
        Exception = 6
    }

    /// <summary>
    /// 移库明细状态
    /// </summary>
    public enum TransferDetailStatus
    {
        /// <summary>
        /// 待执行（等待执行）
        /// </summary>
        [Display(Name = "待执行")]
        Pending = 0,

        /// <summary>
        /// 已拣货（从源库位已取出）
        /// </summary>
        [Display(Name = "已拣货")]
        Picked = 1,

        /// <summary>
        /// 在途中（物料在转运过程中）
        /// </summary>
        [Display(Name = "在途中")]
        InTransit = 2,

        /// <summary>
        /// 已完成（已放置到目标库位）
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 3,

        /// <summary>
        /// 已取消（明细被取消）
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 4,

        /// <summary>
        /// 异常（执行过程中出现异常）
        /// </summary>
        [Display(Name = "异常")]
        Exception = 5
    }
} 