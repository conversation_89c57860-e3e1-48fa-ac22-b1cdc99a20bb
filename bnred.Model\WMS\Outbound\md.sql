-- ----------------------------
-- Table structure for WMS_Outbounds
-- ----------------------------
DROP TABLE IF EXISTS [WMS_Outbounds];
CREATE TABLE [WMS_Outbounds] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [OutboundNo] nvarchar(50) NOT NULL, -- 出库单号
  [Type] int NOT NULL, -- 出库类型 (需要映射到 WMS_EnumDictionary, C#类型: DocumentType)
  [MaterialId] nvarchar(50) NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [WarehouseId] nvarchar(50) NOT NULL, -- 仓库ID
  [LocationId] nvarchar(50) NULL, -- 位置ID
  [Quantity] decimal(18,2) NOT NULL, -- 数量
  [Unit] nvarchar(20) NULL, -- 单位
  [UnitPrice] decimal(18,2) NOT NULL, -- 单价
  [TotalAmount] decimal(18,2) NOT NULL, -- 总金额
  [CustomerId] nvarchar(50) NULL, -- 客户ID (外键可能指向 WMS_Customer 或类似表)
  [DocumentDate] datetime2(7) NOT NULL, -- 单据日期
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_Outbounds
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_OutboundOrders
-- ----------------------------
DROP TABLE IF EXISTS [WMS_OutboundOrders];
CREATE TABLE [WMS_OutboundOrders] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [OrderNo] nvarchar(50) NOT NULL, -- 出库单号
  [Type] int NOT NULL, -- 出库类型 (需要映射到 WMS_EnumDictionary, C#类型: OutboundType)
  [WarehouseId] nvarchar(50) NOT NULL, -- 仓库ID
  [PlanTime] datetime2(7) NULL, -- 预计出库日期
  [ActualTime] datetime2(7) NULL, -- 实际出库日期
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_OutboundOrders
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_OutboundOrderDetails
-- ----------------------------
DROP TABLE IF EXISTS [WMS_OutboundOrderDetails];
CREATE TABLE [WMS_OutboundOrderDetails] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [OutboundOrderId] nvarchar(50) NOT NULL, -- 出库单ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [LocationId] nvarchar(50) NOT NULL, -- 库位ID
  [PlanQuantity] decimal(18,4) NOT NULL, -- 计划数量
  [ActualQuantity] decimal(18,4) NULL, -- 实际数量
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_OutboundOrderDetails
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_Outbounds
-- ----------------------------
ALTER TABLE [WMS_Outbounds] ADD CONSTRAINT [FK_WMS_Outbounds_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Outbounds] ADD CONSTRAINT [FK_WMS_Outbounds_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Outbounds] ADD CONSTRAINT [FK_WMS_Outbounds_Warehouse] FOREIGN KEY ([WarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_Outbounds] ADD CONSTRAINT [FK_WMS_Outbounds_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
-- ALTER TABLE [WMS_Outbounds] ADD CONSTRAINT [FK_WMS_Outbounds_Customer] FOREIGN KEY ([CustomerId]) REFERENCES [WMS_Customer] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION; -- 请根据实际客户表进行调整
-- GO

-- ----------------------------
-- Foreign Keys structure for table WMS_OutboundOrders
-- ----------------------------
ALTER TABLE [WMS_OutboundOrders] ADD CONSTRAINT [FK_WMS_OutboundOrders_Warehouse] FOREIGN KEY ([WarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- Foreign Keys structure for table WMS_OutboundOrderDetails
-- ----------------------------
ALTER TABLE [WMS_OutboundOrderDetails] ADD CONSTRAINT [FK_WMS_OutboundOrderDetails_OutboundOrder] FOREIGN KEY ([OutboundOrderId]) REFERENCES [WMS_OutboundOrders] ([ID]) ON DELETE CASCADE ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_OutboundOrderDetails] ADD CONSTRAINT [FK_WMS_OutboundOrderDetails_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_OutboundOrderDetails] ADD CONSTRAINT [FK_WMS_OutboundOrderDetails_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_OutboundOrderDetails] ADD CONSTRAINT [FK_WMS_OutboundOrderDetails_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- MS_Description for table WMS_Outbounds
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'OutboundNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'Type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'WarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'位置ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'Quantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单位' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'Unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'UnitPrice'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总金额' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'TotalAmount'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'客户ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'CustomerId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'单据日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'DocumentDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Outbounds', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_OutboundOrders
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库订单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'OrderNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库类型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'Type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'WarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预计出库日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'PlanTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际出库日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'ActualTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrders', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_OutboundOrderDetails
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库单明细' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库单ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'OutboundOrderId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'计划数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'PlanQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'ActualQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_OutboundOrderDetails', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO 