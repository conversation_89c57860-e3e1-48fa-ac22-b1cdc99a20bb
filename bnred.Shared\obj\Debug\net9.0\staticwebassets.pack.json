{"Files": [{"Id": "D:\\work\\wms2025\\bnred.Shared\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\bnred.Shared.bundle.scp.css", "PackagePath": "staticwebassets\\bnred.Shared.gjyvc1v7oj.bundle.scp.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\css\\loading.css", "PackagePath": "staticwebassets\\css\\loading.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\all.css", "PackagePath": "staticwebassets\\font-awesome\\css\\all.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\all.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\all.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\brands.css", "PackagePath": "staticwebassets\\font-awesome\\css\\brands.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\brands.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\brands.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\fontawesome.css", "PackagePath": "staticwebassets\\font-awesome\\css\\fontawesome.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\fontawesome.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\fontawesome.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\regular.css", "PackagePath": "staticwebassets\\font-awesome\\css\\regular.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\regular.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\regular.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\solid.css", "PackagePath": "staticwebassets\\font-awesome\\css\\solid.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\solid.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\solid.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\svg-with-js.css", "PackagePath": "staticwebassets\\font-awesome\\css\\svg-with-js.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\svg-with-js.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\svg-with-js.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\v4-font-face.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-font-face.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\v4-font-face.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-font-face.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\v4-shims.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-shims.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\v4-shims.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v4-shims.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\v5-font-face.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v5-font-face.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\css\\v5-font-face.min.css", "PackagePath": "staticwebassets\\font-awesome\\css\\v5-font-face.min.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-brands-400.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-brands-400.ttf"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-brands-400.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-brands-400.woff2"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-regular-400.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-regular-400.ttf"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-regular-400.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-regular-400.woff2"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-solid-900.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-solid-900.ttf"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-solid-900.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-solid-900.woff2"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-v4compatibility.ttf", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-v4compatibility.ttf"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font-awesome\\webfonts\\fa-v4compatibility.woff2", "PackagePath": "staticwebassets\\font-awesome\\webfonts\\fa-v4compatibility.woff2"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font\\iconfont.css", "PackagePath": "staticwebassets\\font\\iconfont.css"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font\\iconfont.eot", "PackagePath": "staticwebassets\\font\\iconfont.eot"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font\\iconfont.svg", "PackagePath": "staticwebassets\\font\\iconfont.svg"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font\\iconfont.ttf", "PackagePath": "staticwebassets\\font\\iconfont.ttf"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font\\iconfont.woff", "PackagePath": "staticwebassets\\font\\iconfont.woff"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\font\\iconfont.woff2", "PackagePath": "staticwebassets\\font\\iconfont.woff2"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\images\\logo.png", "PackagePath": "staticwebassets\\images\\logo.png"}, {"Id": "D:\\work\\wms2025\\bnred.Shared\\wwwroot\\js\\common.js", "PackagePath": "staticwebassets\\js\\common.js"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.bnred.Shared.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.bnred.Shared.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.bnred.Shared.props", "PackagePath": "build\\bnred.Shared.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.bnred.Shared.props", "PackagePath": "buildMultiTargeting\\bnred.Shared.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.bnred.Shared.props", "PackagePath": "buildTransitive\\bnred.Shared.props"}], "ElementsToRemove": []}