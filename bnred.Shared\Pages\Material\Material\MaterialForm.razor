@using bnred.Model.Material
@using bnred.ViewModel.Material.MaterialVMs
@using BootstrapBlazor.Components
@using WalkingTec.Mvvm.Core

<ValidateForm Model="@Model" OnValidSubmit="@OnSave">
    <Tabs>
        <TabItem Text="基本信息" Icon="fas fa-info-circle">
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Code" 
                                  DisplayText="物料编码" 
                                  PlaceHolder="请输入物料编码"
                                  IsRequired="true"
                                  ShowRequiredMark="true" />
                </div>
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Name" 
                                  DisplayText="物料名称" 
                                  PlaceHolder="请输入物料名称"
                                  IsRequired="true"
                                  ShowRequiredMark="true" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Specification" 
                                  DisplayText="规格型号" 
                                  PlaceHolder="请输入规格型号" />
                </div>
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Brand" 
                                  DisplayText="品牌" 
                                  PlaceHolder="请输入品牌" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <Select @bind-Value="@Model.CategoryId" 
                           DisplayText="物料分类"
                           PlaceHolder="请选择物料分类"
                           IsRequired="true"
                           ShowRequiredMark="true">
                        @if (Categories != null)
                        {
                            @foreach (var category in Categories)
                            {
                                <SelectOption Text="@category.Name" Value="@category.ID" />
                            }
                        }
                    </Select>
                </div>
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Unit" 
                                  DisplayText="基本单位" 
                                  PlaceHolder="请输入基本单位"
                                  IsRequired="true"
                                  ShowRequiredMark="true" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <Select @bind-Value="@Model.Status" 
                           DisplayText="状态"
                           IsRequired="true"
                           ShowRequiredMark="true">
                        <SelectOption Text="启用" Value="@MaterialStatus.Active" />
                        <SelectOption Text="停用" Value="@MaterialStatus.Inactive" />
                    </Select>
                </div>
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Origin" 
                                  DisplayText="产地" 
                                  PlaceHolder="请输入产地" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <Textarea @bind-Value="@Model.Description" 
                             DisplayText="物料描述" 
                             PlaceHolder="请输入物料描述"
                             rows="3" />
                </div>
            </div>
        </TabItem>
        
        <TabItem Text="技术参数" Icon="fas fa-cogs">
            <div class="row">
                <div class="col-md-4">
                    <BootstrapInputNumber @bind-Value="@Model.Length" 
                                        DisplayText="长度(mm)" 
                                        PlaceHolder="请输入长度" />
                </div>
                <div class="col-md-4">
                    <BootstrapInputNumber @bind-Value="@Model.Width" 
                                        DisplayText="宽度(mm)" 
                                        PlaceHolder="请输入宽度" />
                </div>
                <div class="col-md-4">
                    <BootstrapInputNumber @bind-Value="@Model.Height" 
                                        DisplayText="高度(mm)" 
                                        PlaceHolder="请输入高度" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.Weight" 
                                        DisplayText="重量(kg)" 
                                        PlaceHolder="请输入重量" />
                </div>
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.Volume" 
                                        DisplayText="体积(m³)" 
                                        PlaceHolder="请输入体积" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Color" 
                                  DisplayText="颜色" 
                                  PlaceHolder="请输入颜色" />
                </div>
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Material" 
                                  DisplayText="材质" 
                                  PlaceHolder="请输入材质" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <Textarea @bind-Value="@Model.TechnicalSpecs" 
                             DisplayText="技术指标" 
                             PlaceHolder="请输入技术指标"
                             rows="4" />
                </div>
            </div>
        </TabItem>
        
        <TabItem Text="存储要求" Icon="fas fa-warehouse">
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.MinTemperature" 
                                        DisplayText="最低温度(℃)" 
                                        PlaceHolder="请输入最低温度" />
                </div>
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.MaxTemperature" 
                                        DisplayText="最高温度(℃)" 
                                        PlaceHolder="请输入最高温度" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.MinHumidity" 
                                        DisplayText="最低湿度(%)" 
                                        PlaceHolder="请输入最低湿度" />
                </div>
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.MaxHumidity" 
                                        DisplayText="最高湿度(%)" 
                                        PlaceHolder="请输入最高湿度" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.RequireVentilation" 
                           DisplayText="需要通风" />
                </div>
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.AvoidSunlight" 
                           DisplayText="避免阳光直射" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.MaxStackLayers" 
                                        DisplayText="最大堆码层数" 
                                        PlaceHolder="请输入最大堆码层数" />
                </div>
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.ShelfLife" 
                                        DisplayText="保质期(天)" 
                                        PlaceHolder="请输入保质期" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <Textarea @bind-Value="@Model.StorageRequirements" 
                             DisplayText="特殊存储要求" 
                             PlaceHolder="请输入特殊存储要求"
                             rows="3" />
                </div>
            </div>
        </TabItem>
        
        <TabItem Text="安全信息" Icon="fas fa-shield-alt">
            <div class="row">
                <div class="col-md-6">
                    <Select @bind-Value="@Model.DangerLevel" 
                           DisplayText="危险等级"
                           PlaceHolder="请选择危险等级">
                        <SelectOption Text="无危险" Value="@DangerLevel.None" />
                        <SelectOption Text="低危险" Value="@DangerLevel.Low" />
                        <SelectOption Text="中危险" Value="@DangerLevel.Medium" />
                        <SelectOption Text="高危险" Value="@DangerLevel.High" />
                    </Select>
                </div>
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.IsHazardous" 
                           DisplayText="危险品" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.IsFlammable" 
                           DisplayText="易燃品" />
                </div>
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.IsExplosive" 
                           DisplayText="易爆品" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.IsCorrosive" 
                           DisplayText="腐蚀品" />
                </div>
                <div class="col-md-6">
                    <Switch @bind-Value="@Model.IsToxic" 
                           DisplayText="有毒品" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <Textarea @bind-Value="@Model.SafetyNotes" 
                             DisplayText="安全注意事项" 
                             PlaceHolder="请输入安全注意事项"
                             rows="4" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <Textarea @bind-Value="@Model.MSDS" 
                             DisplayText="MSDS信息" 
                             PlaceHolder="请输入MSDS信息"
                             rows="4" />
                </div>
            </div>
        </TabItem>
        
        <TabItem Text="其他信息" Icon="fas fa-ellipsis-h">
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInput @bind-Value="@Model.Supplier" 
                                  DisplayText="主要供应商" 
                                  PlaceHolder="请输入主要供应商" />
                </div>
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.StandardCost" 
                                        DisplayText="标准成本" 
                                        PlaceHolder="请输入标准成本" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.MinOrderQuantity" 
                                        DisplayText="最小订购量" 
                                        PlaceHolder="请输入最小订购量" />
                </div>
                <div class="col-md-6">
                    <BootstrapInputNumber @bind-Value="@Model.SafetyStock" 
                                        DisplayText="安全库存" 
                                        PlaceHolder="请输入安全库存" />
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <Textarea @bind-Value="@Model.Remark" 
                             DisplayText="备注" 
                             PlaceHolder="请输入备注"
                             rows="3" />
                </div>
            </div>
        </TabItem>
    </Tabs>
    
    <div class="text-center mt-3">
        <Button ButtonType="ButtonType.Submit" Color="Color.Primary" Icon="fas fa-save">
            保存
        </Button>
        <Button Color="Color.Secondary" OnClick="@OnCancel" Icon="fas fa-times">
            取消
        </Button>
    </div>
</ValidateForm>

@code {
    /// <summary>
    /// 物料模型
    /// </summary>
    [Parameter] public MaterialVM Model { get; set; } = new();
    
    /// <summary>
    /// 保存回调
    /// </summary>
    [Parameter] public EventCallback<MaterialVM> OnSave { get; set; }
    
    /// <summary>
    /// 取消回调
    /// </summary>
    [Parameter] public EventCallback OnCancel { get; set; }
    
    /// <summary>
    /// 物料分类列表
    /// </summary>
    private List<MaterialCategoryVM>? Categories;

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }

    /// <summary>
    /// 加载物料分类
    /// </summary>
    private async Task LoadCategories()
    {
        try
        {
            // TODO: 调用API获取物料分类列表
            Categories = new List<MaterialCategoryVM>();
        }
        catch (Exception ex)
        {
            // 处理异常
            Console.WriteLine($"加载物料分类失败：{ex.Message}");
        }
    }
}
