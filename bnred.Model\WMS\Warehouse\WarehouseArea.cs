using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库区域信息
    /// 用于存储仓库内部区域的基本信息
    /// </summary>
    [Table("WarehouseAreas")]
    public class WarehouseArea : BasePoco 
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 区域编码
        /// </summary>
        [Display(Name = "区域编码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        [Display(Name = "区域名称")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Display(Name = "仓库")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse Warehouse { get; set; }

        /// <summary>
        /// 区域类型
        /// </summary>
        [Display(Name = "区域类型")]
        public AreaType Type { get; set; }

        /// <summary>
        /// 区域状态
        /// </summary>
        [Display(Name = "区域状态")]
        public AreaStatus Status { get; set; }

        /// <summary>
        /// 面积(平方米)
        /// </summary>
        [Display(Name = "面积(平方米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Area { get; set; }

        /// <summary>
        /// 容量(立方米)
        /// </summary>
        [Display(Name = "容量(立方米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Volume { get; set; }

        /// <summary>
        /// 最大存储量
        /// </summary>
        [Display(Name = "最大存储量")]
        public int? MaxCapacity { get; set; }

        /// <summary>
        /// 当前存储量
        /// </summary>
        [Display(Name = "当前存储量")]
        public int? CurrentCapacity { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        [Display(Name = "负责人")]
 
        public Guid? ManagerId { get; set; }
 
        public FrameworkUser Manager { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Display(Name = "描述")]
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 库位列表
        /// </summary>
        [Display(Name = "库位列表")]
        public List<Location> Locations { get; set; }
    }
}