-- #############################################################################
-- # SQL DDL for WMS Alert Module
-- # Target File: bnred.Model/WMS/Alert/md.sql
-- #############################################################################

-- #############################################################################
-- # Table: WMS_InventoryAlerts (库存预警表)
-- # 来自: InventoryAlert.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_InventoryAlerts', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_InventoryAlerts;
GO
CREATE TABLE WMS_InventoryAlerts (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    AlertDate DATETIME2 NOT NULL,                        -- 预警日期
    MaterialId NVARCHAR(50) NOT NULL,                    -- 物料ID
    WarehouseId NVARCHAR(50) NOT NULL,                   -- 仓库ID
    CurrentQuantity DECIMAL(18,4) NOT NULL,              -- 当前库存数量
    MinQuantity DECIMAL(18,4) NOT NULL,                  -- 预设最小库存量
    MaxQuantity DECIMAL(18,4) NOT NULL,                  -- 预设最大库存量
    AlertType INT NOT NULL,                              -- 预警类型 (例如：低于最小库存，高于最大库存 - 关联 WMS_EnumDictionary, EnumType='bnred.Model.WMS.Enums.AlertType')
    Status INT NOT NULL,                                 -- 预警状态 (例如：待处理，已处理，已忽略 - 关联 WMS_EnumDictionary, EnumType='bnred.Model.WMS.Enums.AlertStatus')
    Remark NVARCHAR(500) NULL,                           -- 备注
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_InventoryAlerts table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存预警表，记录库存超出预设阈值（最低或最高）的情况。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts';
-- MS_Description for WMS_InventoryAlerts columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预警发生的日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'AlertDate';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'发生预警的物料ID (外键关联 WMS_Materials.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'MaterialId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'发生预警的仓库ID (外键关联 WMS_Warehouses.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'WarehouseId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预警发生时物料的当前库存数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'CurrentQuantity';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'该物料/仓库组合的预设最小库存量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'MinQuantity';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'该物料/仓库组合的预设最大库存量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'MaxQuantity';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预警类型 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.WMS.Enums.AlertType\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'AlertType';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预警处理状态 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.WMS.Enums.AlertStatus\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAlerts', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Foreign Key Constraints for WMS Alert Module
-- #############################################################################
ALTER TABLE WMS_InventoryAlerts
ADD CONSTRAINT FK_InventoryAlerts_Material FOREIGN KEY (MaterialId) REFERENCES dbo.WMS_Materials(ID); -- Assumes WMS_Materials in Material/md.sql
GO

ALTER TABLE WMS_InventoryAlerts
ADD CONSTRAINT FK_InventoryAlerts_Warehouse FOREIGN KEY (WarehouseId) REFERENCES dbo.WMS_Warehouses(ID); -- Assumes WMS_Warehouses in WMS/Warehouse/md.sql
GO

PRINT 'SQL for WMS Alert module created in bnred.Model/WMS/Alert/md.sql';
GO 