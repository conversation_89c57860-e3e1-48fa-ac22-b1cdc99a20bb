﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WalkingTec.Mvvm.Core;
using bnred.Controllers;
using bnred.ViewModel.Warehouse.WarehouseVMs;
using bnred.Model.WMS.Warehouse;
using bnred.DataAccess;


namespace bnred.Test
{
    [TestClass]
    public class WarehouseApiTest
    {
        private WarehouseController _controller;
        private string _seed;

        public WarehouseApiTest()
        {
            _seed = Guid.NewGuid().ToString();
            _controller = MockController.CreateApi<WarehouseController>(new DataContext(_seed, DBTypeEnum.Memory), "user");
        }

        [TestMethod]
        public void SearchTest()
        {
            ContentResult rv = _controller.Search(new WarehouseSearcher()) as ContentResult;
            Assert.IsTrue(string.IsNullOrEmpty(rv.Content)==false);
        }

        [TestMethod]
        public void CreateTest()
        {
            WarehouseVM vm = _controller.Wtm.CreateVM<WarehouseVM>();
            Warehouse v = new Warehouse();
            
            v.ID = "5wS0xIi";
            v.Code = "yzmiRSNDjQWrROCV6qkBnH0hbkk9t";
            v.Name = "Q5Yv9bRNtbDAa6XmbrzGdkbwysiH9";
            v.Type = bnred.Model.WMS.Enums.WarehouseType.Transit;
            v.Status = bnred.Model.WMS.Enums.WarehouseStatus.Maintenance;
            v.Address = "rxADBl4siTk529ftr0cTJnKbF1lX80v4WgqoRYOVyo";
            v.ContactPerson = "MjuOaOaNsecD2lHG";
            v.ContactPhone = "m";
            v.Area = 93;
            v.Volume = 5;
            v.ManagerID = AddFrameworkUser();
            v.Description = "9Sa5BTIPNuVj3cvwcMFM5TjWYoW14OTJAKWi1XywGfMl3jkQfj4bOLVFnJPoxfxuzdBESJWbredRyPy4KcbF8LDI6bcoZz2JtoOc6tqijecLqykzL3qCMf5mj1XqzE3H4bQ8HjAGNk974BHw1QCLpSZQGV91XWJWVXGK1rW6tGqvYChfnTVZtWkf0TS2YNV8Ul1s5wpkN9xc0rmJxbIdQ8ubBxJfT4qWPwHREzEEnTx24XtOXY9AMpn4iRoqyiv1zaj1yhxT4V0F99mCOy1WVqk6eEvcrDWSlpPvmAMG";
            v.Remark = "gu8uiLoahOt6xYrWSJuCixnQ6dbdBlCYYIIzKYP3BrqmzYJUydW0IM6wjQQqnn01Ni02rjcESQvhd5M8Nd25cLhXioOjlEOTMphBbEXjfH2vSQiUYnsqJ5fhicdQqkQ2MuQr3cEyNcqZWz4p9r15x8EYn9iMIn0qQwvw8evG19QaXbyQesL3ZvkyizlaKRidrAu6iGmqsTURBo1TfJgxZsYHRxcQI6b8xetwsINSznxbPKRQ5iP";
            vm.Entity = v;
            var rv = _controller.Add(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Warehouse>().Find(v.ID);
                
                Assert.AreEqual(data.ID, "5wS0xIi");
                Assert.AreEqual(data.Code, "yzmiRSNDjQWrROCV6qkBnH0hbkk9t");
                Assert.AreEqual(data.Name, "Q5Yv9bRNtbDAa6XmbrzGdkbwysiH9");
                Assert.AreEqual(data.Type, bnred.Model.WMS.Enums.WarehouseType.Transit);
                Assert.AreEqual(data.Status, bnred.Model.WMS.Enums.WarehouseStatus.Maintenance);
                Assert.AreEqual(data.Address, "rxADBl4siTk529ftr0cTJnKbF1lX80v4WgqoRYOVyo");
                Assert.AreEqual(data.ContactPerson, "MjuOaOaNsecD2lHG");
                Assert.AreEqual(data.ContactPhone, "m");
                Assert.AreEqual(data.Area, 93);
                Assert.AreEqual(data.Volume, 5);
                Assert.AreEqual(data.Description, "9Sa5BTIPNuVj3cvwcMFM5TjWYoW14OTJAKWi1XywGfMl3jkQfj4bOLVFnJPoxfxuzdBESJWbredRyPy4KcbF8LDI6bcoZz2JtoOc6tqijecLqykzL3qCMf5mj1XqzE3H4bQ8HjAGNk974BHw1QCLpSZQGV91XWJWVXGK1rW6tGqvYChfnTVZtWkf0TS2YNV8Ul1s5wpkN9xc0rmJxbIdQ8ubBxJfT4qWPwHREzEEnTx24XtOXY9AMpn4iRoqyiv1zaj1yhxT4V0F99mCOy1WVqk6eEvcrDWSlpPvmAMG");
                Assert.AreEqual(data.Remark, "gu8uiLoahOt6xYrWSJuCixnQ6dbdBlCYYIIzKYP3BrqmzYJUydW0IM6wjQQqnn01Ni02rjcESQvhd5M8Nd25cLhXioOjlEOTMphBbEXjfH2vSQiUYnsqJ5fhicdQqkQ2MuQr3cEyNcqZWz4p9r15x8EYn9iMIn0qQwvw8evG19QaXbyQesL3ZvkyizlaKRidrAu6iGmqsTURBo1TfJgxZsYHRxcQI6b8xetwsINSznxbPKRQ5iP");
                Assert.AreEqual(data.CreateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.CreateTime.Value).Seconds < 10);
            }
        }

        [TestMethod]
        public void EditTest()
        {
            Warehouse v = new Warehouse();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
       			
                v.ID = "5wS0xIi";
                v.Code = "yzmiRSNDjQWrROCV6qkBnH0hbkk9t";
                v.Name = "Q5Yv9bRNtbDAa6XmbrzGdkbwysiH9";
                v.Type = bnred.Model.WMS.Enums.WarehouseType.Transit;
                v.Status = bnred.Model.WMS.Enums.WarehouseStatus.Maintenance;
                v.Address = "rxADBl4siTk529ftr0cTJnKbF1lX80v4WgqoRYOVyo";
                v.ContactPerson = "MjuOaOaNsecD2lHG";
                v.ContactPhone = "m";
                v.Area = 93;
                v.Volume = 5;
                v.ManagerID = AddFrameworkUser();
                v.Description = "9Sa5BTIPNuVj3cvwcMFM5TjWYoW14OTJAKWi1XywGfMl3jkQfj4bOLVFnJPoxfxuzdBESJWbredRyPy4KcbF8LDI6bcoZz2JtoOc6tqijecLqykzL3qCMf5mj1XqzE3H4bQ8HjAGNk974BHw1QCLpSZQGV91XWJWVXGK1rW6tGqvYChfnTVZtWkf0TS2YNV8Ul1s5wpkN9xc0rmJxbIdQ8ubBxJfT4qWPwHREzEEnTx24XtOXY9AMpn4iRoqyiv1zaj1yhxT4V0F99mCOy1WVqk6eEvcrDWSlpPvmAMG";
                v.Remark = "gu8uiLoahOt6xYrWSJuCixnQ6dbdBlCYYIIzKYP3BrqmzYJUydW0IM6wjQQqnn01Ni02rjcESQvhd5M8Nd25cLhXioOjlEOTMphBbEXjfH2vSQiUYnsqJ5fhicdQqkQ2MuQr3cEyNcqZWz4p9r15x8EYn9iMIn0qQwvw8evG19QaXbyQesL3ZvkyizlaKRidrAu6iGmqsTURBo1TfJgxZsYHRxcQI6b8xetwsINSznxbPKRQ5iP";
                context.Set<Warehouse>().Add(v);
                context.SaveChanges();
            }

            WarehouseVM vm = _controller.Wtm.CreateVM<WarehouseVM>();
            var oldID = v.ID;
            v = new Warehouse();
            v.ID = oldID;
       		
            v.Code = "IVIgqQPsSQ1kFwn5o3y1X2ILhNjqz8r3X2185AmSDi9j4mo";
            v.Name = "O";
            v.Type = bnred.Model.WMS.Enums.WarehouseType.FinishedProduct;
            v.Status = bnred.Model.WMS.Enums.WarehouseStatus.Suspended;
            v.Address = "H5rXdmsF9kDvFAgp9arMIoogqHZl6DNviUxKK8szZbTX3egDYzA5wXkEl0wh6AeRd3M5do6EfnUe0lxP0V4VBibP6HNpA5b4G";
            v.ContactPerson = "GGsK9EAtXE5Dc3gyFkfDom537GOazOBqXi5n8T";
            v.ContactPhone = "n5kRlHStj3Qb69";
            v.Area = 34;
            v.Volume = 33;
            v.Description = "orECnlzYjzoKdp5SefD6QZpITtF0KK0z7wUbnMWKAg8O0eeN3GyeC95MQDTsETaS90WflOiLU9nUtFZsMw3BLUHyeVOzs8SplE4U229cXJuLoE2xAGvCKjKVDhcqMNvoIOmnmvpx29Xxz9iMMmrxI3IBIau3ZRUZpFqyvmSt8PEufhk2P1KUYYczNntOrn4HqhlpGbSeJCppUFS9AEc9xZCmjaGCZNLrYYlBCtQ0PZQaX2Po2dvKBDx2Hr6jHxs43V4OEieGHQgqhoQeFGKVs2fJJ8dXpGgZEXcQrDTf0L6XtSmLrZ5ir2634YJPSo4qiQ2klmtWaWeiIvB0EyCbIgbTyEZXE1ccWPbq4Tn6Qwn2CXvsgwvKEbc";
            v.Remark = "hdryYc0Dpk1RQs9p3Xw3OuQOKxYtkvB56JYN17IXrzrenW80lWqMAILq7kgQZmn6sdfsa8DdiX4p9zJCUJu85h6Gn4unWszAEMoaOQKOR1CHKLedQs1bywJivMFMaMrLZiJqpovgIY7qerBinmi6RBMPXbiqQBQoas1sw6g80fAENfcXe5CX4Mywf46zyy4c60gFLhsusXULQx";
            vm.Entity = v;
            vm.FC = new Dictionary<string, object>();
			
            vm.FC.Add("Entity.ID", "");
            vm.FC.Add("Entity.Code", "");
            vm.FC.Add("Entity.Name", "");
            vm.FC.Add("Entity.Type", "");
            vm.FC.Add("Entity.Status", "");
            vm.FC.Add("Entity.Address", "");
            vm.FC.Add("Entity.ContactPerson", "");
            vm.FC.Add("Entity.ContactPhone", "");
            vm.FC.Add("Entity.Area", "");
            vm.FC.Add("Entity.Volume", "");
            vm.FC.Add("Entity.ManagerID", "");
            vm.FC.Add("Entity.Description", "");
            vm.FC.Add("Entity.Remark", "");
            var rv = _controller.Edit(vm);
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data = context.Set<Warehouse>().Find(v.ID);
 				
                Assert.AreEqual(data.Code, "IVIgqQPsSQ1kFwn5o3y1X2ILhNjqz8r3X2185AmSDi9j4mo");
                Assert.AreEqual(data.Name, "O");
                Assert.AreEqual(data.Type, bnred.Model.WMS.Enums.WarehouseType.FinishedProduct);
                Assert.AreEqual(data.Status, bnred.Model.WMS.Enums.WarehouseStatus.Suspended);
                Assert.AreEqual(data.Address, "H5rXdmsF9kDvFAgp9arMIoogqHZl6DNviUxKK8szZbTX3egDYzA5wXkEl0wh6AeRd3M5do6EfnUe0lxP0V4VBibP6HNpA5b4G");
                Assert.AreEqual(data.ContactPerson, "GGsK9EAtXE5Dc3gyFkfDom537GOazOBqXi5n8T");
                Assert.AreEqual(data.ContactPhone, "n5kRlHStj3Qb69");
                Assert.AreEqual(data.Area, 34);
                Assert.AreEqual(data.Volume, 33);
                Assert.AreEqual(data.Description, "orECnlzYjzoKdp5SefD6QZpITtF0KK0z7wUbnMWKAg8O0eeN3GyeC95MQDTsETaS90WflOiLU9nUtFZsMw3BLUHyeVOzs8SplE4U229cXJuLoE2xAGvCKjKVDhcqMNvoIOmnmvpx29Xxz9iMMmrxI3IBIau3ZRUZpFqyvmSt8PEufhk2P1KUYYczNntOrn4HqhlpGbSeJCppUFS9AEc9xZCmjaGCZNLrYYlBCtQ0PZQaX2Po2dvKBDx2Hr6jHxs43V4OEieGHQgqhoQeFGKVs2fJJ8dXpGgZEXcQrDTf0L6XtSmLrZ5ir2634YJPSo4qiQ2klmtWaWeiIvB0EyCbIgbTyEZXE1ccWPbq4Tn6Qwn2CXvsgwvKEbc");
                Assert.AreEqual(data.Remark, "hdryYc0Dpk1RQs9p3Xw3OuQOKxYtkvB56JYN17IXrzrenW80lWqMAILq7kgQZmn6sdfsa8DdiX4p9zJCUJu85h6Gn4unWszAEMoaOQKOR1CHKLedQs1bywJivMFMaMrLZiJqpovgIY7qerBinmi6RBMPXbiqQBQoas1sw6g80fAENfcXe5CX4Mywf46zyy4c60gFLhsusXULQx");
                Assert.AreEqual(data.UpdateBy, "user");
                Assert.IsTrue(DateTime.Now.Subtract(data.UpdateTime.Value).Seconds < 10);
            }

        }

		[TestMethod]
        public void GetTest()
        {
            Warehouse v = new Warehouse();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
        		
                v.ID = "5wS0xIi";
                v.Code = "yzmiRSNDjQWrROCV6qkBnH0hbkk9t";
                v.Name = "Q5Yv9bRNtbDAa6XmbrzGdkbwysiH9";
                v.Type = bnred.Model.WMS.Enums.WarehouseType.Transit;
                v.Status = bnred.Model.WMS.Enums.WarehouseStatus.Maintenance;
                v.Address = "rxADBl4siTk529ftr0cTJnKbF1lX80v4WgqoRYOVyo";
                v.ContactPerson = "MjuOaOaNsecD2lHG";
                v.ContactPhone = "m";
                v.Area = 93;
                v.Volume = 5;
                v.ManagerID = AddFrameworkUser();
                v.Description = "9Sa5BTIPNuVj3cvwcMFM5TjWYoW14OTJAKWi1XywGfMl3jkQfj4bOLVFnJPoxfxuzdBESJWbredRyPy4KcbF8LDI6bcoZz2JtoOc6tqijecLqykzL3qCMf5mj1XqzE3H4bQ8HjAGNk974BHw1QCLpSZQGV91XWJWVXGK1rW6tGqvYChfnTVZtWkf0TS2YNV8Ul1s5wpkN9xc0rmJxbIdQ8ubBxJfT4qWPwHREzEEnTx24XtOXY9AMpn4iRoqyiv1zaj1yhxT4V0F99mCOy1WVqk6eEvcrDWSlpPvmAMG";
                v.Remark = "gu8uiLoahOt6xYrWSJuCixnQ6dbdBlCYYIIzKYP3BrqmzYJUydW0IM6wjQQqnn01Ni02rjcESQvhd5M8Nd25cLhXioOjlEOTMphBbEXjfH2vSQiUYnsqJ5fhicdQqkQ2MuQr3cEyNcqZWz4p9r15x8EYn9iMIn0qQwvw8evG19QaXbyQesL3ZvkyizlaKRidrAu6iGmqsTURBo1TfJgxZsYHRxcQI6b8xetwsINSznxbPKRQ5iP";
                context.Set<Warehouse>().Add(v);
                context.SaveChanges();
            }
            var rv = _controller.Get(v.ID.ToString());
            Assert.IsNotNull(rv);
        }

        [TestMethod]
        public void BatchDeleteTest()
        {
            Warehouse v1 = new Warehouse();
            Warehouse v2 = new Warehouse();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
				
                v1.ID = "5wS0xIi";
                v1.Code = "yzmiRSNDjQWrROCV6qkBnH0hbkk9t";
                v1.Name = "Q5Yv9bRNtbDAa6XmbrzGdkbwysiH9";
                v1.Type = bnred.Model.WMS.Enums.WarehouseType.Transit;
                v1.Status = bnred.Model.WMS.Enums.WarehouseStatus.Maintenance;
                v1.Address = "rxADBl4siTk529ftr0cTJnKbF1lX80v4WgqoRYOVyo";
                v1.ContactPerson = "MjuOaOaNsecD2lHG";
                v1.ContactPhone = "m";
                v1.Area = 93;
                v1.Volume = 5;
                v1.ManagerID = AddFrameworkUser();
                v1.Description = "9Sa5BTIPNuVj3cvwcMFM5TjWYoW14OTJAKWi1XywGfMl3jkQfj4bOLVFnJPoxfxuzdBESJWbredRyPy4KcbF8LDI6bcoZz2JtoOc6tqijecLqykzL3qCMf5mj1XqzE3H4bQ8HjAGNk974BHw1QCLpSZQGV91XWJWVXGK1rW6tGqvYChfnTVZtWkf0TS2YNV8Ul1s5wpkN9xc0rmJxbIdQ8ubBxJfT4qWPwHREzEEnTx24XtOXY9AMpn4iRoqyiv1zaj1yhxT4V0F99mCOy1WVqk6eEvcrDWSlpPvmAMG";
                v1.Remark = "gu8uiLoahOt6xYrWSJuCixnQ6dbdBlCYYIIzKYP3BrqmzYJUydW0IM6wjQQqnn01Ni02rjcESQvhd5M8Nd25cLhXioOjlEOTMphBbEXjfH2vSQiUYnsqJ5fhicdQqkQ2MuQr3cEyNcqZWz4p9r15x8EYn9iMIn0qQwvw8evG19QaXbyQesL3ZvkyizlaKRidrAu6iGmqsTURBo1TfJgxZsYHRxcQI6b8xetwsINSznxbPKRQ5iP";
                v2.ID = "j2ILtKI7WqlQBRwd87NiBh21cyIe7oR9ZHZtX70QHzd6";
                v2.Code = "IVIgqQPsSQ1kFwn5o3y1X2ILhNjqz8r3X2185AmSDi9j4mo";
                v2.Name = "O";
                v2.Type = bnred.Model.WMS.Enums.WarehouseType.FinishedProduct;
                v2.Status = bnred.Model.WMS.Enums.WarehouseStatus.Suspended;
                v2.Address = "H5rXdmsF9kDvFAgp9arMIoogqHZl6DNviUxKK8szZbTX3egDYzA5wXkEl0wh6AeRd3M5do6EfnUe0lxP0V4VBibP6HNpA5b4G";
                v2.ContactPerson = "GGsK9EAtXE5Dc3gyFkfDom537GOazOBqXi5n8T";
                v2.ContactPhone = "n5kRlHStj3Qb69";
                v2.Area = 34;
                v2.Volume = 33;
                v2.ManagerID = v1.ManagerID; 
                v2.Description = "orECnlzYjzoKdp5SefD6QZpITtF0KK0z7wUbnMWKAg8O0eeN3GyeC95MQDTsETaS90WflOiLU9nUtFZsMw3BLUHyeVOzs8SplE4U229cXJuLoE2xAGvCKjKVDhcqMNvoIOmnmvpx29Xxz9iMMmrxI3IBIau3ZRUZpFqyvmSt8PEufhk2P1KUYYczNntOrn4HqhlpGbSeJCppUFS9AEc9xZCmjaGCZNLrYYlBCtQ0PZQaX2Po2dvKBDx2Hr6jHxs43V4OEieGHQgqhoQeFGKVs2fJJ8dXpGgZEXcQrDTf0L6XtSmLrZ5ir2634YJPSo4qiQ2klmtWaWeiIvB0EyCbIgbTyEZXE1ccWPbq4Tn6Qwn2CXvsgwvKEbc";
                v2.Remark = "hdryYc0Dpk1RQs9p3Xw3OuQOKxYtkvB56JYN17IXrzrenW80lWqMAILq7kgQZmn6sdfsa8DdiX4p9zJCUJu85h6Gn4unWszAEMoaOQKOR1CHKLedQs1bywJivMFMaMrLZiJqpovgIY7qerBinmi6RBMPXbiqQBQoas1sw6g80fAENfcXe5CX4Mywf46zyy4c60gFLhsusXULQx";
                context.Set<Warehouse>().Add(v1);
                context.Set<Warehouse>().Add(v2);
                context.SaveChanges();
            }

            var rv = _controller.BatchDelete(new string[] { v1.ID.ToString(), v2.ID.ToString() });
            Assert.IsInstanceOfType(rv, typeof(OkObjectResult));

            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                var data1 = context.Set<Warehouse>().Find(v1.ID);
                var data2 = context.Set<Warehouse>().Find(v2.ID);
                Assert.AreEqual(data1, null);
            Assert.AreEqual(data2, null);
            }

            rv = _controller.BatchDelete(new string[] {});
            Assert.IsInstanceOfType(rv, typeof(OkResult));

        }

        private Guid AddFileAttachment()
        {
            FileAttachment v = new FileAttachment();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.FileName = "GpSqGNpfcvSVVSWsZ";
                v.FileExt = "a";
                v.Path = "6kujZNvHco";
                v.Length = 9;
                v.UploadTime = DateTime.Parse("2024-07-13 12:54:49");
                v.SaveMode = "mFIQo3WEbjsy";
                v.ExtraInfo = "hJ2v5xPlt";
                v.HandlerInfo = "iQvF";
                context.Set<FileAttachment>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }

        private Guid AddFrameworkUser()
        {
            FrameworkUser v = new FrameworkUser();
            using (var context = new DataContext(_seed, DBTypeEnum.Memory))
            {
                try{

                v.Email = "sJj2tni9jrhjUM8YKr0cyPSbzEhQn8gajHE6NzLreqgRzZGO";
                v.Gender = WalkingTec.Mvvm.Core.GenderEnum.Female;
                v.CellPhone = "AofdjjM";
                v.HomePhone = "UWhpnxQ6CZ9T7w";
                v.Address = "ucKWgzTIWo1YwRk2HE1UHpipG2G961M4pIGeSnz4cmreSpRWie69APEF6z5iGCsaJJhRs4MbbyyI6lCcC1Dg8n33vWusNPo0zhd9AeumCOZbdrwz7MexHDAPwDJNBfoaTh3Ry4vKCbIbZjWDfafTRyuhyEGWleSKx5LOIYPyNQ";
                v.ZipCode = "rceTIakE";
                v.ITCode = "K5NqYLsY";
                v.Password = "lOi7lHdqRilSR6tG";
                v.Name = "IC0WY";
                v.IsValid = true;
                v.PhotoId = AddFileAttachment();
                context.Set<FrameworkUser>().Add(v);
                context.SaveChanges();
                }
                catch{}
            }
            return v.ID;
        }


    }
}
