@page "/Material/Material"
@using bnred.Model.Material
@using bnred.ViewModel.Material.MaterialVMs
@using WalkingTec.Mvvm.Core
@using WalkingTec.Mvvm.Core.Extensions
@using BootstrapBlazor.Components
@inherits BasePage
@attribute [Authorize]

<PageTitle>物料管理</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <Card>
                <CardHeader>
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-boxes"></i> 物料管理
                        </h4>
                        <div>
                            <Button Color="Color.Primary" OnClick="@(() => ShowCreateDialog())" Icon="fas fa-plus">
                                新增物料
                            </Button>
                            <Button Color="Color.Success" OnClick="@(() => ShowImportDialog())" Icon="fas fa-upload">
                                批量导入
                            </Button>
                            <Button Color="Color.Info" OnClick="@(() => ExportData())" Icon="fas fa-download">
                                导出数据
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardBody>
                    <!-- 搜索条件区域 -->
                    <div class="search-panel mb-3">
                        <Row>
                            <Col span="6">
                                <BootstrapInput @bind-Value="@SearchModel.Code" 
                                              PlaceHolder="请输入物料编码" 
                                              DisplayText="物料编码" />
                            </Col>
                            <Col span="6">
                                <BootstrapInput @bind-Value="@SearchModel.Name" 
                                              PlaceHolder="请输入物料名称" 
                                              DisplayText="物料名称" />
                            </Col>
                            <Col span="6">
                                <Select @bind-Value="@SearchModel.CategoryId" 
                                       PlaceHolder="请选择物料分类"
                                       DisplayText="物料分类">
                                    <SelectOption Text="全部" Value="" />
                                    @if (Categories != null)
                                    {
                                        @foreach (var category in Categories)
                                        {
                                            <SelectOption Text="@category.Name" Value="@category.ID" />
                                        }
                                    }
                                </Select>
                            </Col>
                            <Col span="6">
                                <Select @bind-Value="@SearchModel.Status" 
                                       PlaceHolder="请选择状态"
                                       DisplayText="状态">
                                    <SelectOption Text="全部" Value="" />
                                    <SelectOption Text="启用" Value="@MaterialStatus.Active" />
                                    <SelectOption Text="停用" Value="@MaterialStatus.Inactive" />
                                </Select>
                            </Col>
                        </Row>
                        <Row class="mt-2">
                            <Col span="12" class="text-end">
                                <Button Color="Color.Primary" OnClick="@(() => SearchData())" Icon="fas fa-search">
                                    查询
                                </Button>
                                <Button Color="Color.Secondary" OnClick="@(() => ResetSearch())" Icon="fas fa-undo">
                                    重置
                                </Button>
                            </Col>
                        </Row>
                    </div>

                    <!-- 数据表格区域 -->
                    <Table TItem="MaterialListVM" 
                           IsPagination="true" 
                           PageItemsSource="@PageItemsSource"
                           IsStriped="true" 
                           IsBordered="true"
                           ShowSkeleton="true"
                           ShowLoading="@IsLoading"
                           OnQueryAsync="@OnQueryAsync">
                        <TableColumns>
                            <TableColumn @bind-Field="@context.Code" Text="物料编码" Sortable="true" />
                            <TableColumn @bind-Field="@context.Name" Text="物料名称" Sortable="true" />
                            <TableColumn @bind-Field="@context.Specification" Text="规格型号" />
                            <TableColumn @bind-Field="@context.CategoryName" Text="物料分类" />
                            <TableColumn @bind-Field="@context.Unit" Text="基本单位" />
                            <TableColumn @bind-Field="@context.Status" Text="状态">
                                <Template Context="value">
                                    @if (value.Value?.Status == MaterialStatus.Active)
                                    {
                                        <Badge Color="Color.Success">启用</Badge>
                                    }
                                    else
                                    {
                                        <Badge Color="Color.Danger">停用</Badge>
                                    }
                                </Template>
                            </TableColumn>
                            <TableColumn @bind-Field="@context.CreateTime" Text="创建时间" FormatString="yyyy-MM-dd HH:mm" />
                            <TableColumn @bind-Field="@context.ID" Text="操作" Width="200">
                                <Template Context="value">
                                    <div class="btn-group" role="group">
                                        <Button Size="Size.Small" Color="Color.Info" 
                                               OnClick="@(() => ShowDetailDialog(value.Value?.ID))" 
                                               Icon="fas fa-eye" Title="查看详情">
                                        </Button>
                                        <Button Size="Size.Small" Color="Color.Warning" 
                                               OnClick="@(() => ShowEditDialog(value.Value?.ID))" 
                                               Icon="fas fa-edit" Title="编辑">
                                        </Button>
                                        <Button Size="Size.Small" Color="Color.Danger" 
                                               OnClick="@(() => DeleteMaterial(value.Value?.ID))" 
                                               Icon="fas fa-trash" Title="删除">
                                        </Button>
                                        <Button Size="Size.Small" Color="Color.Secondary" 
                                               OnClick="@(() => ShowBarcodeDialog(value.Value?.ID))" 
                                               Icon="fas fa-qrcode" Title="条码管理">
                                        </Button>
                                    </div>
                                </Template>
                            </TableColumn>
                        </TableColumns>
                    </Table>
                </CardBody>
            </Card>
        </div>
    </div>
</div>

<!-- 新增/编辑对话框 -->
<Modal @ref="CreateEditModal" Title="@ModalTitle" Size="Size.Large">
    <BodyTemplate>
        @if (CurrentMaterial != null)
        {
            <MaterialForm Model="@CurrentMaterial" OnSave="@SaveMaterial" OnCancel="@CloseModal" />
        }
    </BodyTemplate>
</Modal>

<!-- 详情对话框 -->
<Modal @ref="DetailModal" Title="物料详情" Size="Size.Large">
    <BodyTemplate>
        @if (CurrentMaterial != null)
        {
            <MaterialDetail Model="@CurrentMaterial" />
        }
    </BodyTemplate>
</Modal>

<!-- 条码管理对话框 -->
<Modal @ref="BarcodeModal" Title="条码管理" Size="Size.Large">
    <BodyTemplate>
        @if (CurrentMaterialId != null)
        {
            <MaterialBarcodeManager MaterialId="@CurrentMaterialId" />
        }
    </BodyTemplate>
</Modal>

<!-- 导入对话框 -->
<Modal @ref="ImportModal" Title="批量导入物料" Size="Size.Large">
    <BodyTemplate>
        <MaterialImport OnImportCompleted="@OnImportCompleted" />
    </BodyTemplate>
</Modal>

@code {
    // 搜索模型
    private MaterialSearchVM SearchModel = new();
    
    // 当前操作的物料
    private MaterialVM? CurrentMaterial;
    private string? CurrentMaterialId;
    
    // 物料分类列表
    private List<MaterialCategoryVM>? Categories;
    
    // 模态框引用
    private Modal? CreateEditModal;
    private Modal? DetailModal;
    private Modal? BarcodeModal;
    private Modal? ImportModal;
    
    // 界面状态
    private bool IsLoading = false;
    private string ModalTitle = "";
    
    // 分页设置
    private readonly int[] PageItemsSource = { 10, 20, 50, 100 };

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadCategories();
    }

    /// <summary>
    /// 加载物料分类
    /// </summary>
    private async Task LoadCategories()
    {
        try
        {
            // TODO: 调用API获取物料分类列表
            Categories = new List<MaterialCategoryVM>();
        }
        catch (Exception ex)
        {
            await ShowMessage("加载物料分类失败：" + ex.Message, MessageType.Error);
        }
    }

    /// <summary>
    /// 查询数据
    /// </summary>
    private async Task<QueryData<MaterialListVM>> OnQueryAsync(QueryPageOptions options)
    {
        IsLoading = true;
        try
        {
            // TODO: 调用API查询物料数据
            var result = new QueryData<MaterialListVM>
            {
                Items = new List<MaterialListVM>(),
                TotalCount = 0,
                IsFiltered = true,
                IsSorted = true
            };
            
            return result;
        }
        catch (Exception ex)
        {
            await ShowMessage("查询数据失败：" + ex.Message, MessageType.Error);
            return new QueryData<MaterialListVM>();
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 搜索数据
    /// </summary>
    private async Task SearchData()
    {
        // TODO: 实现搜索逻辑
        StateHasChanged();
    }

    /// <summary>
    /// 重置搜索条件
    /// </summary>
    private async Task ResetSearch()
    {
        SearchModel = new MaterialSearchVM();
        await SearchData();
    }

    /// <summary>
    /// 显示新增对话框
    /// </summary>
    private async Task ShowCreateDialog()
    {
        ModalTitle = "新增物料";
        CurrentMaterial = new MaterialVM();
        await CreateEditModal!.Show();
    }

    /// <summary>
    /// 显示编辑对话框
    /// </summary>
    private async Task ShowEditDialog(string? id)
    {
        if (string.IsNullOrEmpty(id)) return;
        
        ModalTitle = "编辑物料";
        // TODO: 根据ID加载物料数据
        CurrentMaterial = new MaterialVM { ID = id };
        await CreateEditModal!.Show();
    }

    /// <summary>
    /// 显示详情对话框
    /// </summary>
    private async Task ShowDetailDialog(string? id)
    {
        if (string.IsNullOrEmpty(id)) return;
        
        // TODO: 根据ID加载物料详情
        CurrentMaterial = new MaterialVM { ID = id };
        await DetailModal!.Show();
    }

    /// <summary>
    /// 显示条码管理对话框
    /// </summary>
    private async Task ShowBarcodeDialog(string? id)
    {
        if (string.IsNullOrEmpty(id)) return;
        
        CurrentMaterialId = id;
        await BarcodeModal!.Show();
    }

    /// <summary>
    /// 显示导入对话框
    /// </summary>
    private async Task ShowImportDialog()
    {
        await ImportModal!.Show();
    }

    /// <summary>
    /// 保存物料
    /// </summary>
    private async Task SaveMaterial(MaterialVM model)
    {
        try
        {
            // TODO: 调用API保存物料数据
            await ShowMessage("保存成功", MessageType.Success);
            await CloseModal();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowMessage("保存失败：" + ex.Message, MessageType.Error);
        }
    }

    /// <summary>
    /// 删除物料
    /// </summary>
    private async Task DeleteMaterial(string? id)
    {
        if (string.IsNullOrEmpty(id)) return;
        
        var confirmed = await ShowConfirm("确定要删除这个物料吗？");
        if (confirmed)
        {
            try
            {
                // TODO: 调用API删除物料
                await ShowMessage("删除成功", MessageType.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                await ShowMessage("删除失败：" + ex.Message, MessageType.Error);
            }
        }
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    private async Task ExportData()
    {
        try
        {
            // TODO: 实现数据导出功能
            await ShowMessage("导出功能开发中", MessageType.Info);
        }
        catch (Exception ex)
        {
            await ShowMessage("导出失败：" + ex.Message, MessageType.Error);
        }
    }

    /// <summary>
    /// 导入完成回调
    /// </summary>
    private async Task OnImportCompleted()
    {
        await ImportModal!.Close();
        StateHasChanged();
    }

    /// <summary>
    /// 关闭模态框
    /// </summary>
    private async Task CloseModal()
    {
        await CreateEditModal!.Close();
        await DetailModal!.Close();
        CurrentMaterial = null;
    }
}
