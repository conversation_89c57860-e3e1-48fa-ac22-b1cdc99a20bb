using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 批次质量状态枚举
    /// </summary>
    public enum BatchQualityStatus
    {
        /// <summary>
        /// 待检
        /// </summary>
        [Display(Name = "Pending")]
        Pending = 0,

        /// <summary>
        /// 合格
        /// </summary>
        [Display(Name = "Qualified")]
        Qualified = 1,

        /// <summary>
        /// 不合格
        /// </summary>
        [Display(Name = "Unqualified")]
        Unqualified = 2,

        /// <summary>
        /// 待复检
        /// </summary>
        [Display(Name = "ReInspection")]
        ReInspection = 3,

        /// <summary>
        /// 报废
        /// </summary>
        [Display(Name = "Scrapped")]
        Scrapped = 4
    }

    /// <summary>
    /// 批次状态枚举
    /// </summary>
    public enum BatchStatus
    {
        /// <summary>
        /// 待入库
        /// </summary>
        [Display(Name = "PendingIn")]
        PendingIn = 0,

        /// <summary>
        /// 已入库
        /// </summary>
        [Display(Name = "InStock")]
        InStock = 1,

        /// <summary>
        /// 待出库
        /// </summary>
        [Display(Name = "PendingOut")]
        PendingOut = 2,

        /// <summary>
        /// 已出库
        /// </summary>
        [Display(Name = "OutStock")]
        OutStock = 3,

        /// <summary>
        /// 冻结
        /// </summary>
        [Display(Name = "Frozen")]
        Frozen = 4,

        /// <summary>
        /// 报废
        /// </summary>
        [Display(Name = "Scrapped")]
        Scrapped = 5
    }
}