using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 规则类型
    /// </summary>
    public enum RuleType
    {
        [Display(Name = "上架规则")]
        PutAway = 0,

        [Display(Name = "拣货规则")]
        Picking = 1,

        [Display(Name = "补货规则")]
        Replenishment = 2,

        [Display(Name = "库位分配")]
        LocationAssignment = 3,

        [Display(Name = "库存预警")]
        StockAlert = 4,

        [Display(Name = "批次管理")]
        BatchManagement = 5,

        [Display(Name = "其他规则")]
        Other = 99
    }
} 