using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
 
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Batch;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums.Inventory;

namespace bnred.Model.WMS.Inventory
{
    /// <summary>
    /// 库存信息
    /// 存储当前库存状态
    /// </summary>
    [Table("WMS_Inventory")]
    [Index(nameof(MaterialId), nameof(BatchId), nameof(WarehouseId), nameof(LocationId), IsUnique = true)]
    public class Inventory : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        
        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public virtual Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [Display(Name = "库位")]
        [StringLength(50)]
        public string LocationId { get; set; }
        
        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public virtual Location Location { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }
        
        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public virtual Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string BatchId { get; set; }
        
        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public virtual Batch.Batch Batch { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        [Display(Name = "数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 分配数量
        /// </summary>
        [Display(Name = "分配数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal AllocatedQuantity { get; set; }

        /// <summary>
        /// 可用数量
        /// </summary>
        [Display(Name = "可用数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal AvailableQuantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [Display(Name = "单位")]
        [StringLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        [Required]
        [Display(Name = "库存状态")]
        public InventoryStatus Status { get; set; }

        /// <summary>
        /// 上次移动时间
        /// </summary>
        [Display(Name = "上次移动时间")]
        public DateTime? LastMovementTime { get; set; }

        /// <summary>
        /// 入库时间
        /// </summary>
        [Required]
        [Display(Name = "入库时间")]
        public DateTime InboundTime { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        [Display(Name = "生产日期")]
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 到期日期
        /// </summary>
        [Display(Name = "到期日期")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 成本
        /// </summary>
        [Display(Name = "成本")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Cost { get; set; }

        /// <summary>
        /// 所有者ID
        /// </summary>
        [Display(Name = "所有者")]
        [StringLength(50)]
        public string OwnerId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
}