using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// WMS系统通知类型
    /// </summary>
    public enum WMSNotificationType
    {
        /// <summary>
        /// 系统通知
        /// </summary>
        [Display(Name = "系统通知")]
        System = 0,

        /// <summary>
        /// 预警通知
        /// </summary>
        [Display(Name = "预警通知")]
        Alert = 1,

        /// <summary>
        /// 任务通知
        /// </summary>
        [Display(Name = "任务通知")]
        Task = 2,

        /// <summary>
        /// 消息通知
        /// </summary>
        [Display(Name = "消息通知")]
        Message = 3,

        /// <summary>
        /// 其他通知
        /// </summary>
        [Display(Name = "其他通知")]
        Other = 4
    }

    /// <summary>
    /// WMS系统通知优先级
    /// </summary>
    public enum WMSNotificationPriority
    {
        /// <summary>
        /// 低
        /// </summary>
        [Display(Name = "低")]
        Low = 0,

        /// <summary>
        /// 中
        /// </summary>
        [Display(Name = "中")]
        Medium = 1,

        /// <summary>
        /// 高
        /// </summary>
        [Display(Name = "高")]
        High = 2,

        /// <summary>
        /// 紧急
        /// </summary>
        [Display(Name = "紧急")]
        Urgent = 3
    }

    /// <summary>
    /// WMS系统通知状态
    /// </summary>
    public enum WMSNotificationStatus
    {
        /// <summary>
        /// 未读
        /// </summary>
        [Display(Name = "未读")]
        Unread = 0,

        /// <summary>
        /// 已读
        /// </summary>
        [Display(Name = "已读")]
        Read = 1,

        /// <summary>
        /// 已处理
        /// </summary>
        [Display(Name = "已处理")]
        Processed = 2,

        /// <summary>
        /// 已忽略
        /// </summary>
        [Display(Name = "已忽略")]
        Ignored = 3,

        /// <summary>
        /// 已过期
        /// </summary>
        [Display(Name = "已过期")]
        Expired = 4
    }
} 