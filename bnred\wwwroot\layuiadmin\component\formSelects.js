/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;layui.define(["form","jquery"],function(e){layui.form.config.verify=$.extend(layui.form.config.verify,{selectRequired:function(e,r){var a=r.attributes["wtm-name"].value,l=$(r).parents(".layui-form");if(0==l.find('input[name="'+a+'"]').length)return"必填项不能为空"}});const r={selects:{},on:function(e){if(!e||!e.layFilter)return void alert("请传入lay-filter");var a=e.layFilter;r.selects[a]=$.extend({},{layFilter:"",left:"【",right:"】",separator:"",$dom:null,arr:[]},e),layui.use(["form","jquery"],function(){var l=layui.form,t=layui.jquery,i=r.selects[a];i.$dom=t('select[lay-filter="'+i.layFilter+'"]').next(),i.$dom.find("dl").css("display","none"),r.refresh(i),r.show(i),l.on("select("+i.layFilter+")",function(a){var l=r.exchange(a);if(l){var t=!1;for(var n in i.arr)i.arr[n]&&i.arr[n].val==l.val&&(i.arr.splice(n,1),t=!0);t||i.arr.push(l)}r.refresh(i),r.show(i),i.$dom.find("dl").css("display","block"),e.selectFunc&&e.selectFunc(i.arr)}),t(document).on("click",'select[lay-filter="'+i.layFilter+'"] + div input',function(){r.show(i)}),t(document).on("click",'body:not(select[lay-filter="'+i.layFilter+'"] + div)',function(e){var r=t(e.target).parents(".layui-form-select").prev().attr("lay-filter")==i.layFilter,a="block"==i.$dom.find("dl").css("display");r?i.$dom.find("dl").css("display",a?"none":"block"):a&&i.$dom.find("dl").css("display","none")})})},show:function(e){e.$dom.find(".layui-this").removeClass("layui-this");var r="";for(var a in e.arr){var l=e.arr[a];l&&(r+=e.separator+e.left+l.name+e.right,e.$dom.find('dd[lay-value="'+l.val+'"]').addClass("layui-this"))}e.separator&&e.separator.length>0&&r.startsWith(e.separator)&&(r=r.substr(e.separator.length)),e.$dom.find(".layui-select-title input").val(r)},refresh:function(e){var r=$("select[lay-filter='"+e.layFilter+"']");r.length>0&&(r[0].name="");for(var a=$('input[name="'+e.layFilter+'"]'),l=0;l<a.length;l++)a[l].remove();for(var l=0;l<e.arr.length;l++)r.parent().append('<input name="'+e.layFilter+'" hidden value="'+e.arr[l].val+'"></input>')},exchange:function(e){if(e.value)return{name:$(e.elem).find("option[value="+e.value+"]").text(),val:e.value}}};e("formSelects",r)});