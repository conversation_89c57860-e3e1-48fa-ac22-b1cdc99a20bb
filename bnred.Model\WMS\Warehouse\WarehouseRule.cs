using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseRules")]
    public class WarehouseRule : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Required]
        [StringLength(50)]
        public string RuleCode { get; set; }

        [Required]
        [StringLength(100)]
        public string RuleName { get; set; }

        [Required]
        public RuleType Type { get; set; }

        [Required]
        public int Priority { get; set; }

        [Required]
        public bool IsActive { get; set; }

        [Required]
        [StringLength(1000)]
        public string RuleContent { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 