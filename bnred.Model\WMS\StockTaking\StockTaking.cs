using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums.Inventory;
using bnred.Model.WMS.Models;
using bnred.Model.WMS.StockTaking;
namespace bnred.Model.WMS.Models
{
    /// <summary>
    /// 盘点单
    /// </summary>
    [Table("WMS_StockTakings")]
    [Index(nameof(Code), IsUnique = true)]
    public class StockTaking : TopBasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 盘点单号
        /// </summary>
        [Required(ErrorMessage = "盘点单号不能为空")]
        [StringLength(50, ErrorMessage = "盘点单号长度不能超过50个字符")]
        public string Code { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        [Required(ErrorMessage = "盘点日期不能为空")]
        public DateTime StockTakingDate { get; set; }

        /// <summary>
        /// 盘点状态
        /// </summary>
        [Required(ErrorMessage = "盘点状态不能为空")]
        public StockTakingStatus Status { get; set; }

        /// <summary>
        /// 盘点人ID
        /// </summary>
        [Required(ErrorMessage = "盘点人不能为空")]
     
        public Guid? StockTakerId { get; set; }

 
        public FrameworkUser StockTaker { get; set; }

        /// <summary>
        /// 审核人ID
        /// </summary>
     
        public Guid? AuditorId { get; set; }
 
        public FrameworkUser Auditor { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Remark { get; set; }

        /// <summary>
        /// 盘点明细
        /// </summary>
        public List<bnred.Model.WMS.StockTaking.StockTakingDetail> Details { get; set; }
    }
}