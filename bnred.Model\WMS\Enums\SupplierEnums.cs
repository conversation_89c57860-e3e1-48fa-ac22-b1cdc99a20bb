using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 供应商类型枚举
    /// 用于标识不同的供应商类型
    /// </summary>
    public enum SupplierType
    {
        /// <summary>
        /// 生产厂商
        /// 直接生产产品的供应商
        /// </summary>
        [Display(Name = "生产厂商")]
        Manufacturer = 0,

        /// <summary>
        /// 代理商
        /// 代理其他厂商产品的供应商
        /// </summary>
        [Display(Name = "代理商")]
        Agent = 1,

        /// <summary>
        /// 贸易商
        /// 从事贸易业务的供应商
        /// </summary>
        [Display(Name = "贸易商")]
        Trader = 2,

        /// <summary>
        /// 服务商
        /// 提供服务的供应商
        /// </summary>
        [Display(Name = "服务商")]
        ServiceProvider = 3
    }

    /// <summary>
    /// 供应商等级枚举
    /// 用于标识供应商的评级等级
    /// </summary>
    public enum SupplierLevel
    {
        /// <summary>
        /// 战略供应商
        /// 长期合作的重要供应商
        /// </summary>
        [Display(Name = "战略供应商")]
        Strategic = 0,

        /// <summary>
        /// 核心供应商
        /// 主要合作的供应商
        /// </summary>
        [Display(Name = "核心供应商")]
        Core = 1,

        /// <summary>
        /// 普通供应商
        /// 一般合作的供应商
        /// </summary>
        [Display(Name = "普通供应商")]
        Regular = 2,

        /// <summary>
        /// 临时供应商
        /// 临时合作的供应商
        /// </summary>
        [Display(Name = "临时供应商")]
        Temporary = 3
    }

    /// <summary>
    /// 供应商状态枚举
    /// 用于标识供应商的合作状态
    /// </summary>
    public enum SupplierStatus
    {
        /// <summary>
        /// 正常合作
        /// 正常业务往来状态
        /// </summary>
        [Display(Name = "正常合作")]
        Active = 0,

        /// <summary>
        /// 合作暂停
        /// 暂时停止业务往来
        /// </summary>
        [Display(Name = "合作暂停")]
        Suspended = 1,

        /// <summary>
        /// 观察期
        /// 供应商在观察评估期
        /// </summary>
        [Display(Name = "观察期")]
        Probation = 2,

        /// <summary>
        /// 已终止
        /// 已终止合作关系
        /// </summary>
        [Display(Name = "已终止")]
        Terminated = 3,

        /// <summary>
        /// 黑名单
        /// 列入黑名单的供应商
        /// </summary>
        [Display(Name = "黑名单")]
        Blacklisted = 4
    }
} 