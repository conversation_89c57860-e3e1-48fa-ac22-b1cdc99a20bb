using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Transfer
{
    /// <summary>
    /// 移库单
    /// 用于记录仓库内或仓库间的物料移动
    /// </summary>
    [Table("WMS_TransferOrders")]
    public class TransferOrder : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 移库单号
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "移库单号")]
        [StringLength(50, ErrorMessage = "{0}最多输入{1}个字符")]
        public string TransferNo { get; set; }

        /// <summary>
        /// 移库类型
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "移库类型")]
        public TransferType Type { get; set; }

        /// <summary>
        /// 移库状态
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "移库状态")]
        public TransferStatus Status { get; set; }

        /// <summary>
        /// 源仓库ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "源仓库")]
        [StringLength(50)]
        public string SourceWarehouseId { get; set; }
        
        /// <summary>
        /// 源仓库
        /// </summary>
        [Display(Name = "源仓库")]
        public Warehouse.Warehouse SourceWarehouse { get; set; }

        /// <summary>
        /// 目标仓库ID
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "目标仓库")]
        [StringLength(50)]
        public string TargetWarehouseId { get; set; }
        
        /// <summary>
        /// 目标仓库
        /// </summary>
        [Display(Name = "目标仓库")]
        public Warehouse.Warehouse TargetWarehouse { get; set; }

        /// <summary>
        /// 计划日期
        /// </summary>
        [Required(ErrorMessage = "{0}是必填项")]
        [Display(Name = "计划日期")]
        public DateTime PlanDate { get; set; }

        /// <summary>
        /// 实际完成日期
        /// </summary>
        [Display(Name = "实际完成日期")]
        public DateTime? ActualDate { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [Display(Name = "创建人")]
      
        public Guid? CreatorId { get; set; }
        
 
        public FrameworkUser Creator { get; set; }

        /// <summary>
        /// 执行人ID
        /// </summary>
        [Display(Name = "执行人")]
     
        public Guid? ExecutorId { get; set; }
        
 
        public FrameworkUser Executor { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [Display(Name = "优先级")]
        public TaskPriority Priority { get; set; } = TaskPriority.Normal;

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500, ErrorMessage = "{0}最多输入{1}个字符")]
        public string Remark { get; set; }

        /// <summary>
        /// 明细列表
        /// </summary>
        [Display(Name = "明细列表")]
        public List<TransferOrderDetail> Details { get; set; }
    }
} 