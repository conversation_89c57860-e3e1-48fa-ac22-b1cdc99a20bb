﻿using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using WalkingTec.Mvvm.Core;
using bnred.Model;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Inventory;
using bnred.Model.WMS.Alert;
using bnred.Model.WMS.StockTaking;
using bnred.Model.WMS.Transaction;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Inbound;
using bnred.Model.WMS.Outbound;
using bnred.Model.WMS.Transfer;
using bnred.Model.WMS.Quality;
using bnred.Model.Material;
using bnred.Model.Supplier;
 
 
using bnred.Model.WMS.Models;
using System.Text.RegularExpressions;

namespace bnred.DataAccess
{
    public class DataContext : FrameworkContext
    {
        // 基础模型
        public DbSet<FrameworkUser> FrameworkUsers { get; set; }
 
        public DbSet<Language> Languages { get; set; }
        public DbSet<Localization> Localizations { get; set; }

        // 仓库管理相关模型
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<WarehouseArea> WarehouseAreas { get; set; }
        public DbSet<WarehouseAddress> WarehouseAddresses { get; set; }

        public DbSet<WarehouseBusinessHour> WarehouseBusinessHours { get; set; }
        public DbSet<WarehouseContact> WarehouseContacts { get; set; }
        public DbSet<WarehouseDocument> WarehouseDocuments { get; set; }
        public DbSet<WarehouseEquipment> WarehouseEquipments { get; set; }
        public DbSet<WarehouseEquipmentMaintenance> WarehouseEquipmentMaintenances { get; set; }
        public DbSet<WarehouseExtension> WarehouseExtensions { get; set; }
        public DbSet<WarehouseKPI> WarehouseKPIs { get; set; }
        public DbSet<WarehouseLocalization> WarehouseLocalizations { get; set; }
        public DbSet<WarehouseRule> WarehouseRules { get; set; }
        public DbSet<WarehouseSecurity> WarehouseSecurities { get; set; }
        public DbSet<WarehouseStatistics> WarehouseStatistics { get; set; }
        public DbSet<WarehouseStrategy> WarehouseStrategies { get; set; }
        public DbSet<WarehouseStrategyCondition> WarehouseStrategyConditions { get; set; }
        public DbSet<WarehouseStrategyParameter> WarehouseStrategyParameters { get; set; }
        public DbSet<WarehouseTask> WarehouseTasks { get; set; }
        public DbSet<WarehouseTaskDetail> WarehouseTaskDetails { get; set; }
        public DbSet<WarehouseTaskException> WarehouseTaskExceptions { get; set; }
        public DbSet<WarehouseTaskLog> WarehouseTaskLogs { get; set; }
        public DbSet<LocationExtension> LocationExtensions { get; set; }
        public DbSet<LocationLocalization> LocationLocalizations { get; set; }

        // 库存管理相关模型
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<InventoryAdjustment> InventoryAdjustments { get; set; }
        public DbSet<InventoryLedger> InventoryLedgers { get; set; }
        public DbSet<InventoryMovement> InventoryMovements { get; set; }
        public DbSet<InventorySnapshot> InventorySnapshots { get; set; }

        // 预警相关模型
        public DbSet<InventoryAlert> InventoryAlerts { get; set; }

        // 盘点相关模型
        public DbSet<Model.WMS.Models.StockTaking> StockTakings { get; set; }
        public DbSet<Model.WMS.StockTaking.StockTakingDetail> StockTakingDetails { get; set; }

        // 交易相关模型
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<StockTransaction> StockTransactions { get; set; }

        // 批次相关模型
        public DbSet<Batch> Batches { get; set; }

        // 物料相关模型
        public DbSet<Material> Materials { get; set; }
        public DbSet<MaterialBarcode> MaterialBarcodes { get; set; }
        public DbSet<MaterialCategory> MaterialCategories { get; set; }
        public DbSet<MaterialLocalization> MaterialLocalizations { get; set; }
        public DbSet<MaterialPrice> MaterialPrices { get; set; }

        // 供应商相关模型
        public DbSet<Supplier> Suppliers { get; set; }

        // 入库相关模型
        public DbSet<Inbound> Inbounds { get; set; }

        // 出库相关模型
        public DbSet<Outbound> Outbounds { get; set; }



        public DataContext(CS cs)
                : base(cs)
        {
        }

        public DataContext(string cs, DBTypeEnum dbtype)
            : base(cs, dbtype)
        {
        }

        public DataContext(string cs, DBTypeEnum dbtype, string version = null)
            : base(cs, dbtype, version)
        {
        }


        public DataContext(DbContextOptions<DataContext> options) : base(options) { }

        /// <summary>
        /// 配置实体关系
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置Transaction表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.Warehouse)
                .WithMany()
                .HasForeignKey(t => t.WarehouseID)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.FromWarehouse)
                .WithMany()
                .HasForeignKey(t => t.FromWarehouseID)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置StockTransaction表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<StockTransaction>()
                .HasOne(st => st.Warehouse)
                .WithMany()
                .HasForeignKey(st => st.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StockTransaction>()
                .HasOne(st => st.Location)
                .WithMany()
                .HasForeignKey(st => st.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置Inventory表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<Inventory>()
                .HasOne(i => i.Warehouse)
                .WithMany()
                .HasForeignKey(i => i.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Inventory>()
                .HasOne(i => i.Location)
                .WithMany()
                .HasForeignKey(i => i.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置InventoryAdjustment表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<InventoryAdjustment>()
                .HasOne(ia => ia.Warehouse)
                .WithMany()
                .HasForeignKey(ia => ia.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<InventoryAdjustment>()
                .HasOne(ia => ia.Location)
                .WithMany()
                .HasForeignKey(ia => ia.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置InventoryLedger表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<InventoryLedger>()
                .HasOne(il => il.Warehouse)
                .WithMany()
                .HasForeignKey(il => il.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<InventoryLedger>()
                .HasOne(il => il.Location)
                .WithMany()
                .HasForeignKey(il => il.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置InventoryMovement表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.SourceWarehouse)
                .WithMany()
                .HasForeignKey(im => im.SourceWarehouseId)
                .OnDelete(DeleteBehavior.NoAction);
                
            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.TargetWarehouse)
                .WithMany()
                .HasForeignKey(im => im.TargetWarehouseId)
                .OnDelete(DeleteBehavior.NoAction);
                
            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.SourceLocation)
                .WithMany()
                .HasForeignKey(im => im.SourceLocationId)
                .OnDelete(DeleteBehavior.NoAction);
                
            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.TargetLocation)
                .WithMany()
                .HasForeignKey(im => im.TargetLocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置InventorySnapshot表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<InventorySnapshot>()
                .HasOne(ins => ins.Warehouse)
                .WithMany()
                .HasForeignKey(ins => ins.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<InventorySnapshot>()
                .HasOne(ins => ins.Location)
                .WithMany()
                .HasForeignKey(ins => ins.LocationId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置Inbound表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<Inbound>()
                .HasOne(ib => ib.Warehouse)
                .WithMany()
                .HasForeignKey(ib => ib.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置Outbound表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<Outbound>()
                .HasOne(ob => ob.Warehouse)
                .WithMany()
                .HasForeignKey(ob => ob.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置WarehouseContact表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<WarehouseContact>()
                .HasOne(wc => wc.Warehouse)
                .WithMany()
                .HasForeignKey(wc => wc.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置WarehouseSecurity表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<WarehouseSecurity>()
                .HasOne(ws => ws.Warehouse)
                .WithMany()
                .HasForeignKey(ws => ws.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);

            // 配置WarehouseKPI表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<WarehouseKPI>()
                .HasOne(wk => wk.Warehouse)
                .WithMany()
                .HasForeignKey(wk => wk.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);
            
            // 配置WarehouseTask表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<WarehouseTask>()
                .HasOne(wt => wt.Warehouse)
                .WithMany()
                .HasForeignKey(wt => wt.WarehouseId)
                .OnDelete(DeleteBehavior.NoAction);
            
            // 配置WarehouseTaskDetail表的外键关系为NO ACTION，避免循环或多重级联路径
            modelBuilder.Entity<WarehouseTaskDetail>()
                .HasOne(wtd => wtd.Task)
                .WithMany()
                .HasForeignKey(wtd => wtd.TaskId)
                .OnDelete(DeleteBehavior.NoAction);
            
            modelBuilder.Entity<WarehouseTaskDetail>()
                .HasOne(wtd => wtd.FromLocation)
                .WithMany()
                .HasForeignKey(wtd => wtd.FromLocationId)
                .OnDelete(DeleteBehavior.NoAction);
            
            modelBuilder.Entity<WarehouseTaskDetail>()
                .HasOne(wtd => wtd.ToLocation)
                .WithMany()
                .HasForeignKey(wtd => wtd.ToLocationId)
                .OnDelete(DeleteBehavior.NoAction);
        }

        



        public override async Task<bool> DataInit(object allModules, bool IsSpa)
        {
            var state = await base.DataInit(allModules, IsSpa);
            bool emptydb = false;
            try
            {
                emptydb = Set<FrameworkUser>().Count() == 0 && Set<FrameworkUserRole>().Count() == 0;
            }
            catch { }
            if (state == true || emptydb == true)
            {
                //when state is true, means it's the first time EF create database, do data init here
                //当state是true的时候，表示这是第一次创建数据库，可以在这里进行数据初始化
                var user = new FrameworkUser
                {
                    ITCode = "admin",
                    Password = Utils.GetMD5String("000000"),
                    IsValid = true,
                    Name = "Admin"
                };

                var userrole = new FrameworkUserRole
                {
                    UserCode = user.ITCode,
                    RoleCode = "001"
                };

                var adminmenus = Set<FrameworkMenu>().Where(x => x.Url != null && x.Url.StartsWith("/api") == false).ToList();
                foreach (var item in adminmenus)
                {
                    item.Url = "/_admin" + item.Url;
                }

                Set<FrameworkUser>().Add(user);
                Set<FrameworkUserRole>().Add(userrole);
                await SaveChangesAsync();
            }
            return state;
        }

    }

    /// <summary>
    /// DesignTimeFactory for EF Migration, use your full connection string,
    /// EF will find this class and use the connection defined here to run Add-Migration and Update-Database
    /// </summary>
    public class DataContextFactory : IDesignTimeDbContextFactory<DataContext>
    {
        public DataContext CreateDbContext(string[] args)
        {
            return new DataContext("your full connection string", DBTypeEnum.SqlServer);
        }
    }

}
