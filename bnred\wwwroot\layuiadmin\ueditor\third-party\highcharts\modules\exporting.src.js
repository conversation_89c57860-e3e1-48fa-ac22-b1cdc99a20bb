/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function(t){var e,n,o=t.Chart,i=t.addEvent,r=t.removeEvent,a=t.createElement,s=t.discardElement,l=t.css,p=t.merge,c=t.each,h=t.extend,u=Math,d=u.max,m=document,g=window,x=t.isTouchDevice,y="M",f="L",b="div",v="hidden",w="none",S="highcharts-",k="absolute",E="px",C=t.Renderer.prototype.symbols,F=t.getOptions();h(F.lang,{printChart:"Print chart",downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"}),F.navigation={menuStyle:{border:"1px solid #A0A0A0",background:"#FFFFFF",padding:"5px 0"},menuItemStyle:{padding:"0 10px",background:w,color:"#303030",fontSize:x?"14px":"11px"},menuItemHoverStyle:{background:"#4572A5",color:"#FFFFFF"},buttonOptions:{symbolFill:"#E0E0E0",symbolSize:14,symbolStroke:"#666",symbolStrokeWidth:3,symbolX:12.5,symbolY:10.5,align:"right",buttonSpacing:3,height:22,theme:{fill:"white",stroke:"none"},verticalAlign:"top",width:24}},F.exporting={type:"image/png",url:"http://export.highcharts.com/",buttons:{contextButton:{menuClassName:S+"contextmenu",symbol:"menu",_titleKey:"contextButtonTitle",menuItems:[{textKey:"printChart",onclick:function(){this.print()}},{separator:!0},{textKey:"downloadPNG",onclick:function(){this.exportChart()}},{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}]}}},t.post=function(t,e){var n,o;o=a("form",{method:"post",action:t,enctype:"multipart/form-data"},{display:w},m.body);for(n in e)a("input",{type:v,name:n,value:e[n]},null,o);o.submit(),s(o)},h(o.prototype,{getSVG:function(n){var o,i,r,l,u,d,g,x,y=this,f=p(y.options,n);return m.createElementNS||(m.createElementNS=function(t,e){return m.createElement(e)}),i=a(b,null,{position:k,top:"-9999em",width:y.chartWidth+E,height:y.chartHeight+E},m.body),g=y.renderTo.style.width,x=y.renderTo.style.height,u=f.exporting.sourceWidth||f.chart.width||/px$/.test(g)&&parseInt(g,10)||600,d=f.exporting.sourceHeight||f.chart.height||/px$/.test(x)&&parseInt(x,10)||400,h(f.chart,{animation:!1,renderTo:i,forExport:!0,width:u,height:d}),f.exporting.enabled=!1,f.series=[],c(y.series,function(t){l=p(t.options,{animation:!1,showCheckbox:!1,visible:t.visible}),l.isInternal||f.series.push(l)}),o=new t.Chart(f,y.callback),c(["xAxis","yAxis"],function(t){c(y[t],function(n,i){var r=o[t][i],a=n.getExtremes(),s=a.userMin,l=a.userMax;!r||s===e&&l===e||r.setExtremes(s,l,!0,!1)})}),r=o.container.innerHTML,f=null,o.destroy(),s(i),r=r.replace(/zIndex="[^"]+"/g,"").replace(/isShadow="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery[0-9]+="[^"]+"/g,"").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ href=/g," xlink:href=").replace(/\n/," ").replace(/<\/svg>.*?$/,"</svg>").replace(/&nbsp;/g," ").replace(/&shy;/g,"­").replace(/<IMG /g,"<image ").replace(/height=([^" ]+)/g,'height="$1"').replace(/width=([^" ]+)/g,'width="$1"').replace(/hc-svg-href="([^"]+)">/g,'xlink:href="$1"/>').replace(/id=([^" >]+)/g,'id="$1"').replace(/class=([^" >]+)/g,'class="$1"').replace(/ transform /g," ").replace(/:(path|rect)/g,"$1").replace(/style="([^"]+)"/g,function(t){return t.toLowerCase()}),r=r.replace(/(url\(#highcharts-[0-9]+)&quot;/g,"$1").replace(/&quot;/g,"'")},exportChart:function(e,n){e=e||{};var o=this,i=o.options.exporting,r=o.getSVG(p({chart:{borderRadius:0}},i.chartOptions,n,{exporting:{sourceWidth:e.sourceWidth||i.sourceWidth,sourceHeight:e.sourceHeight||i.sourceHeight}}));e=p(o.options.exporting,e),t.post(e.url,{filename:e.filename||"chart",type:e.type,width:e.width||0,scale:e.scale||2,svg:r})},print:function(){var t=this,e=t.container,n=[],o=e.parentNode,i=m.body,r=i.childNodes;t.isPrinting||(t.isPrinting=!0,c(r,function(t,e){1===t.nodeType&&(n[e]=t.style.display,t.style.display=w)}),i.appendChild(e),g.focus(),g.print(),setTimeout(function(){o.appendChild(e),c(r,function(t,e){1===t.nodeType&&(t.style.display=n[e])}),t.isPrinting=!1},1e3))},contextMenu:function(t,e,n,o,r,s,p){var u,m,g,x,y=this,f=y.options.navigation,v=f.menuItemStyle,S=y.chartWidth,C=y.chartHeight,F="cache-"+t,G=y[F],M=d(r,s),D="3px 3px 10px #888";G||(y[F]=G=a(b,{className:t},{position:k,zIndex:1e3,padding:M+E},y.container),u=a(b,null,h({MozBoxShadow:D,WebkitBoxShadow:D,boxShadow:D},f.menuStyle),G),m=function(){l(G,{display:w}),p&&p.setState(0),y.openMenu=!1},i(G,"mouseleave",function(){g=setTimeout(m,500)}),i(G,"mouseenter",function(){clearTimeout(g)}),i(document,"mousedown",function(e){y.pointer.inClass(e.target,t)||m()}),c(e,function(t){if(t){var e=t.separator?a("hr",null,null,u):a(b,{onmouseover:function(){l(this,f.menuItemHoverStyle)},onmouseout:function(){l(this,v)},onclick:function(){m(),t.onclick.apply(y,arguments)},innerHTML:t.text||y.options.lang[t.textKey]},h({cursor:"pointer"},v),u);y.exportDivElements.push(e)}}),y.exportDivElements.push(u,G),y.exportMenuWidth=G.offsetWidth,y.exportMenuHeight=G.offsetHeight),x={display:"block"},n+y.exportMenuWidth>S?x.right=S-n-r-M+E:x.left=n-M+E,o+s+y.exportMenuHeight>C&&"top"!==p.alignOptions.verticalAlign?x.bottom=C-o-M+E:x.top=o+s-M+E,l(G,x),y.openMenu=!0},addButton:function(e){var o,i,r=this,a=r.renderer,s=p(r.options.navigation.buttonOptions,e),l=s.onclick,c=s.menuItems,u={stroke:s.symbolStroke,fill:s.symbolFill},d=s.symbolSize||12;if(r.btnCount||(r.btnCount=0),r.exportDivElements||(r.exportDivElements=[],r.exportSVGElements=[]),s.enabled!==!1){var m,g=s.theme,x=g.states,y=x&&x.hover,f=x&&x.select;delete g.states,l?m=function(){l.apply(r,arguments)}:c&&(m=function(){r.contextMenu(i.menuClassName,c,i.translateX,i.translateY,i.width,i.height,i),i.setState(2)}),s.text&&s.symbol?g.paddingLeft=t.pick(g.paddingLeft,25):s.text||h(g,{width:s.width,height:s.height,padding:0}),i=a.button(s.text,0,0,m,g,y,f).attr({title:r.options.lang[s._titleKey],"stroke-linecap":"round"}),i.menuClassName=e.menuClassName||S+"menu-"+r.btnCount++,s.symbol&&(o=a.symbol(s.symbol,s.symbolX-d/2,s.symbolY-d/2,d,d).attr(h(u,{"stroke-width":s.symbolStrokeWidth||1,zIndex:1})).add(i)),i.add().align(h(s,{width:i.width,x:t.pick(s.x,n)}),!0,"spacingBox"),n+=(i.width+s.buttonSpacing)*("right"===s.align?-1:1),r.exportSVGElements.push(i,o)}},destroyExport:function(t){var e,n,o=t.target;for(e=0;e<o.exportSVGElements.length;e++)n=o.exportSVGElements[e],n&&(n.onclick=n.ontouchstart=null,o.exportSVGElements[e]=n.destroy());for(e=0;e<o.exportDivElements.length;e++)n=o.exportDivElements[e],r(n,"mouseleave"),o.exportDivElements[e]=n.onmouseout=n.onmouseover=n.ontouchstart=n.onclick=null,s(n)}}),C.menu=function(t,e,n,o){var i=[y,t,e+2.5,f,t+n,e+2.5,y,t,e+o/2+.5,f,t+n,e+o/2+.5,y,t,e+o-1.5,f,t+n,e+o-1.5];return i},o.prototype.callbacks.push(function(t){var e,o=t.options.exporting,r=o.buttons;if(n=0,o.enabled!==!1){for(e in r)t.addButton(r[e]);i(t,"destroy",t.destroyExport)}})}(Highcharts);