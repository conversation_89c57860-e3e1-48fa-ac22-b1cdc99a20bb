-- #############################################################################
-- # Enum Dictionary Table Definition
-- #############################################################################

-- Drop table if it exists (optional, for re-running the script)
IF OBJECT_ID('dbo.WMS_EnumDictionary', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_EnumDictionary;
GO

CREATE TABLE WMS_EnumDictionary (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    EnumType NVARCHAR(500) NOT NULL,    -- C# Enum Type Full Name (e.g., "bnred.Model.WMS.Enums.MaterialStatus")
    EnumName NVARCHAR(255) NOT NULL,    -- C# Enum Field Name (e.g., "Active", "PurchaseOrder")
    EnumValue INT NOT NULL,             -- Integer value of the enum
    Description NVARCHAR(1000) NULL,    -- Description from [Display(Name="...")] or /// summary
    CultureName NVARCHAR(10) NULL,      -- Optional: For localized descriptions if needed (e.g., "en-US", "zh-CN")
    Remarks NVARCHAR(MAX) NULL          -- Additional remarks or context
);
GO

-- Add MS_Description for WMS_EnumDictionary table and its columns
EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', @value=N'存储系统中所有C#枚举类型的定义、值和描述，方便查询和维护。', 
    @level0type=N'SCHEMA',@level0name=N'dbo', 
    @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键ID', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'C#中枚举类型的完整名称 (例如: bnred.Model.WMS.Enums.MaterialStatus)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumType';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'C#中枚举的成员名称 (例如: Active, Disabled)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'枚举成员对应的整数值', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'EnumValue';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'枚举成员的描述，通常来自[Display(Name="...")]特性或XML注释', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'可选的区域性名称，用于存储多语言描述 (例如: en-US, zh-CN)', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'CultureName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'其他备注信息', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_EnumDictionary', @level2type=N'COLUMN',@level2name=N'Remarks';
GO

CREATE UNIQUE INDEX UX_EnumDictionary_Type_Name_Value ON WMS_EnumDictionary (EnumType, EnumName, EnumValue);
CREATE INDEX IX_EnumDictionary_Type_Value ON WMS_EnumDictionary (EnumType, EnumValue);
GO

PRINT 'WMS_EnumDictionary table definition created successfully in WMS_EnumDictionary.sql';
GO 