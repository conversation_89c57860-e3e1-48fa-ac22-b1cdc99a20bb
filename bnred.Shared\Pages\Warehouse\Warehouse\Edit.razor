﻿@page "/Warehouse/Warehouse/Edit/{id}"
@using bnred.ViewModel.Warehouse.WarehouseVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <BootstrapInput @bind-Value="@Model.Entity.ID"  />
            <BootstrapInput @bind-Value="@Model.Entity.Code"  />
            <BootstrapInput @bind-Value="@Model.Entity.Name"  />
            <Select @bind-Value="@Model.Entity.Type"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <Select @bind-Value="@Model.Entity.Status"  PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Address"  />
            <BootstrapInput @bind-Value="@Model.Entity.ContactPerson"  />
            <BootstrapInput @bind-Value="@Model.Entity.ContactPhone"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Area"  />
            <BootstrapInputNumber @bind-Value="@Model.Entity.Volume"  />
            <Select @bind-Value="@Model.Entity.ManagerID" Items="@AllFrameworkUsers" PlaceHolder="@WtmBlazor.Localizer["Sys.PleaseSelect"]"/>
            <BootstrapInput @bind-Value="@Model.Entity.Description"  />
            <BootstrapInput @bind-Value="@Model.Entity.Remark"  />
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {

    private WarehouseVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllFrameworkUsers = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllFrameworkUsers = await WtmBlazor.Api.CallItemsApi("/api/Warehouse/GetFrameworkUsers", placeholder: WtmBlazor.Localizer["Sys.PleaseSelect"]);

        var rv = await WtmBlazor.Api.CallAPI<WarehouseVM>($"/api/Warehouse/{id}");
        Model = rv.Data;
         await base.OnInitializedAsync();
   }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/Warehouse/edit", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
