/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function t(e,r,i){function n(a,s){if(!r[a]){if(!e[a]){var l="function"==typeof require&&require;if(!s&&l)return l(a,!0);if(o)return o(a,!0);var c=new Error("Cannot find module '"+a+"'");throw c.code="MODULE_NOT_FOUND",c}var u=r[a]={exports:{}};e[a][0].call(u.exports,function(t){var r=e[a][1][t];return n(r?r:t)},u,u.exports,t,e,r,i)}return r[a].exports}for(var o="function"==typeof require&&require,a=0;a<i.length;a++)n(i[a]);return n}({1:[function(t,e,r){function i(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","loop","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height"],ins:["datetime"],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],sup:[],strong:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","loop","preload","src","height","width"]}}function n(t,e,r){}function o(t,e,r){}function a(t,e,r){}function s(t,e,r){}function l(t){return t.replace(T,"&lt;").replace(I,"&gt;")}function c(t,e,r,i){if(i=i||A,r=m(r),"href"===e||"src"===e){if(r=k.trim(r),"#"===r)return"#";if("http://"!==r.substr(0,7)&&"https://"!==r.substr(0,8)&&"mailto:"!==r.substr(0,7)&&"#"!==r[0]&&"/"!==r[0])return""}else if("background"===e){if(E.lastIndex=0,E.test(r))return""}else if("style"===e){if(j.lastIndex=0,j.test(r))return"";if(q.lastIndex=0,q.test(r)&&(E.lastIndex=0,E.test(r)))return"";r=i.process(r)}return r=h(r)}function u(t){return t.replace(S,"&quot;")}function f(t){return t.replace(C,'"')}function g(t){return t.replace(O,function(t,e){return"x"===e[0]||"X"===e[0]?String.fromCharCode(parseInt(e.substr(1),16)):String.fromCharCode(parseInt(e,10))})}function p(t){return t.replace(L,":").replace(z," ")}function d(t){for(var e="",r=0,i=t.length;r<i;r++)e+=t.charCodeAt(r)<32?" ":t.charAt(r);return k.trim(e)}function m(t){return t=f(t),t=g(t),t=p(t),t=d(t)}function h(t){return t=u(t),t=l(t)}function b(){return""}function v(t,e){function r(e){return!!i||k.indexOf(t,e)!==-1}"function"!=typeof e&&(e=function(){});var i=!Array.isArray(t),n=[],o=!1;return{onIgnoreTag:function(t,i,a){if(r(t)){if(a.isClosing){var s="[/removed]",l=a.position+s.length;return n.push([o!==!1?o:a.position,l]),o=!1,s}return o||(o=a.position),"[removed]"}return e(t,i,a)},remove:function(t){var e="",r=0;return k.forEach(n,function(i){e+=t.slice(r,i[0]),r=i[1]}),e+=t.slice(r)}}}function w(t){return t.replace(F,"")}function x(t){var e=t.split("");return e=e.filter(function(t){var e=t.charCodeAt(0);return 127!==e&&(!(e<=31)||(10===e||13===e))}),e.join("")}var y=t("cssfilter").FilterCSS,k=t("./util"),A=new y,T=/</g,I=/>/g,S=/"/g,C=/&quot;/g,O=/&#([a-zA-Z0-9]*);?/gim,L=/&colon;?/gim,z=/&newline;?/gim,E=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a)\:/gi,j=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,q=/u\s*r\s*l\s*\(.*/gi,F=/<!--[\s\S]*?-->/g;r.whiteList=i(),r.getDefaultWhiteList=i,r.onTag=n,r.onIgnoreTag=o,r.onTagAttr=a,r.onIgnoreTagAttr=s,r.safeAttrValue=c,r.escapeHtml=l,r.escapeQuote=u,r.unescapeQuote=f,r.escapeHtmlEntities=g,r.escapeDangerHtml5Entities=p,r.clearNonPrintableCharacter=d,r.friendlyAttrValue=m,r.escapeAttrValue=h,r.onIgnoreTagStripAll=b,r.StripTagBody=v,r.stripCommentTag=w,r.stripBlankChar=x,r.cssFilter=A},{"./util":4,cssfilter:8}],2:[function(t,e,r){function i(t,e){var r=new a(e);return r.process(t)}var n=t("./default"),o=t("./parser"),a=t("./xss");r=e.exports=i,r.FilterXSS=a;for(var s in n)r[s]=n[s];for(var s in o)r[s]=o[s];"undefined"!=typeof window&&(window.filterXSS=e.exports)},{"./default":1,"./parser":3,"./xss":5}],3:[function(t,e,r){function i(t){var e=t.indexOf(" ");if(e===-1)var r=t.slice(1,-1);else var r=t.slice(1,e+1);return r=f.trim(r).toLowerCase(),"/"===r.slice(0,1)&&(r=r.slice(1)),"/"===r.slice(-1)&&(r=r.slice(0,-1)),r}function n(t){return"</"===t.slice(0,2)}function o(t,e,r){"user strict";var o="",a=0,s=!1,l=!1,c=0,u=t.length,f="",g="";for(c=0;c<u;c++){var p=t.charAt(c);if(s===!1){if("<"===p){s=c;continue}}else if(l===!1){if("<"===p){o+=r(t.slice(a,c)),s=c,a=c;continue}if(">"===p){o+=r(t.slice(a,s)),f=t.slice(s,c+1),g=i(f),o+=e(s,o.length,g,f,n(f)),a=c+1,s=!1;continue}if(('"'===p||"'"===p)&&"="===t.charAt(c-1)){l=p;continue}}else if(p===l){l=!1;continue}}return a<t.length&&(o+=r(t.substr(a))),o}function a(t,e){"user strict";function r(t,r){if(t=f.trim(t),t=t.replace(g,"").toLowerCase(),!(t.length<1)){var i=e(t,r||"");i&&n.push(i)}}for(var i=0,n=[],o=!1,a=t.length,c=0;c<a;c++){var p,d,m=t.charAt(c);if(o!==!1||"="!==m)if(o===!1||c!==i||'"'!==m&&"'"!==m||"="!==t.charAt(c-1))if(" "!==m);else{if(o===!1){if(d=s(t,c),d===-1){p=f.trim(t.slice(i,c)),r(p),o=!1,i=c+1;continue}c=d-1;continue}if(d=l(t,c-1),d===-1){p=f.trim(t.slice(i,c)),p=u(p),r(o,p),o=!1,i=c+1;continue}}else{if(d=t.indexOf(m,c+1),d===-1)break;p=f.trim(t.slice(i+1,d)),r(o,p),o=!1,c=d,i=c+1}else o=t.slice(i,c),i=c+1}return i<t.length&&(o===!1?r(t.slice(i)):r(o,u(f.trim(t.slice(i))))),f.trim(n.join(" "))}function s(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r)return"="===r?e:-1}}function l(t,e){for(;e>0;e--){var r=t[e];if(" "!==r)return"="===r?e:-1}}function c(t){return'"'===t[0]&&'"'===t[t.length-1]||"'"===t[0]&&"'"===t[t.length-1]}function u(t){return c(t)?t.substr(1,t.length-2):t}var f=t("./util"),g=/[^a-zA-Z0-9_:\.\-]/gim;r.parseTag=o,r.parseAttr=a},{"./util":4}],4:[function(t,e,r){e.exports={indexOf:function(t,e){var r,i;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1},forEach:function(t,e,r){var i,n;if(Array.prototype.forEach)return t.forEach(e,r);for(i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")}}},{}],5:[function(t,e,r){function i(t){return void 0===t||null===t}function n(t){var e=t.indexOf(" ");if(e===-1)return{html:"",closing:"/"===t[t.length-2]};t=f.trim(t.slice(e+1,-1));var r="/"===t[t.length-1];return r&&(t=f.trim(t.slice(0,-1))),{html:t,closing:r}}function o(t){t=t||{},t.stripIgnoreTag&&(t.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),t.onIgnoreTag=s.onIgnoreTagStripAll),t.whiteList=t.whiteList||s.whiteList,t.onTag=t.onTag||s.onTag,t.onTagAttr=t.onTagAttr||s.onTagAttr,t.onIgnoreTag=t.onIgnoreTag||s.onIgnoreTag,t.onIgnoreTagAttr=t.onIgnoreTagAttr||s.onIgnoreTagAttr,t.safeAttrValue=t.safeAttrValue||s.safeAttrValue,t.escapeHtml=t.escapeHtml||s.escapeHtml,t.css=t.css||{},this.options=t,this.cssFilter=new a(t.css)}var a=t("cssfilter").FilterCSS,s=t("./default"),l=t("./parser"),c=l.parseTag,u=l.parseAttr,f=t("./util");o.prototype.process=function(t){if(t=t||"",t=t.toString(),!t)return"";var e=this,r=e.options,o=r.whiteList,a=r.onTag,l=r.onIgnoreTag,g=r.onTagAttr,p=r.onIgnoreTagAttr,d=r.safeAttrValue,m=r.escapeHtml,h=e.cssFilter;r.stripBlankChar&&(t=s.stripBlankChar(t)),r.allowCommentTag||(t=s.stripCommentTag(t));var b=!1;if(r.stripIgnoreTagBody){var b=s.StripTagBody(r.stripIgnoreTagBody,l);l=b.onIgnoreTag}var v=c(t,function(t,e,r,s,c){var b={sourcePosition:t,position:e,isClosing:c,isWhite:r in o},v=a(r,s,b);if(!i(v))return v;if(b.isWhite){if(b.isClosing)return"</"+r+">";var w=n(s),x=o[r],y=u(w.html,function(t,e){var n=f.indexOf(x,t)!==-1,o=g(r,t,e,n);if(!i(o))return o;if(n)return e=d(r,t,e,h),e?t+'="'+e+'"':t;var o=p(r,t,e,n);return i(o)?void 0:o}),s="<"+r;return y&&(s+=" "+y),w.closing&&(s+=" /"),s+=">"}var v=l(r,s,b);return i(v)?m(s):v},m);return b&&(v=b.remove(v)),v},e.exports=o},{"./default":1,"./parser":3,"./util":4,cssfilter:8}],6:[function(t,e,r){function i(t){return void 0===t||null===t}function n(t){t=t||{},t.whiteList=t.whiteList||o.whiteList,t.onAttr=t.onAttr||o.onAttr,t.onIgnoreAttr=t.onIgnoreAttr||o.onIgnoreAttr,this.options=t}var o=t("./default"),a=t("./parser");t("./util");n.prototype.process=function(t){if(t=t||"",t=t.toString(),!t)return"";var e=this,r=e.options,n=r.whiteList,o=r.onAttr,s=r.onIgnoreAttr,l=a(t,function(t,e,r,a,l){var c=n[r],u=!1;c===!0?u=c:"function"==typeof c?u=c(a):c instanceof RegExp&&(u=c.test(a)),u!==!0&&(u=!1);var f={position:e,sourcePosition:t,source:l,isWhite:u};if(u){var g=o(r,a,f);return i(g)?r+":"+a:g}var g=s(r,a,f);if(!i(g))return g});return l},e.exports=n},{"./default":7,"./parser":9,"./util":10}],7:[function(t,e,r){function i(){var t={};return t["align-content"]=!1,t["align-items"]=!1,t["align-self"]=!1,t["alignment-adjust"]=!1,t["alignment-baseline"]=!1,t.all=!1,t["anchor-point"]=!1,t.animation=!1,t["animation-delay"]=!1,t["animation-direction"]=!1,t["animation-duration"]=!1,t["animation-fill-mode"]=!1,t["animation-iteration-count"]=!1,t["animation-name"]=!1,t["animation-play-state"]=!1,t["animation-timing-function"]=!1,t.azimuth=!1,t["backface-visibility"]=!1,t.background=!0,t["background-attachment"]=!0,t["background-clip"]=!0,t["background-color"]=!0,t["background-image"]=!0,t["background-origin"]=!0,t["background-position"]=!0,t["background-repeat"]=!0,t["background-size"]=!0,t["baseline-shift"]=!1,t.binding=!1,t.bleed=!1,t["bookmark-label"]=!1,t["bookmark-level"]=!1,t["bookmark-state"]=!1,t.border=!0,t["border-bottom"]=!0,t["border-bottom-color"]=!0,t["border-bottom-left-radius"]=!0,t["border-bottom-right-radius"]=!0,t["border-bottom-style"]=!0,t["border-bottom-width"]=!0,t["border-collapse"]=!0,t["border-color"]=!0,t["border-image"]=!0,t["border-image-outset"]=!0,t["border-image-repeat"]=!0,t["border-image-slice"]=!0,t["border-image-source"]=!0,t["border-image-width"]=!0,t["border-left"]=!0,t["border-left-color"]=!0,t["border-left-style"]=!0,t["border-left-width"]=!0,t["border-radius"]=!0,t["border-right"]=!0,t["border-right-color"]=!0,t["border-right-style"]=!0,t["border-right-width"]=!0,t["border-spacing"]=!0,t["border-style"]=!0,t["border-top"]=!0,t["border-top-color"]=!0,t["border-top-left-radius"]=!0,t["border-top-right-radius"]=!0,t["border-top-style"]=!0,t["border-top-width"]=!0,t["border-width"]=!0,t.bottom=!1,t["box-decoration-break"]=!0,t["box-shadow"]=!0,t["box-sizing"]=!0,t["box-snap"]=!0,t["box-suppress"]=!0,t["break-after"]=!0,t["break-before"]=!0,t["break-inside"]=!0,t["caption-side"]=!1,t.chains=!1,t.clear=!0,t.clip=!1,t["clip-path"]=!1,t["clip-rule"]=!1,t.color=!0,t["color-interpolation-filters"]=!0,t["column-count"]=!1,t["column-fill"]=!1,t["column-gap"]=!1,t["column-rule"]=!1,t["column-rule-color"]=!1,t["column-rule-style"]=!1,t["column-rule-width"]=!1,t["column-span"]=!1,t["column-width"]=!1,t.columns=!1,t.contain=!1,t.content=!1,t["counter-increment"]=!1,t["counter-reset"]=!1,t["counter-set"]=!1,t.crop=!1,t.cue=!1,t["cue-after"]=!1,t["cue-before"]=!1,t.cursor=!1,t.direction=!1,t.display=!0,t["display-inside"]=!0,t["display-list"]=!0,t["display-outside"]=!0,t["dominant-baseline"]=!1,t.elevation=!1,t["empty-cells"]=!1,t.filter=!1,t.flex=!1,t["flex-basis"]=!1,t["flex-direction"]=!1,t["flex-flow"]=!1,t["flex-grow"]=!1,t["flex-shrink"]=!1,t["flex-wrap"]=!1,t["float"]=!1,t["float-offset"]=!1,t["flood-color"]=!1,t["flood-opacity"]=!1,t["flow-from"]=!1,t["flow-into"]=!1,t.font=!0,t["font-family"]=!0,t["font-feature-settings"]=!0,t["font-kerning"]=!0,t["font-language-override"]=!0,t["font-size"]=!0,t["font-size-adjust"]=!0,t["font-stretch"]=!0,t["font-style"]=!0,t["font-synthesis"]=!0,t["font-variant"]=!0,t["font-variant-alternates"]=!0,t["font-variant-caps"]=!0,t["font-variant-east-asian"]=!0,t["font-variant-ligatures"]=!0,t["font-variant-numeric"]=!0,t["font-variant-position"]=!0,t["font-weight"]=!0,t.grid=!1,t["grid-area"]=!1,t["grid-auto-columns"]=!1,t["grid-auto-flow"]=!1,t["grid-auto-rows"]=!1,t["grid-column"]=!1,t["grid-column-end"]=!1,t["grid-column-start"]=!1,t["grid-row"]=!1,t["grid-row-end"]=!1,t["grid-row-start"]=!1,t["grid-template"]=!1,t["grid-template-areas"]=!1,t["grid-template-columns"]=!1,t["grid-template-rows"]=!1,t["hanging-punctuation"]=!1,t.height=!0,t.hyphens=!1,t.icon=!1,t["image-orientation"]=!1,t["image-resolution"]=!1,t["ime-mode"]=!1,t["initial-letters"]=!1,t["inline-box-align"]=!1,t["justify-content"]=!1,t["justify-items"]=!1,t["justify-self"]=!1,t.left=!1,t["letter-spacing"]=!0,t["lighting-color"]=!0,t["line-box-contain"]=!1,t["line-break"]=!1,t["line-grid"]=!1,t["line-height"]=!1,t["line-snap"]=!1,t["line-stacking"]=!1,t["line-stacking-ruby"]=!1,t["line-stacking-shift"]=!1,t["line-stacking-strategy"]=!1,t["list-style"]=!0,t["list-style-image"]=!0,t["list-style-position"]=!0,t["list-style-type"]=!0,t.margin=!0,t["margin-bottom"]=!0,t["margin-left"]=!0,t["margin-right"]=!0,t["margin-top"]=!0,t["marker-offset"]=!1,t["marker-side"]=!1,t.marks=!1,t.mask=!1,t["mask-box"]=!1,t["mask-box-outset"]=!1,t["mask-box-repeat"]=!1,t["mask-box-slice"]=!1,t["mask-box-source"]=!1,t["mask-box-width"]=!1,t["mask-clip"]=!1,t["mask-image"]=!1,t["mask-origin"]=!1,t["mask-position"]=!1,t["mask-repeat"]=!1,t["mask-size"]=!1,t["mask-source-type"]=!1,t["mask-type"]=!1,t["max-height"]=!0,t["max-lines"]=!1,t["max-width"]=!0,t["min-height"]=!0,t["min-width"]=!0,t["move-to"]=!1,t["nav-down"]=!1,t["nav-index"]=!1,t["nav-left"]=!1,t["nav-right"]=!1,t["nav-up"]=!1,t["object-fit"]=!1,t["object-position"]=!1,t.opacity=!1,t.order=!1,t.orphans=!1,t.outline=!1,t["outline-color"]=!1,t["outline-offset"]=!1,t["outline-style"]=!1,t["outline-width"]=!1,t.overflow=!1,t["overflow-wrap"]=!1,t["overflow-x"]=!1,t["overflow-y"]=!1,t.padding=!0,t["padding-bottom"]=!0,t["padding-left"]=!0,t["padding-right"]=!0,t["padding-top"]=!0,t.page=!1,t["page-break-after"]=!1,t["page-break-before"]=!1,t["page-break-inside"]=!1,t["page-policy"]=!1,t.pause=!1,t["pause-after"]=!1,t["pause-before"]=!1,t.perspective=!1,t["perspective-origin"]=!1,t.pitch=!1,t["pitch-range"]=!1,t["play-during"]=!1,t.position=!1,t["presentation-level"]=!1,t.quotes=!1,t["region-fragment"]=!1,t.resize=!1,t.rest=!1,t["rest-after"]=!1,t["rest-before"]=!1,t.richness=!1,t.right=!1,t.rotation=!1,t["rotation-point"]=!1,t["ruby-align"]=!1,t["ruby-merge"]=!1,t["ruby-position"]=!1,t["shape-image-threshold"]=!1,t["shape-outside"]=!1,t["shape-margin"]=!1,t.size=!1,t.speak=!1,t["speak-as"]=!1,t["speak-header"]=!1,t["speak-numeral"]=!1,t["speak-punctuation"]=!1,t["speech-rate"]=!1,t.stress=!1,t["string-set"]=!1,t["tab-size"]=!1,t["table-layout"]=!1,t["text-align"]=!0,t["text-align-last"]=!0,t["text-combine-upright"]=!0,t["text-decoration"]=!0,t["text-decoration-color"]=!0,t["text-decoration-line"]=!0,t["text-decoration-skip"]=!0,t["text-decoration-style"]=!0,t["text-emphasis"]=!0,t["text-emphasis-color"]=!0,t["text-emphasis-position"]=!0,t["text-emphasis-style"]=!0,t["text-height"]=!0,t["text-indent"]=!0,t["text-justify"]=!0,t["text-orientation"]=!0,t["text-overflow"]=!0,t["text-shadow"]=!0,t["text-space-collapse"]=!0,t["text-transform"]=!0,t["text-underline-position"]=!0,t["text-wrap"]=!0,t.top=!1,t.transform=!1,t["transform-origin"]=!1,t["transform-style"]=!1,t.transition=!1,t["transition-delay"]=!1,t["transition-duration"]=!1,t["transition-property"]=!1,t["transition-timing-function"]=!1,t["unicode-bidi"]=!1,t["vertical-align"]=!1,t.visibility=!1,t["voice-balance"]=!1,t["voice-duration"]=!1,t["voice-family"]=!1,t["voice-pitch"]=!1,t["voice-range"]=!1,t["voice-rate"]=!1,t["voice-stress"]=!1,t["voice-volume"]=!1,t.volume=!1,t["white-space"]=!1,t.widows=!1,t.width=!0,t["will-change"]=!1,t["word-break"]=!0,t["word-spacing"]=!0,t["word-wrap"]=!0,t["wrap-flow"]=!1,t["wrap-through"]=!1,t["writing-mode"]=!1,t["z-index"]=!1,t}function n(t,e,r){}function o(t,e,r){}r.whiteList=i(),r.getDefaultWhiteList=i,r.onAttr=n,r.onIgnoreAttr=o},{}],8:[function(t,e,r){function i(t,e){var r=new o(e);return r.process(t)}var n=t("./default"),o=t("./css");r=e.exports=i,r.FilterCSS=o;for(var a in n)r[a]=n[a];"undefined"!=typeof window&&(window.filterCSS=e.exports)},{"./css":6,"./default":7}],9:[function(t,e,r){function i(t,e){function r(){if(!o){var r=n.trim(t.slice(a,s)),i=r.indexOf(":");if(i!==-1){var c=n.trim(r.slice(0,i)),u=n.trim(r.slice(i+1));if(c){var f=e(a,l.length,c,u,r);f&&(l+=f+"; ")}}}a=s+1}t=n.trimRight(t),";"!==t[t.length-1]&&(t+=";");for(var i=t.length,o=!1,a=0,s=0,l="";s<i;s++){var c=t[s];if("/"===c&&"*"===t[s+1]){var u=t.indexOf("*/",s+2);if(u===-1)break;s=u+1,a=s+1,o=!1}else"("===c?o=!0:")"===c?o=!1:";"===c?o||r():"\n"===c&&r()}return n.trim(l)}var n=t("./util");e.exports=i},{"./util":10}],10:[function(t,e,r){e.exports={indexOf:function(t,e){var r,i;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1},forEach:function(t,e,r){var i,n;if(Array.prototype.forEach)return t.forEach(e,r);for(i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(t){return String.prototype.trimRight?t.trimRight():t.replace(/(\s*$)/g,"")}}},{}]},{},[2]);