using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Material
{
    [Table("WMS_MaterialBarcodes")]
    public class MaterialBarcode : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }
        public virtual bnred.Model.Material.Material Material { get; set; }

        [Required]
        [StringLength(50)]
        public string Barcode { get; set; }

        [Required]
        public BarcodeType Type { get; set; }

        [Required]
        public bool IsDefault { get; set; }

        [Required]
        public bool IsActive { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 