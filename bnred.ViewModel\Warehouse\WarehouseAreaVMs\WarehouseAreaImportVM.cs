﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using WalkingTec.Mvvm.Core;
using WalkingTec.Mvvm.Core.Extensions;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;


namespace bnred.ViewModel.Warehouse.WarehouseAreaVMs
{
    public partial class WarehouseAreaTemplateVM : BaseTemplateVM
    {
        public ExcelPropety ID_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.ID);
        [Display(Name = "区域编码")]
        public ExcelPropety Code_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Code);
        [Display(Name = "区域名称")]
        public ExcelPropety Name_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Name);
        [Display(Name = "仓库")]
        public ExcelPropety Warehouse_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.WarehouseId);
        [Display(Name = "区域类型")]
        public ExcelPropety Type_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Type);
        [Display(Name = "区域状态")]
        public ExcelPropety Status_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Status);
        [Display(Name = "面积(平方米)")]
        public ExcelPropety Area_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Area);
        [Display(Name = "容量(立方米)")]
        public ExcelPropety Volume_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Volume);
        [Display(Name = "最大存储量")]
        public ExcelPropety MaxCapacity_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.MaxCapacity);
        [Display(Name = "当前存储量")]
        public ExcelPropety CurrentCapacity_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.CurrentCapacity);
        public ExcelPropety Manager_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.ManagerId);
        [Display(Name = "描述")]
        public ExcelPropety Description_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Description);
        [Display(Name = "备注")]
        public ExcelPropety Remark_Excel = ExcelPropety.CreateProperty<WarehouseArea>(x => x.Remark);

	    protected override void InitVM()
        {
            Warehouse_Excel.DataType = ColumnDataType.ComboBox;
            Warehouse_Excel.ListItems = DC.Set<bnred.Model.WMS.Warehouse.Warehouse>().GetSelectListItems(Wtm, y => y.Name);
            Manager_Excel.DataType = ColumnDataType.ComboBox;
            Manager_Excel.ListItems = DC.Set<FrameworkUser>().GetSelectListItems(Wtm, y => y.Name);
        }

    }

    public class WarehouseAreaImportVM : BaseImportVM<WarehouseAreaTemplateVM, WarehouseArea>
    {

    }

}
