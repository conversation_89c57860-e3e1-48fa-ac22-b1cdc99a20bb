using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 成本类型枚举
    /// 用于标识不同的成本类型
    /// </summary>
    public enum CostType
    {
        /// <summary>
        /// 采购成本
        /// 物料采购的成本
        /// </summary>
        [Display(Name = "采购成本")]
        Purchase = 0,

        /// <summary>
        /// 仓储成本
        /// 物料存储的成本
        /// </summary>
        [Display(Name = "仓储成本")]
        Storage = 1,

        /// <summary>
        /// 运输成本
        /// 物料运输的成本
        /// </summary>
        [Display(Name = "运输成本")]
        Transportation = 2,

        /// <summary>
        /// 人工成本
        /// 作业人员的成本
        /// </summary>
        [Display(Name = "人工成本")]
        Labor = 3,

        /// <summary>
        /// 管理成本
        /// 仓库管理的成本
        /// </summary>
        [Display(Name = "管理成本")]
        Management = 4,

        /// <summary>
        /// 设备成本
        /// 仓储设备的成本
        /// </summary>
        [Display(Name = "设备成本")]
        Equipment = 5
    }

    /// <summary>
    /// 成本计算方法枚举
    /// 用于标识不同的成本计算方法
    /// </summary>
    public enum CostMethod
    {
        /// <summary>
        /// 移动平均法
        /// 根据每次入库的数量和金额重新计算平均成本
        /// </summary>
        [Display(Name = "移动平均法")]
        MovingAverage = 0,

        /// <summary>
        /// 先进先出法
        /// 按照库存入库的时间顺序计算成本
        /// </summary>
        [Display(Name = "先进先出法")]
        FIFO = 1,

        /// <summary>
        /// 后进先出法
        /// 按照库存入库的相反时间顺序计算成本
        /// </summary>
        [Display(Name = "后进先出法")]
        LIFO = 2,

        /// <summary>
        /// 加权平均法
        /// 按照期间内所有入库的总金额除以总数量计算成本
        /// </summary>
        [Display(Name = "加权平均法")]
        WeightedAverage = 3,

        /// <summary>
        /// 标准成本法
        /// 使用预先设定的标准成本进行计算
        /// </summary>
        [Display(Name = "标准成本法")]
        StandardCost = 4
    }
} 