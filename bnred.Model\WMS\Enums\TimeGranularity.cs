using System.ComponentModel.DataAnnotations;

namespace bnred.Model
{
    /// <summary>
    /// 时间粒度
    /// </summary>
    public enum TimeGranularity
    {
        /// <summary>
        /// 按日
        /// </summary>
        [Display(Name = "按日")]
        Daily = 1,

        /// <summary>
        /// 按周
        /// </summary>
        [Display(Name = "按周")]
        Weekly = 2,

        /// <summary>
        /// 按月
        /// </summary>
        [Display(Name = "按月")]
        Monthly = 3,

        /// <summary>
        /// 按季度
        /// </summary>
        [Display(Name = "按季度")]
        Quarterly = 4,

        /// <summary>
        /// 按年
        /// </summary>
        [Display(Name = "按年")]
        Yearly = 5
    }
} 