using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseExtensions")]
    public class WarehouseExtension : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal TotalArea { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal UsableArea { get; set; }

        [Required]
        public bool HasTemperatureControl { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinTemperature { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxTemperature { get; set; }

        [Required]
        public bool HasHumidityControl { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinHumidity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxHumidity { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 