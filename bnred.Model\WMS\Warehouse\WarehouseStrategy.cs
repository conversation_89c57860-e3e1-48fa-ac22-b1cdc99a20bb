using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseStrategies")]
    public class WarehouseStrategy : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Required]
        [StringLength(50)]
        public string StrategyCode { get; set; }

        [Required]
        [StringLength(100)]
        public string StrategyName { get; set; }

        [Required]
        public StrategyType Type { get; set; }

        [Required]
        public bool IsDefault { get; set; }

        [Required]
        public bool IsActive { get; set; }

        [Required]
        [StringLength(1000)]
        public string StrategyContent { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 