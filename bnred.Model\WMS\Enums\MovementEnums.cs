using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 移动类型
    /// </summary>
    public enum MovementType
    {
        /// <summary>
        /// 入库
        /// </summary>
        [Display(Name = "入库")]
        Inbound = 0,

        /// <summary>
        /// 出库
        /// </summary>
        [Display(Name = "出库")]
        Outbound = 1,

        /// <summary>
        /// 调拨
        /// </summary>
        [Display(Name = "调拨")]
        Transfer = 2,

        /// <summary>
        /// 移库
        /// </summary>
        [Display(Name = "移库")]
        Move = 3,

        /// <summary>
        /// 盘盈
        /// </summary>
        [Display(Name = "盘盈")]
        GainFromCounting = 4,

        /// <summary>
        /// 盘亏
        /// </summary>
        [Display(Name = "盘亏")]
        LossFromCounting = 5,

        /// <summary>
        /// 报损
        /// </summary>
        [Display(Name = "报损")]
        Damage = 6,

        /// <summary>
        /// 上架
        /// </summary>
        [Display(Name = "上架")]
        PutAway = 7,

        /// <summary>
        /// 拣货
        /// </summary>
        [Display(Name = "拣货")]
        Picking = 8,

        /// <summary>
        /// 补货
        /// </summary>
        [Display(Name = "补货")]
        Replenishment = 9,
        
        /// <summary>
        /// 销售出库
        /// </summary>
        [Display(Name = "销售出库")]
        SalesOutbound = 10,

        /// <summary>
        /// 生产领料出库
        /// </summary>
        [Display(Name = "生产领料出库")]
        ProductionOutbound = 11,

        /// <summary>
        /// 退货出库
        /// </summary>
        [Display(Name = "退货出库")]
        ReturnOutbound = 12,

        /// <summary>
        /// 调拨出库
        /// </summary>
        [Display(Name = "调拨出库")]
        TransferOutbound = 13,

        /// <summary>
        /// 盘亏出库
        /// </summary>
        [Display(Name = "盘亏出库")]
        CountingOutbound = 14,

        /// <summary>
        /// 其他出库
        /// </summary>
        [Display(Name = "其他出库")]
        OtherOutbound = 15,

        /// <summary>
        /// 库存调整
        /// </summary>
        [Display(Name = "库存调整")]
        Adjustment = 20,

        /// <summary>
        /// 库存移动
        /// </summary>
        [Display(Name = "库存移动")]
        Movement = 21,

        /// <summary>
        /// 库存盘点
        /// </summary>
        [Display(Name = "库存盘点")]
        StockTaking = 22,

        /// <summary>
        /// 质量检验
        /// </summary>
        [Display(Name = "质量检验")]
        QualityCheck = 23,

        /// <summary>
        /// 其他
        /// </summary>
        [Display(Name = "其他")]
        Other = 99
    }

    /// <summary>
    /// 移动状态
    /// </summary>
    public enum MovementStatus
    {
        /// <summary>
        /// 待分配
        /// </summary>
        [Display(Name = "待分配")]
        Pending = 0,

        /// <summary>
        /// 已分配
        /// </summary>
        [Display(Name = "已分配")]
        Assigned = 1,

        /// <summary>
        /// 进行中
        /// </summary>
        [Display(Name = "进行中")]
        Processing = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 4,

        /// <summary>
        /// 异常
        /// </summary>
        [Display(Name = "异常")]
        Exception = 5
    }

    /// <summary>
    /// 移动优先级
    /// </summary>
    public enum MovementPriority
    {
        /// <summary>
        /// 低
        /// </summary>
        [Display(Name = "低")]
        Low = 0,

        /// <summary>
        /// 中
        /// </summary>
        [Display(Name = "中")]
        Medium = 1,

        /// <summary>
        /// 高
        /// </summary>
        [Display(Name = "高")]
        High = 2,

        /// <summary>
        /// 紧急
        /// </summary>
        [Display(Name = "紧急")]
        Urgent = 3
    }
} 