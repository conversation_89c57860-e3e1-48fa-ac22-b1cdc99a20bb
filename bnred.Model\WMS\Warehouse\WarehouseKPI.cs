using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseKPIs")]
    public class WarehouseKPI : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Required]
        public DateTime KPIDate { get; set; }

        [Required]
        public KPIType Type { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TargetValue { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal ActualValue { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Achievement { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 