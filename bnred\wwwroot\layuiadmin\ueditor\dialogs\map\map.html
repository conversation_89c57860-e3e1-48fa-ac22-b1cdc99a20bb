<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><title></title><script src="../internal.js"></script><script src="http://api.map.baidu.com/api?v=1.1&services=true"></script><style>.content{width:530px;height:350px;margin:10px auto}.content table{width:100%}.content table td{vertical-align:middle}#address,#city{height:21px;background:#fff;border:1px solid #d7d7d7;line-height:21px}#city{width:60px}#address{width:130px}#is_dynamic_label span{vertical-align:middle;margin:3px 0 3px 3px}#is_dynamic_label input{vertical-align:middle;margin:3px 3px 3px 50px}</style></head><body><div class="content"><table><tr><td><var id="lang_city"></var>:</td><td><input id="city" type="text"></td><td><var id="lang_address"></var>:</td><td><input id="address" type="text"></td><td><a href="javascript:doSearch()" class="button"><var id="lang_search"></var></a></td><td><label id="is_dynamic_label" for="is_dynamic"><input id="is_dynamic" type="checkbox" name="is_dynamic"><span><var id="lang_dynamicmap"></var></span></label></td></tr></table><div style="width:100%;height:340px;margin:5px auto;border:1px solid gray" id="container"></div></div><script>var marker,point,styleStr,map=new BMap.Map("container");function doSearch(){document.getElementById("city").value?new BMap.LocalSearch(document.getElementById("city").value,{onSearchComplete:function(e){if(e&&e.getNumPois()){for(var t=[],a=0;a<e.getCurrentNumPois();a++)t.push(e.getPoi(a).point);1<t.length?map.setViewport(t):map.centerAndZoom(t[0],13),point=map.getCenter(),marker.setPoint(point)}else alert(lang.errorMsg)}}).search(document.getElementById("address").value||document.getElementById("city").value):alert(lang.cityMsg)}function getPars(e,t){return new RegExp(t+"=((\\d+|[.,])*)","g").exec(e)[1]}function init(){var e,t,a,r=editor.selection.getRange().getClosedNode(),n=r&&/api[.]map[.]baidu[.]com/gi.test(r.getAttribute("src")),o=r&&domUtils.hasClass(r,"ueditor_baidumap");n||o?(styleStr=(o?(e=decodeURIComponent(r.getAttribute("src")),$G("is_dynamic").checked=!0):e=r.getAttribute("src"),r.style.cssText),t=getPars(e,"center").split(","),a=getPars(e,"markers").split(","),point=new BMap.Point(Number(t[0]),Number(t[1])),marker=new BMap.Marker(new BMap.Point(Number(a[0]),Number(a[1]))),map.addControl(new BMap.NavigationControl),map.centerAndZoom(point,Number(getPars(e,"zoom")))):(point=new BMap.Point(116.404,39.915),marker=new BMap.Marker(point),map.addControl(new BMap.NavigationControl),map.centerAndZoom(point,10));marker.enableDragging(),map.addOverlay(marker)}map.enableScrollWheelZoom(),map.enableContinuousZoom(),init(),document.getElementById("address").onkeydown=function(e){13==(e=e||event).keyCode&&doSearch()},dialog.onok=function(){var e=map.getCenter(),t=map.zoomLevel,a=map.getSize(),r=a.width,n=a.height,o=marker.getPoint();if($G("is_dynamic").checked){var i=editor.options.UEDITOR_HOME_URL,m=[i+(/\/$/.test(i)?"":"/")+"dialogs/map/show.html#center="+e.lng+","+e.lat,"&zoom="+t,"&width="+r,"&height="+n,"&markers="+o.lng+","+o.lat,"&markerStyles=l,A"].join("");editor.execCommand("inserthtml",'<iframe class="ueditor_baidumap" src="'+m+'"'+(styleStr?' style="'+styleStr+'"':"")+' frameborder="0" width="'+(r+4)+'" height="'+(n+4)+'"></iframe>')}else{m="http://api.map.baidu.com/staticimage?center="+e.lng+","+e.lat+"&zoom="+t+"&width="+a.width+"&height="+a.height+"&markers="+o.lng+","+o.lat;editor.execCommand("inserthtml",'<img width="'+a.width+'"height="'+a.height+'" src="'+m+'"'+(styleStr?' style="'+styleStr+'"':"")+"/>")}},document.getElementById("address").focus()</script></body></html>