using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Warehouse;
 
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Enums;

namespace bnred.Model.WMS.Inbound
{
    /// <summary>
    /// 入库单
    /// 记录物料入库的基本信息
    /// </summary>
    [Table("Inbounds")]
    public class Inbound :  BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 入库单号
        /// </summary>
        [Display(Name = "入库单号")]
        [Required(ErrorMessage = "入库单号是必填项")]
        [StringLength(50)]
        public string InboundNo { get; set; }

        /// <summary>
        /// 入库类型
        /// </summary>
        [Display(Name = "入库类型")]
        public DocumentType Type { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        [Display(Name = "位置")]
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Display(Name = "数量")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Display(Name = "单位")]
        [StringLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Display(Name = "单价")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [Display(Name = "总金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Display(Name = "供应商")]
        [StringLength(50)]
        public string SupplierId { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Display(Name = "单据日期")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 入库单状态
    /// </summary>
    public enum InboundStatus
    {
        /// <summary>
        /// 待审核
        /// </summary>
        [Display(Name = "待审核")]
        Pending = 0,

        /// <summary>
        /// 已审核
        /// </summary>
        [Display(Name = "已审核")]
        Approved = 1,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Display(Name = "已拒绝")]
        Rejected = 2,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 3,

        /// <summary>
        /// 已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 4
    }
}