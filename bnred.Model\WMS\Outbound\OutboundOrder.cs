using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Outbound
{
    /// <summary>
    /// 出库单
    /// </summary>
    [Table("WMS_OutboundOrders")]
    public class OutboundOrder : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 出库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "出库单号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        [Required]
        [Display(Name = "出库类型")]
        public OutboundType Type { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [Display(Name = "仓库ID")]
        public Guid WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public virtual Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 预计出库日期
        /// </summary>
        [Display(Name = "预计出库日期")]
        public DateTime? PlanTime { get; set; }

        /// <summary>
        /// 实际出库日期
        /// </summary>
        [Display(Name = "实际出库日期")]
        public DateTime? ActualTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        [Display(Name = "备注")]
        public string Remark { get; set; }
    }
}