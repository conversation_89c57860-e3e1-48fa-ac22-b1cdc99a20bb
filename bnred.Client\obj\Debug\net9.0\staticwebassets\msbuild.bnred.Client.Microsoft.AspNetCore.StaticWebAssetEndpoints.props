﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="appsettings.j9lqcc224j.json">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\appsettings.json'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j9lqcc224j"},{"Name":"integrity","Value":"sha256-2TW/ZJs6ZmmfRka/1hC60gXkTAzen2hE6A6hk6SHNqA="},{"Name":"label","Value":"appsettings.json"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"487"},{"Name":"Content-Type","Value":"application/json"},{"Name":"ETag","Value":"\u00222TW/ZJs6ZmmfRka/1hC60gXkTAzen2hE6A6hk6SHNqA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="appsettings.json">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\appsettings.json'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2TW/ZJs6ZmmfRka/1hC60gXkTAzen2hE6A6hk6SHNqA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"487"},{"Name":"Content-Type","Value":"application/json"},{"Name":"ETag","Value":"\u00222TW/ZJs6ZmmfRka/1hC60gXkTAzen2hE6A6hk6SHNqA=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="bnred.Client.o0pb5r8dcj.styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\bnred.Client.styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o0pb5r8dcj"},{"Name":"integrity","Value":"sha256-mHtuuUFoUsnj5MvTOqUVEafjR468RLeI0U1\u002BzBXeFlY="},{"Name":"label","Value":"bnred.Client.styles.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"75"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022mHtuuUFoUsnj5MvTOqUVEafjR468RLeI0U1\u002BzBXeFlY=\u0022"},{"Name":"Last-Modified","Value":"Mon, 03 Mar 2025 13:24:31 GMT"},{"Name":"Link","Value":"\u003C_content/bnred.Shared/bnred.Shared.gjyvc1v7oj.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="bnred.Client.styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\bnred.Client.styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mHtuuUFoUsnj5MvTOqUVEafjR468RLeI0U1\u002BzBXeFlY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"75"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022mHtuuUFoUsnj5MvTOqUVEafjR468RLeI0U1\u002BzBXeFlY=\u0022"},{"Name":"Last-Modified","Value":"Mon, 03 Mar 2025 13:24:31 GMT"},{"Name":"Link","Value":"\u003C_content/bnred.Shared/bnred.Shared.gjyvc1v7oj.bundle.scp.css\u003E; rel=\u0022preload\u0022; as=\u0022style\u0022"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="fav.6jx2rxa9oc.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fav.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6jx2rxa9oc"},{"Name":"integrity","Value":"sha256-vur7WppiFGepb4SZSLILZMuKtNQ37IXfeOZfYtz3jHM="},{"Name":"label","Value":"fav.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4286"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022vur7WppiFGepb4SZSLILZMuKtNQ37IXfeOZfYtz3jHM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="fav.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fav.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vur7WppiFGepb4SZSLILZMuKtNQ37IXfeOZfYtz3jHM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4286"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022vur7WppiFGepb4SZSLILZMuKtNQ37IXfeOZfYtz3jHM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="index.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zZNzDHG/12PI7IY79y8Ck0asad\u002BQ5/O9f3wDhCm2t4A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2853"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022zZNzDHG/12PI7IY79y8Ck0asad\u002BQ5/O9f3wDhCm2t4A=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="index.ru6an5k9d2.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ru6an5k9d2"},{"Name":"integrity","Value":"sha256-zZNzDHG/12PI7IY79y8Ck0asad\u002BQ5/O9f3wDhCm2t4A="},{"Name":"label","Value":"index.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2853"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022zZNzDHG/12PI7IY79y8Ck0asad\u002BQ5/O9f3wDhCm2t4A=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="manifest.1kagnj3j5z.json">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\manifest.json'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1kagnj3j5z"},{"Name":"integrity","Value":"sha256-j9N/O1q/NmP6RuXAvu4vdKXGLgniM1\u002BLB5laVu/LeHk="},{"Name":"label","Value":"manifest.json"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"284"},{"Name":"Content-Type","Value":"application/json"},{"Name":"ETag","Value":"\u0022j9N/O1q/NmP6RuXAvu4vdKXGLgniM1\u002BLB5laVu/LeHk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="manifest.json">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\manifest.json'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-j9N/O1q/NmP6RuXAvu4vdKXGLgniM1\u002BLB5laVu/LeHk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"284"},{"Name":"Content-Type","Value":"application/json"},{"Name":"ETag","Value":"\u0022j9N/O1q/NmP6RuXAvu4vdKXGLgniM1\u002BLB5laVu/LeHk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="pwaicon.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\pwaicon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YVcb4Zj3PzZ5wDhYyo3QoR4z3btsHT1gKOak3me7Rqg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4375"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YVcb4Zj3PzZ5wDhYyo3QoR4z3btsHT1gKOak3me7Rqg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="pwaicon.t167t4j96c.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\pwaicon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t167t4j96c"},{"Name":"integrity","Value":"sha256-YVcb4Zj3PzZ5wDhYyo3QoR4z3btsHT1gKOak3me7Rqg="},{"Name":"label","Value":"pwaicon.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4375"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YVcb4Zj3PzZ5wDhYyo3QoR4z3btsHT1gKOak3me7Rqg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_framework/blazor.webassembly.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\_framework\blazor.webassembly.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I4LggERZ/f\u002BYpdIbmQq/DkpppLSwVDlVHxq6Yvx/QAc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"60340"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022I4LggERZ/f\u002BYpdIbmQq/DkpppLSwVDlVHxq6Yvx/QAc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 30 Nov 2023 19:04:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>