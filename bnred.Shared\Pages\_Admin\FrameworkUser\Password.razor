@page "/_Admin/FrameworkUser/Password/{id}"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">
        <BootstrapInput @bind-Value="@Model.Entity.Password" type="password" />
        <div></div>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Edit"]" IsAsync="true" />
    </div>
</ValidateForm>
@code {
    private FrameworkUserVM Model = new FrameworkUserVM();
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    protected override void OnInitialized()
    {
        Model.Entity = new FrameworkUser { ID = new Guid(id) };
    }


    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, $"/api/_frameworkuser/password", (s) => "Sys.OprationSuccess", method: HttpMethodEnum.PUT);
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
