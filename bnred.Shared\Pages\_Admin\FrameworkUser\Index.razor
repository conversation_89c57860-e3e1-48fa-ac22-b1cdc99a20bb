@page "/_Admin/FrameworkUser"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkUserVms;
@inherits BasePage
@attribute [ActionDescription("MenuKey.UserManagement", "WalkingTec.Mvvm.Admin.Api,FrameworkUser")]

@if ( UserInfo.Attributes["IsMainHost"].ToString() == "False")
{
    <WTSearchPanel OnSearch="@DoSearch">
        <ValidateForm Model="@SearchModel">
            <Row ItemsPerRow="ItemsPerRow.Three" RowType="RowType.Inline">
                <Select @bind-Value="@SearchModel.IsValid" Items="@WtmBlazor.GlobalSelectItems.SearcherBoolItems" />
                <BootstrapInput @bind-Value="@SearchModel.ITCode" />
                <BootstrapInput @bind-Value="@SearchModel.Name" />
            </Row>
        </ValidateForm>
    </WTSearchPanel>

    <Table @ref="dataTable" TItem="FrameworkUser_View" OnQueryAsync="OnSearch" IsPagination="true" IsStriped="true" IsBordered="true" ShowRefresh="false"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true" ShowExtendEditButton="false" ShowExtendDeleteButton="false" ShowDefaultButtons="false" style="margin-top:10px;" AllowResizing="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.ITCode" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Gender" Sortable="true" />
            <TableColumn @bind-Field="@context.CellPhone" />
            <TableColumn @bind-Field="@context.RoleName_view" />
            <TableColumn @bind-Field="@context.GroupName_view" />
            <TableColumn @bind-Field="@context.PhotoId">
                <Template Context="data">
                    <Avatar @key="data.Value" Size="Size.ExtraSmall" GetUrlAsync="()=>WtmBlazor.GetBase64Image(data.Value.ToString(),150,150)" />
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.IsValid" ComponentType="@typeof(Switch)" />
        </TableColumns>
        <TableToolbarTemplate>
            @if (IsAccessable("/api/_frameworkuser/Add"))
            {
                <TableToolbarButton TItem="FrameworkUser_View" Color="Color.Primary" Icon="fa fa-fw fa-plus" Text="@WtmBlazor.Localizer["Sys.Create"]" OnClickCallback="OnCreateClick" />
            }
            @if (IsAccessable("/api/_frameworkuser/BatchDelete"))
            {
                <TableToolbarPopConfirmButton TItem="FrameworkUser_View" Color="Color.Danger"
                                          Icon="fa fa-fw fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]"
                                          OnConfirm="@OnBatchDeleteClick" Content="@WtmBlazor.Localizer["Sys.BatchDeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]"
                                          ConfirmButtonText="@WtmBlazor.Localizer["Sys.BatchDelete"]" ConfirmButtonColor="Color.Danger" />
            }

            @if (IsAccessable("/api/_frameworkuser/Import"))
            {
                <TableToolbarButton TItem="FrameworkUser_View" Color="Color.Primary" Icon="fa fa-fw fa-upload" Text="@WtmBlazor.Localizer["Sys.Import"]" OnClickCallback="@OnImportClick" />
            }
            @if (IsAccessable("/api/_frameworkuser/ExportExcel"))
            {
                <TableToolbarButton TItem="FrameworkUser_View" Color="Color.Primary" Icon="fa fa-fw fa-download" Text="@WtmBlazor.Localizer["Sys.Export"]" OnClickCallback="@OnExportClick" IsAsync="true" />
            }
        </TableToolbarTemplate>
        <RowButtonTemplate>
            @if (IsAccessable("/api/_frameworkuser/Edit"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Success" Icon="fa fa-edit" Text="@WtmBlazor.Localizer["Sys.Edit"]" OnClick="() => OnEditClick(context)" />
            }
            @if (IsAccessable("/api/_frameworkuser/{id}"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Info" Icon="fa fa-info" Text="@WtmBlazor.Localizer["Sys.Details"]" OnClick="()=>OnDetailsClick(context)" />
            }
            @if (IsAccessable("/api/_frameworkuser/BatchDelete"))
            {
                <PopConfirmButton Icon="fa fa-trash" Text="@WtmBlazor.Localizer["Sys.Delete"]" OnConfirm="() => OnDeleteClick(context)" Color="Color.Danger" Size="Size.ExtraSmall"
                                  Content="@WtmBlazor.Localizer["Sys.DeleteConfirm"]" CloseButtonText="@WtmBlazor.Localizer["Sys.Close"]" ConfirmButtonText="@WtmBlazor.Localizer["Sys.Delete"]" ConfirmButtonColor="Color.Danger" />
            }
            @if (IsAccessable("/api/_frameworkuser/password"))
            {
                <TableCellButton Size="Size.ExtraSmall" Color="Color.Warning" Icon="fa fa-key" Text="@WtmBlazor.Localizer["Login.ChangePassword"]" OnClick="() => OnPasswordClick(context)" />
            }
        </RowButtonTemplate>
    </Table>
}
else
{
    <div>@WtmBlazor.Localizer["_Admin.HasMainHost"]</div>
}
@code{

    private FrameworkUserSearcher SearchModel = new FrameworkUserSearcher();
    private Table<FrameworkUser_View> dataTable;

    private async Task<QueryData<FrameworkUser_View>> OnSearch(QueryPageOptions opts)
    {
        return await StartSearch<FrameworkUser_View>("/api/_frameworkuser/search", SearchModel, opts);
    }

    private void DoSearch()
    {
        dataTable.QueryAsync();
    }

    private async Task OnCreateClick(IEnumerable<FrameworkUser_View> items)
    {
        if (await OpenDialog<Create>(WtmBlazor.Localizer["Sys.Create"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnEditClick(FrameworkUser_View item)
    {
        if (await OpenDialog<Edit>(WtmBlazor.Localizer["Sys.Edit"], x => x.id == item.ID.ToString()) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

    private async Task OnPasswordClick(FrameworkUser_View item)
    {
        await OpenDialog<Password>(WtmBlazor.Localizer["Login.ChangePassword"], x => x.id == item.ID.ToString());
    }

    private async Task OnDetailsClick(FrameworkUser_View item)
    {
        await OpenDialog<Details>(WtmBlazor.Localizer["Sys.Details"], x => x.id == item.ID.ToString());
    }

    private async Task OnBatchDeleteClick()
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await PostsData(dataTable.SelectedRows.Select(x => x.ID).ToList(), $"/api/_frameworkuser/batchdelete", (s) => WtmBlazor.Localizer["Sys.BatchDeleteSuccess", s]);
            await dataTable.QueryAsync();
        }
        else
        {
            await WtmBlazor.Toast.Information(WtmBlazor.Localizer["Sys.Info"], WtmBlazor.Localizer["Sys.SelectOneRowMin"]);
        }
    }

    private async Task OnDeleteClick(FrameworkUser_View item)
    {
        await PostsData(new List<string> { item.ID.ToString() }, $"/api/_frameworkuser/batchdelete", (s) => "Sys.OprationSuccess");
        await dataTable.QueryAsync();
    }


    private async Task OnExportClick(IEnumerable<FrameworkUser_View> items)
    {
        if (dataTable.SelectedRows?.Any() == true)
        {
            await Download("/api/_frameworkuser/ExportExcelByIds", dataTable.SelectedRows.Select(x => x.ID.ToString()).ToList());
        }
        else
        {
            await Download("/api/_frameworkuser/ExportExcel", SearchModel);
        }
    }
    private async Task OnImportClick(IEnumerable<FrameworkUser_View> items)
    {
        if (await OpenDialog<Import>(WtmBlazor.Localizer["Sys.Import"]) == DialogResult.Yes)
        {
            await dataTable.QueryAsync();
        }
    }

}
