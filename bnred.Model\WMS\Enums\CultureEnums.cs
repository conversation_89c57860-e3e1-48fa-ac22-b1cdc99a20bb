using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 支持的语言文化
    /// </summary>
    public enum SupportedCulture
    {
        /// <summary>
        /// 简体中文
        /// </summary>
        [Display(Name = "简体中文")]
        zh_CN = 0,

        /// <summary>
        /// 英文
        /// </summary>
        [Display(Name = "English")]
        en_US = 1,

        /// <summary>
        /// 繁体中文
        /// </summary>
        [Display(Name = "繁體中文")]
        zh_TW = 2,

        /// <summary>
        /// 日文
        /// </summary>
        [Display(Name = "日本語")]
        ja_JP = 3,

        /// <summary>
        /// 韩文
        /// </summary>
        [Display(Name = "한국어")]
        ko_KR = 4,

        /// <summary>
        /// 法语
        /// </summary>
        [Display(Name = "Français")]
        fr_FR = 5
    }
}