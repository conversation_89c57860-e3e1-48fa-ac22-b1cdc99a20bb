using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.WMS.Enums;
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums.Inventory;
namespace bnred.Model.WMS.Inventory
{
    /// <summary>
    /// 库存快照
    /// 记录特定时间点的库存状态，用于历史数据分析
    /// </summary>
    [Table("WMS_InventorySnapshots")]
    public class InventorySnapshot : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 快照时间
        /// </summary>
        [Required]
        [Display(Name = "快照时间")]
        public DateTime SnapshotTime { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [Display(Name = "物料")]
        [StringLength(50)]
        public string MaterialId { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Display(Name = "物料")]
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Display(Name = "批次")]
        [StringLength(50)]
        public string? BatchId { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Display(Name = "批次")]
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [Display(Name = "仓库")]
        [StringLength(50)]
        public string WarehouseId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        [Display(Name = "仓库")]
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [Display(Name = "库位")]
        [StringLength(50)]
        public string LocationId { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        [Display(Name = "库位")]
        public Location Location { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        [Display(Name = "数量")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [Display(Name = "单位")]
        [StringLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        [Required]
        [Display(Name = "库存状态")]
        public InventoryStatus Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
}