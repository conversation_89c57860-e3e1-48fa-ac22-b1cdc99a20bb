/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;!function(){function t(t,e){var i;t||(t={});for(i in e)t[i]=e[i];return t}function e(){var t,e=arguments.length,i={},s=function(t,e){var i,n;"object"!=typeof t&&(t={});for(n in e)e.hasOwnProperty(n)&&(i=e[n],t[n]=i&&"object"==typeof i&&"[object Array]"!==Object.prototype.toString.call(i)&&"number"!=typeof i.nodeType?s(t[n]||{},i):e[n]);return t};for(t=0;t<e;t++)i=s(i,arguments[t]);return i}function i(t,e){return parseInt(t,e||10)}function s(t){return"string"==typeof t}function n(t){return"object"==typeof t}function o(t){return"[object Array]"===Object.prototype.toString.call(t)}function r(t){return"number"==typeof t}function a(t){return pt.log(t)/pt.LN10}function h(t){return pt.pow(10,t)}function l(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}}function c(t){return t!==V&&null!==t}function d(t,e,i){var o,r;if(s(e))c(i)?t.setAttribute(e,i):t&&t.getAttribute&&(r=t.getAttribute(e));else if(c(e)&&n(e))for(o in e)t.setAttribute(o,e[o]);return r}function p(t){return o(t)?t:[t]}function u(){var t,e,i=arguments,s=i.length;for(t=0;t<s;t++)if(e=i[t],"undefined"!=typeof e&&null!==e)return e}function g(e,i){Lt&&i&&i.opacity!==V&&(i.filter="alpha(opacity="+100*i.opacity+")"),t(e.style,i)}function f(e,i,s,n,o){return e=ct.createElement(e),i&&t(e,i),o&&g(e,{padding:0,border:Et,margin:0}),s&&g(e,s),n&&n.appendChild(e),e}function m(e,i){var s=function(){};return s.prototype=new e,t(s.prototype,i),s}function x(t,e,s,n){var o=U.lang,t=+t||0,r=e===-1?(t.toString().split(".")[1]||"").length:isNaN(e=yt(e))?2:e,e=void 0===s?o.decimalPoint:s,n=void 0===n?o.thousandsSep:n,o=t<0?"-":"",s=String(i(t=yt(t).toFixed(r))),a=s.length>3?s.length%3:0;return o+(a?s.substr(0,a)+n:"")+s.substr(a).replace(/(\d{3})(?=\d)/g,"$1"+n)+(r?e+yt(t-s).toFixed(r).slice(2):"")}function y(t,e){return Array((e||2)+1-String(t).length).join(0)+t}function v(t,e,i){var s=t[e];t[e]=function(){var t=Array.prototype.slice.call(arguments);return t.unshift(s),i.apply(this,t)}}function b(t,e){for(var i,s,n,o,r,a="{",h=!1,l=[];(a=t.indexOf(a))!==-1;){if(i=t.slice(0,a),h){for(s=i.split(":"),n=s.shift().split("."),r=n.length,i=e,o=0;o<r;o++)i=i[n[o]];s.length&&(s=s.join(":"),n=/\.([0-9])/,o=U.lang,r=void 0,/f$/.test(s)?(r=(r=s.match(n))?r[1]:-1,i=x(i,r,o.decimalPoint,s.indexOf(",")>-1?o.thousandsSep:"")):i=Z(s,i))}l.push(i),t=t.slice(a+1),a=(h=!h)?"}":"{"}return l.push(t),l.join("")}function k(t){return pt.pow(10,gt(pt.log(t)/pt.LN10))}function w(t,e,i,s){var n,i=u(i,1);for(n=t/i,e||(e=[1,2,2.5,5,10],s&&s.allowDecimals===!1&&(1===i?e=[1,2,5,10]:i<=.1&&(e=[1/i]))),s=0;s<e.length&&(t=e[s],!(n<=(e[s]+(e[s+1]||e[s]))/2));s++);return t*=i}function T(t,e){var i,s=e||[[Nt,[1,2,5,10,20,25,50,100,200,500]],[Ft,[1,2,5,10,15,30]],[Vt,[1,2,5,10,15,30]],[jt,[1,2,3,4,6,8,12]],[_t,[1,2]],[Ut,[1,2]],[Zt,[1,2,3,4,6]],[Kt,null]],n=s[s.length-1],o=q[n[0]],r=n[1];for(i=0;i<s.length&&(n=s[i],o=q[n[0]],r=n[1],!(s[i+1]&&t<=(o*r[r.length-1]+q[s[i+1][0]])/2));i++);return o===q[Kt]&&t<5*o&&(r=[1,2,5]),s=w(t/o,r,n[0]===Kt?k(t/o):1),{unitRange:o,count:s,unitName:n[0]}}function S(e,i,s,n){var o,r=[],a={},h=U.global.useUTC,l=new Date(i),d=e.unitRange,p=e.count;if(c(i)){d>=q[Ft]&&(l.setMilliseconds(0),l.setSeconds(d>=q[Vt]?0:p*gt(l.getSeconds()/p))),d>=q[Vt]&&l[ot](d>=q[jt]?0:p*gt(l[Q]()/p)),d>=q[jt]&&l[rt](d>=q[_t]?0:p*gt(l[tt]()/p)),d>=q[_t]&&l[at](d>=q[Zt]?1:p*gt(l[it]()/p)),d>=q[Zt]&&(l[ht](d>=q[Kt]?0:p*gt(l[st]()/p)),o=l[nt]()),d>=q[Kt]&&(o-=o%p,l[lt](o)),d===q[Ut]&&l[at](l[it]()-l[et]()+u(n,1)),i=1,o=l[nt]();for(var n=l.getTime(),g=l[st](),f=l[it](),m=h?0:(864e5+6e4*l.getTimezoneOffset())%864e5;n<s;)r.push(n),d===q[Kt]?n=J(o+i*p,0):d===q[Zt]?n=J(o,g+i*p):h||d!==q[_t]&&d!==q[Ut]?n+=d*p:n=J(o,g,f+i*p*(d===q[_t]?1:7)),i++;r.push(n),se(ne(r,function(t){return d<=q[jt]&&t%q[_t]===m}),function(t){a[t]=_t})}return r.info=t(e,{higherRanks:a,totalRange:d*p}),r}function L(){this.symbol=this.color=0}function P(t,e){var i,s,n=t.length;for(s=0;s<n;s++)t[s].ss_i=s;for(t.sort(function(t,s){return i=e(t,s),0===i?t.ss_i-s.ss_i:i}),s=0;s<n;s++)delete t[s].ss_i}function A(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i}function C(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i}function M(t,e){for(var i in t)t[i]&&t[i]!==e&&t[i].destroy&&t[i].destroy(),delete t[i]}function D(t){_||(_=f(Yt)),t&&_.appendChild(t),_.innerHTML=""}function I(t,e){var i="Highcharts error #"+t+": www.highcharts.com/errors/"+t;if(e)throw i;dt.console&&console.log(i)}function z(t){return parseFloat(t.toPrecision(14))}function B(t,e){K=u(t,e.animation)}function O(){var t=U.global.useUTC,e=t?"getUTC":"get",i=t?"setUTC":"set";J=t?Date.UTC:function(t,e,i,s,n,o){return new Date(t,e,u(i,1),u(s,0),u(n,0),u(o,0)).getTime()},Q=e+"Minutes",tt=e+"Hours",et=e+"Day",it=e+"Date",st=e+"Month",nt=e+"FullYear",ot=i+"Minutes",rt=i+"Hours",at=i+"Date",ht=i+"Month",lt=i+"FullYear"}function H(){}function R(t,e,i,s){this.axis=t,this.pos=e,this.type=i||"",this.isNew=!0,!i&&!s&&this.addLabel()}function X(t,e){this.axis=t,e&&(this.options=e,this.id=e.id)}function W(t,e,i,s,n,o){var r=t.chart.inverted;this.axis=t,this.isNegative=i,this.options=e,this.x=s,this.total=null,this.points={},this.stack=n,this.percent="percent"===o,this.alignOptions={align:e.align||(r?i?"left":"right":"center"),verticalAlign:e.verticalAlign||(r?"middle":i?"bottom":"top"),y:u(e.y,r?4:i?14:-6),x:u(e.x,r?i?-6:6:0)},this.textAlign=e.textAlign||(r?i?"right":"left":"center")}function Y(){this.init.apply(this,arguments)}function E(){this.init.apply(this,arguments)}function G(t,e){this.init(t,e)}function N(t,e){this.init(t,e)}function F(){this.init.apply(this,arguments)}var V,j,_,U,Z,K,$,q,J,Q,tt,et,it,st,nt,ot,rt,at,ht,lt,ct=document,dt=window,pt=Math,ut=pt.round,gt=pt.floor,ft=pt.ceil,mt=pt.max,xt=pt.min,yt=pt.abs,vt=pt.cos,bt=pt.sin,kt=pt.PI,wt=2*kt/360,Tt=navigator.userAgent,St=dt.opera,Lt=/msie/i.test(Tt)&&!St,Pt=8===ct.documentMode,At=/AppleWebKit/.test(Tt),Ct=/Firefox/.test(Tt),Mt=/(Mobile|Android|Windows Phone)/.test(Tt),Dt="http://www.w3.org/2000/svg",It=!!ct.createElementNS&&!!ct.createElementNS(Dt,"svg").createSVGRect,zt=Ct&&parseInt(Tt.split("Firefox/")[1],10)<4,Bt=!It&&!Lt&&!!ct.createElement("canvas").getContext,Ot=ct.documentElement.ontouchstart!==V,Ht={},Rt=0,Xt=function(){},Wt=[],Yt="div",Et="none",Gt="rgba(192,192,192,"+(It?1e-4:.002)+")",Nt="millisecond",Ft="second",Vt="minute",jt="hour",_t="day",Ut="week",Zt="month",Kt="year",$t="stroke-width",qt={};dt.Highcharts=dt.Highcharts?I(16,!0):{},Z=function(e,i,s){if(!c(i)||isNaN(i))return"Invalid date";var n,e=u(e,"%Y-%m-%d %H:%M:%S"),o=new Date(i),r=o[tt](),a=o[et](),h=o[it](),l=o[st](),d=o[nt](),p=U.lang,g=p.weekdays,o=t({a:g[a].substr(0,3),A:g[a],d:y(h),e:h,b:p.shortMonths[l],B:p.months[l],m:y(l+1),y:d.toString().substr(2,2),Y:d,H:y(r),I:y(r%12||12),l:r%12||12,M:y(o[Q]()),p:r<12?"AM":"PM",P:r<12?"am":"pm",S:y(o.getSeconds()),L:y(ut(i%1e3),3)},Highcharts.dateFormats);for(n in o)for(;e.indexOf("%"+n)!==-1;)e=e.replace("%"+n,"function"==typeof o[n]?o[n](i):o[n]);return s?e.substr(0,1).toUpperCase()+e.substr(1):e},L.prototype={wrapColor:function(t){this.color>=t&&(this.color=0)},wrapSymbol:function(t){this.symbol>=t&&(this.symbol=0)}},q=function(){for(var t=0,e=arguments,i=e.length,s={};t<i;t++)s[e[t++]]=e[t];return s}(Nt,1,Ft,1e3,Vt,6e4,jt,36e5,_t,864e5,Ut,6048e5,Zt,26784e5,Kt,31556952e3),$={init:function(t,e,i){var s,n,o,e=e||"",r=t.shift,a=e.indexOf("C")>-1,h=a?7:3,e=e.split(" "),i=[].concat(i),l=function(t){for(s=t.length;s--;)"M"===t[s]&&t.splice(s+1,0,t[s+1],t[s+2],t[s+1],t[s+2])};if(a&&(l(e),l(i)),t.isArea&&(n=e.splice(e.length-6,6),o=i.splice(i.length-6,6)),r<=i.length/h&&e.length===i.length)for(;r--;)i=[].concat(i).splice(0,h).concat(i);if(t.shift=0,e.length)for(t=i.length;e.length<t;)r=[].concat(e).splice(e.length-h,h),a&&(r[h-6]=r[h-2],r[h-5]=r[h-1]),e=e.concat(r);return n&&(e=e.concat(n),i=i.concat(o)),[e,i]},step:function(t,e,i,s){var n=[],o=t.length;if(1===i)n=s;else if(o===e.length&&i<1)for(;o--;)s=parseFloat(t[o]),n[o]=isNaN(s)?t[o]:i*parseFloat(e[o]-s)+s;else n=e;return n}},function(e){dt.HighchartsAdapter=dt.HighchartsAdapter||e&&{init:function(t){var i,n=e.fx,o=n.step,r=e.Tween,a=r&&r.propHooks;i=e.cssHooks.opacity,e.extend(e.easing,{easeOutQuad:function(t,e,i,s,n){return-s*(e/=n)*(e-2)+i}}),e.each(["cur","_default","width","height","opacity"],function(t,e){var i,s,h=o;"cur"===e?h=n.prototype:"_default"===e&&r&&(h=a[e],e="set"),(i=h[e])&&(h[e]=function(n){if(n=t?n:this,"align"!==n.prop)return s=n.elem,s.attr?s.attr(n.prop,"cur"===e?V:n.now):i.apply(this,arguments)})}),v(i,"get",function(t,e,i){return e.attr?e.opacity||0:t.call(this,e,i)}),i=function(e){var i,s=e.elem;e.started||(i=t.init(s,s.d,s.toD),e.start=i[0],e.end=i[1],e.started=!0),s.attr("d",t.step(e.start,e.end,e.pos,s.toD))},r?a.d={set:i}:o.d=i,this.each=Array.prototype.forEach?function(t,e){return Array.prototype.forEach.call(t,e)}:function(t,e){for(var i=0,s=t.length;i<s;i++)if(e.call(t[i],t[i],i,t)===!1)return i},e.fn.highcharts=function(){var t,e,i="Chart",n=arguments;return s(n[0])&&(i=n[0],n=Array.prototype.slice.call(n,1)),t=n[0],t!==V&&(t.chart=t.chart||{},t.chart.renderTo=this[0],new Highcharts[i](t,n[1]),e=this),t===V&&(e=Wt[d(this[0],"data-highcharts-chart")]),e}},getScript:e.getScript,inArray:e.inArray,adapterRun:function(t,i){return e(t)[i]()},grep:e.grep,map:function(t,e){for(var i=[],s=0,n=t.length;s<n;s++)i[s]=e.call(t[s],t[s],s,t);return i},offset:function(t){return e(t).offset()},addEvent:function(t,i,s){e(t).bind(i,s)},removeEvent:function(t,i,s){var n=ct.removeEventListener?"removeEventListener":"detachEvent";ct[n]&&t&&!t[n]&&(t[n]=function(){}),e(t).unbind(i,s)},fireEvent:function(i,s,n,o){var r,a=e.Event(s),h="detached"+s;!Lt&&n&&(delete n.layerX,delete n.layerY),t(a,n),i[s]&&(i[h]=i[s],i[s]=null),e.each(["preventDefault","stopPropagation"],function(t,e){var i=a[e];a[e]=function(){try{i.call(a)}catch(t){"preventDefault"===e&&(r=!0)}}}),e(i).trigger(a),i[h]&&(i[s]=i[h],i[h]=null),o&&!a.isDefaultPrevented()&&!r&&o(a)},washMouseEvent:function(t){var e=t.originalEvent||t;return e.pageX===V&&(e.pageX=t.pageX,e.pageY=t.pageY),e},animate:function(t,i,s){var n=e(t);t.style||(t.style={}),i.d&&(t.toD=i.d,i.d=1),n.stop(),i.opacity!==V&&t.attr&&(i.opacity+="px"),n.animate(i,s)},stop:function(t){e(t).stop()}}}(dt.jQuery);var Jt=dt.HighchartsAdapter,Qt=Jt||{};Jt&&Jt.init.call(Jt,$);var te=Qt.adapterRun,ee=Qt.getScript,ie=Qt.inArray,se=Qt.each,ne=Qt.grep,oe=Qt.offset,re=Qt.map,ae=Qt.addEvent,he=Qt.removeEvent,le=Qt.fireEvent,ce=Qt.washMouseEvent,de=Qt.animate,pe=Qt.stop,Qt={enabled:!0,x:0,y:15,style:{color:"#666",cursor:"default",fontSize:"11px",lineHeight:"14px"}};U={colors:"#2f7ed8,#0d233a,#8bbc21,#910000,#1aadce,#492970,#f28f43,#77a1e5,#c42525,#a6c96a".split(","),symbols:["circle","diamond","square","triangle","triangle-down"],lang:{loading:"Loading...",months:"January,February,March,April,May,June,July,August,September,October,November,December".split(","),shortMonths:"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec".split(","),weekdays:"Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday".split(","),decimalPoint:".",numericSymbols:"k,M,G,T,P,E".split(","),resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1",thousandsSep:","},global:{useUTC:!0,canvasToolsURL:"http://code.highcharts.com/3.0.6/modules/canvas-tools.js",VMLRadialGradientURL:"http://code.highcharts.com/3.0.6/gfx/vml-radial-gradient.png"},chart:{borderColor:"#4572A7",borderRadius:5,defaultSeriesType:"line",ignoreHiddenSeries:!0,spacing:[10,10,15,10],style:{fontFamily:'"Lucida Grande", "Lucida Sans Unicode", Verdana, Arial, Helvetica, sans-serif',fontSize:"12px"},backgroundColor:"#FFFFFF",plotBorderColor:"#C0C0C0",resetZoomButton:{theme:{zIndex:20},position:{align:"right",x:-10,y:10}}},title:{text:"Chart title",align:"center",margin:15,style:{color:"#274b6d",fontSize:"16px"}},subtitle:{text:"",align:"center",style:{color:"#4d759e"}},plotOptions:{line:{allowPointSelect:!1,showCheckbox:!1,animation:{duration:1e3},events:{},lineWidth:2,marker:{enabled:!0,lineWidth:0,radius:4,lineColor:"#FFFFFF",states:{hover:{enabled:!0},select:{fillColor:"#FFFFFF",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:e(Qt,{align:"center",enabled:!1,formatter:function(){return null===this.y?"":x(this.y,-1)},verticalAlign:"bottom",y:0}),cropThreshold:300,pointRange:0,showInLegend:!0,states:{hover:{marker:{}},select:{marker:{}}},stickyTracking:!0}},labels:{style:{position:"absolute",color:"#3E576F"}},legend:{enabled:!0,align:"center",layout:"horizontal",labelFormatter:function(){return this.name},borderWidth:1,borderColor:"#909090",borderRadius:5,navigation:{activeColor:"#274b6d",inactiveColor:"#CCC"},shadow:!1,itemStyle:{cursor:"pointer",color:"#274b6d",fontSize:"12px"},itemHoverStyle:{color:"#000"},itemHiddenStyle:{color:"#CCC"},itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},symbolWidth:16,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"1em"},style:{position:"absolute",backgroundColor:"white",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:It,backgroundColor:"rgba(255, 255, 255, .85)",borderWidth:1,borderRadius:3,dateTimeLabelFormats:{millisecond:"%A, %b %e, %H:%M:%S.%L",second:"%A, %b %e, %H:%M:%S",minute:"%A, %b %e, %H:%M",hour:"%A, %b %e, %H:%M",day:"%A, %b %e, %Y",week:"Week from %A, %b %e, %Y",month:"%B %Y",year:"%Y"},headerFormat:'<span style="font-size: 10px">{point.key}</span><br/>',pointFormat:'<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b><br/>',shadow:!0,snap:Mt?25:10,style:{color:"#333333",cursor:"default",fontSize:"12px",padding:"8px",whiteSpace:"nowrap"}},credits:{enabled:!0,text:"Highcharts.com",href:"http://www.highcharts.com",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#909090",fontSize:"9px"}}};var ue=U.plotOptions,Jt=ue.line;O();var ge=function(t){var s,n,o=[];return function(t){t&&t.stops?n=re(t.stops,function(t){return ge(t[1])}):(s=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/.exec(t))?o=[i(s[1]),i(s[2]),i(s[3]),parseFloat(s[4],10)]:(s=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(t))?o=[i(s[1],16),i(s[2],16),i(s[3],16),1]:(s=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(t))&&(o=[i(s[1]),i(s[2]),i(s[3]),1])}(t),{get:function(i){var s;return n?(s=e(t),s.stops=[].concat(s.stops),se(n,function(t,e){s.stops[e]=[s.stops[e][0],t.get(i)]})):s=o&&!isNaN(o[0])?"rgb"===i?"rgb("+o[0]+","+o[1]+","+o[2]+")":"a"===i?o[3]:"rgba("+o.join(",")+")":t,s},brighten:function(t){if(n)se(n,function(e){e.brighten(t)});else if(r(t)&&0!==t){var e;for(e=0;e<3;e++)o[e]+=i(255*t),o[e]<0&&(o[e]=0),o[e]>255&&(o[e]=255)}return this},rgba:o,setOpacity:function(t){return o[3]=t,this}}};H.prototype={init:function(t,e){this.element="span"===e?f(e):ct.createElementNS(Dt,e),this.renderer=t,this.attrSetters={}},opacity:1,animate:function(t,i,s){i=u(i,K,!0),pe(this),i?(i=e(i),s&&(i.complete=s),de(this,t,i)):(this.attr(t),s&&s())},attr:function(t,e){var n,o,r,a,h,l,p,g=this.element,f=g.nodeName.toLowerCase(),m=this.renderer,x=this.attrSetters,y=this.shadows,v=this;if(s(t)&&c(e)&&(n=t,t={},t[n]=e),s(t))n=t,"circle"===f?n={x:"cx",y:"cy"}[n]||n:"strokeWidth"===n&&(n="stroke-width"),v=d(g,n)||this[n]||0,"d"!==n&&"visibility"!==n&&"fill"!==n&&(v=parseFloat(v));else{for(n in t)if(h=!1,o=t[n],r=x[n]&&x[n].call(this,o,n),r!==!1){if(r!==V&&(o=r),"d"===n)o&&o.join&&(o=o.join(" ")),/(NaN| {2}|^$)/.test(o)&&(o="M 0 0");else if("x"===n&&"text"===f)for(r=0;r<g.childNodes.length;r++)a=g.childNodes[r],d(a,"x")===d(g,"x")&&d(a,"x",o);else if(!this.rotation||"x"!==n&&"y"!==n)if("fill"===n)o=m.color(o,g,n);else if("circle"!==f||"x"!==n&&"y"!==n)if("rect"===f&&"r"===n)d(g,{rx:o,ry:o}),h=!0;else if("translateX"===n||"translateY"===n||"rotation"===n||"verticalAlign"===n||"scaleX"===n||"scaleY"===n)h=p=!0;else if("stroke"===n)o=m.color(o,g,n);else if("dashstyle"===n){if(n="stroke-dasharray",o=o&&o.toLowerCase(),"solid"===o)o=Et;else if(o){for(o=o.replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(","),r=o.length;r--;)o[r]=i(o[r])*u(t["stroke-width"],this["stroke-width"]);o=o.join(",")}}else"width"===n?o=i(o):"align"===n?(n="text-anchor",o={left:"start",center:"middle",right:"end"}[o]):"title"===n&&(r=g.getElementsByTagName("title")[0],r||(r=ct.createElementNS(Dt,"title"),g.appendChild(r)),r.textContent=o);else n={x:"cx",y:"cy"}[n]||n;else p=!0;if("strokeWidth"===n&&(n="stroke-width"),"stroke-width"!==n&&"stroke"!==n||(this[n]=o,this.stroke&&this["stroke-width"]?(d(g,"stroke",this.stroke),d(g,"stroke-width",this["stroke-width"]),this.hasStroke=!0):"stroke-width"===n&&0===o&&this.hasStroke&&(g.removeAttribute("stroke"),this.hasStroke=!1),h=!0),this.symbolName&&/^(x|y|width|height|r|start|end|innerR|anchorX|anchorY)/.test(n)&&(l||(this.symbolAttr(t),l=!0),h=!0),y&&/^(width|height|visibility|x|y|d|transform|cx|cy|r)$/.test(n))for(r=y.length;r--;)d(y[r],n,"height"===n?mt(o-(y[r].cutHeight||0),0):o);("width"===n||"height"===n)&&"rect"===f&&o<0&&(o=0),this[n]=o,"text"===n?(o!==this.textStr&&delete this.bBox,this.textStr=o,this.added&&m.buildText(this)):h||d(g,n,o)}p&&this.updateTransform()}return v},addClass:function(t){var e=this.element,i=d(e,"class")||"";return i.indexOf(t)===-1&&d(e,"class",i+" "+t),this},symbolAttr:function(t){var e=this;se("x,y,r,start,end,width,height,innerR,anchorX,anchorY".split(","),function(i){e[i]=u(t[i],e[i])}),e.attr({d:e.renderer.symbols[e.symbolName](e.x,e.y,e.width,e.height,e)})},clip:function(t){return this.attr("clip-path",t?"url("+this.renderer.url+"#"+t.id+")":Et)},crisp:function(t,e,i,s,n){var o,r,a={},h={},t=t||this.strokeWidth||this.attr&&this.attr("stroke-width")||0;r=ut(t)%2/2,h.x=gt(e||this.x||0)+r,h.y=gt(i||this.y||0)+r,h.width=gt((s||this.width||0)-2*r),h.height=gt((n||this.height||0)-2*r),h.strokeWidth=t;for(o in h)this[o]!==h[o]&&(this[o]=a[o]=h[o]);return a},css:function(e){var i,s=this.element,n=e&&e.width&&"text"===s.nodeName.toLowerCase(),o="",r=function(t,e){return"-"+e.toLowerCase()};if(e&&e.color&&(e.fill=e.color),this.styles=e=t(this.styles,e),Bt&&n&&delete e.width,Lt&&!It)n&&delete e.width,g(this.element,e);else{for(i in e)o+=i.replace(/([A-Z])/g,r)+":"+e[i]+";";d(s,"style",o)}return n&&this.added&&this.renderer.buildText(this),this},on:function(t,e){var i=this,s=i.element;return Ot&&"click"===t?(s.ontouchstart=function(t){i.touchEventFired=Date.now(),t.preventDefault(),e.call(s,t)},s.onclick=function(t){(Tt.indexOf("Android")===-1||Date.now()-(i.touchEventFired||0)>1100)&&e.call(s,t)}):s["on"+t]=e,this},setRadialReference:function(t){return this.element.radialReference=t,this},translate:function(t,e){return this.attr({translateX:t,translateY:e})},invert:function(){return this.inverted=!0,this.updateTransform(),this},htmlCss:function(e){var i=this.element;return(i=e&&"SPAN"===i.tagName&&e.width)&&(delete e.width,this.textWidth=i,this.updateTransform()),this.styles=t(this.styles,e),g(this.element,e),this},htmlGetBBox:function(){var t=this.element,e=this.bBox;return e||("text"===t.nodeName&&(t.style.position="absolute"),e=this.bBox={x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}),e},htmlUpdateTransform:function(){if(this.added){var t=this.renderer,e=this.element,s=this.translateX||0,n=this.translateY||0,o=this.x||0,r=this.y||0,a=this.textAlign||"left",h={left:0,center:.5,right:1}[a],l=a&&"left"!==a,d=this.shadows;if(g(e,{marginLeft:s,marginTop:n}),d&&se(d,function(t){g(t,{marginLeft:s+1,marginTop:n+1})}),this.inverted&&se(e.childNodes,function(i){t.invertChild(i,e)}),"SPAN"===e.tagName){var p,f,m,d=this.rotation;p=0;var x,y=1,v=0;m=i(this.textWidth);var b=this.xCorr||0,k=this.yCorr||0,w=[d,a,e.innerHTML,this.textWidth].join(",");w!==this.cTT&&(c(d)&&(p=d*wt,y=vt(p),v=bt(p),this.setSpanRotation(d,v,y)),p=u(this.elemWidth,e.offsetWidth),f=u(this.elemHeight,e.offsetHeight),p>m&&/[ \-]/.test(e.textContent||e.innerText)&&(g(e,{width:m+"px",display:"block",whiteSpace:"normal"}),p=m),m=t.fontMetrics(e.style.fontSize).b,b=y<0&&-p,k=v<0&&-f,x=y*v<0,b+=v*m*(x?1-h:h),k-=y*m*(d?x?h:1-h:1),l&&(b-=p*h*(y<0?-1:1),d&&(k-=f*h*(v<0?-1:1)),g(e,{textAlign:a})),this.xCorr=b,this.yCorr=k),g(e,{left:o+b+"px",top:r+k+"px"}),At&&(f=e.offsetHeight),this.cTT=w}}else this.alignOnAdd=!0},setSpanRotation:function(t){var e={};e[Lt?"-ms-transform":At?"-webkit-transform":Ct?"MozTransform":St?"-o-transform":""]=e.transform="rotate("+t+"deg)",g(this.element,e)},updateTransform:function(){var t=this.translateX||0,e=this.translateY||0,i=this.scaleX,s=this.scaleY,n=this.inverted,o=this.rotation;n&&(t+=this.attr("width"),e+=this.attr("height")),t=["translate("+t+","+e+")"],n?t.push("rotate(90) scale(-1,1)"):o&&t.push("rotate("+o+" "+(this.x||0)+" "+(this.y||0)+")"),(c(i)||c(s))&&t.push("scale("+u(i,1)+" "+u(s,1)+")"),t.length&&d(this.element,"transform",t.join(" "))},toFront:function(){var t=this.element;return t.parentNode.appendChild(t),this},align:function(t,e,i){var n,o,r,a,h={};return o=this.renderer,r=o.alignedObjects,t?(this.alignOptions=t,this.alignByTranslate=e,(!i||s(i))&&(this.alignTo=n=i||"renderer",l(r,this),r.push(this),i=null)):(t=this.alignOptions,e=this.alignByTranslate,n=this.alignTo),i=u(i,o[n],o),n=t.align,o=t.verticalAlign,r=(i.x||0)+(t.x||0),a=(i.y||0)+(t.y||0),"right"!==n&&"center"!==n||(r+=(i.width-(t.width||0))/{right:1,center:2}[n]),h[e?"translateX":"x"]=ut(r),"bottom"!==o&&"middle"!==o||(a+=(i.height-(t.height||0))/({bottom:1,middle:2}[o]||1)),h[e?"translateY":"y"]=ut(a),this[this.placed?"animate":"attr"](h),this.placed=!0,this.alignAttr=h,this},getBBox:function(){var e,i=this.bBox,s=this.renderer,n=this.rotation;e=this.element;var o=this.styles,r=n*wt;if(!i){if(e.namespaceURI===Dt||s.forExport){try{i=e.getBBox?t({},e.getBBox()):{width:e.offsetWidth,height:e.offsetHeight}}catch(a){}(!i||i.width<0)&&(i={width:0,height:0})}else i=this.htmlGetBBox();s.isSVG&&(s=i.width,e=i.height,Lt&&o&&"11px"===o.fontSize&&"22.7"===e.toPrecision(3)&&(i.height=e=14),n&&(i.width=yt(e*bt(r))+yt(s*vt(r)),i.height=yt(e*vt(r))+yt(s*bt(r)))),this.bBox=i}return i},show:function(){return this.attr({visibility:"visible"})},hide:function(){return this.attr({visibility:"hidden"})},fadeOut:function(t){var e=this;e.animate({opacity:0},{duration:t||150,complete:function(){e.hide()}})},add:function(t){var e,s=this.renderer,n=t||s,o=n.element||s.box,r=o.childNodes,a=this.element,h=d(a,"zIndex");if(t&&(this.parentGroup=t),this.parentInverted=t&&t.inverted,void 0!==this.textStr&&s.buildText(this),h&&(n.handleZ=!0,h=i(h)),n.handleZ)for(n=0;n<r.length;n++)if(t=r[n],s=d(t,"zIndex"),t!==a&&(i(s)>h||!c(h)&&c(s))){o.insertBefore(a,t),e=!0;break}return e||o.appendChild(a),this.added=!0,le(this,"add"),this},safeRemoveChild:function(t){var e=t.parentNode;e&&e.removeChild(t)},destroy:function(){var t,e,i=this,s=i.element||{},n=i.shadows,o=i.renderer.isSVG&&"SPAN"===s.nodeName&&s.parentNode;if(s.onclick=s.onmouseout=s.onmouseover=s.onmousemove=s.point=null,pe(i),i.clipPath&&(i.clipPath=i.clipPath.destroy()),i.stops){for(e=0;e<i.stops.length;e++)i.stops[e]=i.stops[e].destroy();i.stops=null}for(i.safeRemoveChild(s),n&&se(n,function(t){i.safeRemoveChild(t)});o&&0===o.childNodes.length;)s=o.parentNode,i.safeRemoveChild(o),o=s;i.alignTo&&l(i.renderer.alignedObjects,i);for(t in i)delete i[t];return null},shadow:function(t,e,i){var s,n,o,r,a,h,l=[],c=this.element;if(t){for(r=u(t.width,3),a=(t.opacity||.15)/r,h=this.parentInverted?"(-1,-1)":"("+u(t.offsetX,1)+", "+u(t.offsetY,1)+")",s=1;s<=r;s++)n=c.cloneNode(0),o=2*r+1-2*s,d(n,{isShadow:"true",stroke:t.color||"black","stroke-opacity":a*s,"stroke-width":o,transform:"translate"+h,fill:Et}),i&&(d(n,"height",mt(d(n,"height")-o,0)),n.cutHeight=o),e?e.element.appendChild(n):c.parentNode.insertBefore(n,c),l.push(n);this.shadows=l}return this}};var fe=function(){this.init.apply(this,arguments)};fe.prototype={Element:H,init:function(t,e,i,s){var n,o,r=location;n=this.createElement("svg").attr({version:"1.1"}),o=n.element,t.appendChild(o),t.innerHTML.indexOf("xmlns")===-1&&d(o,"xmlns",Dt),this.isSVG=!0,this.box=o,this.boxWrapper=n,this.alignedObjects=[],this.url=(Ct||At)&&ct.getElementsByTagName("base").length?r.href.replace(/#.*?$/,"").replace(/([\('\)])/g,"\\$1").replace(/ /g,"%20"):"",this.createElement("desc").add().element.appendChild(ct.createTextNode("Created with Highcharts 3.0.6")),this.defs=this.createElement("defs").add(),this.forExport=s,this.gradients={},this.setSize(e,i,!1);var a;Ct&&t.getBoundingClientRect&&(this.subPixelFix=e=function(){g(t,{left:0,top:0}),a=t.getBoundingClientRect(),g(t,{left:ft(a.left)-a.left+"px",top:ft(a.top)-a.top+"px"})},e(),ae(dt,"resize",e))},isHidden:function(){return!this.boxWrapper.getBBox().width},destroy:function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),M(this.gradients||{}),this.gradients=null,t&&(this.defs=t.destroy()),this.subPixelFix&&he(dt,"resize",this.subPixelFix),this.alignedObjects=null},createElement:function(t){var e=new this.Element;return e.init(this,t),e},draw:function(){},buildText:function(t){for(var e=t.element,s=this,n=s.forExport,o=u(t.textStr,"").toString().replace(/<(b|strong)>/g,'<span style="font-weight:bold">').replace(/<(i|em)>/g,'<span style="font-style:italic">').replace(/<a/g,"<span").replace(/<\/(b|strong|i|em|a)>/g,"</span>").split(/<br.*?>/g),r=e.childNodes,a=/style="([^"]+)"/,h=/href="(http[^"]+)"/,l=d(e,"x"),c=t.styles,p=c&&c.width&&i(c.width),f=c&&c.lineHeight,m=r.length;m--;)e.removeChild(r[m]);p&&!t.added&&this.box.appendChild(e),""===o[o.length-1]&&o.pop(),se(o,function(o,r){var u,m=0,o=o.replace(/<span/g,"|||<span").replace(/<\/span>/g,"</span>|||");u=o.split("|||"),se(u,function(o){if(""!==o||1===u.length){var x,y={},v=ct.createElementNS(Dt,"tspan");if(a.test(o)&&(x=o.match(a)[1].replace(/(;| |^)color([ :])/,"$1fill$2"),d(v,"style",x)),h.test(o)&&!n&&(d(v,"onclick",'location.href="'+o.match(h)[1]+'"'),g(v,{cursor:"pointer"})),o=(o.replace(/<(.|\n)*?>/g,"")||" ").replace(/&lt;/g,"<").replace(/&gt;/g,">")," "!==o&&(v.appendChild(ct.createTextNode(o)),m?y.dx=0:y.x=l,d(v,y),!m&&r&&(!It&&n&&g(v,{display:"block"}),d(v,"dy",f||s.fontMetrics(/px$/.test(v.style.fontSize)?v.style.fontSize:c.fontSize).h,At&&v.offsetHeight)),e.appendChild(v),m++,p))for(var b,k,o=o.replace(/([^\^])-/g,"$1- ").split(" "),y=t._clipHeight,w=[],T=i(f||16),S=1;o.length||w.length;)delete t.bBox,b=t.getBBox(),k=b.width,b=k>p,b&&1!==o.length?(v.removeChild(v.firstChild),w.unshift(o.pop())):(o=w,w=[],o.length&&(S++,y&&S*T>y?(o=["..."],t.attr("title",t.textStr)):(v=ct.createElementNS(Dt,"tspan"),d(v,{dy:T,x:l}),x&&d(v,"style",x),e.appendChild(v),k>p&&(p=k)))),o.length&&v.appendChild(ct.createTextNode(o.join(" ").replace(/- /g,"-")))}})})},button:function(i,s,n,o,r,a,h,l){var c,d,p,u,g,f,m=this.label(i,s,n,null,null,null,null,null,"button"),x=0,i={x1:0,y1:0,x2:0,y2:1},r=e({"stroke-width":1,stroke:"#CCCCCC",fill:{linearGradient:i,stops:[[0,"#FEFEFE"],[1,"#F6F6F6"]]},r:2,padding:5,style:{color:"black"}},r);return p=r.style,delete r.style,a=e(r,{stroke:"#68A",fill:{linearGradient:i,stops:[[0,"#FFF"],[1,"#ACF"]]}},a),u=a.style,delete a.style,h=e(r,{stroke:"#68A",fill:{linearGradient:i,stops:[[0,"#9BD"],[1,"#CDF"]]}},h),g=h.style,delete h.style,l=e(r,{style:{color:"#CCC"}},l),f=l.style,delete l.style,ae(m.element,Lt?"mouseover":"mouseenter",function(){3!==x&&m.attr(a).css(u)}),ae(m.element,Lt?"mouseout":"mouseleave",function(){3!==x&&(c=[r,a,h][x],d=[p,u,g][x],m.attr(c).css(d))}),m.setState=function(t){(m.state=x=t)?2===t?m.attr(h).css(g):3===t&&m.attr(l).css(f):m.attr(r).css(p)},m.on("click",function(){3!==x&&o.call(m)}).attr(r).css(t({cursor:"default"},p))},crispLine:function(t,e){return t[1]===t[4]&&(t[1]=t[4]=ut(t[1])-e%2/2),t[2]===t[5]&&(t[2]=t[5]=ut(t[2])+e%2/2),t},path:function(e){var i={fill:Et};return o(e)?i.d=e:n(e)&&t(i,e),this.createElement("path").attr(i)},circle:function(t,e,i){return t=n(t)?t:{x:t,y:e,r:i},this.createElement("circle").attr(t)},arc:function(t,e,i,s,o,r){return n(t)&&(e=t.y,i=t.r,s=t.innerR,o=t.start,r=t.end,t=t.x),t=this.symbol("arc",t||0,e||0,i||0,i||0,{innerR:s||0,start:o||0,end:r||0}),t.r=i,t},rect:function(t,e,i,s,o,r){return o=n(t)?t.r:o,o=this.createElement("rect").attr({rx:o,ry:o,fill:Et}),o.attr(n(t)?t:o.crisp(r,t,e,mt(i,0),mt(s,0)))},setSize:function(t,e,i){var s=this.alignedObjects,n=s.length;for(this.width=t,this.height=e,this.boxWrapper[u(i,!0)?"animate":"attr"]({width:t,height:e});n--;)s[n].align()},g:function(t){var e=this.createElement("g");return c(t)?e.attr({"class":"highcharts-"+t}):e},image:function(e,i,s,n,o){var r={preserveAspectRatio:Et};return arguments.length>1&&t(r,{x:i,y:s,width:n,height:o}),r=this.createElement("image").attr(r),r.element.setAttributeNS?r.element.setAttributeNS("http://www.w3.org/1999/xlink","href",e):r.element.setAttribute("hc-svg-href",e),r},symbol:function(e,i,s,n,o,r){var a,h,l,c=this.symbols[e],c=c&&c(ut(i),ut(s),n,o,r),d=/^url\((.*?)\)$/;return c?(a=this.path(c),t(a,{symbolName:e,x:i,y:s,width:n,height:o}),r&&t(a,r)):d.test(e)&&(l=function(t,e){t.element&&(t.attr({width:e[0],height:e[1]}),t.alignByTranslate||t.translate(ut((n-e[0])/2),ut((o-e[1])/2)))},h=e.match(d)[1],e=Ht[h],a=this.image(h).attr({x:i,y:s}),a.isImg=!0,e?l(a,e):(a.attr({width:0,height:0}),f("img",{onload:function(){l(a,Ht[h]=[this.width,this.height])},src:h}))),a},symbols:{circle:function(t,e,i,s){var n=.166*i;return["M",t+i/2,e,"C",t+i+n,e,t+i+n,e+s,t+i/2,e+s,"C",t-n,e+s,t-n,e,t+i/2,e,"Z"]},square:function(t,e,i,s){return["M",t,e,"L",t+i,e,t+i,e+s,t,e+s,"Z"]},triangle:function(t,e,i,s){return["M",t+i/2,e,"L",t+i,e+s,t,e+s,"Z"]},"triangle-down":function(t,e,i,s){return["M",t,e,"L",t+i,e,t+i/2,e+s,"Z"]},diamond:function(t,e,i,s){return["M",t+i/2,e,"L",t+i,e+s/2,t+i/2,e+s,t,e+s/2,"Z"]},arc:function(t,e,i,s,n){var o=n.start,i=n.r||i||s,r=n.end-.001,s=n.innerR,a=n.open,h=vt(o),l=bt(o),c=vt(r),r=bt(r),n=n.end-o<kt?0:1;return["M",t+i*h,e+i*l,"A",i,i,0,n,1,t+i*c,e+i*r,a?"M":"L",t+s*c,e+s*r,"A",s,s,0,n,0,t+s*h,e+s*l,a?"":"Z"]}},clipRect:function(t,e,i,s){var n="highcharts-"+Rt++,o=this.createElement("clipPath").attr({id:n}).add(this.defs),t=this.rect(t,e,i,s,0).add(o);return t.id=n,t.clipPath=o,t},color:function(t,i,s){var n,r,a,h,l,p,u,g,f=this,m=/^rgba/,x=[];if(t&&t.linearGradient?r="linearGradient":t&&t.radialGradient&&(r="radialGradient"),r){s=t[r],a=f.gradients,l=t.stops,i=i.radialReference,o(s)&&(t[r]=s={x1:s[0],y1:s[1],x2:s[2],y2:s[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===r&&i&&!c(s.gradientUnits)&&(s=e(s,{cx:i[0]-i[2]/2+s.cx*i[2],cy:i[1]-i[2]/2+s.cy*i[2],r:s.r*i[2],gradientUnits:"userSpaceOnUse"}));for(g in s)"id"!==g&&x.push(g,s[g]);for(g in l)x.push(l[g]);return x=x.join(","),a[x]?t=a[x].id:(s.id=t="highcharts-"+Rt++,a[x]=h=f.createElement(r).attr(s).add(f.defs),h.stops=[],se(l,function(t){m.test(t[1])?(n=ge(t[1]),p=n.get("rgb"),u=n.get("a")):(p=t[1],u=1),t=f.createElement("stop").attr({offset:t[0],"stop-color":p,"stop-opacity":u}).add(h),h.stops.push(t)})),"url("+f.url+"#"+t+")"}return m.test(t)?(n=ge(t),d(i,s+"-opacity",n.get("a")),n.get("rgb")):(i.removeAttribute(s+"-opacity"),t)},text:function(t,e,i,s){var n=U.chart.style,o=Bt||!It&&this.forExport;return s&&!this.forExport?this.html(t,e,i):(e=ut(u(e,0)),i=ut(u(i,0)),t=this.createElement("text").attr({x:e,y:i,text:t}).css({fontFamily:n.fontFamily,fontSize:n.fontSize}),o&&t.css({position:"absolute"}),t.x=e,t.y=i,t)},html:function(e,i,s){var n=U.chart.style,o=this.createElement("span"),r=o.attrSetters,a=o.element,h=o.renderer;return r.text=function(t){return t!==a.innerHTML&&delete this.bBox,a.innerHTML=t,!1},r.x=r.y=r.align=function(t,e){return"align"===e&&(e="textAlign"),
o[e]=t,o.htmlUpdateTransform(),!1},o.attr({text:e,x:ut(i),y:ut(s)}).css({position:"absolute",whiteSpace:"nowrap",fontFamily:n.fontFamily,fontSize:n.fontSize}),o.css=o.htmlCss,h.isSVG&&(o.add=function(e){var i,s=h.box.parentNode,n=[];if(e){if(i=e.div,!i){for(;e;)n.push(e),e=e.parentGroup;se(n.reverse(),function(e){var n;i=e.div=e.div||f(Yt,{className:d(e.element,"class")},{position:"absolute",left:(e.translateX||0)+"px",top:(e.translateY||0)+"px"},i||s),n=i.style,t(e.attrSetters,{translateX:function(t){n.left=t+"px"},translateY:function(t){n.top=t+"px"},visibility:function(t,e){n[e]=t}})})}}else i=s;return i.appendChild(a),o.added=!0,o.alignOnAdd&&o.htmlUpdateTransform(),o}),o},fontMetrics:function(t){var t=i(t||11),t=t<24?t+4:ut(1.2*t),e=ut(.8*t);return{h:t,b:e}},label:function(i,s,n,o,r,a,h,l,d){function p(){var t,i;t=P.element.style,x=(void 0===y||void 0===v||L.styles.textAlign)&&P.getBBox(),L.width=(y||x.width||0)+2*C+M,L.height=(v||x.height||0)+2*C,w=C+S.fontMetrics(t&&t.fontSize).b,T&&(m||(t=ut(-A*C),i=l?-w:0,L.box=m=o?S.symbol(o,t,i,L.width,L.height):S.rect(t,i,L.width,L.height,0,I[$t]),m.add(L)),m.isImg||m.attr(e({width:L.width,height:L.height},I)),I=null)}function u(){var t,e=L.styles,e=e&&e.textAlign,i=M+C*(1-A);t=l?0:w,!c(y)||"center"!==e&&"right"!==e||(i+={center:.5,right:1}[e]*(y-x.width)),(i!==P.x||t!==P.y)&&P.attr({x:i,y:t}),P.x=i,P.y=t}function g(t,e){m?m.attr(t,e):I[t]=e}function f(){P.add(L),L.attr({text:i,x:s,y:n}),m&&c(r)&&L.attr({anchorX:r,anchorY:a})}var m,x,y,v,b,k,w,T,S=this,L=S.g(d),P=S.text("",0,0,h).attr({zIndex:1}),A=0,C=3,M=0,D=0,I={},h=L.attrSetters;ae(L,"add",f),h.width=function(t){return y=t,!1},h.height=function(t){return v=t,!1},h.padding=function(t){return c(t)&&t!==C&&(C=t,u()),!1},h.paddingLeft=function(t){return c(t)&&t!==M&&(M=t,u()),!1},h.align=function(t){return A={left:0,center:.5,right:1}[t],!1},h.text=function(t,e){return P.attr(e,t),p(),u(),!1},h[$t]=function(t,e){return T=!0,D=t%2/2,g(e,t),!1},h.stroke=h.fill=h.r=function(t,e){return"fill"===e&&(T=!0),g(e,t),!1},h.anchorX=function(t,e){return r=t,g(e,t+D-b),!1},h.anchorY=function(t,e){return a=t,g(e,t-k),!1},h.x=function(t){return L.x=t,t-=A*((y||x.width)+C),b=ut(t),L.attr("translateX",b),!1},h.y=function(t){return k=L.y=ut(t),L.attr("translateY",k),!1};var z=L.css;return t(L,{css:function(t){if(t){var i={},t=e(t);se("fontSize,fontWeight,fontFamily,color,lineHeight,width,textDecoration,textShadow".split(","),function(e){t[e]!==V&&(i[e]=t[e],delete t[e])}),P.css(i)}return z.call(L,t)},getBBox:function(){return{width:x.width+2*C,height:x.height+2*C,x:x.x-C,y:x.y-C}},shadow:function(t){return m&&m.shadow(t),L},destroy:function(){he(L,"add",f),he(L.element,"mouseenter"),he(L.element,"mouseleave"),P&&(P=P.destroy()),m&&(m=m.destroy()),H.prototype.destroy.call(L),L=S=p=u=g=f=null}})}},j=fe;var me;if(!It&&!Bt){Highcharts.VMLElement=me={init:function(t,e){var i=["<",e,' filled="f" stroked="f"'],s=["position: ","absolute",";"],n=e===Yt;("shape"===e||n)&&s.push("left:0;top:0;width:1px;height:1px;"),s.push("visibility: ",n?"hidden":"visible"),i.push(' style="',s.join(""),'"/>'),e&&(i=n||"span"===e||"img"===e?i.join(""):t.prepVML(i),this.element=f(i)),this.renderer=t,this.attrSetters={}},add:function(t){var e=this.renderer,i=this.element,s=e.box,s=t?t.element||t:s;return t&&t.inverted&&e.invertChild(i,s),s.appendChild(i),this.added=!0,this.alignOnAdd&&!this.deferUpdateTransform&&this.updateTransform(),le(this,"add"),this},updateTransform:H.prototype.htmlUpdateTransform,setSpanRotation:function(t,e,i){g(this.element,{filter:t?["progid:DXImageTransform.Microsoft.Matrix(M11=",i,", M12=",-e,", M21=",e,", M22=",i,", sizingMethod='auto expand')"].join(""):Et})},pathToVML:function(t){for(var e,i=t.length,s=[];i--;)r(t[i])?s[i]=ut(10*t[i])-5:"Z"===t[i]?s[i]="x":(s[i]=t[i],!t.isArc||"wa"!==t[i]&&"at"!==t[i]||(e="wa"===t[i]?1:-1,s[i+5]===s[i+7]&&(s[i+7]-=e),s[i+6]===s[i+8]&&(s[i+8]-=e)));return s.join(" ")||"x"},attr:function(t,e){var i,n,o,a,h,l=this.element||{},p=l.style,u=l.nodeName,g=this.renderer,m=this.symbolName,x=this.shadows,y=this.attrSetters,v=this;if(s(t)&&c(e)&&(i=t,t={},t[i]=e),s(t))i=t,v="strokeWidth"===i||"stroke-width"===i?this.strokeweight:this[i];else for(i in t)if(n=t[i],h=!1,o=y[i]&&y[i].call(this,n,i),o!==!1&&null!==n){if(o!==V&&(n=o),m&&/^(x|y|r|start|end|width|height|innerR|anchorX|anchorY)/.test(i))a||(this.symbolAttr(t),a=!0),h=!0;else if("d"===i){if(n=n||[],this.d=n.join(" "),l.path=n=this.pathToVML(n),x)for(o=x.length;o--;)x[o].path=x[o].cutOff?this.cutOffPath(n,x[o].cutOff):n;h=!0}else if("visibility"===i){if(x)for(o=x.length;o--;)x[o].style[i]=n;"DIV"===u&&(n="hidden"===n?"-999em":0,Pt||(p[i]=n?"visible":"hidden"),i="top"),p[i]=n,h=!0}else"zIndex"===i?(n&&(p[i]=n),h=!0):ie(i,["x","y","width","height"])!==-1?(this[i]=n,"x"===i||"y"===i?i={x:"left",y:"top"}[i]:n=mt(0,n),this.updateClipping?(this[i]=n,this.updateClipping()):p[i]=n,h=!0):"class"===i&&"DIV"===u?l.className=n:"stroke"===i?(n=g.color(n,l,i),i="strokecolor"):"stroke-width"===i||"strokeWidth"===i?(l.stroked=!!n,i="strokeweight",this[i]=n,r(n)&&(n+="px")):"dashstyle"===i?((l.getElementsByTagName("stroke")[0]||f(g.prepVML(["<stroke/>"]),null,null,l))[i]=n||"solid",this.dashstyle=n,h=!0):"fill"===i?"SPAN"===u?p.color=n:"IMG"!==u&&(l.filled=n!==Et,n=g.color(n,l,i,this),i="fillcolor"):"opacity"===i?h=!0:"shape"===u&&"rotation"===i?(this[i]=l.style[i]=n,l.style.left=-ut(bt(n*wt)+1)+"px",l.style.top=ut(vt(n*wt))+"px"):"translateX"===i||"translateY"===i||"rotation"===i?(this[i]=n,this.updateTransform(),h=!0):"text"===i&&(this.bBox=null,l.innerHTML=n,h=!0);h||(Pt?l[i]=n:d(l,i,n))}return v},clip:function(t){var e,i=this;return t?(e=t.members,l(e,i),e.push(i),i.destroyClip=function(){l(e,i)},t=t.getCSS(i)):(i.destroyClip&&i.destroyClip(),t={clip:Pt?"inherit":"rect(auto)"}),i.css(t)},css:H.prototype.htmlCss,safeRemoveChild:function(t){t.parentNode&&D(t)},destroy:function(){return this.destroyClip&&this.destroyClip(),H.prototype.destroy.apply(this)},on:function(t,e){return this.element["on"+t]=function(){var t=dt.event;t.target=t.srcElement,e(t)},this},cutOffPath:function(t,e){var s,t=t.split(/[ ,]/);return s=t.length,9!==s&&11!==s||(t[s-4]=t[s-2]=i(t[s-2])-10*e),t.join(" ")},shadow:function(t,e,s){var n,o,r,a,h,l,c,d=[],p=this.element,g=this.renderer,m=p.style,x=p.path;if(x&&"string"!=typeof x.value&&(x="x"),h=x,t){for(l=u(t.width,3),c=(t.opacity||.15)/l,n=1;n<=3;n++)a=2*l+1-2*n,s&&(h=this.cutOffPath(x.value,a+.5)),r=['<shape isShadow="true" strokeweight="',a,'" filled="false" path="',h,'" coordsize="10 10" style="',p.style.cssText,'" />'],o=f(g.prepVML(r),null,{left:i(m.left)+u(t.offsetX,1),top:i(m.top)+u(t.offsetY,1)}),s&&(o.cutOff=a+1),r=['<stroke color="',t.color||"black",'" opacity="',c*n,'"/>'],f(g.prepVML(r),null,null,o),e?e.element.appendChild(o):p.parentNode.insertBefore(o,p),d.push(o);this.shadows=d}return this}},me=m(H,me);var xe={Element:me,isIE8:Tt.indexOf("MSIE 8.0")>-1,init:function(t,e,i){var s,n;this.alignedObjects=[],s=this.createElement(Yt),n=s.element,n.style.position="relative",t.appendChild(s.element),this.isVML=!0,this.box=n,this.boxWrapper=s,this.setSize(e,i,!1),ct.namespaces.hcv||(ct.namespaces.add("hcv","urn:schemas-microsoft-com:vml"),(ct.styleSheets.length?ct.styleSheets[0]:ct.createStyleSheet()).cssText+="hcv\\:fill, hcv\\:path, hcv\\:shape, hcv\\:stroke{ behavior:url(#default#VML); display: inline-block; } ")},isHidden:function(){return!this.box.offsetWidth},clipRect:function(e,i,s,o){var r=this.createElement(),a=n(e);return t(r,{members:[],left:(a?e.x:e)+1,top:(a?e.y:i)+1,width:(a?e.width:s)-1,height:(a?e.height:o)-1,getCSS:function(e){var i=e.element,s=i.nodeName,e=e.inverted,n=this.top-("shape"===s?i.offsetTop:0),o=this.left,i=o+this.width,r=n+this.height,n={clip:"rect("+ut(e?o:n)+"px,"+ut(e?r:i)+"px,"+ut(e?i:r)+"px,"+ut(e?n:o)+"px)"};return!e&&Pt&&"DIV"===s&&t(n,{width:i+"px",height:r+"px"}),n},updateClipping:function(){se(r.members,function(t){t.css(r.getCSS(t))})}})},color:function(t,e,i,s){var n,o,r,a=this,h=/^rgba/,l=Et;if(t&&t.linearGradient?r="gradient":t&&t.radialGradient&&(r="pattern"),r){var c,d,p,u,g,m,x,y,v=t.linearGradient||t.radialGradient,b="",t=t.stops,k=[],w=function(){o=['<fill colors="'+k.join(",")+'" opacity="',g,'" o:opacity2="',u,'" type="',r,'" ',b,'focus="100%" method="any" />'],f(a.prepVML(o),null,null,e)};if(p=t[0],y=t[t.length-1],p[0]>0&&t.unshift([0,p[1]]),y[0]<1&&t.push([1,y[1]]),se(t,function(t,e){h.test(t[1])?(n=ge(t[1]),c=n.get("rgb"),d=n.get("a")):(c=t[1],d=1),k.push(100*t[0]+"% "+c),e?(g=d,m=c):(u=d,x=c)}),"fill"===i)if("gradient"===r)i=v.x1||v[0]||0,t=v.y1||v[1]||0,p=v.x2||v[2]||0,v=v.y2||v[3]||0,b='angle="'+(90-180*pt.atan((v-t)/(p-i))/kt)+'"',w();else{var T,l=v.r,S=2*l,L=2*l,P=v.cx,A=v.cy,C=e.radialReference,l=function(){C&&(T=s.getBBox(),P+=(C[0]-T.x)/T.width-.5,A+=(C[1]-T.y)/T.height-.5,S*=C[2]/T.width,L*=C[2]/T.height),b='src="'+U.global.VMLRadialGradientURL+'" size="'+S+","+L+'" origin="0.5,0.5" position="'+P+","+A+'" color2="'+x+'" ',w()};s.added?l():ae(s,"add",l),l=m}else l=c}else h.test(t)&&"IMG"!==e.tagName?(n=ge(t),o=["<",i,' opacity="',n.get("a"),'"/>'],f(this.prepVML(o),null,null,e),l=n.get("rgb")):(l=e.getElementsByTagName(i),l.length&&(l[0].opacity=1,l[0].type="solid"),l=t);return l},prepVML:function(t){var e=this.isIE8,t=t.join("");return e?(t=t.replace("/>",' xmlns="urn:schemas-microsoft-com:vml" />'),t=t.indexOf('style="')===-1?t.replace("/>",' style="display:inline-block;behavior:url(#default#VML);" />'):t.replace('style="','style="display:inline-block;behavior:url(#default#VML);')):t=t.replace("<","<hcv:"),t},text:fe.prototype.html,path:function(e){var i={coordsize:"10 10"};return o(e)?i.d=e:n(e)&&t(i,e),this.createElement("shape").attr(i)},circle:function(t,e,i){var s=this.symbol("circle");return n(t)&&(i=t.r,e=t.y,t=t.x),s.isCircle=!0,s.r=i,s.attr({x:t,y:e})},g:function(t){var e;return t&&(e={className:"highcharts-"+t,"class":"highcharts-"+t}),this.createElement(Yt).attr(e)},image:function(t,e,i,s,n){var o=this.createElement("img").attr({src:t});return arguments.length>1&&o.attr({x:e,y:i,width:s,height:n}),o},rect:function(t,e,i,s,o,r){var a=this.symbol("rect");return a.r=n(t)?t.r:o,a.attr(n(t)?t:a.crisp(r,t,e,mt(i,0),mt(s,0)))},invertChild:function(t,e){var s=e.style;g(t,{flip:"x",left:i(s.width)-1,top:i(s.height)-1,rotation:-90})},symbols:{arc:function(t,e,i,s,n){var o=n.start,r=n.end,a=n.r||i||s,i=n.innerR,s=vt(o),h=bt(o),l=vt(r),c=bt(r);return r-o===0?["x"]:(o=["wa",t-a,e-a,t+a,e+a,t+a*s,e+a*h,t+a*l,e+a*c],n.open&&!i&&o.push("e","M",t,e),o.push("at",t-i,e-i,t+i,e+i,t+i*l,e+i*c,t+i*s,e+i*h,"x","e"),o.isArc=!0,o)},circle:function(t,e,i,s,n){return n&&(i=s=2*n.r),n&&n.isCircle&&(t-=i/2,e-=s/2),["wa",t,e,t+i,e+s,t+i,e+s/2,t+i,e+s/2,"e"]},rect:function(t,e,i,s,n){var o,r=t+i,a=e+s;return c(n)&&n.r?(o=xt(n.r,i,s),r=["M",t+o,e,"L",r-o,e,"wa",r-2*o,e,r,e+2*o,r-o,e,r,e+o,"L",r,a-o,"wa",r-2*o,a-2*o,r,a,r,a-o,r-o,a,"L",t+o,a,"wa",t,a-2*o,t+2*o,a,t+o,a,t,a-o,"L",t,e+o,"wa",t,e,t+2*o,e+2*o,t,e+o,t+o,e,"x","e"]):r=fe.prototype.symbols.square.apply(0,arguments),r}}};Highcharts.VMLRenderer=me=function(){this.init.apply(this,arguments)},me.prototype=e(fe.prototype,xe),j=me}var ye;Bt&&(Highcharts.CanVGRenderer=me=function(){Dt="http://www.w3.org/1999/xhtml"},me.prototype.symbols={},ye=function(){function t(){var t,i=e.length;for(t=0;t<i;t++)e[t]();e=[]}var e=[];return{push:function(i,s){0===e.length&&ee(s,t),e.push(i)}}}(),j=me),R.prototype={addLabel:function(){var e,i=this.axis,s=i.options,n=i.chart,o=i.horiz,a=i.categories,l=i.series[0]&&i.series[0].names,d=this.pos,p=s.labels,g=i.tickPositions,o=o&&a&&!p.step&&!p.staggerLines&&!p.rotation&&n.plotWidth/g.length||!o&&(n.margin[3]||.33*n.chartWidth),f=d===g[0],m=d===g[g.length-1],l=a?u(a[d],l&&l[d],d):d,a=this.label,x=g.info;i.isDatetimeAxis&&x&&(e=s.dateTimeLabelFormats[x.higherRanks[d]||x.unitName]),this.isFirst=f,this.isLast=m,s=i.labelFormatter.call({axis:i,chart:n,isFirst:f,isLast:m,dateTimeLabelFormat:e,value:i.isLog?z(h(l)):l}),d=o&&{width:mt(1,ut(o-2*(p.padding||10)))+"px"},d=t(d,p.style),c(a)?a&&a.attr({text:s}).css(d):(e={align:i.labelAlign},r(p.rotation)&&(e.rotation=p.rotation),o&&p.ellipsis&&(e._clipHeight=i.len/g.length),this.label=c(s)&&p.enabled?n.renderer.text(s,0,0,p.useHTML).attr(e).css(d).add(i.labelGroup):null)},getLabelSize:function(){var t=this.label,e=this.axis;return t?(this.labelBBox=t.getBBox())[e.horiz?"height":"width"]:0},getLabelSides:function(){var t=this.axis,e=this.labelBBox.width,t=e*{left:0,center:.5,right:1}[t.labelAlign]-t.options.labels.x;return[-t,e-t]},handleOverflow:function(t,e){var i=!0,s=this.axis,n=s.chart,o=this.isFirst,r=this.isLast,a=e.x,h=s.reversed,l=s.tickPositions;if(o||r){var c=this.getLabelSides(),d=c[0],c=c[1],n=n.plotLeft,p=n+s.len,l=(s=s.ticks[l[t+(o?1:-1)]])&&s.label.xy&&s.label.xy.x+s.getLabelSides()[o?0:1];o&&!h||r&&h?a+d<n&&(a=n-d,s&&a+c>l&&(i=!1)):a+c>p&&(a=p-c,s&&a+d<l&&(i=!1)),e.x=a}return i},getPosition:function(t,e,i,s){var n=this.axis,o=n.chart,r=s&&o.oldChartHeight||o.chartHeight;return{x:t?n.translate(e+i,null,null,s)+n.transB:n.left+n.offset+(n.opposite?(s&&o.oldChartWidth||o.chartWidth)-n.right-n.left:0),y:t?r-n.bottom+n.offset-(n.opposite?n.height:0):r-n.translate(e+i,null,null,s)-n.transB}},getLabelPosition:function(t,e,i,s,n,o,r,a){var h=this.axis,l=h.transA,d=h.reversed,p=h.staggerLines,u=h.chart.renderer.fontMetrics(n.style.fontSize).b,g=n.rotation,t=t+n.x-(o&&s?o*l*(d?-1:1):0),e=e+n.y-(o&&!s?o*l*(d?1:-1):0);return g&&2===h.side&&(e-=u-u*vt(g*wt)),!c(n.y)&&!g&&(e+=u-i.getBBox().height/2),p&&(e+=r/(a||1)%p*(h.labelOffset/p)),{x:t,y:e}},getMarkPath:function(t,e,i,s,n,o){return o.crispLine(["M",t,e,"L",t+(n?0:-i),e+(n?i:0)],s)},render:function(t,e,i){var s=this.axis,n=s.options,o=s.chart.renderer,r=s.horiz,a=this.type,h=this.label,l=this.pos,c=n.labels,d=this.gridLine,p=a?a+"Grid":"grid",g=a?a+"Tick":"tick",f=n[p+"LineWidth"],m=n[p+"LineColor"],x=n[p+"LineDashStyle"],y=n[g+"Length"],p=n[g+"Width"]||0,v=n[g+"Color"],b=n[g+"Position"],g=this.mark,k=c.step,w=!0,T=s.tickmarkOffset,S=this.getPosition(r,l,T,e),L=S.x,S=S.y,P=r&&L===s.pos+s.len||!r&&S===s.pos?-1:1,A=s.staggerLines;this.isActive=!0,f&&(l=s.getPlotLinePath(l+T,f*P,e,!0),d===V&&(d={stroke:m,"stroke-width":f},x&&(d.dashstyle=x),a||(d.zIndex=1),e&&(d.opacity=0),this.gridLine=d=f?o.path(l).attr(d).add(s.gridGroup):null),!e&&d&&l&&d[this.isNew?"attr":"animate"]({d:l,opacity:i})),p&&y&&("inside"===b&&(y=-y),s.opposite&&(y=-y),e=this.getMarkPath(L,S,y,p*P,r,o),g?g.animate({d:e,opacity:i}):this.mark=o.path(e).attr({stroke:v,"stroke-width":p,opacity:i}).add(s.axisGroup)),h&&!isNaN(L)&&(h.xy=S=this.getLabelPosition(L,S,h,r,c,T,t,k),this.isFirst&&!this.isLast&&!u(n.showFirstLabel,1)||this.isLast&&!this.isFirst&&!u(n.showLastLabel,1)?w=!1:!A&&r&&"justify"===c.overflow&&!this.handleOverflow(t,S)&&(w=!1),k&&t%k&&(w=!1),w&&!isNaN(S.y)?(S.opacity=i,h[this.isNew?"attr":"animate"](S),this.isNew=!1):h.attr("y",-9999))},destroy:function(){M(this,this.axis)}},X.prototype={render:function(){var t,i=this,s=i.axis,n=s.horiz,o=(s.pointRange||0)/2,r=i.options,h=r.label,l=i.label,d=r.width,p=r.to,g=r.from,f=c(g)&&c(p),m=r.value,x=r.dashStyle,y=i.svgElem,v=[],b=r.color,k=r.zIndex,w=r.events,T=s.chart.renderer;if(s.isLog&&(g=a(g),p=a(p),m=a(m)),d)v=s.getPlotLinePath(m,d),o={stroke:b,"stroke-width":d},x&&(o.dashstyle=x);else{if(!f)return;g=mt(g,s.min-o),p=xt(p,s.max+o),v=s.getPlotBandPath(g,p,r),o={fill:b},r.borderWidth&&(o.stroke=r.borderColor,o["stroke-width"]=r.borderWidth)}if(c(k)&&(o.zIndex=k),y)v?y.animate({d:v},null,y.onGetPath):(y.hide(),y.onGetPath=function(){y.show()});else if(v&&v.length&&(i.svgElem=y=T.path(v).attr(o).add(),w))for(t in r=function(t){y.on(t,function(e){w[t].apply(i,[e])})},w)r(t);return h&&c(h.text)&&v&&v.length&&s.width>0&&s.height>0?(h=e({align:n&&f&&"center",x:n?!f&&4:10,verticalAlign:!n&&f&&"middle",y:n?f?16:10:f?6:-4,rotation:n&&!f&&90},h),l||(i.label=l=T.text(h.text,0,0,h.useHTML).attr({align:h.textAlign||h.align,rotation:h.rotation,zIndex:k}).css(h.style).add()),s=[v[1],v[4],u(v[6],v[1])],v=[v[2],v[5],u(v[7],v[2])],n=A(s),f=A(v),l.align(h,!1,{x:n,y:f,width:C(s)-n,height:C(v)-f}),l.show()):l&&l.hide(),i},destroy:function(){l(this.axis.plotLinesAndBands,this),delete this.axis,M(this)}},W.prototype={destroy:function(){M(this,this.axis)},render:function(t){var e=this.options,i=e.format,i=i?b(i,this):e.formatter.call(this);this.label?this.label.attr({text:i,visibility:"hidden"}):this.label=this.axis.chart.renderer.text(i,0,0,e.useHTML).css(e.style).attr({align:this.textAlign,rotation:e.rotation,visibility:"hidden"}).add(t)},setOffset:function(t,e){var i=this.axis,s=i.chart,n=s.inverted,o=this.isNegative,r=i.translate(this.percent?100:this.total,0,0,0,1),i=i.translate(0),i=yt(r-i),a=s.xAxis[0].translate(this.x)+t,h=s.plotHeight,o={x:n?o?r:r-i:a,y:n?h-a-e:o?h-r-i:h-r,width:n?i:e,height:n?e:i};(n=this.label)&&(n.align(this.alignOptions,null,o),o=n.alignAttr,n.attr({visibility:this.options.crop===!1||s.isInsidePlot(o.x,o.y)?It?"inherit":"visible":"hidden"}))}},Y.prototype={defaultOptions:{dateTimeLabelFormats:{millisecond:"%H:%M:%S.%L",second:"%H:%M:%S",minute:"%H:%M",hour:"%H:%M",day:"%e. %b",week:"%e. %b",month:"%b '%y",year:"%Y"},endOnTick:!1,gridLineColor:"#C0C0C0",labels:Qt,lineColor:"#C0D0E0",lineWidth:1,minPadding:.01,maxPadding:.01,minorGridLineColor:"#E0E0E0",minorGridLineWidth:1,minorTickColor:"#A0A0A0",minorTickLength:2,minorTickPosition:"outside",startOfWeek:1,startOnTick:!1,tickColor:"#C0D0E0",tickLength:5,tickmarkPlacement:"between",tickPixelInterval:100,tickPosition:"outside",tickWidth:1,title:{align:"middle",style:{color:"#4d759e",fontWeight:"bold"}},type:"linear"},defaultYAxisOptions:{endOnTick:!0,gridLineWidth:1,tickPixelInterval:72,showLastLabel:!0,labels:{x:-8,y:3},lineWidth:0,maxPadding:.05,minPadding:.05,startOnTick:!0,tickWidth:0,title:{rotation:270,text:"Values"},stackLabels:{enabled:!1,formatter:function(){return x(this.total,-1)},style:Qt.style}},defaultLeftAxisOptions:{labels:{x:-8,y:null},title:{rotation:270}},defaultRightAxisOptions:{labels:{x:8,y:null},title:{rotation:90}},defaultBottomAxisOptions:{labels:{x:0,y:14},title:{rotation:0}},defaultTopAxisOptions:{labels:{x:0,y:-5},title:{rotation:0}},init:function(t,e){var i=e.isX;this.horiz=t.inverted?!i:i,this.xOrY=(this.isXAxis=i)?"x":"y",this.opposite=e.opposite,this.side=this.horiz?this.opposite?0:2:this.opposite?1:3,this.setOptions(e);var s=this.options,n=s.type;this.labelFormatter=s.labels.formatter||this.defaultLabelFormatter,this.userOptions=e,this.minPixelPadding=0,this.chart=t,this.reversed=s.reversed,this.zoomEnabled=s.zoomEnabled!==!1,this.categories=s.categories||"category"===n,this.isLog="logarithmic"===n,this.isDatetimeAxis="datetime"===n,this.isLinked=c(s.linkedTo),this.tickmarkOffset=this.categories&&"between"===s.tickmarkPlacement?.5:0,this.ticks={},this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},this.len=0,this.minRange=this.userMinRange=s.minRange||s.maxZoom,this.range=s.range,this.offset=s.offset||0,this.stacks={},this.oldStacks={},this.stackExtremes={},this.min=this.max=null;var o,s=this.options.events;ie(this,t.axes)===-1&&(t.axes.push(this),t[i?"xAxis":"yAxis"].push(this)),this.series=this.series||[],t.inverted&&i&&this.reversed===V&&(this.reversed=!0),this.removePlotLine=this.removePlotBand=this.removePlotBandOrLine;for(o in s)ae(this,o,s[o]);this.isLog&&(this.val2lin=a,this.lin2val=h)},setOptions:function(t){this.options=e(this.defaultOptions,this.isXAxis?{}:this.defaultYAxisOptions,[this.defaultTopAxisOptions,this.defaultRightAxisOptions,this.defaultBottomAxisOptions,this.defaultLeftAxisOptions][this.side],e(U[this.isXAxis?"xAxis":"yAxis"],t))},update:function(i,s){var n=this.chart,i=n.options[this.xOrY+"Axis"][this.options.index]=e(this.userOptions,i);this.destroy(!0),this._addedPlotLB=this.userMin=this.userMax=V,this.init(n,t(i,{events:V})),n.isDirtyBox=!0,u(s,!0)&&n.redraw()},remove:function(t){var e=this.chart,i=this.xOrY+"Axis";se(this.series,function(t){t.remove(!1)}),l(e.axes,this),l(e[i],this),e.options[i].splice(this.options.index,1),se(e[i],function(t,e){t.options.index=e}),this.destroy(),e.isDirtyBox=!0,u(t,!0)&&e.redraw()},defaultLabelFormatter:function(){var t,e=this.axis,i=this.value,s=e.categories,n=this.dateTimeLabelFormat,o=U.lang.numericSymbols,r=o&&o.length,a=e.options.labels.format,e=e.isLog?i:e.tickInterval;if(a)t=b(a,this);else if(s)t=i;else if(n)t=Z(n,i);else if(r&&e>=1e3)for(;r--&&t===V;)s=Math.pow(1e3,r+1),e>=s&&null!==o[r]&&(t=x(i/s,-1)+o[r]);return t===V&&(t=i>=1e3?x(i,0):x(i,-1)),t},getSeriesExtremes:function(){var t=this,e=t.chart;t.hasVisibleSeries=!1,t.dataMin=t.dataMax=null,t.stackExtremes={},t.buildStacks(),se(t.series,function(i){if(i.visible||!e.options.chart.ignoreHiddenSeries){var s;s=i.options.threshold;var n;t.hasVisibleSeries=!0,t.isLog&&s<=0&&(s=null),t.isXAxis?(s=i.xData,s.length&&(t.dataMin=xt(u(t.dataMin,s[0]),A(s)),t.dataMax=mt(u(t.dataMax,s[0]),C(s)))):(i.getExtremes(),n=i.dataMax,i=i.dataMin,c(i)&&c(n)&&(t.dataMin=xt(u(t.dataMin,i),i),t.dataMax=mt(u(t.dataMax,n),n)),c(s)&&(t.dataMin>=s?(t.dataMin=s,t.ignoreMinPadding=!0):t.dataMax<s&&(t.dataMax=s,t.ignoreMaxPadding=!0)))}})},translate:function(t,e,i,s,n,o){var a=this.len,h=1,l=0,c=s?this.oldTransA:this.transA,s=s?this.oldMin:this.min,d=this.minPixelPadding,n=(this.options.ordinal||this.isLog&&n)&&this.lin2val;return c||(c=this.transA),i&&(h*=-1,l=a),this.reversed&&(h*=-1,l-=h*a),e?(t=t*h+l,t-=d,t=t/c+s,n&&(t=this.lin2val(t))):(n&&(t=this.val2lin(t)),"between"===o&&(o=.5),t=h*(t-s)*c+l+h*d+(r(o)?c*o*this.pointRange:0)),t},toPixels:function(t,e){return this.translate(t,!1,!this.horiz,null,!0)+(e?0:this.pos)},toValue:function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,null,!0)},getPlotLinePath:function(t,e,i,s){var n,o,r,a,h=this.chart,l=this.left,c=this.top,t=this.translate(t,null,null,i),d=i&&h.oldChartHeight||h.chartHeight,p=i&&h.oldChartWidth||h.chartWidth;return n=this.transB,i=o=ut(t+n),n=r=ut(d-t-n),isNaN(t)?a=!0:this.horiz?(n=c,r=d-this.bottom,(i<l||i>l+this.width)&&(a=!0)):(i=l,o=p-this.right,(n<c||n>c+this.height)&&(a=!0)),a&&!s?null:h.renderer.crispLine(["M",i,n,"L",o,r],e||0)},getPlotBandPath:function(t,e){var i=this.getPlotLinePath(e),s=this.getPlotLinePath(t);return s&&i?s.push(i[4],i[5],i[1],i[2]):s=null,s},getLinearTickPositions:function(t,e,i){for(var s,e=z(gt(e/t)*t),i=z(ft(i/t)*t),n=[];e<=i&&(n.push(e),e=z(e+t),e!==s);)s=e;return n},getLogTickPositions:function(t,e,i,s){var n=this.options,o=this.len,r=[];if(s||(this._minorAutoInterval=null),t>=.5)t=ut(t),r=this.getLinearTickPositions(t,e,i);else if(t>=.08)for(var l,c,d,p,g,o=gt(e),n=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9];o<i+1&&!g;o++)for(c=n.length,l=0;l<c&&!g;l++)d=a(h(o)*n[l]),d>e&&(!s||p<=i)&&r.push(p),p>i&&(g=!0),p=d;else e=h(e),i=h(i),t=n[s?"minorTickInterval":"tickInterval"],t=u("auto"===t?null:t,this._minorAutoInterval,(i-e)*(n.tickPixelInterval/(s?5:1))/((s?o/this.tickPositions.length:o)||1)),t=w(t,null,k(t)),r=re(this.getLinearTickPositions(t,e,i),a),s||(this._minorAutoInterval=t/5);return s||(this.tickInterval=t),r},getMinorTickPositions:function(){var t,e=this.options,i=this.tickPositions,s=this.minorTickInterval,n=[];if(this.isLog)for(t=i.length,e=1;e<t;e++)n=n.concat(this.getLogTickPositions(s,i[e-1],i[e],!0));else if(this.isDatetimeAxis&&"auto"===e.minorTickInterval)n=n.concat(S(T(s),this.min,this.max,e.startOfWeek)),n[0]<this.min&&n.shift();else for(i=this.min+(i[0]-this.min)%s;i<=this.max;i+=s)n.push(i);return n},adjustForMinRange:function(){var t,e,i,s,n,o,r=this.options,a=this.min,h=this.max,l=this.dataMax-this.dataMin>=this.minRange;if(this.isXAxis&&this.minRange===V&&!this.isLog&&(c(r.min)||c(r.max)?this.minRange=null:(se(this.series,function(t){for(n=t.xData,i=o=t.xIncrement?1:n.length-1;i>0;i--)s=n[i]-n[i-1],(e===V||s<e)&&(e=s)}),this.minRange=xt(5*e,this.dataMax-this.dataMin))),h-a<this.minRange){var d=this.minRange;t=(d-h+a)/2,t=[a-t,u(r.min,a-t)],l&&(t[2]=this.dataMin),a=C(t),h=[a+d,u(r.max,a+d)],l&&(h[2]=this.dataMax),h=A(h),h-a<d&&(t[0]=h-d,t[1]=u(r.min,h-d),a=C(t))}this.min=a,this.max=h},setAxisTranslation:function(t){var e,i=this.max-this.min,n=0,o=0,r=0,a=this.linkedParent,h=this.transA;this.isXAxis&&(a?(o=a.minPointOffset,r=a.pointRangePadding):se(this.series,function(t){var a=t.pointRange,h=t.options.pointPlacement,l=t.closestPointRange;a>i&&(a=0),n=mt(n,a),o=mt(o,s(h)?0:a/2),r=mt(r,"on"===h?0:a),!t.noSharedTooltip&&c(l)&&(e=c(e)?xt(e,l):l)}),a=this.ordinalSlope&&e?this.ordinalSlope/e:1,this.minPointOffset=o*=a,this.pointRangePadding=r*=a,this.pointRange=xt(n,i),this.closestPointRange=e),t&&(this.oldTransA=h),this.translationSlope=this.transA=h=this.len/(i+r||1),this.transB=this.horiz?this.left:this.bottom,this.minPixelPadding=h*o},setTickPositions:function(t){var e,i=this,s=i.chart,n=i.options,o=i.isLog,r=i.isDatetimeAxis,h=i.isXAxis,l=i.isLinked,d=i.options.tickPositioner,p=n.maxPadding,g=n.minPadding,f=n.tickInterval,m=n.minTickInterval,x=n.tickPixelInterval,y=i.categories;l?(i.linkedParent=s[h?"xAxis":"yAxis"][n.linkedTo],s=i.linkedParent.getExtremes(),i.min=u(s.min,s.dataMin),i.max=u(s.max,s.dataMax),n.type!==i.linkedParent.options.type&&I(11,1)):(i.min=u(i.userMin,n.min,i.dataMin),i.max=u(i.userMax,n.max,i.dataMax)),o&&(!t&&xt(i.min,u(i.dataMin,i.min))<=0&&I(10,1),i.min=z(a(i.min)),i.max=z(a(i.max))),i.range&&(i.userMin=i.min=mt(i.min,i.max-i.range),i.userMax=i.max,t)&&(i.range=null),i.beforePadding&&i.beforePadding(),i.adjustForMinRange(),!y&&!i.usePercentage&&!l&&c(i.min)&&c(i.max)&&(s=i.max-i.min)&&(c(n.min)||c(i.userMin)||!g||!(i.dataMin<0)&&i.ignoreMinPadding||(i.min-=s*g),c(n.max)||c(i.userMax)||!p||!(i.dataMax>0)&&i.ignoreMaxPadding||(i.max+=s*p)),i.min===i.max||void 0===i.min||void 0===i.max?i.tickInterval=1:l&&!f&&x===i.linkedParent.options.tickPixelInterval?i.tickInterval=i.linkedParent.tickInterval:(i.tickInterval=u(f,y?1:(i.max-i.min)*x/mt(i.len,x)),!c(f)&&i.len<x&&!this.isRadial&&(e=!0,i.tickInterval/=4)),h&&!t&&se(i.series,function(t){t.processData(i.min!==i.oldMin||i.max!==i.oldMax)}),i.setAxisTranslation(!0),i.beforeSetTickPositions&&i.beforeSetTickPositions(),i.postProcessTickInterval&&(i.tickInterval=i.postProcessTickInterval(i.tickInterval)),i.pointRange&&(i.tickInterval=mt(i.pointRange,i.tickInterval)),!f&&i.tickInterval<m&&(i.tickInterval=m),r||o||f||(i.tickInterval=w(i.tickInterval,null,k(i.tickInterval),n)),i.minorTickInterval="auto"===n.minorTickInterval&&i.tickInterval?i.tickInterval/5:n.minorTickInterval,i.tickPositions=t=n.tickPositions?[].concat(n.tickPositions):d&&d.apply(i,[i.min,i.max]),t||(!i.ordinalPositions&&(i.max-i.min)/i.tickInterval>mt(2*i.len,200)&&I(19,!0),t=r?(i.getNonLinearTimeTicks||S)(T(i.tickInterval,n.units),i.min,i.max,n.startOfWeek,i.ordinalPositions,i.closestPointRange,!0):o?i.getLogTickPositions(i.tickInterval,i.min,i.max):i.getLinearTickPositions(i.tickInterval,i.min,i.max),e&&t.splice(1,t.length-2),i.tickPositions=t),l||(o=t[0],r=t[t.length-1],l=i.minPointOffset||0,n.startOnTick?i.min=o:i.min-l>o&&t.shift(),n.endOnTick?i.max=r:i.max+l<r&&t.pop(),1===t.length&&(i.min-=.001,i.max+=.001))},setMaxTicks:function(){var t=this.chart,e=t.maxTicks||{},i=this.tickPositions,s=this._maxTicksKey=[this.xOrY,this.pos,this.len].join("-");!this.isLinked&&!this.isDatetimeAxis&&i&&i.length>(e[s]||0)&&this.options.alignTicks!==!1&&(e[s]=i.length),t.maxTicks=e},adjustTickAmount:function(){var t=this._maxTicksKey,e=this.tickPositions,i=this.chart.maxTicks;if(i&&i[t]&&!this.isDatetimeAxis&&!this.categories&&!this.isLinked&&this.options.alignTicks!==!1){var s=this.tickAmount,n=e.length;if(this.tickAmount=t=i[t],n<t){for(;e.length<t;)e.push(z(e[e.length-1]+this.tickInterval));this.transA*=(n-1)/(t-1),this.max=e[e.length-1]}c(s)&&t!==s&&(this.isDirty=!0)}},setScale:function(){var t,e,i,s,n=this.stacks;if(this.oldMin=this.min,this.oldMax=this.max,this.oldAxisLength=this.len,this.setAxisSize(),s=this.len!==this.oldAxisLength,se(this.series,function(t){(t.isDirtyData||t.isDirty||t.xAxis.isDirty)&&(i=!0)}),s||i||this.isLinked||this.forceRedraw||this.userMin!==this.oldUserMin||this.userMax!==this.oldUserMax){if(!this.isXAxis)for(t in n)delete n[t];this.forceRedraw=!1,this.getSeriesExtremes(),this.setTickPositions(),this.oldUserMin=this.userMin,this.oldUserMax=this.userMax,this.isDirty||(this.isDirty=s||this.min!==this.oldMin||this.max!==this.oldMax)}else if(!this.isXAxis){this.oldStacks&&(n=this.stacks=this.oldStacks);for(t in n)for(e in n[t])n[t][e].cum=n[t][e].total}this.setMaxTicks()},setExtremes:function(e,i,s,n,o){var r=this,a=r.chart,s=u(s,!0),o=t(o,{min:e,max:i});le(r,"setExtremes",o,function(){r.userMin=e,r.userMax=i,r.eventArgs=o,r.isDirtyExtremes=!0,s&&a.redraw(n)})},zoom:function(t,e){return this.allowZoomOutside||(c(this.dataMin)&&t<=this.dataMin&&(t=V),c(this.dataMax)&&e>=this.dataMax&&(e=V)),this.displayBtn=t!==V||e!==V,this.setExtremes(t,e,!1,V,{trigger:"zoom"}),!0},setAxisSize:function(){var t,e,i=this.chart,s=this.options,n=s.offsetLeft||0,o=s.offsetRight||0,r=this.horiz;this.left=e=u(s.left,i.plotLeft+n),this.top=t=u(s.top,i.plotTop),this.width=n=u(s.width,i.plotWidth-n+o),this.height=s=u(s.height,i.plotHeight),this.bottom=i.chartHeight-s-t,this.right=i.chartWidth-n-e,this.len=mt(r?n:s,0),this.pos=r?e:t},getExtremes:function(){var t=this.isLog;return{min:t?z(h(this.min)):this.min,max:t?z(h(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},getThreshold:function(t){var e=this.isLog,i=e?h(this.min):this.min,e=e?h(this.max):this.max;return i>t||null===t?t=i:e<t&&(t=e),this.translate(t,0,1,0,1)},addPlotBand:function(t){this.addPlotBandOrLine(t,"plotBands")},addPlotLine:function(t){this.addPlotBandOrLine(t,"plotLines")},addPlotBandOrLine:function(t,e){var i=new X(this,t).render(),s=this.userOptions;return i&&(e&&(s[e]=s[e]||[],s[e].push(t)),this.plotLinesAndBands.push(i)),i},autoLabelAlign:function(t){return t=(u(t,0)-90*this.side+720)%360,t>15&&t<165?"right":t>195&&t<345?"left":"center"},getOffset:function(){var t,e,i,s,n,o,r,a=this,h=a.chart,l=h.renderer,d=a.options,p=a.tickPositions,g=a.ticks,f=a.horiz,m=a.side,x=h.inverted?[1,0,3,2][m]:m,y=0,v=0,b=d.title,k=d.labels,w=0,T=h.axisOffset,S=h.clipOffset,L=[-1,1,1,-1][m],P=1,A=u(k.maxStaggerLines,5);if(a.hasData=t=a.hasVisibleSeries||c(a.min)&&c(a.max)&&!!p,a.showAxis=h=t||u(d.showEmpty,!0),a.staggerLines=a.horiz&&k.staggerLines,a.axisGroup||(a.gridGroup=l.g("grid").attr({zIndex:d.gridZIndex||1}).add(),a.axisGroup=l.g("axis").attr({zIndex:d.zIndex||2}).add(),a.labelGroup=l.g("axis-labels").attr({zIndex:k.zIndex||7}).add()),t||a.isLinked){if(a.labelAlign=u(k.align||a.autoLabelAlign(k.rotation)),se(p,function(t){g[t]?g[t].addLabel():g[t]=new R(a,t)}),a.horiz&&!a.staggerLines&&A&&!k.rotation){for(i=a.reversed?[].concat(p).reverse():p;P<A;){for(t=[],s=!1,k=0;k<i.length;k++)n=i[k],o=(o=g[n].label&&g[n].label.getBBox())?o.width:0,r=k%P,o&&(n=a.translate(n),t[r]!==V&&n<t[r]&&(s=!0),t[r]=n+o);if(!s)break;P++}P>1&&(a.staggerLines=P)}se(p,function(t){0!==m&&2!==m&&{1:"left",3:"right"}[m]!==a.labelAlign||(w=mt(g[t].getLabelSize(),w))}),a.staggerLines&&(w*=a.staggerLines,a.labelOffset=w)}else for(i in g)g[i].destroy(),delete g[i];b&&b.text&&b.enabled!==!1&&(a.axisTitle||(a.axisTitle=l.text(b.text,0,0,b.useHTML).attr({zIndex:7,rotation:b.rotation||0,align:b.textAlign||{low:"left",middle:"center",high:"right"}[b.align]}).css(b.style).add(a.axisGroup),a.axisTitle.isNew=!0),h&&(y=a.axisTitle.getBBox()[f?"height":"width"],v=u(b.margin,f?5:10),e=b.offset),a.axisTitle[h?"show":"hide"]()),a.offset=L*u(d.offset,T[m]),a.axisTitleMargin=u(e,w+v+(2!==m&&w&&L*d.labels[f?"y":"x"])),T[m]=mt(T[m],a.axisTitleMargin+y+L*a.offset),S[x]=mt(S[x],2*gt(d.lineWidth/2))},getLinePath:function(t){var e=this.chart,i=this.opposite,s=this.offset,n=this.horiz,o=this.left+(i?this.width:0)+s,s=e.chartHeight-this.bottom-(i?this.height:0)+s;return i&&(t*=-1),
e.renderer.crispLine(["M",n?this.left:o,n?s:this.top,"L",n?e.chartWidth-this.right:o,n?s:e.chartHeight-this.bottom],t)},getTitlePosition:function(){var t=this.horiz,e=this.left,s=this.top,n=this.len,o=this.options.title,r=t?e:s,a=this.opposite,h=this.offset,l=i(o.style.fontSize||12),n={low:r+(t?0:n),middle:r+n/2,high:r+(t?n:0)}[o.align],e=(t?s+this.height:e)+(t?1:-1)*(a?-1:1)*this.axisTitleMargin+(2===this.side?l:0);return{x:t?n:e+(a?this.width:0)+h+(o.x||0),y:t?e-(a?this.height:0)+h:n+(o.y||0)}},render:function(){var t,e=this,i=e.chart,s=i.renderer,n=e.options,o=e.isLog,r=e.isLinked,a=e.tickPositions,l=e.axisTitle,d=e.stacks,p=e.ticks,u=e.minorTicks,g=e.alternateBands,f=n.stackLabels,m=n.alternateGridColor,x=e.tickmarkOffset,y=n.lineWidth,v=i.hasRendered&&c(e.oldMin)&&!isNaN(e.oldMin);t=e.hasData;var b,k,w=e.showAxis;if(se([p,u,g],function(t){for(var e in t)t[e].isActive=!1}),(t||r)&&(e.minorTickInterval&&!e.categories&&se(e.getMinorTickPositions(),function(t){u[t]||(u[t]=new R(e,t,"minor")),v&&u[t].isNew&&u[t].render(null,!0),u[t].render(null,!1,1)}),a.length&&(se(a.slice(1).concat([a[0]]),function(t,i){i=i===a.length-1?0:i+1,(!r||t>=e.min&&t<=e.max)&&(p[t]||(p[t]=new R(e,t)),v&&p[t].isNew&&p[t].render(i,!0),p[t].render(i,!1,1))}),x&&0===e.min&&(p[-1]||(p[-1]=new R(e,(-1),null,(!0))),p[-1].render(-1))),m&&se(a,function(t,i){i%2===0&&t<e.max&&(g[t]||(g[t]=new X(e)),b=t+x,k=a[i+1]!==V?a[i+1]+x:e.max,g[t].options={from:o?h(b):b,to:o?h(k):k,color:m},g[t].render(),g[t].isActive=!0)}),e._addedPlotLB||(se((n.plotLines||[]).concat(n.plotBands||[]),function(t){e.addPlotBandOrLine(t)}),e._addedPlotLB=!0)),se([p,u,g],function(t){var e,s,n=[],o=K?K.duration||500:0,r=function(){for(s=n.length;s--;)t[n[s]]&&!t[n[s]].isActive&&(t[n[s]].destroy(),delete t[n[s]])};for(e in t)t[e].isActive||(t[e].render(e,!1,0),t[e].isActive=!1,n.push(e));t!==g&&i.hasRendered&&o?o&&setTimeout(r,o):r()}),y&&(t=e.getLinePath(y),e.axisLine?e.axisLine.animate({d:t}):e.axisLine=s.path(t).attr({stroke:n.lineColor,"stroke-width":y,zIndex:7}).add(e.axisGroup),e.axisLine[w?"show":"hide"]()),l&&w&&(l[l.isNew?"attr":"animate"](e.getTitlePosition()),l.isNew=!1),f&&f.enabled){var T,S,n=e.stackTotalGroup;n||(e.stackTotalGroup=n=s.g("stack-labels").attr({visibility:"visible",zIndex:6}).add()),n.translate(i.plotLeft,i.plotTop);for(T in d)for(S in s=d[T])s[S].render(n)}e.isDirty=!1},removePlotBandOrLine:function(t){for(var e=this.plotLinesAndBands,i=this.options,s=this.userOptions,n=e.length;n--;)e[n].id===t&&e[n].destroy();se([i.plotLines||[],s.plotLines||[],i.plotBands||[],s.plotBands||[]],function(e){for(n=e.length;n--;)e[n].id===t&&l(e,e[n])})},setTitle:function(t,e){this.update({title:t},e)},redraw:function(){var t=this.chart.pointer;t.reset&&t.reset(!0),this.render(),se(this.plotLinesAndBands,function(t){t.render()}),se(this.series,function(t){t.isDirty=!0})},buildStacks:function(){var t=this.series,e=t.length;if(!this.isXAxis){for(;e--;)t[e].setStackedPoints();if(this.usePercentage)for(e=0;e<t.length;e++)t[e].setPercentStacks()}},setCategories:function(t,e){this.update({categories:t},e)},destroy:function(t){var e,i=this,s=i.stacks,n=i.plotLinesAndBands;t||he(i);for(e in s)M(s[e]),s[e]=null;for(se([i.ticks,i.minorTicks,i.alternateBands],function(t){M(t)}),t=n.length;t--;)n[t].destroy();se("stackTotalGroup,axisLine,axisGroup,gridGroup,labelGroup,axisTitle".split(","),function(t){i[t]&&(i[t]=i[t].destroy())})}},E.prototype={init:function(t,e){var s=e.borderWidth,n=e.style,o=i(n.padding);this.chart=t,this.options=e,this.crosshairs=[],this.now={x:0,y:0},this.isHidden=!0,this.label=t.renderer.label("",0,0,e.shape,null,null,e.useHTML,null,"tooltip").attr({padding:o,fill:e.backgroundColor,"stroke-width":s,r:e.borderRadius,zIndex:8}).css(n).css({padding:0}).add().attr({y:-999}),Bt||this.label.shadow(e.shadow),this.shared=e.shared},destroy:function(){se(this.crosshairs,function(t){t&&t.destroy()}),this.label&&(this.label=this.label.destroy()),clearTimeout(this.hideTimer),clearTimeout(this.tooltipTimeout)},move:function(e,i,s,n){var o=this,r=o.now,a=o.options.animation!==!1&&!o.isHidden;t(r,{x:a?(2*r.x+e)/3:e,y:a?(r.y+i)/2:i,anchorX:a?(2*r.anchorX+s)/3:s,anchorY:a?(r.anchorY+n)/2:n}),o.label.attr(r),a&&(yt(e-r.x)>1||yt(i-r.y)>1)&&(clearTimeout(this.tooltipTimeout),this.tooltipTimeout=setTimeout(function(){o&&o.move(e,i,s,n)},32))},hide:function(){var t,e=this;clearTimeout(this.hideTimer),this.isHidden||(t=this.chart.hoverPoints,this.hideTimer=setTimeout(function(){e.label.fadeOut(),e.isHidden=!0},u(this.options.hideDelay,500)),t&&se(t,function(t){t.setState()}),this.chart.hoverPoints=null)},hideCrosshairs:function(){se(this.crosshairs,function(t){t&&t.hide()})},getAnchor:function(t,e){var i,s,n=this.chart,o=n.inverted,r=n.plotTop,a=0,h=0,t=p(t);return i=t[0].tooltipPos,this.followPointer&&e&&(e.chartX===V&&(e=n.pointer.normalize(e)),i=[e.chartX-n.plotLeft,e.chartY-r]),i||(se(t,function(t){s=t.series.yAxis,a+=t.plotX,h+=(t.plotLow?(t.plotLow+t.plotHigh)/2:t.plotY)+(!o&&s?s.top-r:0)}),a/=t.length,h/=t.length,i=[o?n.plotWidth-h:a,this.shared&&!o&&t.length>1&&e?e.chartY-r:o?n.plotHeight-a:h]),re(i,ut)},getPosition:function(t,e,i){var s,n=this.chart,o=n.plotLeft,r=n.plotTop,a=n.plotWidth,h=n.plotHeight,l=u(this.options.distance,12),c=i.plotX,i=i.plotY,n=c+o+(n.inverted?l:-t-l),d=i-e+r+15;return n<7&&(n=o+mt(c,0)+l),n+t>o+a&&(n-=n+t-(o+a),d=i-e+r-l,s=!0),d<r+5&&(d=r+5,s&&i>=d&&i<=d+e&&(d=i+r+l)),d+e>r+h&&(d=mt(r,r+h-e-l)),{x:n,y:d}},defaultFormatter:function(t){var e,i=this.points||p(this),s=i[0].series;return e=[s.tooltipHeaderFormatter(i[0])],se(i,function(t){s=t.series,e.push(s.tooltipFormatter&&s.tooltipFormatter(t)||t.point.tooltipFormatter(s.tooltipOptions.pointFormat))}),e.push(t.options.footerFormat||""),e.join("")},refresh:function(t,e){var i,s,n,o=this.chart,r=this.label,h=this.options,l={},c=[];n=h.formatter||this.defaultFormatter;var d,l=o.hoverPoints,g=h.crosshairs,f=this.shared;if(clearTimeout(this.hideTimer),this.followPointer=p(t)[0].series.tooltipOptions.followPointer,s=this.getAnchor(t,e),i=s[0],s=s[1],!f||t.series&&t.series.noSharedTooltip?l=t.getLabelConfig():(o.hoverPoints=t,l&&se(l,function(t){t.setState()}),se(t,function(t){t.setState("hover"),c.push(t.getLabelConfig())}),l={x:t[0].category,y:t[0].y},l.points=c,t=t[0]),n=n.call(l,this),l=t.series,n===!1?this.hide():(this.isHidden&&(pe(r),r.attr("opacity",1).show()),r.attr({text:n}),d=h.borderColor||t.color||l.color||"#606060",r.attr({stroke:d}),this.updatePosition({plotX:i,plotY:s}),this.isHidden=!1),g)for(g=p(g),r=g.length;r--;)f=t.series,h=f[r?"yAxis":"xAxis"],g[r]&&h&&(l=r?u(t.stackY,t.y):t.x,h.isLog&&(l=a(l)),1===r&&f.modifyValue&&(l=f.modifyValue(l)),h=h.getPlotLinePath(l,1),this.crosshairs[r]?this.crosshairs[r].attr({d:h,visibility:"visible"}):(l={"stroke-width":g[r].width||1,stroke:g[r].color||"#C0C0C0",zIndex:g[r].zIndex||2},g[r].dashStyle&&(l.dashstyle=g[r].dashStyle),this.crosshairs[r]=o.renderer.path(h).attr(l).add()));le(o,"tooltipRefresh",{text:n,x:i+o.plotLeft,y:s+o.plotTop,borderColor:d})},updatePosition:function(t){var e=this.chart,i=this.label,i=(this.options.positioner||this.getPosition).call(this,i.width,i.height,t);this.move(ut(i.x),ut(i.y),t.plotX+e.plotLeft,t.plotY+e.plotTop)}},G.prototype={init:function(t,e){var i,s=e.chart,n=s.events,o=Bt?"":s.zoomType,s=t.inverted;this.options=e,this.chart=t,this.zoomX=i=/x/.test(o),this.zoomY=o=/y/.test(o),this.zoomHor=i&&!s||o&&s,this.zoomVert=o&&!s||i&&s,this.runChartClick=n&&!!n.click,this.pinchDown=[],this.lastValidTouch={},e.tooltip.enabled&&(t.tooltip=new E(t,e.tooltip)),this.setDOMEvents()},normalize:function(e,i){var s,n,e=e||dt.event;return e.target||(e.target=e.srcElement),e=ce(e),n=e.touches?e.touches.item(0):e,i||(this.chartPosition=i=oe(this.chart.container)),n.pageX===V?(s=mt(e.x,e.clientX-i.left),n=e.y):(s=n.pageX-i.left,n=n.pageY-i.top),t(e,{chartX:ut(s),chartY:ut(n)})},getCoordinates:function(t){var e={xAxis:[],yAxis:[]};return se(this.chart.axes,function(i){e[i.isXAxis?"xAxis":"yAxis"].push({axis:i,value:i.toValue(t[i.horiz?"chartX":"chartY"])})}),e},getIndex:function(t){var e=this.chart;return e.inverted?e.plotHeight+e.plotTop-t.chartY:t.chartX-e.plotLeft},runPointActions:function(t){var e,i,s,n=this.chart,o=n.series,r=n.tooltip,a=n.hoverPoint,h=n.hoverSeries,l=n.chartWidth,c=this.getIndex(t);if(r&&this.options.tooltip.shared&&(!h||!h.noSharedTooltip)){for(e=[],i=o.length,s=0;s<i;s++)o[s].visible&&o[s].options.enableMouseTracking!==!1&&!o[s].noSharedTooltip&&o[s].tooltipPoints.length&&(n=o[s].tooltipPoints[c])&&n.series&&(n._dist=yt(c-n.clientX),l=xt(l,n._dist),e.push(n));for(i=e.length;i--;)e[i]._dist>l&&e.splice(i,1);e.length&&e[0].clientX!==this.hoverX&&(r.refresh(e,t),this.hoverX=e[0].clientX)}h&&h.tracker?(n=h.tooltipPoints[c])&&n!==a&&n.onMouseOver(t):r&&r.followPointer&&!r.isHidden&&(t=r.getAnchor([{}],t),r.updatePosition({plotX:t[0],plotY:t[1]}))},reset:function(t){var e=this.chart,i=e.hoverSeries,s=e.hoverPoint,n=e.tooltip,e=n&&n.shared?e.hoverPoints:s;(t=t&&n&&e)&&p(e)[0].plotX===V&&(t=!1),t?n.refresh(e):(s&&s.onMouseOut(),i&&i.onMouseOut(),n&&(n.hide(),n.hideCrosshairs()),this.hoverX=null)},scaleGroups:function(t,e){var i,s=this.chart;se(s.series,function(n){i=t||n.getPlotBox(),n.xAxis&&n.xAxis.zoomEnabled&&(n.group.attr(i),n.markerGroup&&(n.markerGroup.attr(i),n.markerGroup.clip(e?s.clipRect:null)),n.dataLabelsGroup&&n.dataLabelsGroup.attr(i))}),s.clipRect.attr(e||s.clipBox)},pinchTranslateDirection:function(t,e,i,s,n,o,r){var a,h,l,c=this.chart,d=t?"x":"y",p=t?"X":"Y",u="chart"+p,g=t?"width":"height",f=c["plot"+(t?"Left":"Top")],m=1,x=c.inverted,y=c.bounds[t?"h":"v"],v=1===e.length,b=e[0][u],k=i[0][u],w=!v&&e[1][u],T=!v&&i[1][u],i=function(){!v&&yt(b-w)>20&&(m=yt(k-T)/yt(b-w)),h=(f-k)/m+b,a=c["plot"+(t?"Width":"Height")]/m};i(),e=h,e<y.min?(e=y.min,l=!0):e+a>y.max&&(e=y.max-a,l=!0),l?(k-=.8*(k-r[d][0]),v||(T-=.8*(T-r[d][1])),i()):r[d]=[k,T],x||(o[d]=h-f,o[g]=a),o=x?1/m:m,n[g]=a,n[d]=e,s[x?t?"scaleY":"scaleX":"scale"+p]=m,s["translate"+p]=o*f+(k-o*b)},pinch:function(e){var i=this,s=i.chart,n=i.pinchDown,o=s.tooltip&&s.tooltip.options.followTouchMove,r=e.touches,a=r.length,h=i.lastValidTouch,l=i.zoomHor||i.pinchHor,c=i.zoomVert||i.pinchVert,d=l||c,p=i.selectionMarker,u={},g=1===a&&(i.inClass(e.target,"highcharts-tracker")&&s.runTrackerClick||s.runChartClick),f={};(d||o)&&!g&&e.preventDefault(),re(r,function(t){return i.normalize(t)}),"touchstart"===e.type?(se(r,function(t,e){n[e]={chartX:t.chartX,chartY:t.chartY}}),h.x=[n[0].chartX,n[1]&&n[1].chartX],h.y=[n[0].chartY,n[1]&&n[1].chartY],se(s.axes,function(t){if(t.zoomEnabled){var e=s.bounds[t.horiz?"h":"v"],i=t.minPixelPadding,n=t.toPixels(t.dataMin),o=t.toPixels(t.dataMax),r=xt(n,o),n=mt(n,o);e.min=xt(t.pos,r-i),e.max=mt(t.pos+t.len,n+i)}})):n.length&&(p||(i.selectionMarker=p=t({destroy:Xt},s.plotBox)),l&&i.pinchTranslateDirection(!0,n,r,u,p,f,h),c&&i.pinchTranslateDirection(!1,n,r,u,p,f,h),i.hasPinched=d,i.scaleGroups(u,f),!d&&o&&1===a&&this.runPointActions(i.normalize(e)))},dragStart:function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=this.mouseDownX=t.chartX,e.mouseDownY=this.mouseDownY=t.chartY},drag:function(t){var e,i=this.chart,s=i.options.chart,n=t.chartX,o=t.chartY,r=this.zoomHor,a=this.zoomVert,h=i.plotLeft,l=i.plotTop,c=i.plotWidth,d=i.plotHeight,p=this.mouseDownX,u=this.mouseDownY;n<h?n=h:n>h+c&&(n=h+c),o<l?o=l:o>l+d&&(o=l+d),this.hasDragged=Math.sqrt(Math.pow(p-n,2)+Math.pow(u-o,2)),this.hasDragged>10&&(e=i.isInsidePlot(p-h,u-l),i.hasCartesianSeries&&(this.zoomX||this.zoomY)&&e&&!this.selectionMarker&&(this.selectionMarker=i.renderer.rect(h,l,r?1:c,a?1:d,0).attr({fill:s.selectionMarkerFill||"rgba(69,114,167,0.25)",zIndex:7}).add()),this.selectionMarker&&r&&(n-=p,this.selectionMarker.attr({width:yt(n),x:(n>0?0:n)+p})),this.selectionMarker&&a&&(n=o-u,this.selectionMarker.attr({height:yt(n),y:(n>0?0:n)+u})),e&&!this.selectionMarker&&s.panning&&i.pan(t,s.panning))},drop:function(e){var i=this.chart,s=this.hasPinched;if(this.selectionMarker){var n,o={xAxis:[],yAxis:[],originalEvent:e.originalEvent||e},r=this.selectionMarker,a=r.x,h=r.y;(this.hasDragged||s)&&(se(i.axes,function(t){if(t.zoomEnabled){var e=t.horiz,i=t.toValue(e?a:h),e=t.toValue(e?a+r.width:h+r.height);!isNaN(i)&&!isNaN(e)&&(o[t.xOrY+"Axis"].push({axis:t,min:xt(i,e),max:mt(i,e)}),n=!0)}}),n&&le(i,"selection",o,function(e){i.zoom(t(e,s?{animation:!1}:null))})),this.selectionMarker=this.selectionMarker.destroy(),s&&this.scaleGroups()}i&&(g(i.container,{cursor:i._cursor}),i.cancelClick=this.hasDragged>10,i.mouseIsDown=this.hasDragged=this.hasPinched=!1,this.pinchDown=[])},onContainerMouseDown:function(t){t=this.normalize(t),t.preventDefault&&t.preventDefault(),this.dragStart(t)},onDocumentMouseUp:function(t){this.drop(t)},onDocumentMouseMove:function(t){var e=this.chart,i=this.chartPosition,s=e.hoverSeries,t=this.normalize(t,i);i&&s&&!this.inClass(t.target,"highcharts-tracker")&&!e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)&&this.reset()},onContainerMouseLeave:function(){this.reset(),this.chartPosition=null},onContainerMouseMove:function(t){var e=this.chart,t=this.normalize(t);t.returnValue=!1,"mousedown"===e.mouseIsDown&&this.drag(t),(this.inClass(t.target,"highcharts-tracker")||e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop))&&!e.openMenu&&this.runPointActions(t)},inClass:function(t,e){for(var i;t;){if(i=d(t,"class")){if(i.indexOf(e)!==-1)return!0;if(i.indexOf("highcharts-container")!==-1)return!1}t=t.parentNode}},onTrackerMouseOut:function(t){var e=this.chart.hoverSeries;!e||e.options.stickyTracking||this.inClass(t.toElement||t.relatedTarget,"highcharts-tooltip")||e.onMouseOut()},onContainerClick:function(e){var i,s,n,o=this.chart,r=o.hoverPoint,a=o.plotLeft,h=o.plotTop,l=o.inverted,e=this.normalize(e);e.cancelBubble=!0,o.cancelClick||(r&&this.inClass(e.target,"highcharts-tracker")?(i=this.chartPosition,s=r.plotX,n=r.plotY,t(r,{pageX:i.left+a+(l?o.plotWidth-n:s),pageY:i.top+h+(l?o.plotHeight-s:n)}),le(r.series,"click",t(e,{point:r})),o.hoverPoint&&r.firePointEvent("click",e)):(t(e,this.getCoordinates(e)),o.isInsidePlot(e.chartX-a,e.chartY-h)&&le(o,"click",e)))},onContainerTouchStart:function(t){var e=this.chart;1===t.touches.length?(t=this.normalize(t),e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)?(this.runPointActions(t),this.pinch(t)):this.reset()):2===t.touches.length&&this.pinch(t)},onContainerTouchMove:function(t){(1===t.touches.length||2===t.touches.length)&&this.pinch(t)},onDocumentTouchEnd:function(t){this.drop(t)},setDOMEvents:function(){var t,e=this,i=e.chart.container;this._events=t=[[i,"onmousedown","onContainerMouseDown"],[i,"onmousemove","onContainerMouseMove"],[i,"onclick","onContainerClick"],[i,"mouseleave","onContainerMouseLeave"],[ct,"mousemove","onDocumentMouseMove"],[ct,"mouseup","onDocumentMouseUp"]],Ot&&t.push([i,"ontouchstart","onContainerTouchStart"],[i,"ontouchmove","onContainerTouchMove"],[ct,"touchend","onDocumentTouchEnd"]),se(t,function(t){e["_"+t[2]]=function(i){e[t[2]](i)},0===t[1].indexOf("on")?t[0][t[1]]=e["_"+t[2]]:ae(t[0],t[1],e["_"+t[2]])})},destroy:function(){var t=this;se(t._events,function(e){0===e[1].indexOf("on")?e[0][e[1]]=null:he(e[0],e[1],t["_"+e[2]])}),delete t._events,clearInterval(t.tooltipTimeout)}},N.prototype={init:function(t,s){var n=this,o=s.itemStyle,r=u(s.padding,8),a=s.itemMarginTop||0;this.options=s,s.enabled&&(n.baseline=i(o.fontSize)+3+a,n.itemStyle=o,n.itemHiddenStyle=e(o,s.itemHiddenStyle),n.itemMarginTop=a,n.padding=r,n.initialItemX=r,n.initialItemY=r-5,n.maxItemWidth=0,n.chart=t,n.itemHeight=0,n.lastLineHeight=0,n.render(),ae(n.chart,"endResize",function(){n.positionCheckboxes()}))},colorizeItem:function(t,e){var i,s=this.options,n=t.legendItem,o=t.legendLine,r=t.legendSymbol,a=this.itemHiddenStyle.color,s=e?s.itemStyle.color:a,h=e?t.color:a,a=t.options&&t.options.marker,l={stroke:h,fill:h};if(n&&n.css({fill:s,color:s}),o&&o.attr({stroke:h}),r){if(a&&r.isMarker)for(i in a=t.convertAttribs(a))n=a[i],n!==V&&(l[i]=n);r.attr(l)}},positionItem:function(t){var e=this.options,i=e.symbolPadding,e=!e.rtl,s=t._legendItemPos,n=s[0],s=s[1],o=t.checkbox;t.legendGroup&&t.legendGroup.translate(e?n:this.legendWidth-n-2*i-4,s),o&&(o.x=n,o.y=s)},destroyItem:function(t){var e=t.checkbox;se(["legendItem","legendLine","legendSymbol","legendGroup"],function(e){t[e]&&(t[e]=t[e].destroy())}),e&&D(t.checkbox)},destroy:function(){var t=this.group,e=this.box;e&&(this.box=e.destroy()),t&&(this.group=t.destroy())},positionCheckboxes:function(t){var e,i=this.group.alignAttr,s=this.clipHeight||this.legendHeight;i&&(e=i.translateY,se(this.allItems,function(n){var o,r=n.checkbox;r&&(o=e+r.y+(t||0)+3,g(r,{left:i.translateX+n.legendItemWidth+r.x-20+"px",top:o+"px",display:o>e-6&&o<e+s-6?"":Et}))}))},renderTitle:function(){var t=this.padding,e=this.options.title,i=0;e.text&&(this.title||(this.title=this.chart.renderer.label(e.text,t-3,t-4,null,null,null,null,null,"legend-title").attr({zIndex:1}).css(e.style).add(this.group)),t=this.title.getBBox(),i=t.height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:i})),this.titleHeight=i},renderItem:function(t){var i,s=this,n=s.chart,o=n.renderer,r=s.options,a="horizontal"===r.layout,h=r.symbolWidth,l=r.symbolPadding,c=s.itemStyle,d=s.itemHiddenStyle,p=s.padding,g=a?u(r.itemDistance,8):0,m=!r.rtl,x=r.width,y=r.itemMarginBottom||0,v=s.itemMarginTop,k=s.initialItemX,w=t.legendItem,T=t.series||t,S=T.options,L=S.showCheckbox,P=r.useHTML;!w&&(t.legendGroup=o.g("legend-item").attr({zIndex:1}).add(s.scrollGroup),T.drawLegendSymbol(s,t),t.legendItem=w=o.text(r.labelFormat?b(r.labelFormat,t):r.labelFormatter.call(t),m?h+l:-l,s.baseline,P).css(e(t.visible?c:d)).attr({align:m?"left":"right",zIndex:2}).add(t.legendGroup),(P?w:t.legendGroup).on("mouseover",function(){t.setState("hover"),w.css(s.options.itemHoverStyle)}).on("mouseout",function(){w.css(t.visible?c:d),t.setState()}).on("click",function(e){var i=function(){t.setVisible()},e={browserEvent:e};t.firePointEvent?t.firePointEvent("legendItemClick",e,i):le(t,"legendItemClick",e,i)}),s.colorizeItem(t,t.visible),S&&L)&&(t.checkbox=f("input",{type:"checkbox",checked:t.selected,defaultChecked:t.selected},r.itemCheckboxStyle,n.container),ae(t.checkbox,"click",function(e){le(t,"checkboxClick",{checked:e.target.checked},function(){t.select()})})),o=w.getBBox(),i=t.legendItemWidth=r.itemWidth||h+l+o.width+g+(L?20:0),r=i,s.itemHeight=h=o.height,a&&s.itemX-k+r>(x||n.chartWidth-2*p-k)&&(s.itemX=k,s.itemY+=v+s.lastLineHeight+y,s.lastLineHeight=0),s.maxItemWidth=mt(s.maxItemWidth,r),s.lastItemY=v+s.itemY+y,s.lastLineHeight=mt(h,s.lastLineHeight),t._legendItemPos=[s.itemX,s.itemY],a?s.itemX+=r:(s.itemY+=v+h+y,s.lastLineHeight=h),s.offsetWidth=x||mt((a?s.itemX-k-g:r)+p,s.offsetWidth)},render:function(){var e,i,s,n,o=this,r=o.chart,a=r.renderer,h=o.group,l=o.box,d=o.options,p=o.padding,u=d.borderWidth,g=d.backgroundColor;o.itemX=o.initialItemX,o.itemY=o.initialItemY,o.offsetWidth=0,o.lastItemY=0,h||(o.group=h=a.g("legend").attr({zIndex:7}).add(),o.contentGroup=a.g().attr({zIndex:1}).add(h),o.scrollGroup=a.g().add(o.contentGroup)),o.renderTitle(),e=[],se(r.series,function(t){var i=t.options;i.showInLegend&&!c(i.linkedTo)&&(e=e.concat(t.legendItems||("point"===i.legendType?t.data:t)))}),P(e,function(t,e){return(t.options&&t.options.legendIndex||0)-(e.options&&e.options.legendIndex||0)}),d.reversed&&e.reverse(),o.allItems=e,o.display=i=!!e.length,se(e,function(t){o.renderItem(t)}),s=d.width||o.offsetWidth,n=o.lastItemY+o.lastLineHeight+o.titleHeight,n=o.handleOverflow(n),(u||g)&&(s+=p,n+=p,l?s>0&&n>0&&(l[l.isNew?"attr":"animate"](l.crisp(null,null,null,s,n)),l.isNew=!1):(o.box=l=a.rect(0,0,s,n,d.borderRadius,u||0).attr({stroke:d.borderColor,"stroke-width":u||0,fill:g||Et}).add(h).shadow(d.shadow),l.isNew=!0),l[i?"show":"hide"]()),o.legendWidth=s,o.legendHeight=n,se(e,function(t){o.positionItem(t)}),i&&h.align(t({width:s,height:n},d),!0,"spacingBox"),r.isResizing||this.positionCheckboxes()},handleOverflow:function(t){var e=this,i=this.chart,s=i.renderer,n=this.options,o=n.y,o=i.spacingBox.height+("top"===n.verticalAlign?-o:o)-this.padding,r=n.maxHeight,a=this.clipRect,h=n.navigation,l=u(h.animation,!0),c=h.arrowSize||12,d=this.nav;return"horizontal"===n.layout&&(o/=2),r&&(o=xt(o,r)),t>o&&!n.useHTML?(this.clipHeight=i=o-20-this.titleHeight,this.pageCount=ft(t/i),this.currentPage=u(this.currentPage,1),this.fullHeight=t,a||(a=e.clipRect=s.clipRect(0,0,9999,0),e.contentGroup.clip(a)),a.attr({height:i}),d||(this.nav=d=s.g().attr({zIndex:1}).add(this.group),this.up=s.symbol("triangle",0,0,c,c).on("click",function(){e.scroll(-1,l)}).add(d),this.pager=s.text("",15,10).css(h.style).add(d),this.down=s.symbol("triangle-down",0,0,c,c).on("click",function(){e.scroll(1,l)}).add(d)),e.scroll(0),t=o):d&&(a.attr({height:i.chartHeight}),d.hide(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},scroll:function(t,e){var i=this.pageCount,s=this.currentPage+t,n=this.clipHeight,o=this.options.navigation,r=o.activeColor,a=o.inactiveColor,o=this.pager,h=this.padding;s>i&&(s=i),s>0&&(e!==V&&B(e,this.chart),this.nav.attr({translateX:h,translateY:n+7+this.titleHeight,visibility:"visible"}),this.up.attr({fill:1===s?a:r}).css({cursor:1===s?"default":"pointer"}),o.attr({text:s+"/"+this.pageCount}),this.down.attr({x:18+this.pager.getBBox().width,fill:s===i?a:r}).css({cursor:s===i?"default":"pointer"}),n=-xt(n*(s-1),this.fullHeight-n+h)+1,this.scrollGroup.animate({translateY:n}),o.attr({text:s+"/"+i}),this.currentPage=s,this.positionCheckboxes(n))}},/Trident.*?11\.0/.test(Tt)&&v(N.prototype,"positionItem",function(t,e){var i=this;setTimeout(function(){t.call(i,e)})}),F.prototype={init:function(t,i){var s,n=t.series;t.series=null,s=e(U,t),s.series=t.series=n,n=s.chart,this.margin=this.splashArray("margin",n),this.spacing=this.splashArray("spacing",n);var o=n.events;this.bounds={h:{},v:{}},this.callback=i,this.isResizing=0,this.options=s,this.axes=[],this.series=[],this.hasCartesianSeries=n.showAxes;var r,a=this;if(a.index=Wt.length,Wt.push(a),n.reflow!==!1&&ae(a,"load",function(){a.initReflow()}),o)for(r in o)ae(a,r,o[r]);a.xAxis=[],a.yAxis=[],a.animation=!Bt&&u(n.animation,!0),a.pointCount=0,a.counters=new L,a.firstRender()},initSeries:function(t){var e=this.options.chart;return(e=qt[t.type||e.type||e.defaultSeriesType])||I(17,!0),e=new e,e.init(this,t),e},addSeries:function(t,e,i){var s,n=this;return t&&(e=u(e,!0),le(n,"addSeries",{options:t},function(){s=n.initSeries(t),n.isDirtyLegend=!0,n.linkSeries(),e&&n.redraw(i)})),s},addAxis:function(t,i,s,n){var o=i?"xAxis":"yAxis",r=this.options;new Y(this,e(t,{index:this[o].length,isX:i})),r[o]=p(r[o]||{}),r[o].push(t),u(s,!0)&&this.redraw(n)},isInsidePlot:function(t,e,i){var s=i?e:t,t=i?t:e;return s>=0&&s<=this.plotWidth&&t>=0&&t<=this.plotHeight},adjustTickAmounts:function(){this.options.chart.alignTicks!==!1&&se(this.axes,function(t){t.adjustTickAmount()}),this.maxTicks=null},redraw:function(e){var i,s,n=this.axes,o=this.series,r=this.pointer,a=this.legend,h=this.isDirtyLegend,l=this.isDirtyBox,c=o.length,d=c,p=this.renderer,u=p.isHidden(),g=[];for(B(e,this),u&&this.cloneRenderTo(),this.layOutTitles();d--;)if(e=o[d],e.options.stacking&&(i=!0,e.isDirty)){s=!0;break}if(s)for(d=c;d--;)e=o[d],e.options.stacking&&(e.isDirty=!0);se(o,function(t){t.isDirty&&"point"===t.options.legendType&&(h=!0)}),h&&a.options.enabled&&(a.render(),this.isDirtyLegend=!1),i&&this.getStacks(),this.hasCartesianSeries&&(this.isResizing||(this.maxTicks=null,se(n,function(t){t.setScale()})),this.adjustTickAmounts(),this.getMargins(),se(n,function(t){t.isDirty&&(l=!0)}),se(n,function(e){e.isDirtyExtremes&&(e.isDirtyExtremes=!1,g.push(function(){le(e,"afterSetExtremes",t(e.eventArgs,e.getExtremes())),delete e.eventArgs})),(l||i)&&e.redraw()})),l&&this.drawChartBox(),se(o,function(t){t.isDirty&&t.visible&&(!t.isCartesian||t.xAxis)&&t.redraw()}),r&&r.reset&&r.reset(!0),p.draw(),le(this,"redraw"),u&&this.cloneRenderTo(!0),se(g,function(t){t.call()})},showLoading:function(e){var i=this.options,s=this.loadingDiv,n=i.loading;s||(this.loadingDiv=s=f(Yt,{className:"highcharts-loading"},t(n.style,{zIndex:10,display:Et}),this.container),this.loadingSpan=f("span",null,n.labelStyle,s)),this.loadingSpan.innerHTML=e||i.lang.loading,this.loadingShown||(g(s,{opacity:0,display:"",left:this.plotLeft+"px",top:this.plotTop+"px",width:this.plotWidth+"px",height:this.plotHeight+"px"}),de(s,{opacity:n.style.opacity},{duration:n.showDuration||0}),this.loadingShown=!0)},hideLoading:function(){var t=this.options,e=this.loadingDiv;e&&de(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){g(e,{display:Et})}}),this.loadingShown=!1},get:function(t){var e,i,s=this.axes,n=this.series;for(e=0;e<s.length;e++)if(s[e].options.id===t)return s[e];for(e=0;e<n.length;e++)if(n[e].options.id===t)return n[e];for(e=0;e<n.length;e++)for(i=n[e].points||[],s=0;s<i.length;s++)if(i[s].id===t)return i[s];return null},getAxes:function(){var t=this,e=this.options,i=e.xAxis=p(e.xAxis||{}),e=e.yAxis=p(e.yAxis||{});se(i,function(t,e){t.index=e,t.isX=!0}),se(e,function(t,e){t.index=e}),i=i.concat(e),se(i,function(e){new Y(t,e)}),t.adjustTickAmounts()},getSelectedPoints:function(){var t=[];return se(this.series,function(e){t=t.concat(ne(e.points||[],function(t){return t.selected}))}),t},getSelectedSeries:function(){return ne(this.series,function(t){return t.selected})},getStacks:function(){var t=this;se(t.yAxis,function(t){t.stacks&&t.hasVisibleSeries&&(t.oldStacks=t.stacks)}),se(t.series,function(e){!e.options.stacking||e.visible!==!0&&t.options.chart.ignoreHiddenSeries!==!1||(e.stackKey=e.type+u(e.options.stack,""))})},showResetZoom:function(){var t=this,e=U.lang,i=t.options.chart.resetZoomButton,s=i.theme,n=s.states,o="chart"===i.relativeTo?null:"plotBox";this.resetZoomButton=t.renderer.button(e.resetZoom,null,null,function(){t.zoomOut()},s,n&&n.hover).attr({align:i.position.align,title:e.resetZoomTitle}).add().align(i.position,!1,o)},zoomOut:function(){var t=this;le(t,"selection",{resetSelection:!0},function(){t.zoom()})},zoom:function(t){var e,i,s=this.pointer,o=!1;!t||t.resetSelection?se(this.axes,function(t){e=t.zoom()}):se(t.xAxis.concat(t.yAxis),function(t){var i=t.axis,n=i.isXAxis;(s[n?"zoomX":"zoomY"]||s[n?"pinchX":"pinchY"])&&(e=i.zoom(t.min,t.max),i.displayBtn&&(o=!0))}),i=this.resetZoomButton,o&&!i?this.showResetZoom():!o&&n(i)&&(this.resetZoomButton=i.destroy()),e&&this.redraw(u(this.options.chart.animation,t&&t.animation,this.pointCount<100))},pan:function(t,e){var i,s=this,n=s.hoverPoints;n&&se(n,function(t){t.setState()}),se("xy"===e?[1,0]:[1],function(e){var n=t[e?"chartX":"chartY"],o=s[e?"xAxis":"yAxis"][0],r=s[e?"mouseDownX":"mouseDownY"],a=(o.pointRange||0)/2,h=o.getExtremes(),l=o.toValue(r-n,!0)+a,r=o.toValue(r+s[e?"plotWidth":"plotHeight"]-n,!0)-a;o.series.length&&l>xt(h.dataMin,h.min)&&r<mt(h.dataMax,h.max)&&(o.setExtremes(l,r,!1,!1,{trigger:"pan"}),i=!0),s[e?"mouseDownX":"mouseDownY"]=n}),i&&s.redraw(!1),g(s.container,{cursor:"move"})},setTitle:function(t,i){var s,n,o=this,r=o.options;n=r.title=e(r.title,t),s=r.subtitle=e(r.subtitle,i),r=s,se([["title",t,n],["subtitle",i,r]],function(t){var e=t[0],i=o[e],s=t[1],t=t[2];i&&s&&(o[e]=i=i.destroy()),t&&t.text&&!i&&(o[e]=o.renderer.text(t.text,0,0,t.useHTML).attr({align:t.align,"class":"highcharts-"+e,zIndex:t.zIndex||4}).css(t.style).add())}),o.layOutTitles()},layOutTitles:function(){var e=0,i=this.title,s=this.subtitle,n=this.options,o=n.title,n=n.subtitle,r=this.spacingBox.width-44;!i||(i.css({width:(o.width||r)+"px"}).align(t({y:15},o),!1,"spacingBox"),o.floating||o.verticalAlign)||(e=i.getBBox().height,e>=18&&e<=25&&(e=15)),s&&(s.css({width:(n.width||r)+"px"}).align(t({y:e+o.margin},n),!1,"spacingBox"),!n.floating&&!n.verticalAlign&&(e=ft(e+s.getBBox().height))),this.titleOffset=e},getChartSize:function(){var t=this.options.chart,e=this.renderToClone||this.renderTo;this.containerWidth=te(e,"width"),this.containerHeight=te(e,"height"),this.chartWidth=mt(0,t.width||this.containerWidth||600),this.chartHeight=mt(0,u(t.height,this.containerHeight>19?this.containerHeight:400))},cloneRenderTo:function(t){var e=this.renderToClone,i=this.container;t?e&&(this.renderTo.appendChild(i),D(e),delete this.renderToClone):(i&&i.parentNode===this.renderTo&&this.renderTo.removeChild(i),this.renderToClone=e=this.renderTo.cloneNode(0),g(e,{position:"absolute",top:"-9999px",display:"block"}),ct.body.appendChild(e),i&&e.appendChild(i))},getContainer:function(){var e,n,o,r,a=this.options.chart;this.renderTo=e=a.renderTo,r="highcharts-"+Rt++,s(e)&&(this.renderTo=e=ct.getElementById(e)),e||I(13,!0),n=i(d(e,"data-highcharts-chart")),!isNaN(n)&&Wt[n]&&Wt[n].destroy(),d(e,"data-highcharts-chart",this.index),e.innerHTML="",e.offsetWidth||this.cloneRenderTo(),this.getChartSize(),n=this.chartWidth,o=this.chartHeight,this.container=e=f(Yt,{className:"highcharts-container"+(a.className?" "+a.className:""),id:r},t({position:"relative",overflow:"hidden",width:n+"px",height:o+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)"},a.style),this.renderToClone||e),this._cursor=e.style.cursor,this.renderer=a.forExport?new fe(e,n,o,(!0)):new j(e,n,o),Bt&&this.renderer.create(this,e,n,o)},getMargins:function(){var t,e=this.spacing,i=this.legend,s=this.margin,n=this.options.legend,o=u(n.margin,10),r=n.x,a=n.y,h=n.align,l=n.verticalAlign,d=this.titleOffset;this.resetMargins(),t=this.axisOffset,d&&!c(s[0])&&(this.plotTop=mt(this.plotTop,d+this.options.title.margin+e[0])),i.display&&!n.floating&&("right"===h?c(s[1])||(this.marginRight=mt(this.marginRight,i.legendWidth-r+o+e[1])):"left"===h?c(s[3])||(this.plotLeft=mt(this.plotLeft,i.legendWidth+r+o+e[3])):"top"===l?c(s[0])||(this.plotTop=mt(this.plotTop,i.legendHeight+a+o+e[0])):"bottom"!==l||c(s[2])||(this.marginBottom=mt(this.marginBottom,i.legendHeight-a+o+e[2]))),this.extraBottomMargin&&(this.marginBottom+=this.extraBottomMargin),this.extraTopMargin&&(this.plotTop+=this.extraTopMargin),this.hasCartesianSeries&&se(this.axes,function(t){t.getOffset()}),c(s[3])||(this.plotLeft+=t[3]),c(s[0])||(this.plotTop+=t[0]),c(s[2])||(this.marginBottom+=t[2]),c(s[1])||(this.marginRight+=t[1]),this.setChartSize()},initReflow:function(){function t(t){var o=s.width||te(n,"width"),r=s.height||te(n,"height"),t=t?t.target:dt;i.hasUserSize||!o||!r||t!==dt&&t!==ct||(o===i.containerWidth&&r===i.containerHeight||(clearTimeout(e),i.reflowTimeout=e=setTimeout(function(){i.container&&(i.setSize(o,r,!1),i.hasUserSize=null)},100)),i.containerWidth=o,i.containerHeight=r)}var e,i=this,s=i.options.chart,n=i.renderTo;i.reflow=t,ae(dt,"resize",t),ae(i,"destroy",function(){he(dt,"resize",t)})},setSize:function(t,e,i){var s,n,o,r=this;r.isResizing+=1,o=function(){r&&le(r,"endResize",null,function(){r.isResizing-=1})},B(i,r),r.oldChartHeight=r.chartHeight,r.oldChartWidth=r.chartWidth,c(t)&&(r.chartWidth=s=mt(0,ut(t)),r.hasUserSize=!!s),c(e)&&(r.chartHeight=n=mt(0,ut(e))),g(r.container,{width:s+"px",height:n+"px"}),r.setChartSize(!0),r.renderer.setSize(s,n,i),r.maxTicks=null,se(r.axes,function(t){t.isDirty=!0,t.setScale()}),se(r.series,function(t){t.isDirty=!0}),r.isDirtyLegend=!0,r.isDirtyBox=!0,r.getMargins(),r.redraw(i),r.oldChartHeight=null,le(r,"resize"),K===!1?o():setTimeout(o,K&&K.duration||500)},setChartSize:function(t){var e,i,s,n,o=this.inverted,r=this.renderer,a=this.chartWidth,h=this.chartHeight,l=this.options.chart,c=this.spacing,d=this.clipOffset;this.plotLeft=e=ut(this.plotLeft),this.plotTop=i=ut(this.plotTop),this.plotWidth=s=mt(0,ut(a-e-this.marginRight)),this.plotHeight=n=mt(0,ut(h-i-this.marginBottom)),this.plotSizeX=o?n:s,this.plotSizeY=o?s:n,this.plotBorderWidth=l.plotBorderWidth||0,this.spacingBox=r.spacingBox={x:c[3],y:c[0],width:a-c[3]-c[1],height:h-c[0]-c[2]},this.plotBox=r.plotBox={x:e,y:i,width:s,height:n},a=2*gt(this.plotBorderWidth/2),
o=ft(mt(a,d[3])/2),r=ft(mt(a,d[0])/2),this.clipBox={x:o,y:r,width:gt(this.plotSizeX-mt(a,d[1])/2-o),height:gt(this.plotSizeY-mt(a,d[2])/2-r)},t||se(this.axes,function(t){t.setAxisSize(),t.setAxisTranslation()})},resetMargins:function(){var t=this.spacing,e=this.margin;this.plotTop=u(e[0],t[0]),this.marginRight=u(e[1],t[1]),this.marginBottom=u(e[2],t[2]),this.plotLeft=u(e[3],t[3]),this.axisOffset=[0,0,0,0],this.clipOffset=[0,0,0,0]},drawChartBox:function(){var t,e=this.options.chart,i=this.renderer,s=this.chartWidth,n=this.chartHeight,o=this.chartBackground,r=this.plotBackground,a=this.plotBorder,h=this.plotBGImage,l=e.borderWidth||0,c=e.backgroundColor,d=e.plotBackgroundColor,p=e.plotBackgroundImage,u=e.plotBorderWidth||0,g=this.plotLeft,f=this.plotTop,m=this.plotWidth,x=this.plotHeight,y=this.plotBox,v=this.clipRect,b=this.clipBox;t=l+(e.shadow?8:0),(l||c)&&(o?o.animate(o.crisp(null,null,null,s-t,n-t)):(o={fill:c||Et},l&&(o.stroke=e.borderColor,o["stroke-width"]=l),this.chartBackground=i.rect(t/2,t/2,s-t,n-t,e.borderRadius,l).attr(o).add().shadow(e.shadow))),d&&(r?r.animate(y):this.plotBackground=i.rect(g,f,m,x,0).attr({fill:d}).add().shadow(e.plotShadow)),p&&(h?h.animate(y):this.plotBGImage=i.image(p,g,f,m,x).add()),v?v.animate({width:b.width,height:b.height}):this.clipRect=i.clipRect(b),u&&(a?a.animate(a.crisp(null,g,f,m,x)):this.plotBorder=i.rect(g,f,m,x,0,-u).attr({stroke:e.plotBorderColor,"stroke-width":u,zIndex:1}).add()),this.isDirtyBox=!1},propFromSeries:function(){var t,e,i,s=this,n=s.options.chart,o=s.options.series;se(["inverted","angular","polar"],function(r){for(t=qt[n.type||n.defaultSeriesType],i=s[r]||n[r]||t&&t.prototype[r],e=o&&o.length;!i&&e--;)(t=qt[o[e].type])&&t.prototype[r]&&(i=!0);s[r]=i})},linkSeries:function(){var t=this,e=t.series;se(e,function(t){t.linkedSeries.length=0}),se(e,function(e){var i=e.options.linkedTo;s(i)&&(i=":previous"===i?t.series[e.index-1]:t.get(i))&&(i.linkedSeries.push(e),e.linkedParent=i)})},render:function(){var e,s=this,n=s.axes,o=s.renderer,r=s.options,a=r.labels,h=r.credits;s.setTitle(),s.legend=new N(s,r.legend),s.getStacks(),se(n,function(t){t.setScale()}),s.getMargins(),s.maxTicks=null,se(n,function(t){t.setTickPositions(!0),t.setMaxTicks()}),s.adjustTickAmounts(),s.getMargins(),s.drawChartBox(),s.hasCartesianSeries&&se(n,function(t){t.render()}),s.seriesGroup||(s.seriesGroup=o.g("series-group").attr({zIndex:3}).add()),se(s.series,function(t){t.translate(),t.setTooltipPoints(),t.render()}),a.items&&se(a.items,function(e){var n=t(a.style,e.style),r=i(n.left)+s.plotLeft,h=i(n.top)+s.plotTop+12;delete n.left,delete n.top,o.text(e.html,r,h).attr({zIndex:2}).css(n).add()}),h.enabled&&!s.credits&&(e=h.href,s.credits=o.text(h.text,0,0).on("click",function(){e&&(location.href=e)}).attr({align:h.position.align,zIndex:8}).css(h.style).add().align(h.position)),s.hasRendered=!0},destroy:function(){var t,e=this,i=e.axes,s=e.series,n=e.container,o=n&&n.parentNode;for(le(e,"destroy"),Wt[e.index]=V,e.renderTo.removeAttribute("data-highcharts-chart"),he(e),t=i.length;t--;)i[t]=i[t].destroy();for(t=s.length;t--;)s[t]=s[t].destroy();se("title,subtitle,chartBackground,plotBackground,plotBGImage,plotBorder,seriesGroup,clipRect,credits,pointer,scroller,rangeSelector,legend,resetZoomButton,tooltip,renderer".split(","),function(t){var i=e[t];i&&i.destroy&&(e[t]=i.destroy())}),n&&(n.innerHTML="",he(n),o&&D(n));for(t in e)delete e[t]},isReadyToRender:function(){var t=this;return!(!It&&dt==dt.top&&"complete"!==ct.readyState||Bt&&!dt.canvg)||(Bt?ye.push(function(){t.firstRender()},t.options.global.canvasToolsURL):ct.attachEvent("onreadystatechange",function(){ct.detachEvent("onreadystatechange",t.firstRender),"complete"===ct.readyState&&t.firstRender()}),!1)},firstRender:function(){var t=this,e=t.options,i=t.callback;t.isReadyToRender()&&(t.getContainer(),le(t,"init"),t.resetMargins(),t.setChartSize(),t.propFromSeries(),t.getAxes(),se(e.series||[],function(e){t.initSeries(e)}),t.linkSeries(),le(t,"beforeRender"),t.pointer=new G(t,e),t.render(),t.renderer.draw(),i&&i.apply(t,[t]),se(t.callbacks,function(e){e.apply(t,[t])}),t.cloneRenderTo(!0),le(t,"load"))},splashArray:function(t,e){var i=e[t],i=n(i)?i:[i,i,i,i];return[u(e[t+"Top"],i[0]),u(e[t+"Right"],i[1]),u(e[t+"Bottom"],i[2]),u(e[t+"Left"],i[3])]}},F.prototype.callbacks=[];var ve=function(){};ve.prototype={init:function(t,e,i){return this.series=t,this.applyOptions(e,i),this.pointAttr={},t.options.colorByPoint&&(e=t.options.colors||t.chart.options.colors,this.color=this.color||e[t.colorCounter++],t.colorCounter===e.length)&&(t.colorCounter=0),t.chart.pointCount++,this},applyOptions:function(e,i){var s=this.series,n=s.pointValKey,e=ve.prototype.optionsToObject.call(this,e);return t(this,e),this.options=this.options?t(this.options,e):e,n&&(this.y=this[n]),this.x===V&&s&&(this.x=i===V?s.autoIncrement():i),this},optionsToObject:function(t){var e,i=this.series,s=i.pointArrayMap||["y"],n=s.length,r=0,a=0;if("number"==typeof t||null===t)e={y:t};else if(o(t))for(e={},t.length>n&&(i=typeof t[0],"string"===i?e.name=t[0]:"number"===i&&(e.x=t[0]),r++);a<n;)e[s[a++]]=t[r++];else"object"==typeof t&&(e=t,t.dataLabels&&(i._hasPointLabels=!0),t.marker&&(i._hasPointMarkers=!0));return e},destroy:function(){var t,e=this.series.chart,i=e.hoverPoints;e.pointCount--,i&&(this.setState(),l(i,this),!i.length)&&(e.hoverPoints=null),this===e.hoverPoint&&this.onMouseOut(),(this.graphic||this.dataLabel)&&(he(this),this.destroyElements()),this.legendItem&&e.legend.destroyItem(this);for(t in this)this[t]=null},destroyElements:function(){for(var t,e="graphic,dataLabel,dataLabelUpper,group,connector,shadowGroup".split(","),i=6;i--;)t=e[i],this[t]&&(this[t]=this[t].destroy())},getLabelConfig:function(){return{x:this.category,y:this.y,key:this.name||this.category,series:this.series,point:this,percentage:this.percentage,total:this.total||this.stackTotal}},select:function(t,e){var i=this,s=i.series,n=s.chart,t=u(t,!i.selected);i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,s.options.data[ie(i,s.data)]=i.options,i.setState(t&&"select"),e||se(n.getSelectedPoints(),function(t){t.selected&&t!==i&&(t.selected=t.options.selected=!1,s.options.data[ie(t,s.data)]=t.options,t.setState(""),t.firePointEvent("unselect"))})})},onMouseOver:function(t){var e=this.series,i=e.chart,s=i.tooltip,n=i.hoverPoint;n&&n!==this&&n.onMouseOut(),this.firePointEvent("mouseOver"),s&&(!s.shared||e.noSharedTooltip)&&s.refresh(this,t),this.setState("hover"),i.hoverPoint=this},onMouseOut:function(){var t=this.series.chart,e=t.hoverPoints;e&&ie(this,e)!==-1||(this.firePointEvent("mouseOut"),this.setState(),t.hoverPoint=null)},tooltipFormatter:function(t){var e=this.series,i=e.tooltipOptions,s=u(i.valueDecimals,""),n=i.valuePrefix||"",o=i.valueSuffix||"";return se(e.pointArrayMap||["y"],function(e){e="{point."+e,(n||o)&&(t=t.replace(e+"}",n+e+"}"+o)),t=t.replace(e+"}",e+":,."+s+"f}")}),b(t,{point:this,series:this.series})},update:function(t,e,i){var s,o=this,r=o.series,a=o.graphic,h=r.data,l=r.chart,c=r.options,e=u(e,!0);o.firePointEvent("update",{options:t},function(){o.applyOptions(t),n(t)&&(r.getAttribs(),a)&&(t.marker&&t.marker.symbol?o.graphic=a.destroy():a.attr(o.pointAttr[o.state||""])),s=ie(o,h),r.xData[s]=o.x,r.yData[s]=r.toYData?r.toYData(o):o.y,r.zData[s]=o.z,c.data[s]=o.options,r.isDirty=r.isDirtyData=!0,!r.fixedBox&&r.hasCartesianSeries&&(l.isDirtyBox=!0),"point"===c.legendType&&l.legend.destroyItem(o),e&&l.redraw(i)})},remove:function(t,e){var i,s=this,n=s.series,o=n.points,r=n.chart,a=n.data;B(e,r),t=u(t,!0),s.firePointEvent("remove",null,function(){i=ie(s,a),a.length===o.length&&o.splice(i,1),a.splice(i,1),n.options.data.splice(i,1),n.xData.splice(i,1),n.yData.splice(i,1),n.zData.splice(i,1),s.destroy(),n.isDirty=!0,n.isDirtyData=!0,t&&r.redraw()})},firePointEvent:function(t,e,i){var s=this,n=this.series.options;(n.point.events[t]||s.options&&s.options.events&&s.options.events[t])&&this.importEvents(),"click"===t&&n.allowPointSelect&&(i=function(t){s.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),le(this,t,e,i)},importEvents:function(){if(!this.hasImportedEvents){var t,i=e(this.series.options.point,this.options).events;this.events=i;for(t in i)ae(this,t,i[t]);this.hasImportedEvents=!0}},setState:function(t){var i=this.plotX,s=this.plotY,n=this.series,o=n.options.states,r=ue[n.type].marker&&n.options.marker,a=r&&!r.enabled,h=r&&r.states[t],l=h&&h.enabled===!1,c=n.stateMarkerGraphic,d=this.marker||{},p=n.chart,u=this.pointAttr,t=t||"";t===this.state||this.selected&&"select"!==t||o[t]&&o[t].enabled===!1||t&&(l||a&&!h.enabled)||(this.graphic?(o=r&&this.graphic.symbolName&&u[t].r,this.graphic.attr(e(u[t],o?{x:i-o,y:s-o,width:2*o,height:2*o}:{}))):(t&&h&&(o=h.radius,d=d.symbol||n.symbol,c&&c.currentSymbol!==d&&(c=c.destroy()),c?c.attr({x:i-o,y:s-o}):(n.stateMarkerGraphic=c=p.renderer.symbol(d,i-o,s-o,2*o,2*o).attr(u[t]).add(n.markerGroup),c.currentSymbol=d)),c&&c[t&&p.isInsidePlot(i,s)?"show":"hide"]()),this.state=t)}};var be=function(){};be.prototype={isCartesian:!0,type:"line",pointClass:ve,sorted:!0,requireSorting:!0,pointAttrToOptions:{stroke:"lineColor","stroke-width":"lineWidth",fill:"fillColor",r:"radius"},colorCounter:0,init:function(e,i){var s,n,o=e.series;this.chart=e,this.options=i=this.setOptions(i),this.linkedSeries=[],this.bindAxes(),t(this,{name:i.name,state:"",pointAttr:{},visible:i.visible!==!1,selected:i.selected===!0}),Bt&&(i.animation=!1),n=i.events;for(s in n)ae(this,s,n[s]);(n&&n.click||i.point&&i.point.events&&i.point.events.click||i.allowPointSelect)&&(e.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.setData(i.data,!1),this.isCartesian&&(e.hasCartesianSeries=!0),o.push(this),this._i=o.length-1,P(o,function(t,e){return u(t.options.index,t._i)-u(e.options.index,t._i)}),se(o,function(t,e){t.index=e,t.name=t.name||"Series "+(e+1)})},bindAxes:function(){var t,e=this,i=e.options,s=e.chart;e.isCartesian&&se(["xAxis","yAxis"],function(n){se(s[n],function(s){t=s.options,(i[n]===t.index||i[n]!==V&&i[n]===t.id||i[n]===V&&0===t.index)&&(s.series.push(e),e[n]=s,s.isDirty=!0)}),e[n]||I(18,!0)})},autoIncrement:function(){var t=this.options,e=this.xIncrement,e=u(e,t.pointStart,0);return this.pointInterval=u(this.pointInterval,t.pointInterval,1),this.xIncrement=e+this.pointInterval,e},getSegments:function(){var t,e=-1,i=[],s=this.points,n=s.length;if(n)if(this.options.connectNulls){for(t=n;t--;)null===s[t].y&&s.splice(t,1);s.length&&(i=[s])}else se(s,function(t,o){null===t.y?(o>e+1&&i.push(s.slice(e+1,o)),e=o):o===n-1&&i.push(s.slice(e+1,o+1))});this.segments=i},setOptions:function(t){var i=this.chart.options,s=i.plotOptions,n=s[this.type];return this.userOptions=t,t=e(n,s.series,t),this.tooltipOptions=e(i.tooltip,t.tooltip),null===n.marker&&delete t.marker,t},getColor:function(){var t,e=this.options,i=this.userOptions,s=this.chart.options.colors,n=this.chart.counters;t=e.color||ue[this.type].color,t||e.colorByPoint||(c(i._colorIndex)?e=i._colorIndex:(i._colorIndex=n.color,e=n.color++),t=s[e]),this.color=t,n.wrapColor(s.length)},getSymbol:function(){var t=this.userOptions,e=this.options.marker,i=this.chart,s=i.options.symbols,i=i.counters;this.symbol=e.symbol,this.symbol||(c(t._symbolIndex)?t=t._symbolIndex:(t._symbolIndex=i.symbol,t=i.symbol++),this.symbol=s[t]),/^url/.test(this.symbol)&&(e.radius=0),i.wrapSymbol(s.length)},drawLegendSymbol:function(t){var e,i=this.options,s=i.marker,n=t.options;e=n.symbolWidth;var o=this.chart.renderer,r=this.legendGroup,t=t.baseline-ut(.3*o.fontMetrics(n.itemStyle.fontSize).b);i.lineWidth&&(n={"stroke-width":i.lineWidth},i.dashStyle&&(n.dashstyle=i.dashStyle),this.legendLine=o.path(["M",0,t,"L",e,t]).attr(n).add(r)),s&&s.enabled&&(i=s.radius,this.legendSymbol=e=o.symbol(this.symbol,e/2-i,t-i,2*i,2*i).add(r),e.isMarker=!0)},addPoint:function(t,e,i,s){var n,o=this.options,r=this.data,a=this.graph,h=this.area,l=this.chart,c=this.xData,d=this.yData,p=this.zData,g=this.names,f=a&&a.shift||0,m=o.data;if(B(s,l),i&&se([a,h,this.graphNeg,this.areaNeg],function(t){t&&(t.shift=f+1)}),h&&(h.isArea=!0),e=u(e,!0),s={series:this},this.pointClass.prototype.applyOptions.apply(s,[t]),a=s.x,h=c.length,this.requireSorting&&a<c[h-1])for(n=!0;h&&c[h-1]>a;)h--;c.splice(h,0,a),d.splice(h,0,this.toYData?this.toYData(s):s.y),p.splice(h,0,s.z),g&&(g[a]=s.name),m.splice(h,0,t),n&&(this.data.splice(h,0,null),this.processData()),"point"===o.legendType&&this.generatePoints(),i&&(r[0]&&r[0].remove?r[0].remove(!1):(r.shift(),c.shift(),d.shift(),p.shift(),m.shift())),this.isDirtyData=this.isDirty=!0,e&&(this.getAttribs(),l.redraw())},setData:function(t,e){var i,n=this.points,a=this.options,h=this.chart,l=null,c=this.xAxis,d=c&&c.categories&&!c.categories.length?[]:null;this.xIncrement=null,this.pointRange=c&&c.categories?1:a.pointRange,this.colorCounter=0;var p=[],g=[],f=[],m=t?t.length:[];i=u(a.turboThreshold,1e3);var x=this.pointArrayMap,x=x&&x.length,y=!!this.toYData;if(i&&m>i){for(i=0;null===l&&i<m;)l=t[i],i++;if(r(l)){for(l=u(a.pointStart,0),a=u(a.pointInterval,1),i=0;i<m;i++)p[i]=l,g[i]=t[i],l+=a;this.xIncrement=l}else if(o(l))if(x)for(i=0;i<m;i++)a=t[i],p[i]=a[0],g[i]=a.slice(1,x+1);else for(i=0;i<m;i++)a=t[i],p[i]=a[0],g[i]=a[1];else I(12)}else for(i=0;i<m;i++)t[i]!==V&&(a={series:this},this.pointClass.prototype.applyOptions.apply(a,[t[i]]),p[i]=a.x,g[i]=y?this.toYData(a):a.y,f[i]=a.z,d&&a.name)&&(d[a.x]=a.name);for(s(g[0])&&I(14,!0),this.data=[],this.options.data=t,this.xData=p,this.yData=g,this.zData=f,this.names=d,i=n&&n.length||0;i--;)n[i]&&n[i].destroy&&n[i].destroy();c&&(c.minRange=c.userMinRange),this.isDirty=this.isDirtyData=h.isDirtyBox=!0,u(e,!0)&&h.redraw(!1)},remove:function(t,e){var i=this,s=i.chart,t=u(t,!0);i.isRemoving||(i.isRemoving=!0,le(i,"remove",null,function(){i.destroy(),s.isDirtyLegend=s.isDirtyBox=!0,s.linkSeries(),t&&s.redraw(e)})),i.isRemoving=!1},processData:function(t){var e,i=this.xData,s=this.yData,n=i.length;e=0;var o,r,a=this.xAxis,h=this.options,l=h.cropThreshold,c=this.isCartesian;if(c&&!this.isDirty&&!a.isDirty&&!this.yAxis.isDirty&&!t)return!1;for(c&&this.sorted&&(!l||n>l||this.forceCrop)&&(t=a.min,a=a.max,i[n-1]<t||i[0]>a?(i=[],s=[]):(i[0]<t||i[n-1]>a)&&(e=this.cropData(this.xData,this.yData,t,a),i=e.xData,s=e.yData,e=e.start,o=!0)),a=i.length-1;a>=0;a--)n=i[a]-i[a-1],n>0&&(r===V||n<r)?r=n:n<0&&this.requireSorting&&I(15);this.cropped=o,this.cropStart=e,this.processedXData=i,this.processedYData=s,null===h.pointRange&&(this.pointRange=r||1),this.closestPointRange=r},cropData:function(t,e,i,s){var n,o=t.length,r=0,a=o,h=u(this.cropShoulder,1);for(n=0;n<o;n++)if(t[n]>=i){r=mt(0,n-h);break}for(;n<o;n++)if(t[n]>s){a=n+h;break}return{xData:t.slice(r,a),yData:e.slice(r,a),start:r,end:a}},generatePoints:function(){var t,e,i,s,n=this.options.data,o=this.data,r=this.processedXData,a=this.processedYData,h=this.pointClass,l=r.length,c=this.cropStart||0,d=this.hasGroupedData,u=[];for(o||d||(o=[],o.length=n.length,o=this.data=o),s=0;s<l;s++)e=c+s,d?u[s]=(new h).init(this,[r[s]].concat(p(a[s]))):(o[e]?i=o[e]:n[e]!==V&&(o[e]=i=(new h).init(this,n[e],r[s])),u[s]=i);if(o&&(l!==(t=o.length)||d))for(s=0;s<t;s++)s===c&&!d&&(s+=l),o[s]&&(o[s].destroyElements(),o[s].plotX=V);this.data=o,this.points=u},setStackedPoints:function(){if(this.options.stacking&&(this.visible===!0||this.chart.options.chart.ignoreHiddenSeries===!1)){var t,e,i,s,n,o=this.processedXData,r=this.processedYData,a=[],h=r.length,l=this.options,c=l.threshold,d=l.stack,l=l.stacking,p=this.stackKey,u="-"+p,g=this.negStacks,f=this.yAxis,m=f.stacks,x=f.oldStacks;for(i=0;i<h;i++)s=o[i],n=r[i],e=(t=g&&n<c)?u:p,m[e]||(m[e]={}),m[e][s]||(x[e]&&x[e][s]?(m[e][s]=x[e][s],m[e][s].total=null):m[e][s]=new W(f,f.options.stackLabels,t,s,d,l)),e=m[e][s],e.points[this.index]=[e.cum||0],"percent"===l?(t=t?p:u,g&&m[t]&&m[t][s]?(t=m[t][s],e.total=t.total=mt(t.total,e.total)+yt(n)||0):e.total+=yt(n)||0):e.total+=n||0,e.cum=(e.cum||0)+(n||0),e.points[this.index].push(e.cum),a[i]=e.cum;"percent"===l&&(f.usePercentage=!0),this.stackedYData=a,f.oldStacks={}}},setPercentStacks:function(){var t=this,e=t.stackKey,i=t.yAxis.stacks;se([e,"-"+e],function(e){for(var s,n,o,r=t.xData.length;r--;)n=t.xData[r],s=(o=i[e]&&i[e][n])&&o.points[t.index],(n=s)&&(o=o.total?100/o.total:0,n[0]=z(n[0]*o),n[1]=z(n[1]*o),t.stackedYData[r]=n[1])})},getExtremes:function(){var t,e,i,s,n=this.yAxis,o=this.processedXData,r=this.stackedYData||this.processedYData,a=r.length,h=[],l=0,c=this.xAxis.getExtremes(),d=c.min,c=c.max;for(s=0;s<a;s++)if(e=o[s],i=r[s],t=null!==i&&i!==V&&(!n.isLog||i.length||i>0),e=this.getExtremesFromAll||this.cropped||(o[s+1]||e)>=d&&(o[s-1]||e)<=c,t&&e)if(t=i.length)for(;t--;)null!==i[t]&&(h[l++]=i[t]);else h[l++]=i;this.dataMin=u(void 0,A(h)),this.dataMax=u(void 0,C(h))},translate:function(){this.processedXData||this.processData(),this.generatePoints();for(var t=this.options,e=t.stacking,i=this.xAxis,s=i.categories,n=this.yAxis,o=this.points,a=o.length,h=!!this.modifyValue,l=t.pointPlacement,d="between"===l||r(l),p=t.threshold,t=0;t<a;t++){var g=o[t],f=g.x,m=g.y,x=g.low,y=n.stacks[(this.negStacks&&m<p?"-":"")+this.stackKey];n.isLog&&m<=0&&(g.y=m=null),g.plotX=i.translate(f,0,0,0,1,l,"flags"===this.type),e&&this.visible&&y&&y[f]&&(y=y[f],m=y.points[this.index],x=m[0],m=m[1],0===x&&(x=u(p,n.min)),n.isLog&&x<=0&&(x=null),g.percentage="percent"===e&&m,g.total=g.stackTotal=y.total,g.stackY=m,y.setOffset(this.pointXOffset||0,this.barW||0)),g.yBottom=c(x)?n.translate(x,0,1,0,1):null,h&&(m=this.modifyValue(m,g)),g.plotY="number"==typeof m&&m!==1/0?n.translate(m,0,1,0,1):V,g.clientX=d?i.translate(f,0,0,0,1):g.plotX,g.negative=g.y<(p||0),g.category=s&&s[g.x]!==V?s[g.x]:g.x}this.getSegments()},setTooltipPoints:function(t){var e,i,s,n,o=[],r=this.xAxis,a=r&&r.getExtremes(),h=r?r.tooltipLen||r.len:this.chart.plotSizeX,l=[];if(this.options.enableMouseTracking!==!1){for(t&&(this.tooltipPoints=null),se(this.segments||this.points,function(t){o=o.concat(t)}),r&&r.reversed&&(o=o.reverse()),this.orderTooltipPoints&&this.orderTooltipPoints(o),t=o.length,n=0;n<t;n++)if(r=o[n],e=r.x,e>=a.min&&e<=a.max)for(s=o[n+1],e=i===V?0:i+1,i=o[n+1]?xt(mt(0,gt((r.clientX+(s?s.wrappedClientX||s.clientX:h))/2)),h):h;e>=0&&e<=i;)l[e++]=r;this.tooltipPoints=l}},tooltipHeaderFormatter:function(t){var e,i=this.tooltipOptions,s=i.xDateFormat,n=i.dateTimeLabelFormats,o=this.xAxis,a=o&&"datetime"===o.options.type,i=i.headerFormat,o=o&&o.closestPointRange;if(a&&!s)if(o){for(e in q)if(q[e]>=o){s=n[e];break}}else s=n.day;return a&&s&&r(t.key)&&(i=i.replace("{point.key}","{point.key:"+s+"}")),b(i,{point:t,series:this})},onMouseOver:function(){var t=this.chart,e=t.hoverSeries;e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&le(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},onMouseOut:function(){var t=this.options,e=this.chart,i=e.tooltip,s=e.hoverPoint;s&&s.onMouseOut(),this&&t.events.mouseOut&&le(this,"mouseOut"),i&&!t.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),this.setState(),e.hoverSeries=null},animate:function(e){var i,s=this,o=s.chart,r=o.renderer;i=s.options.animation;var a,h=o.clipBox,l=o.inverted;i&&!n(i)&&(i=ue[s.type].animation),a="_sharedClip"+i.duration+i.easing,e?(e=o[a],i=o[a+"m"],e||(o[a]=e=r.clipRect(t(h,{width:0})),o[a+"m"]=i=r.clipRect(-99,l?-o.plotLeft:-o.plotTop,99,l?o.chartWidth:o.chartHeight)),s.group.clip(e),s.markerGroup.clip(i),s.sharedClipKey=a):((e=o[a])&&(e.animate({width:o.plotSizeX},i),o[a+"m"].animate({width:o.plotSizeX+99},i)),s.animate=null,s.animationTimeout=setTimeout(function(){s.afterAnimate()},i.duration))},afterAnimate:function(){var t=this.chart,e=this.sharedClipKey,i=this.group;i&&this.options.clip!==!1&&(i.clip(t.clipRect),this.markerGroup.clip()),setTimeout(function(){e&&t[e]&&(t[e]=t[e].destroy(),t[e+"m"]=t[e+"m"].destroy())},100)},drawPoints:function(){var e,i,s,n,o,r,a,h,l,c,d=this.points,p=this.chart,g=this.options.marker,f=this.markerGroup;if(g.enabled||this._hasPointMarkers)for(n=d.length;n--;)o=d[n],i=gt(o.plotX),s=o.plotY,l=o.graphic,a=o.marker||{},e=g.enabled&&a.enabled===V||a.enabled,c=p.isInsidePlot(ut(i),s,p.inverted),e&&s!==V&&!isNaN(s)&&null!==o.y?(e=o.pointAttr[o.selected?"select":""],r=e.r,a=u(a.symbol,this.symbol),h=0===a.indexOf("url"),l?l.attr({visibility:c?It?"inherit":"visible":"hidden"}).animate(t({x:i-r,y:s-r},l.symbolName?{width:2*r,height:2*r}:{})):c&&(r>0||h)&&(o.graphic=p.renderer.symbol(a,i-r,s-r,2*r,2*r).attr(e).add(f))):l&&(o.graphic=l.destroy())},convertAttribs:function(t,e,i,s){var n,o,r=this.pointAttrToOptions,a={},t=t||{},e=e||{},i=i||{},s=s||{};for(n in r)o=r[n],a[n]=u(t[o],e[n],i[n],s[n]);return a},getAttribs:function(){var e,i,s,n=this,o=n.options,r=ue[n.type].marker?o.marker:o,a=r.states,h=a.hover,l=n.color,d={stroke:l,fill:l},p=n.points||[],u=[],g=n.pointAttrToOptions,f=o.negativeColor,m=r.lineColor;for(o.marker?(h.radius=h.radius||r.radius+2,h.lineWidth=h.lineWidth||r.lineWidth+1):h.color=h.color||ge(h.color||l).brighten(h.brightness).get(),u[""]=n.convertAttribs(r,d),se(["hover","select"],function(t){u[t]=n.convertAttribs(a[t],u[""])}),n.pointAttr=u,l=p.length;l--;){if(d=p[l],(r=d.options&&d.options.marker||d.options)&&r.enabled===!1&&(r.radius=0),d.negative&&f&&(d.color=d.fillColor=f),e=o.colorByPoint||d.color,d.options)for(s in g)c(r[g[s]])&&(e=!0);e?(r=r||{},i=[],a=r.states||{},e=a.hover=a.hover||{},o.marker||(e.color=ge(e.color||d.color).brighten(e.brightness||h.brightness).get()),i[""]=n.convertAttribs(t({color:d.color,fillColor:d.color,lineColor:null===m?d.color:V},r),u[""]),i.hover=n.convertAttribs(a.hover,u.hover,i[""]),i.select=n.convertAttribs(a.select,u.select,i[""])):i=u,d.pointAttr=i}},update:function(i,s){var n,o=this.chart,r=this.type,a=qt[r].prototype,i=e(this.userOptions,{animation:!1,index:this.index,pointStart:this.xData[0]},{data:this.options.data},i);this.remove(!1);for(n in a)a.hasOwnProperty(n)&&(this[n]=V);t(this,qt[i.type||r].prototype),this.init(o,i),u(s,!0)&&o.redraw(!1)},destroy:function(){var t,e,i,s,n,o=this,r=o.chart,a=/AppleWebKit\/533/.test(Tt),h=o.data||[];for(le(o,"destroy"),he(o),se(["xAxis","yAxis"],function(t){(n=o[t])&&(l(n.series,o),n.isDirty=n.forceRedraw=!0,n.stacks={})}),o.legendItem&&o.chart.legend.destroyItem(o),e=h.length;e--;)(i=h[e])&&i.destroy&&i.destroy();o.points=null,clearTimeout(o.animationTimeout),se("area,graph,dataLabelsGroup,group,markerGroup,tracker,graphNeg,areaNeg,posClip,negClip".split(","),function(e){o[e]&&(t=a&&"group"===e?"hide":"destroy",o[e][t]())}),r.hoverSeries===o&&(r.hoverSeries=null),l(r.series,o);for(s in o)delete o[s]},drawDataLabels:function(){var t,i,s,n,o=this,r=o.options.dataLabels,a=o.points;(r.enabled||o._hasPointLabels)&&(o.dlProcessOptions&&o.dlProcessOptions(r),n=o.plotGroup("dataLabelsGroup","data-labels",o.visible?"visible":"hidden",r.zIndex||6),i=r,se(a,function(a){var h,l,d,p=a.dataLabel,g=a.connector,f=!0;if(t=a.options&&a.options.dataLabels,h=u(t&&t.enabled,i.enabled),p&&!h)a.dataLabel=p.destroy();else if(h){if(r=e(i,t),h=r.rotation,l=a.getLabelConfig(),s=r.format?b(r.format,l):r.formatter.call(l,r),r.style.color=u(r.color,r.style.color,o.color,"black"),p)c(s)?(p.attr({text:s}),f=!1):(a.dataLabel=p=p.destroy(),g&&(a.connector=g.destroy()));else if(c(s)){p={fill:r.backgroundColor,stroke:r.borderColor,"stroke-width":r.borderWidth,r:r.borderRadius||0,rotation:h,padding:r.padding,zIndex:1};for(d in p)p[d]===V&&delete p[d];p=a.dataLabel=o.chart.renderer[h?"text":"label"](s,0,-999,null,null,null,r.useHTML).attr(p).css(r.style).add(n).shadow(r.shadow)}p&&o.alignDataLabel(a,p,r,null,f)}}))},alignDataLabel:function(e,i,s,n,o){var r=this.chart,a=r.inverted,h=u(e.plotX,-999),l=u(e.plotY,-999),c=i.getBBox();(e=this.visible&&r.isInsidePlot(e.plotX,e.plotY,a))&&(n=t({x:a?r.plotWidth-l:h,y:ut(a?r.plotHeight-h:l),width:0,height:0},n),t(s,{width:c.width,height:c.height}),s.rotation?(a={align:s.align,x:n.x+s.x+n.width/2,y:n.y+s.y+n.height/2},i[o?"attr":"animate"](a)):(i.align(s,null,n),a=i.alignAttr,"justify"===u(s.overflow,"justify")?this.justifyDataLabel(i,s,a,c,n,o):u(s.crop,!0)&&(e=r.isInsidePlot(a.x,a.y)&&r.isInsidePlot(a.x+c.width,a.y+c.height)))),e||i.attr({y:-999})},justifyDataLabel:function(t,e,i,s,n,o){var r,a,h=this.chart,l=e.align,c=e.verticalAlign;r=i.x,r<0&&("right"===l?e.align="left":e.x=-r,a=!0),r=i.x+s.width,r>h.plotWidth&&("left"===l?e.align="right":e.x=h.plotWidth-r,a=!0),r=i.y,r<0&&("bottom"===c?e.verticalAlign="top":e.y=-r,a=!0),r=i.y+s.height,r>h.plotHeight&&("top"===c?e.verticalAlign="bottom":e.y=h.plotHeight-r,a=!0),a&&(t.placed=!o,t.align(e,null,n))},getSegmentPath:function(t){var e=this,i=[],s=e.options.step;return se(t,function(n,o){var r,a=n.plotX,h=n.plotY;e.getPointSpline?i.push.apply(i,e.getPointSpline(t,n,o)):(i.push(o?"L":"M"),s&&o&&(r=t[o-1],"right"===s?i.push(r.plotX,h):"center"===s?i.push((r.plotX+a)/2,r.plotY,(r.plotX+a)/2,h):i.push(a,r.plotY)),i.push(n.plotX,n.plotY))}),i},getGraphPath:function(){var t,e=this,i=[],s=[];return se(e.segments,function(n){t=e.getSegmentPath(n),n.length>1?i=i.concat(t):s.push(n[0])}),e.singlePoints=s,e.graphPath=i},drawGraph:function(){var t=this,e=this.options,i=[["graph",e.lineColor||this.color]],s=e.lineWidth,n=e.dashStyle,o=this.getGraphPath(),r=e.negativeColor;r&&i.push(["graphNeg",r]),se(i,function(i,r){var a=i[0],h=t[a];h?(pe(h),h.animate({d:o})):s&&o.length&&(h={stroke:i[1],"stroke-width":s,zIndex:1},n?h.dashstyle=n:h["stroke-linecap"]=h["stroke-linejoin"]="round",t[a]=t.chart.renderer.path(o).attr(h).add(t.group).shadow(!r&&e.shadow))})},clipNeg:function(){var t,e=this.options,i=this.chart,s=i.renderer,n=e.negativeColor||e.negativeFillColor,o=this.graph,r=this.area,a=this.posClip,h=this.negClip;t=i.chartWidth;var l=i.chartHeight,c=mt(t,l),d=this.yAxis;n&&(o||r)&&(n=ut(d.toPixels(e.threshold||0,!0)),e={x:0,y:0,width:c,height:n},c={x:0,y:n,width:c,height:c},i.inverted&&(e.height=c.y=i.plotWidth-n,s.isVML&&(e={x:i.plotWidth-n-i.plotLeft,y:0,width:t,height:l},c={x:n+i.plotLeft-t,y:0,width:i.plotLeft+n,height:t})),d.reversed?(i=c,t=e):(i=e,t=c),a?(a.animate(i),h.animate(t)):(this.posClip=a=s.clipRect(i),this.negClip=h=s.clipRect(t),o&&this.graphNeg&&(o.clip(a),this.graphNeg.clip(h)),r&&(r.clip(a),this.areaNeg.clip(h))))},invertGroups:function(){function t(){var t={width:e.yAxis.len,height:e.xAxis.len};se(["group","markerGroup"],function(i){e[i]&&e[i].attr(t).invert()})}var e=this,i=e.chart;e.xAxis&&(ae(i,"resize",t),ae(e,"destroy",function(){he(i,"resize",t)}),t(),e.invertGroups=t)},plotGroup:function(t,e,i,s,n){var o=this[t],r=!o;return r&&(this[t]=o=this.chart.renderer.g(e).attr({visibility:i,zIndex:s||.1}).add(n)),o[r?"attr":"animate"](this.getPlotBox()),o},getPlotBox:function(){return{translateX:this.xAxis?this.xAxis.left:this.chart.plotLeft,translateY:this.yAxis?this.yAxis.top:this.chart.plotTop,scaleX:1,scaleY:1}},render:function(){var t,e=this.chart,i=this.options,s=i.animation&&!!this.animate&&e.renderer.isSVG,n=this.visible?"visible":"hidden",o=i.zIndex,r=this.hasRendered,a=e.seriesGroup;t=this.plotGroup("group","series",n,o,a),this.markerGroup=this.plotGroup("markerGroup","markers",n,o,a),s&&this.animate(!0),this.getAttribs(),t.inverted=!!this.isCartesian&&e.inverted,this.drawGraph&&(this.drawGraph(),this.clipNeg()),this.drawDataLabels(),this.drawPoints(),this.options.enableMouseTracking!==!1&&this.drawTracker(),e.inverted&&this.invertGroups(),i.clip!==!1&&!this.sharedClipKey&&!r&&t.clip(e.clipRect),s?this.animate():r||this.afterAnimate(),this.isDirty=this.isDirtyData=!1,this.hasRendered=!0},redraw:function(){var t=this.chart,e=this.isDirtyData,i=this.group,s=this.xAxis,n=this.yAxis;i&&(t.inverted&&i.attr({width:t.plotWidth,height:t.plotHeight}),i.animate({translateX:u(s&&s.left,t.plotLeft),translateY:u(n&&n.top,t.plotTop)})),this.translate(),this.setTooltipPoints(!0),this.render(),e&&le(this,"updatedData")},setState:function(t){var e=this.options,i=this.graph,s=this.graphNeg,n=e.states,e=e.lineWidth,t=t||"";this.state!==t&&(this.state=t,n[t]&&n[t].enabled===!1||(t&&(e=n[t].lineWidth||e+1),i&&!i.dashstyle&&(t={"stroke-width":e},i.attr(t),s&&s.attr(t))))},setVisible:function(t,e){var i,s=this,n=s.chart,o=s.legendItem,r=n.options.chart.ignoreHiddenSeries,a=s.visible;i=(s.visible=t=s.userOptions.visible=t===V?!a:t)?"show":"hide",se(["group","dataLabelsGroup","markerGroup","tracker"],function(t){s[t]&&s[t][i]()}),n.hoverSeries===s&&s.onMouseOut(),o&&n.legend.colorizeItem(s,t),s.isDirty=!0,s.options.stacking&&se(n.series,function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),se(s.linkedSeries,function(e){e.setVisible(t,!1)}),r&&(n.isDirtyBox=!0),e!==!1&&n.redraw(),le(s,i)},show:function(){this.setVisible(!0)},hide:function(){this.setVisible(!1)},select:function(t){this.selected=t=t===V?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),le(this,t?"select":"unselect")},drawTracker:function(){var t,e=this,i=e.options,s=i.trackByArea,n=[].concat(s?e.areaPath:e.graphPath),o=n.length,r=e.chart,a=r.pointer,h=r.renderer,l=r.options.tooltip.snap,c=e.tracker,d=i.cursor,p=d&&{cursor:d},d=e.singlePoints,u=function(){r.hoverSeries!==e&&e.onMouseOver()};if(o&&!s)for(t=o+1;t--;)"M"===n[t]&&n.splice(t+1,0,n[t+1]-l,n[t+2],"L"),(t&&"M"===n[t]||t===o)&&n.splice(t,0,"L",n[t-2]+l,n[t-1]);for(t=0;t<d.length;t++)o=d[t],n.push("M",o.plotX-l,o.plotY,"L",o.plotX+l,o.plotY);c?c.attr({d:n}):(e.tracker=h.path(n).attr({"stroke-linejoin":"round",visibility:e.visible?"visible":"hidden",stroke:Gt,fill:s?Gt:Et,"stroke-width":i.lineWidth+(s?0:2*l),zIndex:2}).add(e.group),se([e.tracker,e.markerGroup],function(t){t.addClass("highcharts-tracker").on("mouseover",u).on("mouseout",function(t){a.onTrackerMouseOut(t)}).css(p),Ot&&t.on("touchstart",u)}))}},Qt=m(be),qt.line=Qt,ue.area=e(Jt,{threshold:0}),Qt=m(be,{type:"area",getSegments:function(){var t,e,i,s,n,o=[],r=[],a=[],h=this.xAxis,l=this.yAxis,c=l.stacks[this.stackKey],d={},p=this.points,u=this.options.connectNulls;if(this.options.stacking&&!this.cropped){for(s=0;s<p.length;s++)d[p[s].x]=p[s];for(n in c)a.push(+n);a.sort(function(t,e){return t-e}),se(a,function(s){(!u||d[s]&&null!==d[s].y)&&(d[s]?r.push(d[s]):(t=h.translate(s),i=c[s].percent?c[s].total?100*c[s].cum/c[s].total:0:c[s].cum,e=l.toPixels(i,!0),r.push({y:null,plotX:t,clientX:t,plotY:e,yBottom:e,onMouseOver:Xt})))}),r.length&&o.push(r)}else be.prototype.getSegments.call(this),o=this.segments;this.segments=o},getSegmentPath:function(t){var e,i=be.prototype.getSegmentPath.call(this,t),s=[].concat(i),n=this.options;e=i.length;var o,r=this.yAxis.getThreshold(n.threshold);if(3===e&&s.push("L",i[1],i[2]),n.stacking&&!this.closedStacks)for(e=t.length-1;e>=0;e--)o=u(t[e].yBottom,r),e<t.length-1&&n.step&&s.push(t[e+1].plotX,o),s.push(t[e].plotX,o);else this.closeSegment(s,t,r);return this.areaPath=this.areaPath.concat(s),i},closeSegment:function(t,e,i){t.push("L",e[e.length-1].plotX,i,"L",e[0].plotX,i)},drawGraph:function(){this.areaPath=[],be.prototype.drawGraph.apply(this);var t=this,e=this.areaPath,i=this.options,s=i.negativeColor,n=i.negativeFillColor,o=[["area",this.color,i.fillColor]];(s||n)&&o.push(["areaNeg",s,n]),se(o,function(s){var n=s[0],o=t[n];o?o.animate({d:e}):t[n]=t.chart.renderer.path(e).attr({fill:u(s[2],ge(s[1]).setOpacity(u(i.fillOpacity,.75)).get()),zIndex:0}).add(t.group)})},drawLegendSymbol:function(t,e){e.legendSymbol=this.chart.renderer.rect(0,t.baseline-11,t.options.symbolWidth,12,2).attr({zIndex:3}).add(e.legendGroup)}}),qt.area=Qt,ue.spline=e(Jt),me=m(be,{type:"spline",getPointSpline:function(t,e,i){var s,n,o,r,a=e.plotX,h=e.plotY,l=t[i-1],c=t[i+1];if(l&&c){t=l.plotY,o=c.plotX;var d,c=c.plotY;s=(1.5*a+l.plotX)/2.5,n=(1.5*h+t)/2.5,o=(1.5*a+o)/2.5,r=(1.5*h+c)/2.5,d=(r-n)*(o-a)/(o-s)+h-r,n+=d,r+=d,n>t&&n>h?(n=mt(t,h),r=2*h-n):n<t&&n<h&&(n=xt(t,h),r=2*h-n),r>c&&r>h?(r=mt(c,h),n=2*h-r):r<c&&r<h&&(r=xt(c,h),n=2*h-r),e.rightContX=o,e.rightContY=r}return i?(e=["C",l.rightContX||l.plotX,l.rightContY||l.plotY,s||a,n||h,a,h],
l.rightContX=l.rightContY=null):e=["M",a,h],e}}),qt.spline=me,ue.areaspline=e(ue.area),xe=Qt.prototype,me=m(me,{type:"areaspline",closedStacks:!0,getSegmentPath:xe.getSegmentPath,closeSegment:xe.closeSegment,drawGraph:xe.drawGraph,drawLegendSymbol:xe.drawLegendSymbol}),qt.areaspline=me,ue.column=e(Jt,{borderColor:"#FFFFFF",borderWidth:1,borderRadius:0,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{brightness:.1,shadow:!1},select:{color:"#C0C0C0",borderColor:"#000000",shadow:!1}},dataLabels:{align:null,verticalAlign:null,y:null},stickyTracking:!1,threshold:0}),me=m(be,{type:"column",pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color",r:"borderRadius"},cropShoulder:0,trackerGroups:["group","dataLabelsGroup"],negStacks:!0,init:function(){be.prototype.init.apply(this,arguments);var t=this,e=t.chart;e.hasRendered&&se(e.series,function(e){e.type===t.type&&(e.isDirty=!0)})},getColumnMetrics:function(){var t,e,i=this,s=i.options,n=i.xAxis,o=i.yAxis,r=n.reversed,a={},h=0;s.grouping===!1?h=1:se(i.chart.series,function(s){var n=s.options,r=s.yAxis;s.type===i.type&&s.visible&&o.len===r.len&&o.pos===r.pos&&(n.stacking?(t=s.stackKey,a[t]===V&&(a[t]=h++),e=a[t]):n.grouping!==!1&&(e=h++),s.columnIndex=e)});var n=xt(yt(n.transA)*(n.ordinalSlope||s.pointRange||n.closestPointRange||1),n.len),l=n*s.groupPadding,d=(n-2*l)/h,p=s.pointWidth,s=c(p)?(d-p)/2:d*s.pointPadding,p=u(p,d-2*s);return i.columnMetrics={width:p,offset:s+(l+((r?h-(i.columnIndex||0):i.columnIndex)||0)*d-n/2)*(r?-1:1)}},translate:function(){var t=this.chart,e=this.options,i=e.borderWidth,s=this.yAxis,n=this.translatedThreshold=s.getThreshold(e.threshold),o=u(e.minPointLength,5),e=this.getColumnMetrics(),r=e.width,a=this.barW=ft(mt(r,1+2*i)),h=this.pointXOffset=e.offset,l=-(i%2?.5:0),c=i%2?.5:1;t.renderer.isVML&&t.inverted&&(c+=1),be.prototype.translate.apply(this),se(this.points,function(t){var e,i=u(t.yBottom,n),d=xt(mt(-999-i,t.plotY),s.len+999+i),p=t.plotX+h,g=a,f=xt(d,i),d=mt(d,i)-f;yt(d)<o&&o&&(d=o,f=ut(yt(f-n)>o?i-o:n-(s.translate(t.y,0,1,0,1)<=n?o:0))),t.barX=p,t.pointWidth=r,i=yt(p)<.5,g=ut(p+g)+l,p=ut(p)+l,g-=p,e=yt(f)<.5,d=ut(f+d)+c,f=ut(f)+c,d-=f,i&&(p+=1,g-=1),e&&(f-=1,d+=1),t.shapeType="rect",t.shapeArgs={x:p,y:f,width:g,height:d}})},getSymbol:Xt,drawLegendSymbol:Qt.prototype.drawLegendSymbol,drawGraph:Xt,drawPoints:function(){var t,i=this,s=i.options,n=i.chart.renderer;se(i.points,function(o){var r=o.plotY,a=o.graphic;r===V||isNaN(r)||null===o.y?a&&(o.graphic=a.destroy()):(t=o.shapeArgs,a?(pe(a),a.animate(e(t))):o.graphic=n[o.shapeType](t).attr(o.pointAttr[o.selected?"select":""]).add(i.group).shadow(s.shadow,null,s.stacking&&!s.borderRadius))})},drawTracker:function(){var t=this,e=t.chart,i=e.pointer,s=t.options.cursor,n=s&&{cursor:s},o=function(i){var s,n=i.target;for(e.hoverSeries!==t&&t.onMouseOver();n&&!s;)s=n.point,n=n.parentNode;s!==V&&s!==e.hoverPoint&&s.onMouseOver(i)};se(t.points,function(t){t.graphic&&(t.graphic.element.point=t),t.dataLabel&&(t.dataLabel.element.point=t)}),t._hasTracking||(se(t.trackerGroups,function(e){t[e]&&(t[e].addClass("highcharts-tracker").on("mouseover",o).on("mouseout",function(t){i.onTrackerMouseOut(t)}).css(n),Ot)&&t[e].on("touchstart",o)}),t._hasTracking=!0)},alignDataLabel:function(t,i,s,n,o){var r=this.chart,a=r.inverted,h=t.dlBox||t.shapeArgs,l=t.below||t.plotY>u(this.translatedThreshold,r.plotSizeY),c=u(s.inside,!!this.options.stacking);h&&(n=e(h),a&&(n={x:r.plotWidth-n.y-n.height,y:r.plotHeight-n.x-n.width,width:n.height,height:n.width}),!c)&&(a?(n.x+=l?0:n.width,n.width=0):(n.y+=l?n.height:0,n.height=0)),s.align=u(s.align,!a||c?"center":l?"right":"left"),s.verticalAlign=u(s.verticalAlign,a||c?"middle":l?"top":"bottom"),be.prototype.alignDataLabel.call(this,t,i,s,n,o)},animate:function(t){var e=this.yAxis,i=this.options,s=this.chart.inverted,n={};It&&(t?(n.scaleY=.001,t=xt(e.pos+e.len,mt(e.pos,e.toPixels(i.threshold))),s?n.translateX=t-e.len:n.translateY=t,this.group.attr(n)):(n.scaleY=1,n[s?"translateX":"translateY"]=e.pos,this.group.animate(n,this.options.animation),this.animate=null))},remove:function(){var t=this,e=t.chart;e.hasRendered&&se(e.series,function(e){e.type===t.type&&(e.isDirty=!0)}),be.prototype.remove.apply(t,arguments)}}),qt.column=me,ue.bar=e(ue.column),xe=m(me,{type:"bar",inverted:!0}),qt.bar=xe,ue.scatter=e(Jt,{lineWidth:0,tooltip:{headerFormat:'<span style="font-size: 10px; color:{series.color}">{series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>",followPointer:!0},stickyTracking:!1}),xe=m(be,{type:"scatter",sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["markerGroup"],drawTracker:me.prototype.drawTracker,setTooltipPoints:Xt}),qt.scatter=xe,ue.pie=e(Jt,{borderColor:"#FFFFFF",borderWidth:1,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{distance:30,enabled:!0,formatter:function(){return this.point.name}},ignoreHiddenPoint:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,states:{hover:{brightness:.1,shadow:!1}},stickyTracking:!1,tooltip:{followPointer:!0}}),Jt={type:"pie",isCartesian:!1,pointClass:m(ve,{init:function(){ve.prototype.init.apply(this,arguments);var e,i=this;return i.y<0&&(i.y=null),t(i,{visible:i.visible!==!1,name:u(i.name,"Slice")}),e=function(t){i.slice("select"===t.type)},ae(i,"select",e),ae(i,"unselect",e),i},setVisible:function(t){var e,i=this,s=i.series,n=s.chart;i.visible=i.options.visible=t=t===V?!i.visible:t,s.options.data[ie(i,s.data)]=i.options,e=t?"show":"hide",se(["graphic","dataLabel","connector","shadowGroup"],function(t){i[t]&&i[t][e]()}),i.legendItem&&n.legend.colorizeItem(i,t),!s.isDirty&&s.options.ignoreHiddenPoint&&(s.isDirty=!0,n.redraw())},slice:function(t,e,i){var s=this.series;B(i,s.chart),u(e,!0),this.sliced=this.options.sliced=t=c(t)?t:!this.sliced,s.options.data[ie(this,s.data)]=this.options,t=t?this.slicedTranslation:{translateX:0,translateY:0},this.graphic.animate(t),this.shadowGroup&&this.shadowGroup.animate(t)}}),requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","dataLabelsGroup"],pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color"},getColor:Xt,animate:function(t){var e=this,i=e.points,s=e.startAngleRad;t||(se(i,function(t){var i=t.graphic,t=t.shapeArgs;i&&(i.attr({r:e.center[3]/2,start:s,end:s}),i.animate({r:t.r,start:t.start,end:t.end},e.options.animation))}),e.animate=null)},setData:function(t,e){be.prototype.setData.call(this,t,!1),this.processData(),this.generatePoints(),u(e,!0)&&this.chart.redraw()},generatePoints:function(){var t,e,i,s,n=0,o=this.options.ignoreHiddenPoint;for(be.prototype.generatePoints.call(this),e=this.points,i=e.length,t=0;t<i;t++)s=e[t],n+=o&&!s.visible?0:s.y;for(this.total=n,t=0;t<i;t++)s=e[t],s.percentage=n>0?s.y/n*100:0,s.total=n},getCenter:function(){var t,e,s=this.options,n=this.chart,o=2*(s.slicedOffset||0),r=n.plotWidth-2*o,a=n.plotHeight-2*o,n=s.center,s=[u(n[0],"50%"),u(n[1],"50%"),s.size||"100%",s.innerSize||0],h=xt(r,a);return re(s,function(s,n){return e=/%$/.test(s),t=n<2||2===n&&e,(e?[r,a,h,h][n]*i(s)/100:s)+(t?o:0)})},translate:function(t){this.generatePoints();var e,i,s,n,o,r=0,a=this.options,h=a.slicedOffset,l=h+a.borderWidth,c=a.startAngle||0,d=this.startAngleRad=kt/180*(c-90),c=(this.endAngleRad=kt/180*((a.endAngle||c+360)-90))-d,p=this.points,u=a.dataLabels.distance,a=a.ignoreHiddenPoint,g=p.length;for(t||(this.center=t=this.getCenter()),this.getX=function(e,i){return s=pt.asin((e-t[1])/(t[2]/2+u)),t[0]+(i?-1:1)*vt(s)*(t[2]/2+u)},n=0;n<g;n++)o=p[n],e=d+r*c,a&&!o.visible||(r+=o.percentage/100),i=d+r*c,o.shapeType="arc",o.shapeArgs={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:ut(1e3*e)/1e3,end:ut(1e3*i)/1e3},s=(i+e)/2,s>.75*c&&(s-=2*kt),o.slicedTranslation={translateX:ut(vt(s)*h),translateY:ut(bt(s)*h)},e=vt(s)*t[2]/2,i=bt(s)*t[2]/2,o.tooltipPos=[t[0]+.7*e,t[1]+.7*i],o.half=s<-kt/2||s>kt/2?1:0,o.angle=s,l=xt(l,u/2),o.labelPos=[t[0]+e+vt(s)*u,t[1]+i+bt(s)*u,t[0]+e+vt(s)*l,t[1]+i+bt(s)*l,t[0]+e,t[1]+i,u<0?"center":o.half?"right":"left",s]},setTooltipPoints:Xt,drawGraph:null,drawPoints:function(){var e,i,s,n,o=this,r=o.chart.renderer,a=o.options.shadow;a&&!o.shadowGroup&&(o.shadowGroup=r.g("shadow").add(o.group)),se(o.points,function(h){i=h.graphic,n=h.shapeArgs,s=h.shadowGroup,a&&!s&&(s=h.shadowGroup=r.g("shadow").add(o.shadowGroup)),e=h.sliced?h.slicedTranslation:{translateX:0,translateY:0},s&&s.attr(e),i?i.animate(t(n,e)):h.graphic=i=r.arc(n).setRadialReference(o.center).attr(h.pointAttr[h.selected?"select":""]).attr({"stroke-linejoin":"round"}).attr(e).add(o.group).shadow(a,s),h.visible===!1&&h.setVisible(!1)})},sortByAngle:function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},drawDataLabels:function(){var t,e,i,s,n,o,r,a,h,l,c,d,p=this,g=p.data,f=p.chart,m=p.options.dataLabels,x=u(m.connectorPadding,10),y=u(m.connectorWidth,1),v=f.plotWidth,f=f.plotHeight,b=u(m.softConnector,!0),k=m.distance,w=p.center,T=w[2]/2,S=w[1],L=k>0,P=[[],[]],A=[0,0,0,0],M=function(t,e){return e.y-t.y};if(p.visible&&(m.enabled||p._hasPointLabels)){for(be.prototype.drawDataLabels.apply(p),se(g,function(t){t.dataLabel&&P[t.half].push(t)}),c=0;!r&&g[c];)r=g[c]&&g[c].dataLabel&&(g[c].dataLabel.getBBox().height||21),c++;for(c=2;c--;){var D,g=[],I=[],z=P[c],B=z.length;if(p.sortByAngle(z,c-.5),k>0){for(d=S-T-k;d<=S+T+k;d+=r)g.push(d);if(n=g.length,B>n){for(t=[].concat(z),t.sort(M),d=B;d--;)t[d].rank=d;for(d=B;d--;)z[d].rank>=n&&z.splice(d,1);B=z.length}for(d=0;d<B;d++){t=z[d],o=t.labelPos,t=9999;var O,H;for(H=0;H<n;H++)O=yt(g[H]-o[1]),O<t&&(t=O,D=H);if(D<d&&null!==g[d])D=d;else for(n<B-d+D&&null!==g[d]&&(D=n-B+d);null===g[D];)D++;I.push({i:D,y:g[D]}),g[D]=null}I.sort(M)}for(d=0;d<B;d++)t=z[d],o=t.labelPos,s=t.dataLabel,l=t.visible===!1?"hidden":"visible",t=o[1],k>0?(n=I.pop(),D=n.i,h=n.y,(t>h&&null!==g[D+1]||t<h&&null!==g[D-1])&&(h=t)):h=t,a=m.justify?w[0]+(c?-1:1)*(T+k):p.getX(0===D||D===g.length-1?t:h,c),s._attr={visibility:l,align:o[6]},s._pos={x:a+m.x+({left:x,right:-x}[o[6]]||0),y:h+m.y-10},s.connX=a,s.connY=h,null===this.options.size&&(n=s.width,a-n<x?A[3]=mt(ut(n-a+x),A[3]):a+n>v-x&&(A[1]=mt(ut(a+n-v+x),A[1])),h-r/2<0?A[0]=mt(ut(-h+r/2),A[0]):h+r/2>f&&(A[2]=mt(ut(h+r/2-f),A[2])))}(0===C(A)||this.verifyDataLabelOverflow(A))&&(this.placeDataLabels(),L&&y&&se(this.points,function(t){e=t.connector,o=t.labelPos,(s=t.dataLabel)&&s._pos?(l=s._attr.visibility,a=s.connX,h=s.connY,i=b?["M",a+("left"===o[6]?5:-5),h,"C",a,h,2*o[2]-o[4],2*o[3]-o[5],o[2],o[3],"L",o[4],o[5]]:["M",a+("left"===o[6]?5:-5),h,"L",o[2],o[3],"L",o[4],o[5]],e?(e.animate({d:i}),e.attr("visibility",l)):t.connector=e=p.chart.renderer.path(i).attr({"stroke-width":y,stroke:m.connectorColor||t.color||"#606060",visibility:l}).add(p.group)):e&&(t.connector=e.destroy())}))}},verifyDataLabelOverflow:function(t){var e,i=this.center,s=this.options,n=s.center,o=s=s.minSize||80;return null!==n[0]?o=mt(i[2]-mt(t[1],t[3]),s):(o=mt(i[2]-t[1]-t[3],s),i[0]+=(t[3]-t[1])/2),null!==n[1]?o=mt(xt(o,i[2]-mt(t[0],t[2])),s):(o=mt(xt(o,i[2]-t[0]-t[2]),s),i[1]+=(t[0]-t[2])/2),o<i[2]?(i[2]=o,this.translate(i),se(this.points,function(t){t.dataLabel&&(t.dataLabel._pos=null)}),this.drawDataLabels()):e=!0,e},placeDataLabels:function(){se(this.points,function(t){var e,t=t.dataLabel;t&&((e=t._pos)?(t.attr(t._attr),t[t.moved?"animate":"attr"](e),t.moved=!0):t&&t.attr({y:-999}))})},alignDataLabel:Xt,drawTracker:me.prototype.drawTracker,drawLegendSymbol:Qt.prototype.drawLegendSymbol,getSymbol:Xt},Jt=m(be,Jt),qt.pie=Jt,t(Highcharts,{Axis:Y,Chart:F,Color:ge,Legend:N,Pointer:G,Point:ve,Tick:R,Tooltip:E,Renderer:j,Series:be,SVGElement:H,SVGRenderer:fe,arrayMin:A,arrayMax:C,charts:Wt,dateFormat:Z,format:b,pathAnim:$,getOptions:function(){return U},hasBidiBug:zt,isTouchDevice:Mt,numberFormat:x,seriesTypes:qt,setOptions:function(t){return U=e(U,t),O(),U},addEvent:ae,removeEvent:he,createElement:f,discardElement:D,css:g,each:se,extend:t,map:re,merge:e,pick:u,splat:p,extendClass:m,pInt:i,wrap:v,svg:It,canvas:Bt,vml:!It&&!Bt,product:"Highcharts",version:"3.0.6"})}();