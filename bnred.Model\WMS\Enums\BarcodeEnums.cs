using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 条码类型枚举
    /// 用于标识不同的条码类型
    /// </summary>
    public enum BarcodeType
    {
        /// <summary>
        /// 物料条码
        /// 标识具体物料的条码
        /// </summary>
        [Display(Name = "物料条码")]
        Material = 0,

        /// <summary>
        /// 库位条码
        /// 标识库位的条码
        /// </summary>
        [Display(Name = "库位条码")]
        Location = 1,

        /// <summary>
        /// 容器条码
        /// 标识周转箱等容器的条码
        /// </summary>
        [Display(Name = "容器条码")]
        Container = 2,

        /// <summary>
        /// 托盘条码
        /// 标识托盘的条码
        /// </summary>
        [Display(Name = "托盘条码")]
        Pallet = 3,

        /// <summary>
        /// 包装条码
        /// 标识包装单位的条码
        /// </summary>
        [Display(Name = "包装条码")]
        Package = 4
    }

    /// <summary>
    /// 条码状态枚举
    /// 用于标识条码的使用状态
    /// </summary>
    public enum BarcodeStatus
    {
        /// <summary>
        /// 已生成
        /// 条码已生成但未使用
        /// </summary>
        [Display(Name = "已生成")]
        Generated = 0,

        /// <summary>
        /// 已使用
        /// 条码正在使用中
        /// </summary>
        [Display(Name = "已使用")]
        Used = 1,

        /// <summary>
        /// 已作废
        /// 条码已作废不可用
        /// </summary>
        [Display(Name = "已作废")]
        Voided = 2,

        /// <summary>
        /// 已失效
        /// 条码已过期失效
        /// </summary>
        [Display(Name = "已失效")]
        Expired = 3
    }
} 