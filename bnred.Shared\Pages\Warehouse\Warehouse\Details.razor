﻿@page "/Warehouse/Warehouse/Details/{id}"
@using bnred.ViewModel.Warehouse.WarehouseVMs;
@inherits BasePage

<ValidateForm @ref="vform" Model="@Model" >
    <Row ItemsPerRow="ItemsPerRow.Two" RowType="RowType.Normal">

            <Display @bind-Value="@Model.Entity.ID"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Code"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Name"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Type"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Status"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Address"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ContactPerson"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ContactPhone"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Area"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Volume"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.ManagerID" Lookup="@AllFrameworkUsers"  ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Description"   ShowLabel="true"/>
            <Display @bind-Value="@Model.Entity.Remark"   ShowLabel="true"/>
    </Row>
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
    </div>
</ValidateForm>

@code {

    private WarehouseVM Model = null;
    private ValidateForm vform { get; set; }
    [Parameter]
    public string id { get; set; }

    private List<SelectedItem> AllFrameworkUsers = new List<SelectedItem>();


    protected override async Task OnInitializedAsync()
    {

        AllFrameworkUsers = await WtmBlazor.Api.CallItemsApi("/api/Warehouse/GetFrameworkUsers", placeholder: WtmBlazor.Localizer["Sys.All"]);

        var rv = await WtmBlazor.Api.CallAPI<WarehouseVM>($"/api/Warehouse/{id}");
        Model = rv.Data;
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
