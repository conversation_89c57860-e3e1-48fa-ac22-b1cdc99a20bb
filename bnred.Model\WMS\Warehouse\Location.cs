using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 库位信息
    /// 用于存储仓库内部库位的基本信息
    /// </summary>
    [Table("Locations")]
    public class Location : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 库位编码
        /// </summary>
        [Display(Name = "库位编码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        [Display(Name = "库位名称")]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 库区ID
        /// </summary>
        [Display(Name = "库区")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string AreaId { get; set; }

        /// <summary>
        /// 库区
        /// </summary>
        [Display(Name = "库区")]
        public WarehouseArea Area { get; set; }

        /// <summary>
        /// 库位类型
        /// </summary>
        [Display(Name = "库位类型")]
        public LocationType Type { get; set; }

        /// <summary>
        /// 库位状态
        /// </summary>
        [Display(Name = "库位状态")]
        public LocationStatus Status { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        [Display(Name = "行号")]
        [StringLength(10)]
        public string Row { get; set; }

        /// <summary>
        /// 列号
        /// </summary>
        [Display(Name = "列号")]
        [StringLength(10)]
        public string Column { get; set; }

        /// <summary>
        /// 层号
        /// </summary>
        [Display(Name = "层号")]
        [StringLength(10)]
        public string Level { get; set; }

        /// <summary>
        /// 最大承重(kg)
        /// </summary>
        [Display(Name = "最大承重(kg)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxWeight { get; set; }

        /// <summary>
        /// 最大体积(立方米)
        /// </summary>
        [Display(Name = "最大体积(立方米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxVolume { get; set; }

        /// <summary>
        /// 长度(米)
        /// </summary>
        [Display(Name = "长度(米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Length { get; set; }

        /// <summary>
        /// 宽度(米)
        /// </summary>
        [Display(Name = "宽度(米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Width { get; set; }

        /// <summary>
        /// 高度(米)
        /// </summary>
        [Display(Name = "高度(米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Height { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        [Display(Name = "条码")]
        [StringLength(50)]
        public string Barcode { get; set; }

        /// <summary>
        /// 是否允许混放
        /// </summary>
        [Display(Name = "是否允许混放")]
        public bool AllowMixed { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Display(Name = "是否启用")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
}