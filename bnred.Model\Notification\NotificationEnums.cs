using System.ComponentModel.DataAnnotations;

namespace bnred.Model
{
    /// <summary>
    /// 通知类型枚举
    /// 用于标识不同类型的通知
    /// </summary>
    public enum NotificationType
    {
        /// <summary>
        /// 系统通知
        /// </summary>
        [Display(Name = "系统通知")]
        System = 0,

        /// <summary>
        /// 任务通知
        /// </summary>
        [Display(Name = "任务通知")]
        Task = 1,

        /// <summary>
        /// 警报通知
        /// </summary>
        [Display(Name = "警报通知")]
        Alert = 2,

        /// <summary>
        /// 库存通知
        /// </summary>
        [Display(Name = "库存通知")]
        Inventory = 3,

        /// <summary>
        /// 订单通知
        /// </summary>
        [Display(Name = "订单通知")]
        Order = 4,

        /// <summary>
        /// 审批通知
        /// </summary>
        [Display(Name = "审批通知")]
        Approval = 5,

        /// <summary>
        /// 其他通知
        /// </summary>
        [Display(Name = "其他通知")]
        Other = 6
    }

    /// <summary>
    /// 通知优先级枚举
    /// 用于标识通知的重要程度
    /// </summary>
    public enum NotificationPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        [Display(Name = "低优先级")]
        Low = 0,

        /// <summary>
        /// 中优先级
        /// </summary>
        [Display(Name = "中优先级")]
        Medium = 1,

        /// <summary>
        /// 高优先级
        /// </summary>
        [Display(Name = "高优先级")]
        High = 2,

        /// <summary>
        /// 紧急
        /// </summary>
        [Display(Name = "紧急")]
        Urgent = 3
    }

    /// <summary>
    /// 通知状态枚举
    /// 用于标识通知的处理状态
    /// </summary>
    public enum NotificationStatus
    {
        /// <summary>
        /// 待发送
        /// </summary>
        [Display(Name = "待发送")]
        Pending = 0,

        /// <summary>
        /// 已发送
        /// </summary>
        [Display(Name = "已发送")]
        Sent = 1,

        /// <summary>
        /// 已读取
        /// </summary>
        [Display(Name = "已读取")]
        Read = 2,

        /// <summary>
        /// 已处理
        /// </summary>
        [Display(Name = "已处理")]
        Processed = 3,

        /// <summary>
        /// 已过期
        /// </summary>
        [Display(Name = "已过期")]
        Expired = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 5
    }
} 