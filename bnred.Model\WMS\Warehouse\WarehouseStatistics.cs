using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    [Table("WMS_WarehouseStatistics")]
    public class WarehouseStatistics : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Required]
        public DateTime StatisticsDate { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal TotalInventoryQuantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal TotalInventoryValue { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal InboundQuantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal OutboundQuantity { get; set; }

        [Required]
        public int TotalLocations { get; set; }

        [Required]
        public int UsedLocations { get; set; }

        [Required]
        public int EmptyLocations { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SpaceUtilization { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 