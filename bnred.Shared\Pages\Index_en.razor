<div class="index">
    <div class="welcome container">
        <div class="form-inline">
            <div class="row">
                <div class="col-12 col-lg-6">
                    <div class="welcome-header">
                        <h4>
                            WTM，stands for<code>
                                WalkingTec
                                MVVM
                            </code>
                        </h4>
                        <div>
                            <p>WTM is a rapid development framework, how fast is it?At least at present, in the open source project of <code>dotnet core</code>, I haven't seen a more grounded and faster development framework. The design concept of WTM is to speed up the development speed and reduce the development cost to the greatest extent.</p>
                            <p>
                                Microsoft has finally figured it out in the past two years, The emergence of <code>dotnet core</code> and the acquisition of GitHub are both very right directions. Of course, there is still a long way to go to reach the Java ecosystem, so I will contribute a little.
                            </p>
                        </div>

                    </div>

                </div>
                <div class="col-12 col-lg-6">
                    <div class="welcome-header">
                        <h4>
                            &nbsp;
                        </h4>
                        <p>
                            Since WTM is open sourced, it has been loved by more and more developers, and WTM will surely return your love with a more mature and stable attitude. WTM Blazor version needs special thanks to my friend, Microsoft MVP <PERSON> brought <a href="https://www.blazor.zone/" target="_blank" type="primary">BootstrapBlazor component library</a> . Improve yourself and benefit others, I am not alone!
                        </p>
                        <p>
                            —— Github：<a href="https://github.com/dotnetcore/WTM" target="_blank" type="primary">https://github.com/dotnetcore/WTM</a>
                        </p>
                        <p>
                            —— Docs：<a href="https://wtmdoc.walkingtec.cn" target="_blank" type="primary">https://wtmdoc.walkingtec.cn</a>
                        </p>
                        <p>
                            —— QQ：694148336
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-inline">
            <div class="row">
                <div class="col-12">
                    <div class="welcome-footer">
                        <div class="d-flex flex-wrap q-link">
                            <div>
                                <a target="_self" href="/_Admin/FrameworkUser" :underline="false">
                                    <i class="fa fa-user fa-3"></i>
                                    <p class="link-ctx">Users</p>
                                </a>
                            </div>
                            <div>
                                <a target="_self" href="/_Admin/FrameworkRole" :underline="false">
                                    <i class="fa fa-clipboard fa-3"></i>
                                    <p class="link-ctx">Roles</p>
                                </a>
                            </div>
                            <div>
                                <a target="_self" href="/_Admin/FrameworkMenu" :underline="false">
                                    <i class="fa fa-bars fa-3"></i>
                                    <p class="link-ctx">Menu</p>
                                </a>
                            </div>
                            <div>
                                <a target="_self" href="/_Admin/FrameworkGroup" :underline="false">
                                    <i class="fa fa-users fa-3"></i>
                                    <p class="link-ctx">Groups</p>
                                </a>
                            </div>
                            <div>
                                <a target="_self" href="/_Admin/DataPrivilege" :underline="false">
                                    <i class="fa fa-shield fa-3"></i>
                                    <p class="link-ctx">DataPris</p>
                                </a>
                            </div>
                            <div>
                                <a target="_self" href="/_Admin/ActionLog" :underline="false">
                                    <i class="fa fa-database fa-3"></i>
                                    <p class="link-ctx">Logs</p>
                                </a>
                            </div>
                            <div>
                                <a target="_blank" href="https://wtmdoc.walkingtec.cn/" :underline="false">
                                    <i class="fa fa-file-text fa-3"></i>
                                    <p class="link-ctx">Doc</p>
                                </a>
                            </div>
                            <div>
                                <a target="_blank" href="/_codegen?ui=blazor" :underline="false">
                                    <i class="fa fa-cogs fa-3"></i>
                                    <p class="link-ctx">CodeGen</p>
                                </a>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="intro container">
        <div class="form-inline">
            <div class="row">
                <div class="form-group col-12 col-sm-6 col-md-4">
                    <div class="card border-success" style="height: 293px;">
                        <p class="d-none d-sm-block">
                            <i class="fa fa-cubes"></i>
                        </p>
                        <h3>Rich modules</h3>
                        <div>
                            Provides a variety of base classes, encapsulating most of the common background operations<br />
                            Provides common modules such as users, roles, user groups, menus, logs, permissions configuration, etc.<br />
                        </div>
                    </div>
                </div>
                <div class="form-group col-12 col-sm-6 col-md-4">
                    <div class="card border-primary" style="height: 293px;">
                        <p class="d-none d-sm-block">
                            <i class="fa fa-rocket"></i>
                        </p>
                        <h3>Performance</h3>
                        <div>
                            Support one-to-many, many-to-many association model recognition and code generation<br />
                            Support React, Vue, Blazor, LayUI and other front-end architectures<br />
                            Support sqlserver, sqlite, mysql, pgsql, oracle and other databases<br />
                        </div>
                    </div>
                </div>
                <div class="form-group col-12 col-sm-6 col-md-4">
                    <div class="card border-info" style="height: 293px;">
                        <p class="d-none d-sm-block">
                            <i class="fa fa-trophy"></i>
                        </p>
                        <h3>Easy to use</h3>
                        <div>
                            One-click generation of WTM project<br />
                            One-click generation of additions, deletions, corrections, import and export, batch operation codes<br />
                            Most of the controls of Layui, AntD, Element, Blazor are encapsulated, making it easier to write the front desk<br />
                        </div>
                    </div>
                </div>
                <div class="form-group col-12 col-sm-6 col-md-4">
                    <div class="card border-danger">
                        <p class="d-none d-sm-block">
                            <i class="fa fa-github"></i>
                        </p>
                        <h3>Free and open source</h3>
                        <div>
                            <div class="form-inline">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap" style=" justify-content: space-around;">
                                            <div class="badge-widget">
                                                <i class="fa fa-star"></i>Star
                                                <Badge style="display:block" Color="Color.Success"><span style="padding: 0 2px;">@model.stargazers_count</span></Badge>
                                            </div>
                                            <div class="badge-widget">
                                                <i class="fa fa-code-fork"></i>Frok
                                                <Badge style="display:block" Color="Color.Success"><span style="padding: 0 2px;">@model.forks_count</span></Badge>
                                            </div>
                                            <div class="badge-widget">
                                                <i class="fa fa-eye"></i>Watch
                                                <Badge style="display:block" Color="Color.Success"><span style="padding: 0 2px;">@model.subscribers_count</span></Badge>
                                            </div>
                                            <div class="badge-widget">
                                                <i class="fa fa-calendar"></i>Issue
                                                <Badge style="display:block" Color="Color.Success"><span style="padding: 0 2px;">@model.open_issues_count</span></Badge>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="form-group col-12 col-sm-6 col-md-4">
                    <div class="card border-warning">
                        <p class="d-none d-sm-block">
                            <i class="fa fa-code"></i>
                        </p>
                        <h3>Demonstrations and examples</h3>
                        <div>Detailed documentation and online demonstration, ready to use out of the box</div>
                    </div>
                </div>
                <div class="form-group col-12 col-sm-6 col-md-4">
                    <div class="card border-secondary">
                        <p class="d-none d-sm-block">
                            <i class="fa fa-refresh"></i>
                        </p>
                        <h3>Continuous update</h3>
                        <div>MIT agreement, never close the source, keep updating, respond to questions and feedback in time</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public Index.githubpoco model { get; set; }

}
