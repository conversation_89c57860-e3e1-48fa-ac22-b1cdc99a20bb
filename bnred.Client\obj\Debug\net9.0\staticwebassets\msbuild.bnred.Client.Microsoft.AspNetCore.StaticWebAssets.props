﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\appsettings.json'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>appsettings.json</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName>WasmResource</AssetTraitName>
      <AssetTraitValue>settings</AssetTraitValue>
      <Fingerprint>j9lqcc224j</Fingerprint>
      <Integrity>2TW/ZJs6ZmmfRka/1hC60gXkTAzen2hE6A6hk6SHNqA=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\appsettings.json'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\bnred.Client.styles.css'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>bnred.Client.styles.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>CurrentProject</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName>ScopedCss</AssetTraitName>
      <AssetTraitValue>ApplicationBundle</AssetTraitValue>
      <Fingerprint>o0pb5r8dcj</Fingerprint>
      <Integrity>mHtuuUFoUsnj5MvTOqUVEafjR468RLeI0U1+zBXeFlY=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\bnred.Client.styles.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fav.ico'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>fav.ico</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>6jx2rxa9oc</Fingerprint>
      <Integrity>vur7WppiFGepb4SZSLILZMuKtNQ37IXfeOZfYtz3jHM=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fav.ico'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>index.html</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>ru6an5k9d2</Fingerprint>
      <Integrity>zZNzDHG/12PI7IY79y8Ck0asad+Q5/O9f3wDhCm2t4A=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\manifest.json'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>manifest.json</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>1kagnj3j5z</Fingerprint>
      <Integrity>j9N/O1q/NmP6RuXAvu4vdKXGLgniM1+LB5laVu/LeHk=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\manifest.json'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\pwaicon.png'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>pwaicon.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>t167t4j96c</Fingerprint>
      <Integrity>YVcb4Zj3PzZ5wDhYyo3QoR4z3btsHT1gKOak3me7Rqg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\pwaicon.png'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\_framework\blazor.webassembly.js'))">
      <SourceType>Package</SourceType>
      <SourceId>bnred.Client</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>/</BasePath>
      <RelativePath>_framework/blazor.webassembly.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName>WasmResource</AssetTraitName>
      <AssetTraitValue>boot</AssetTraitValue>
      <Fingerprint>h7l656dkac</Fingerprint>
      <Integrity>I4LggERZ/f+YpdIbmQq/DkpppLSwVDlVHxq6Yvx/QAc=</Integrity>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\_framework\blazor.webassembly.js'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>