using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums;
using bnred.Model.WMS.Enums.Inventory;

namespace bnred.Model.WMS.Alert
{
    [Table("WMS_InventoryAlerts")]
    public class InventoryAlert : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        [Required]
        public DateTime AlertDate { get; set; }

        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }
        public virtual bnred.Model.Material.Material Material { get; set; }

        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        public virtual bnred.Model.WMS.Warehouse.Warehouse Warehouse { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal CurrentQuantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal MinQuantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal MaxQuantity { get; set; }

        [Required]
        public AlertType AlertType { get; set; }

        [Required]
        public AlertStatus Status { get; set; }

        [StringLength(500)]
        public string Remark { get; set; }
    }
} 