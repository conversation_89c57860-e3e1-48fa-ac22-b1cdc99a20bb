﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <CopyRefAssembliesToPublishDirectory>true</CopyRefAssembliesToPublishDirectory>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <Nullable>disable</Nullable>  
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Elsa.Designer.Components.Web" Version="2.14.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.1" />
    <PackageReference Include="WalkingTec.Mvvm.Mvc" Version="8.1.12" />
   <ProjectReference Include="..\bnred.Client\bnred.Client.csproj" />
   <ProjectReference Include="..\bnred.Shared\bnred.Shared.csproj" />
    <ProjectReference Include="..\bnred.DataAccess\bnred.DataAccess.csproj" />
</ItemGroup>
</Project>

