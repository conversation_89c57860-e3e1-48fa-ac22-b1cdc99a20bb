using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;

namespace bnred.Model
{
    /// <summary>
    /// 通知信息
    /// </summary>
    [Table("Notifications")]
    public class Notification : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 通知标题
        /// </summary>
        [Display(Name = "Title")]
        [Required(ErrorMessage = "Validate.{0}required")]
        [StringLength(100)]
        public string Title { get; set; }

        /// <summary>
        /// 通知内容
        /// </summary>
        [Display(Name = "Content")]
        [Required(ErrorMessage = "Validate.{0}required")]
        [StringLength(1000)]
        public string Content { get; set; }

        /// <summary>
        /// 通知消息
        /// </summary>
        [Display(Name = "Message")]
        [StringLength(500)]
        public string Message { get; set; }

        /// <summary>
        /// 通知类型
        /// </summary>
        [Display(Name = "Type")]
        public NotificationType Type { get; set; }

        /// <summary>
        /// 通知优先级
        /// </summary>
        [Display(Name = "Priority")]
        public NotificationPriority Priority { get; set; }

        /// <summary>
        /// 通知状态
        /// </summary>
        [Display(Name = "Status")]
        public NotificationStatus Status { get; set; }

        /// <summary>
        /// 发送者ID
        /// </summary>
        [Display(Name = "Sender")]
   
        public Guid? SenderId { get; set; }
 
        public FrameworkUser Sender { get; set; }

        /// <summary>
        /// 接收者ID
        /// </summary>
        [Display(Name = "Receiver")]
   
        public Guid? ReceiverId { get; set; }
 
        public FrameworkUser Receiver { get; set; }

        /// <summary>
        /// 发送时间
        /// </summary>
        [Display(Name = "SendTime")]
        public DateTime SendTime { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        [Display(Name = "ReadTime")]
        public DateTime? ReadTime { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        [Display(Name = "ProcessTime")]
        public DateTime? ProcessTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        [Display(Name = "ExpiryTime")]
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 是否已读
        /// </summary>
        [Display(Name = "IsRead")]
        public bool IsRead { get; set; }

        /// <summary>
        /// 是否已处理
        /// </summary>
        [Display(Name = "IsProcessed")]
        public bool IsProcessed { get; set; }

        /// <summary>
        /// 处理结果
        /// </summary>
        [Display(Name = "ProcessResult")]
        [StringLength(500)]
        public string ProcessResult { get; set; }

        /// <summary>
        /// 相关链接
        /// </summary>
        [Display(Name = "RelatedLink")]
        [StringLength(200)]
        public string RelatedLink { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [Display(Name = "Remark")]
        [StringLength(200)]
        public string Remark { get; set; }
    }
} 