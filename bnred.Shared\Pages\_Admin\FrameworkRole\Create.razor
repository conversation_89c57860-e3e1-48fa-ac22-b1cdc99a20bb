@page "/_Admin/FrameworkRole/Create"
@using WalkingTec.Mvvm.Mvc.Admin.ViewModels.FrameworkRoleVMs;
@inherits BasePage


<ValidateForm @ref="vform" Model="@Model" OnValidSubmit="@Submit">
    <BootstrapInput @bind-Value="@Model.Entity.RoleCode" />
    <BootstrapInput @bind-Value="@Model.Entity.RoleName" />
    <BootstrapInput @bind-Value="@Model.Entity.RoleRemark" />
    <div class="modal-footer table-modal-footer">
        <Button Color="Color.Primary" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Close"]" OnClick="OnClose" />
        <Button Color="Color.Primary" ButtonType="ButtonType.Submit" Icon="fa fa-save" Text="@WtmBlazor.Localizer["Sys.Create"]" IsAsync="true" />
    </div>
</ValidateForm>

@code {
    private FrameworkRoleVM Model = new FrameworkRoleVM();

    private ValidateForm vform { get; set; }

    private async Task Submit(EditContext context)
    {
        await PostsForm(vform, "/api/_FrameworkRole/add", (s) => "Sys.OprationSuccess");
    }

    public void OnClose()
    {
        CloseDialog();
    }

}
