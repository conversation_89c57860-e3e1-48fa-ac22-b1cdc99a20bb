using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 交易状态
    /// </summary>
    public enum TransactionStatus
    {
        /// <summary>
        /// 草稿
        /// </summary>
        [Display(Name = "草稿")]
        Draft = 0,

        /// <summary>
        /// 待审核
        /// </summary>
        [Display(Name = "待审核")]
        PendingApproval = 1,

        /// <summary>
        /// 已审核
        /// </summary>
        [Display(Name = "已审核")]
        Approved = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 4,

        /// <summary>
        /// 已作废
        /// </summary>
        [Display(Name = "已作废")]
        Voided = 5
    }

    /// <summary>
    /// 交易来源
    /// </summary>
    public enum TransactionSource
    {
        /// <summary>
        /// 手动创建
        /// </summary>
        [Display(Name = "手动创建")]
        Manual = 0,

        /// <summary>
        /// 采购订单
        /// </summary>
        [Display(Name = "采购订单")]
        PurchaseOrder = 1,

        /// <summary>
        /// 销售订单
        /// </summary>
        [Display(Name = "销售订单")]
        SalesOrder = 2,

        /// <summary>
        /// 生产订单
        /// </summary>
        [Display(Name = "生产订单")]
        ProductionOrder = 3,

        /// <summary>
        /// 库存调整
        /// </summary>
        [Display(Name = "库存调整")]
        StockAdjustment = 4,

        /// <summary>
        /// 库存盘点
        /// </summary>
        [Display(Name = "库存盘点")]
        StockTaking = 5
    }

    /// <summary>
    /// 交易优先级
    /// </summary>
    public enum TransactionPriority
    {
        /// <summary>
        /// 低
        /// </summary>
        [Display(Name = "低")]
        Low = 0,

        /// <summary>
        /// 中
        /// </summary>
        [Display(Name = "中")]
        Medium = 1,

        /// <summary>
        /// 高
        /// </summary>
        [Display(Name = "高")]
        High = 2,

        /// <summary>
        /// 紧急
        /// </summary>
        [Display(Name = "紧急")]
        Urgent = 3
    }

    /// <summary>
    /// 仓库交易类型
    /// Warehouse Transaction Type
    /// </summary>
    public enum WarehouseTransactionType
    {
        /// <summary>
        /// 期初
        /// Beginning Balance
        /// </summary>
        [Display(Name = "期初")]
        BeginningBalance = 0,

        /// <summary>
        /// 入库
        /// </summary>
        [Display(Name = "入库")]
        Inbound = 1,

        /// <summary>
        /// 出库
        /// </summary>
        [Display(Name = "出库")]
        Outbound = 2,

        /// <summary>
        /// 移库
        /// </summary>
        [Display(Name = "移库")]
        Movement = 3,

        /// <summary>
        /// 盘点
        /// </summary>
        [Display(Name = "盘点")]
        StockTaking = 4,

        /// <summary>
        /// 报废
        /// </summary>
        [Display(Name = "报废")]
        Scrap = 5,

        /// <summary>
        /// 调拨
        /// </summary>
        [Display(Name = "调拨")]
        Transfer = 6,

        /// <summary>
        /// 其他入库
        /// </summary>
        [Display(Name = "其他入库")]
        OtherInbound = 7,

        /// <summary>
        /// 其他出库
        /// </summary>
        [Display(Name = "其他出库")]
        OtherOutbound = 8
    }
}