using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.Analysis
{
    /// <summary>
    /// 成本统计数据类
    /// 用于汇总分析成本数据
    /// </summary>
    [Table("WMS_CostStatistics")]
    public class CostStatistics : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 开始日期
        /// 统计期间的开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// 统计期间的结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 最高单位成本
        /// 期间内的最高单位成本
        /// </summary>
        public decimal HighestUnitCost { get; set; }

        /// <summary>
        /// 最低单位成本
        /// 期间内的最低单位成本
        /// </summary>
        public decimal LowestUnitCost { get; set; }

        /// <summary>
        /// 平均单位成本
        /// 期间内的平均单位成本
        /// </summary>
        public decimal AverageUnitCost { get; set; }

        /// <summary>
        /// 标准差
        /// 成本波动的标准差
        /// </summary>
        public decimal StandardDeviation { get; set; }

        /// <summary>
        /// 总成本
        /// 期间内的总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 成本变化率
        /// 相比上期的成本变化百分比
        /// </summary>
        public decimal ChangeRate { get; set; }

        /// <summary>
        /// 物料类别
        /// 如果是按类别统计时的类别名称
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 数据来源
        /// 统计数据的来源系统或计算方法
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// 备注
        /// 额外的说明信息
        /// </summary>
        public string Notes { get; set; }
    }
} 