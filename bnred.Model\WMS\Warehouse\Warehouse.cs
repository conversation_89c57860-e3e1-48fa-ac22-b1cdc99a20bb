using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库信息
    /// 用于存储仓库的基本信息
    /// </summary>
    [Table("Warehouses")]
    public class Warehouse : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 仓库编码
        /// </summary>
        [Display(Name = "仓库编码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Display(Name = "仓库名称")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 仓库类型
        /// </summary>
        [Display(Name = "仓库类型")]
        public WarehouseType Type { get; set; }

        /// <summary>
        /// 仓库状态
        /// </summary>
        [Display(Name = "仓库状态")]
        public WarehouseStatus Status { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        [Display(Name = "仓库地址")]
        [StringLength(200)]
        public string Address { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Display(Name = "联系人")]
        [StringLength(50)]
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [Display(Name = "联系电话")]
        [StringLength(20)]
        public string ContactPhone { get; set; }

        /// <summary>
        /// 面积(平方米)
        /// </summary>
        [Display(Name = "面积(平方米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Area { get; set; }

        /// <summary>
        /// 容量(立方米)
        /// </summary>
        [Display(Name = "容量(立方米)")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Volume { get; set; }

 

        /// <summary>
        /// 负责人
        /// </summary>
        [Display(Name = "负责人")]
        public Guid? ManagerID { get; set; }
        public FrameworkUser Manager{ get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Display(Name = "描述")]
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 库区列表
        /// </summary>
        [Display(Name = "库区列表")]
        public List<WarehouseArea> Areas { get; set; }
    }
}