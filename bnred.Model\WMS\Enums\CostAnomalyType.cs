using System.ComponentModel.DataAnnotations;

namespace bnred.Model.Enums
{
    /// <summary>
    /// 成本异常类型枚举
    /// </summary>
    public enum CostAnomalyType
    {
        /// <summary>
        /// 成本突增
        /// 成本在短期内显著增加
        /// </summary>
        CostSpike = 1,

        /// <summary>
        /// 成本突降
        /// 成本在短期内显著降低
        /// </summary>
        CostDrop = 2,

        /// <summary>
        /// 持续偏高
        /// 成本持续高于正常水平
        /// </summary>
        PersistentlyHigh = 3,

        /// <summary>
        /// 持续偏低
        /// 成本持续低于正常水平
        /// </summary>
        PersistentlyLow = 4,

        /// <summary>
        /// 异常波动
        /// 成本频繁且显著波动
        /// </summary>
        AbnormalFluctuation = 5,

        /// <summary>
        /// 计算错误
        /// 成本计算存在明显错误
        /// </summary>
        CalculationError = 6,

        /// <summary>
        /// 数据缺失
        /// 成本数据不完整或缺失
        /// </summary>
        DataMissing = 7,

        /// <summary>
        /// 超出限制
        /// 成本超出预设的限制范围
        /// </summary>
        ExceedLimit = 8,

        /// <summary>
        /// 不一致
        /// 不同系统或记录间的成本数据不一致
        /// </summary>
        Inconsistency = 9,

        /// <summary>
        /// 未分类异常
        /// 其他未归类的异常情况
        /// </summary>
        Unclassified = 10
    }

    /// <summary>
    /// 成本异常级别枚举
    /// </summary>
    public enum CostAnomalyLevel
    {
        /// <summary>
        /// 低风险
        /// 影响较小，需要关注
        /// </summary>
        Low = 1,

        /// <summary>
        /// 中等风险
        /// 需要及时处理
        /// </summary>
        Medium = 2,

        /// <summary>
        /// 高风险
        /// 需要立即处理
        /// </summary>
        High = 3,

        /// <summary>
        /// 严重风险
        /// 需要紧急处理
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// 成本异常状态枚举
    /// </summary>
    public enum CostAnomalyStatus
    {
        /// <summary>
        /// 待处理
        /// 新发现的异常，尚未开始处理
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 处理中
        /// 正在进行异常处理
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已解决
        /// 异常已经得到解决
        /// </summary>
        Resolved = 3,

        /// <summary>
        /// 已关闭
        /// 异常已关闭，无需进一步处理
        /// </summary>
        Closed = 4,

        /// <summary>
        /// 已忽略
        /// 确认为误报或无需处理的异常
        /// </summary>
        Ignored = 5,

        /// <summary>
        /// 需要跟进
        /// 需要进一步观察或处理
        /// </summary>
        NeedsFollowUp = 6
    }
} 