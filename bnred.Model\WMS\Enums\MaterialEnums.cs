using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 物料类型
    /// </summary>
    public enum MaterialType
    {
        /// <summary>
        /// 原材料
        /// </summary>
        [Display(Name = "原材料")]
        RawMaterial = 0,

        /// <summary>
        /// 半成品
        /// </summary>
        [Display(Name = "半成品")]
        SemiFinished = 1,

        /// <summary>
        /// 成品
        /// </summary>
        [Display(Name = "成品")]
        Finished = 2,

        /// <summary>
        /// 包装材料
        /// </summary>
        [Display(Name = "包装材料")]
        Packaging = 3,

        /// <summary>
        /// 消耗品
        /// </summary>
        [Display(Name = "消耗品")]
        Consumable = 4,

        /// <summary>
        /// 其他
        /// </summary>
        [Display(Name = "其他")]
        Other = 99
    }

    /// <summary>
    /// 物料状态
    /// 用于标识物料的使用状态和生命周期
    /// </summary>
    public enum MaterialStatus
    {
        /// <summary>
        /// 正常/启用
        /// 物料可正常使用
        /// </summary>
        [Display(Name = "正常")]
        Normal = 0,

        /// <summary>
        /// 停用
        /// 物料暂停使用
        /// </summary>
        [Display(Name = "停用")]
        Disabled = 1,

        /// <summary>
        /// 淘汰
        /// 物料已被更新替代
        /// </summary>
        [Display(Name = "淘汰")]
        Obsolete = 2,
        
        /// <summary>
        /// 待审核
        /// 物料信息待审核
        /// </summary>
        [Display(Name = "待审核")]
        PendingApproval = 3
    }

    /// <summary>
    /// 物料ABC分类
    /// </summary>
    public enum MaterialABCClass
    {
        /// <summary>
        /// A类
        /// </summary>
        [Display(Name = "A类")]
        A = 0,

        /// <summary>
        /// B类
        /// </summary>
        [Display(Name = "B类")]
        B = 1,

        /// <summary>
        /// C类
        /// </summary>
        [Display(Name = "C类")]
        C = 2
    }

    /// <summary>
    /// 存储条件
    /// </summary>
    public enum StorageCondition
    {
        /// <summary>
        /// 常温
        /// </summary>
        [Display(Name = "常温")]
        Normal = 0,

        /// <summary>
        /// 冷藏
        /// </summary>
        [Display(Name = "冷藏")]
        Refrigerated = 1,

        /// <summary>
        /// 冷冻
        /// </summary>
        [Display(Name = "冷冻")]
        Frozen = 2,

        /// <summary>
        /// 恒温
        /// </summary>
        [Display(Name = "恒温")]
        ConstantTemperature = 3,

        /// <summary>
        /// 防潮
        /// </summary>
        [Display(Name = "防潮")]
        Moisture = 4,

        /// <summary>
        /// 防晒
        /// </summary>
        [Display(Name = "防晒")]
        SunProof = 5,

        /// <summary>
        /// 无特殊要求
        /// </summary>
        [Display(Name = "无特殊要求")]
        NoSpecial = 99
    }
}