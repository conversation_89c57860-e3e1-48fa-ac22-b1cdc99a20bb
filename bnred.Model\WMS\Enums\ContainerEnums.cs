using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 容器类型枚举
    /// 用于标识不同的容器类型
    /// </summary>
    public enum ContainerType
    {
        /// <summary>
        /// 周转箱
        /// 用于物料周转的标准箱
        /// </summary>
        [Display(Name = "周转箱")]
        Box = 0,

        /// <summary>
        /// 托盘
        /// 用于堆垛和运输的托盘
        /// </summary>
        [Display(Name = "托盘")]
        Pallet = 1,

        /// <summary>
        /// 料架
        /// 用于存放物料的料架
        /// </summary>
        [Display(Name = "料架")]
        Rack = 2,

        /// <summary>
        /// 笼车
        /// 用于物料运输的笼车
        /// </summary>
        [Display(Name = "笼车")]
        Cage = 3,

        /// <summary>
        /// 货架
        /// 用于存储的货架
        /// </summary>
        [Display(Name = "货架")]
        Shelf = 4
    }

    /// <summary>
    /// 容器状态枚举
    /// 用于标识容器的使用状态
    /// </summary>
    public enum ContainerStatus
    {
        /// <summary>
        /// 空闲中
        /// 容器处于空闲可用状态
        /// </summary>
        [Display(Name = "空闲中")]
        Empty = 0,

        /// <summary>
        /// 使用中
        /// 容器正在使用中
        /// </summary>
        [Display(Name = "使用中")]
        InUse = 1,

        /// <summary>
        /// 维修中
        /// 容器正在维修中
        /// </summary>
        [Display(Name = "维修中")]
        Maintenance = 2,

        /// <summary>
        /// 已报废
        /// 容器已报废不可用
        /// </summary>
        [Display(Name = "已报废")]
        Scrapped = 3
    }

    /// <summary>
    /// 容器规格枚举
    /// 用于标识容器的规格类型
    /// </summary>
    public enum ContainerSpecification
    {
        /// <summary>
        /// 小型
        /// 小规格容器
        /// </summary>
        [Display(Name = "小型")]
        Small = 0,

        /// <summary>
        /// 中型
        /// 中规格容器
        /// </summary>
        [Display(Name = "中型")]
        Medium = 1,

        /// <summary>
        /// 大型
        /// 大规格容器
        /// </summary>
        [Display(Name = "大型")]
        Large = 2,

        /// <summary>
        /// 特大型
        /// 特大规格容器
        /// </summary>
        [Display(Name = "特大型")]
        ExtraLarge = 3,

        /// <summary>
        /// 定制型
        /// 定制规格容器
        /// </summary>
        [Display(Name = "定制型")]
        Custom = 4
    }
} 