-- #############################################################################
-- # SQL DDL for Notification Module
-- # Target File: bnred.Model/Notification/md.sql
-- #############################################################################

-- #############################################################################
-- # Table: WMS_Notifications (通知信息表)
-- # 来自: Notification.cs
-- #############################################################################
IF OBJECT_ID('dbo.WMS_Notifications', 'U') IS NOT NULL
    DROP TABLE dbo.WMS_Notifications;
GO
CREATE TABLE WMS_Notifications (
    ID NVARCHAR(50) PRIMARY KEY,                         -- 主键ID
    Title NVARCHAR(100) NOT NULL,                        -- 通知标题
    Content NVARCHAR(1000) NOT NULL,                     -- 通知内容
    Message NVARCHAR(500) NULL,                          -- 通知消息 (摘要或附加)
    Type INT NOT NULL,                                   -- 通知类型 (关联 WMS_EnumDictionary, EnumType='bnred.Model.NotificationType')
    Priority INT NOT NULL,                               -- 通知优先级 (关联 WMS_EnumDictionary, EnumType='bnred.Model.NotificationPriority')
    Status INT NOT NULL,                                 -- 通知状态 (关联 WMS_EnumDictionary, EnumType='bnred.Model.NotificationStatus')
    SenderId UNIQUEIDENTIFIER NULL,                      -- 发送者ID
    ReceiverId UNIQUEIDENTIFIER NULL,                    -- 接收者ID
    SendTime DATETIME2 NOT NULL DEFAULT GETDATE(),       -- 发送时间
    ReadTime DATETIME2 NULL,                             -- 阅读时间
    ProcessTime DATETIME2 NULL,                          -- 处理时间
    ExpiryTime DATETIME2 NULL,                           -- 过期时间
    IsRead BIT NOT NULL DEFAULT 0,                       -- 是否已读
    IsProcessed BIT NOT NULL DEFAULT 0,                  -- 是否已处理
    ProcessResult NVARCHAR(500) NULL,                    -- 处理结果
    RelatedLink NVARCHAR(200) NULL,                      -- 相关链接
    Remark NVARCHAR(200) NULL,                           -- 备注信息
    -- BasePoco fields
    CreateTime DATETIME2 NULL,
    CreateBy NVARCHAR(255) NULL,
    UpdateTime DATETIME2 NULL,
    UpdateBy NVARCHAR(255) NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
GO
-- MS_Description for WMS_Notifications table
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知信息表，存储系统生成的各类通知消息。' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications';
-- MS_Description for WMS_Notifications columns
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'ID';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知的标题，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Title';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知的详细内容，必填' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Content';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知的简短消息或摘要' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Message';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知类型 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.NotificationType\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Type';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知优先级 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.NotificationPriority\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Priority';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知状态 (关联 WMS_EnumDictionary, EnumType=\'bnred.Model.NotificationStatus\')' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'发送者用户ID (外键关联 WMS_FrameworkUsers.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'SenderId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'接收者用户ID (外键关联 WMS_FrameworkUsers.ID)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'ReceiverId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知发送的时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'SendTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'接收者阅读通知的时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'ReadTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知被处理的时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'ProcessTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知的过期时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'ExpiryTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标记通知是否已被阅读' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'IsRead';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标记通知是否已被处理' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'IsProcessed';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知的处理结果描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'ProcessResult';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'与通知相关的URL链接' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'RelatedLink';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'Remark';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'CreateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'CreateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'UpdateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'UpdateBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否删除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_Notifications', @level2type=N'COLUMN',@level2name=N'IsDeleted';
GO

-- #############################################################################
-- # Foreign Key Constraints for Notification Module
-- #############################################################################
ALTER TABLE WMS_Notifications
ADD CONSTRAINT FK_Notifications_Sender FOREIGN KEY (SenderId) REFERENCES dbo.WMS_FrameworkUsers(ID); -- Assumes WMS_FrameworkUsers from root_models
GO

ALTER TABLE WMS_Notifications
ADD CONSTRAINT FK_Notifications_Receiver FOREIGN KEY (ReceiverId) REFERENCES dbo.WMS_FrameworkUsers(ID); -- Assumes WMS_FrameworkUsers from root_models
GO

PRINT 'SQL for Notification module created in bnred.Model/Notification/md.sql';
GO
