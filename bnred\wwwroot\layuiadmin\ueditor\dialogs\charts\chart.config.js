/** layuiAdmin.pro-v1.2.1 LPPL License By http://www.layui.com/admin/ */
 ;var typeConfig=[{chart:{type:"line"},plotOptions:{line:{dataLabels:{enabled:!1},enableMouseTracking:!0}}},{chart:{type:"line"},plotOptions:{line:{dataLabels:{enabled:!0},enableMouseTracking:!1}}},{chart:{type:"area"}},{chart:{type:"bar"}},{chart:{type:"column"}},{chart:{plotBackgroundColor:null,plotBorderWidth:null,plotShadow:!1},plotOptions:{pie:{allowPointSelect:!0,cursor:"pointer",dataLabels:{enabled:!0,color:"#000000",connectorColor:"#000000",formatter:function(){return"<b>"+this.point.name+"</b>: "+Math.round(100*this.point.percentage)/100+" %"}}}}}];