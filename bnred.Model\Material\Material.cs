using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums;

namespace bnred.Model.Material
{
    /// <summary>
    /// 物料信息
    /// 用于存储物料的基本信息
    /// </summary>
    [Table("Materials")]
    public class Material : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 物料编码
        /// </summary>
        [Display(Name = "物料编码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Display(Name = "物料名称")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Display(Name = "物料描述")]
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 物料类别ID
        /// </summary>
        [Display(Name = "物料类别")]
        [StringLength(50)]
        public string CategoryId { get; set; }

        /// <summary>
        /// 物料类别
        /// </summary>
        [Display(Name = "物料类别")]
        public MaterialCategory Category { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [Display(Name = "规格型号")]
        [StringLength(100)]
        public string Specification { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        [Display(Name = "计量单位")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        [Display(Name = "条码")]
        [StringLength(50)]
        public string Barcode { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Display(Name = "供应商")]
        [StringLength(50)]
        public string SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [Display(Name = "供应商")]
        public Supplier.Supplier Supplier { get; set; }

        /// <summary>
        /// 标准成本
        /// </summary>
        [Display(Name = "标准成本")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal StandardCost { get; set; }

        /// <summary>
        /// 销售价格
        /// </summary>
        [Display(Name = "销售价格")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SalePrice { get; set; }

        /// <summary>
        /// 最小库存量
        /// </summary>
        [Display(Name = "最小库存量")]
        public decimal MinStock { get; set; }

        /// <summary>
        /// 最大库存量
        /// </summary>
        [Display(Name = "最大库存量")]
        public decimal MaxStock { get; set; }

        /// <summary>
        /// 安全库存量
        /// </summary>
        [Display(Name = "安全库存量")]
        public decimal SafetyStock { get; set; }

        /// <summary>
        /// 是否启用批次管理
        /// </summary>
        [Display(Name = "批次管理")]
        public bool BatchManagement { get; set; }

        /// <summary>
        /// 是否启用序列号管理
        /// </summary>
        [Display(Name = "序列号管理")]
        public bool SerialNumberManagement { get; set; }

        /// <summary>
        /// 保质期(天)
        /// </summary>
        [Display(Name = "保质期(天)")]
        public int? ShelfLife { get; set; }

        /// <summary>
        /// 物料状态
        /// </summary>
        [Display(Name = "物料状态")]
        public MaterialStatus Status { get; set; }

        /// <summary>
        /// 图片路径
        /// </summary>
        [Display(Name = "图片路径")]
        [StringLength(200)]
        public string ImageUrl { get; set; }

        /// <summary>
        /// 附件路径
        /// </summary>
        [Display(Name = "附件路径")]
        [StringLength(200)]
        public string AttachmentUrl { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 本地化信息
        /// </summary>
        [Display(Name = "本地化信息")]
        public List<MaterialLocalization> Localizations { get; set; }
    }
} 