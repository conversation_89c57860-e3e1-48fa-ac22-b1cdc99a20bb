-- ----------------------------
-- Table structure for WMS_InventoryAnalysis
-- ----------------------------
DROP TABLE IF EXISTS [WMS_InventoryAnalysis];
CREATE TABLE [WMS_InventoryAnalysis] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [AnalysisDate] datetime2(7) NOT NULL, -- 分析日期
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [WarehouseId] nvarchar(50) NOT NULL, -- 仓库ID
  [LocationId] nvarchar(50) NULL, -- 库位ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [CurrentStock] decimal(18,4) NOT NULL, -- 当前库存
  [AverageStock] decimal(18,4) NOT NULL, -- 平均库存
  [MinStockLevel] decimal(18,4) NOT NULL, -- 最低库存水平
  [MaxStockLevel] decimal(18,4) NOT NULL, -- 最高库存水平
  [StockTurnoverRate] decimal(18,4) NOT NULL, -- 库存周转率
  [OutOfStockRate] decimal(18,4) NOT NULL, -- 缺货率
  [AnalysisResult] nvarchar(500) NOT NULL, -- 分析结果
  [Suggestions] nvarchar(500) NULL, -- 建议措施
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_InventoryAnalysis
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_CostAnalysisData
-- ----------------------------
DROP TABLE IF EXISTS [WMS_CostAnalysisData];
CREATE TABLE [WMS_CostAnalysisData] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [BatchId] nvarchar(50) NULL, -- 批次ID
  [LocationId] nvarchar(50) NOT NULL, -- 库位ID
  [AnalysisDate] datetime2(7) NOT NULL, -- 分析日期
  [AnomalyLevel] int NOT NULL, -- 异常等级 (需要映射到 WMS_EnumDictionary)
  [CurrentCost] decimal(18,4) NOT NULL, -- 当前成本
  [StandardCost] decimal(18,4) NOT NULL, -- 标准成本
  [CostDifference] decimal(18,4) NOT NULL, -- 成本差异
  [DifferenceRate] decimal(18,4) NOT NULL, -- 差异率
  [AnalysisResult] nvarchar(500) NOT NULL, -- 分析结果
  [Suggestions] nvarchar(500) NULL, -- 建议措施
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_CostAnalysisData
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_InventoryAnalysis
-- ----------------------------
ALTER TABLE [WMS_InventoryAnalysis] ADD CONSTRAINT [FK_WMS_InventoryAnalysis_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_InventoryAnalysis] ADD CONSTRAINT [FK_WMS_InventoryAnalysis_Warehouse] FOREIGN KEY ([WarehouseId]) REFERENCES [WMS_Warehouse] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_InventoryAnalysis] ADD CONSTRAINT [FK_WMS_InventoryAnalysis_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_InventoryAnalysis] ADD CONSTRAINT [FK_WMS_InventoryAnalysis_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- Foreign Keys structure for table WMS_CostAnalysisData
-- ----------------------------
ALTER TABLE [WMS_CostAnalysisData] ADD CONSTRAINT [FK_WMS_CostAnalysisData_Material] FOREIGN KEY ([MaterialId]) REFERENCES [WMS_Material] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_CostAnalysisData] ADD CONSTRAINT [FK_WMS_CostAnalysisData_Batch] FOREIGN KEY ([BatchId]) REFERENCES [WMS_Batch] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_CostAnalysisData] ADD CONSTRAINT [FK_WMS_CostAnalysisData_Location] FOREIGN KEY ([LocationId]) REFERENCES [WMS_Location] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- MS_Description for table WMS_InventoryAnalysis
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存分析' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'分析日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'AnalysisDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仓库ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'WarehouseId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'当前库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'CurrentStock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'平均库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'AverageStock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最低库存水平' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'MinStockLevel'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最高库存水平' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'MaxStockLevel'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存周转率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'StockTurnoverRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'缺货率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'OutOfStockRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'分析结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'AnalysisResult'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'建议措施' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'Suggestions'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_InventoryAnalysis', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_CostAnalysisData
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本分析数据' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'BatchId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库位ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'LocationId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'分析日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'AnalysisDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'异常等级' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'AnomalyLevel'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'当前成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'CurrentCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'StandardCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本差异' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'CostDifference'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'差异率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'DifferenceRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'分析结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'AnalysisResult'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'建议措施' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'Suggestions'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnalysisData', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_CostAnomalyData
-- ----------------------------
DROP TABLE IF EXISTS [WMS_CostAnomalyData];
CREATE TABLE [WMS_CostAnomalyData] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [Date] datetime2(7) NOT NULL, -- 日期
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [AnomalyType] int NOT NULL, -- 异常类型（枚举，映射到WMS_EnumDictionary）
  [ActualValue] decimal(18,4) NOT NULL, -- 实际值
  [ExpectedValue] decimal(18,4) NOT NULL, -- 预期值
  [DeviationRate] decimal(18,4) NOT NULL, -- 偏差率
  [AnomalyLevel] int NOT NULL, -- 异常等级（枚举，映射到WMS_EnumDictionary）
  [Status] int NOT NULL, -- 处理状态（枚举，映射到WMS_EnumDictionary）
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table WMS_CostAnomalyData
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本异常数据' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'Date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'异常类型（枚举，映射到WMS_EnumDictionary）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'AnomalyType'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实际值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'ActualValue'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预期值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'ExpectedValue'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'偏差率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'DeviationRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'异常等级（枚举，映射到WMS_EnumDictionary）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'AnomalyLevel'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'处理状态（枚举，映射到WMS_EnumDictionary）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostAnomalyData', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for WMS_CostDistributionData
-- ----------------------------
DROP TABLE IF EXISTS [WMS_CostDistributionData];
CREATE TABLE [WMS_CostDistributionData] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [MaterialId] nvarchar(50) NULL, -- 物料ID
  [MaterialCode] nvarchar(100) NULL, -- 物料编码
  [MaterialName] nvarchar(200) NULL, -- 物料名称
  [TotalQuantity] decimal(18,4) NOT NULL, -- 总数量
  [TotalCost] decimal(18,4) NOT NULL, -- 总成本
  [AverageUnitCost] decimal(18,4) NOT NULL, -- 平均单位成本
  [MinUnitCost] decimal(18,4) NOT NULL, -- 最低单位成本
  [MaxUnitCost] decimal(18,4) NOT NULL, -- 最高单位成本
  [StandardDeviation] decimal(18,4) NOT NULL, -- 标准差
  [CostPercentage] decimal(18,4) NOT NULL, -- 成本占比
  [Category] nvarchar(100) NULL, -- 物料类别
  [DataSource] nvarchar(100) NULL, -- 数据来源
  [Notes] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table WMS_CostDistributionData
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本分布数据' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'MaterialCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'MaterialName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'TotalQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'TotalCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'平均单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'AverageUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最低单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'MinUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最高单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'MaxUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准差' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'StandardDeviation'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本占比' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'CostPercentage'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料类别' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'Category'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数据来源' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'DataSource'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'Notes'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostDistributionData', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for WMS_CostStatistics
-- ----------------------------
DROP TABLE IF EXISTS [WMS_CostStatistics];
CREATE TABLE [WMS_CostStatistics] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [StartDate] datetime2(7) NOT NULL, -- 开始日期
  [EndDate] datetime2(7) NOT NULL, -- 结束日期
  [HighestUnitCost] decimal(18,4) NOT NULL, -- 最高单位成本
  [LowestUnitCost] decimal(18,4) NOT NULL, -- 最低单位成本
  [AverageUnitCost] decimal(18,4) NOT NULL, -- 平均单位成本
  [StandardDeviation] decimal(18,4) NOT NULL, -- 标准差
  [TotalCost] decimal(18,4) NOT NULL, -- 总成本
  [ChangeRate] decimal(18,4) NOT NULL, -- 成本变化率
  [Category] nvarchar(100) NULL, -- 物料类别
  [DataSource] nvarchar(100) NULL, -- 数据来源
  [Notes] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table WMS_CostStatistics
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本统计数据' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'开始日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'StartDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'结束日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'EndDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最高单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'HighestUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最低单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'LowestUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'平均单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'AverageUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准差' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'StandardDeviation'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'TotalCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本变化率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'ChangeRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料类别' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'Category'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数据来源' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'DataSource'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'Notes'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostStatistics', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for WMS_CostTrendData
-- ----------------------------
DROP TABLE IF EXISTS [WMS_CostTrendData];
CREATE TABLE [WMS_CostTrendData] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [Date] datetime2(7) NOT NULL, -- 日期
  [AverageUnitCost] decimal(18,4) NOT NULL, -- 平均单位成本
  [ChangeRate] decimal(18,4) NOT NULL, -- 变化率
  [MovingAverageCost] decimal(18,4) NOT NULL, -- 移动平均成本
  [TotalValue] decimal(18,4) NOT NULL, -- 总价值
  [TrendDirection] int NOT NULL, -- 趋势方向
  [Category] nvarchar(100) NULL, -- 物料类别
  [DataSource] nvarchar(100) NULL, -- 数据来源
  [Notes] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table WMS_CostTrendData
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'成本趋势数据' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'Date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'平均单位成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'AverageUnitCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'变化率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'ChangeRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'移动平均成本' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'MovingAverageCost'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总价值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'TotalValue'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'趋势方向' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'TrendDirection'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料类别' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'Category'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数据来源' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'DataSource'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'Notes'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_CostTrendData', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for WarehouseAreaUtilizations
-- ----------------------------
DROP TABLE IF EXISTS [WarehouseAreaUtilizations];
CREATE TABLE [WarehouseAreaUtilizations] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [AreaId] nvarchar(50) NOT NULL, -- 库区ID
  [AreaCode] nvarchar(50) NOT NULL, -- 库区编码
  [AreaName] nvarchar(50) NOT NULL, -- 库区名称
  [TotalLocations] int NOT NULL, -- 总库位数
  [UsedLocations] int NOT NULL, -- 已用库位数
  [TotalVolume] decimal(18,2) NOT NULL, -- 总容量（立方米）
  [UsedVolume] decimal(18,2) NOT NULL, -- 已用容量（立方米）
  [VolumeUtilizationRate] decimal(18,2) NOT NULL, -- 容量使用率
  [LocationUtilizationRate] decimal(18,2) NOT NULL, -- 库位使用率
  [LastUpdateTime] datetime2(7) NOT NULL, -- 最后更新时间
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table WarehouseAreaUtilizations
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库区利用率分析结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库区ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'AreaId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库区编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'AreaCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库区名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'AreaName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总库位数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'TotalLocations'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'已用库位数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'UsedLocations'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'总容量（立方米）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'TotalVolume'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'已用容量（立方米）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'UsedVolume'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'容量使用率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'VolumeUtilizationRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库位使用率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'LocationUtilizationRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'LastUpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WarehouseAreaUtilizations', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for InventoryTurnoverAnalyses
-- ----------------------------
DROP TABLE IF EXISTS [InventoryTurnoverAnalyses];
CREATE TABLE [InventoryTurnoverAnalyses] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [MaterialCode] nvarchar(50) NOT NULL, -- 物料编码
  [MaterialName] nvarchar(50) NOT NULL, -- 物料名称
  [BeginningInventory] decimal(18,2) NOT NULL, -- 期初库存
  [EndingInventory] decimal(18,2) NOT NULL, -- 期末库存
  [AverageInventory] decimal(18,2) NOT NULL, -- 平均库存
  [OutboundQuantity] decimal(18,2) NOT NULL, -- 出库数量
  [TurnoverRate] decimal(18,2) NOT NULL, -- 周转率
  [TurnoverDays] decimal(18,2) NOT NULL, -- 周转天数
  [StartDate] datetime2(7) NOT NULL, -- 统计开始日期
  [EndDate] datetime2(7) NOT NULL, -- 统计结束日期
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table InventoryTurnoverAnalyses
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存周转分析结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'期初库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'BeginningInventory'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'期末库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'EndingInventory'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'平均库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'AverageInventory'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出库数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'OutboundQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'周转率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'TurnoverRate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'周转天数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'TurnoverDays'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'统计开始日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'StartDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'统计结束日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'EndDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryTurnoverAnalyses', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for InventoryAlertAnalyses
-- ----------------------------
DROP TABLE IF EXISTS [InventoryAlertAnalyses];
CREATE TABLE [InventoryAlertAnalyses] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [InventoryId] nvarchar(50) NOT NULL, -- 库存ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [MaterialCode] nvarchar(50) NOT NULL, -- 物料编码
  [MaterialName] nvarchar(50) NOT NULL, -- 物料名称
  [CurrentQuantity] decimal(18,2) NOT NULL, -- 当前数量
  [AlertLevel] int NOT NULL, -- 预警等级（枚举，映射到WMS_EnumDictionary，对应InventoryAlertLevel）
  [AlertReason] nvarchar(500) NULL, -- 预警原因
  [LastUpdateTime] datetime2(7) NOT NULL, -- 最后更新时间
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table InventoryAlertAnalyses
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存预警分析结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'InventoryId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'当前数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'CurrentQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预警等级（枚举，映射到WMS_EnumDictionary，对应InventoryAlertLevel）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'AlertLevel'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预警原因' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'AlertReason'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'LastUpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryAlertAnalyses', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- Table structure for InventoryForecastAnalyses
-- ----------------------------
DROP TABLE IF EXISTS [InventoryForecastAnalyses];
CREATE TABLE [InventoryForecastAnalyses] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [MaterialId] nvarchar(50) NOT NULL, -- 物料ID
  [MaterialCode] nvarchar(50) NOT NULL, -- 物料编码
  [MaterialName] nvarchar(50) NOT NULL, -- 物料名称
  [CurrentQuantity] decimal(18,2) NOT NULL, -- 当前数量
  [ForecastDays] int NOT NULL, -- 预测天数
  [ForecastedQuantity] decimal(18,2) NOT NULL, -- 预测数量
  [AvgDailyInbound] decimal(18,2) NOT NULL, -- 日均入库量
  [AvgDailyOutbound] decimal(18,2) NOT NULL, -- 日均出库量
  [MinStock] decimal(18,2) NOT NULL, -- 最小库存
  [MaxStock] decimal(18,2) NOT NULL, -- 最大库存
  [ForecastTime] datetime2(7) NOT NULL, -- 预测时间
  [Suggestions] nvarchar(1000) NULL, -- 建议
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- MS_Description for table InventoryForecastAnalyses
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存预测分析结果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'物料名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'MaterialName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'当前数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'CurrentQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预测天数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'ForecastDays'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预测数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'ForecastedQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'日均入库量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'AvgDailyInbound'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'日均出库量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'AvgDailyOutbound'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最小库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'MinStock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最大库存' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'MaxStock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预测时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'ForecastTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'建议' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'Suggestions'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户代码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'InventoryForecastAnalyses', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO 