using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;
namespace bnred.Model.Material
{
    /// <summary>
    /// 物料类别
    /// 用于对物料进行分类管理
    /// </summary>
    [Table("MaterialCategories")]
    public class MaterialCategory :  BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        
        [Display(Name = "类别名称")]
        [StringLength(50)]
        public new string ParentID { get; set; }
        public MaterialCategory Parent { get; set; }

        /// <summary>
        /// 类别编码
        /// </summary>
        [Display(Name = "类别编码")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 类别名称
        /// </summary>
        [Display(Name = "类别名称")]
        [Required(ErrorMessage = "{0}是必填项")]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 类别描述
        /// </summary>
        [Display(Name = "类别描述")]
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [Display(Name = "排序号")]
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 图标
        /// </summary>
        [Display(Name = "图标")]
        [StringLength(100)]
        public string Icon { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Display(Name = "是否启用")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 物料列表
        /// </summary>
        [Display(Name = "物料列表")]
        public List<Material> Materials { get; set; }


        [Display(Name = "")]
        public List<MaterialCategory> Childs { get; set; }
    }
} 