using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using bnred.Model.Material;
using bnred.Model.WMS.Batch;
using bnred.Model.WMS.Warehouse;
using bnred.Model.WMS.Enums;
using WalkingTec.Mvvm.Core;

namespace bnred.Model.WMS.Transaction
{
    /// <summary>
    /// 库存交易记录
    /// 记录库存变动情况
    /// </summary>
    [Table("WMS_StockTransactions")]
    public class StockTransaction : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();
        
        /// <summary>
        /// 交易单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TransactionNo { get; set; }

        /// <summary>
        /// 移动类型
        /// </summary>
        [Required]
        public MovementType MovementType { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string MaterialId { get; set; }
        
        /// <summary>
        /// 物料
        /// </summary>
        public Material.Material Material { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [StringLength(50)]
        public string BatchId { get; set; }
        
        /// <summary>
        /// 批次
        /// </summary>
        public Batch.Batch Batch { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string WarehouseId { get; set; }
        
        /// <summary>
        /// 仓库
        /// </summary>
        public Warehouse.Warehouse Warehouse { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LocationId { get; set; }
        
        /// <summary>
        /// 库位
        /// </summary>
        public Location Location { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 变动前数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal BeforeQuantity { get; set; }

        /// <summary>
        /// 变动后数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal AfterQuantity { get; set; }

        /// <summary>
        /// 关联单据号
        /// </summary>
        [StringLength(50)]
        public string RelatedOrderNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remark { get; set; }
    }
}