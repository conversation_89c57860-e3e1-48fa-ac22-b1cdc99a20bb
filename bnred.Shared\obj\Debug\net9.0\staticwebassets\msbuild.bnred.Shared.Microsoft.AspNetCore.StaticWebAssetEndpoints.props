﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/bnred.Shared.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\bnred.Shared.gjyvc1v7oj.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZF/w6RPVWQKAofNojBEKe36gNlLuDK3jbT/T09U/3P0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11853"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ZF/w6RPVWQKAofNojBEKe36gNlLuDK3jbT/T09U/3P0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 03 Mar 2025 13:24:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/bnred.Shared.gjyvc1v7oj.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\bnred.Shared.gjyvc1v7oj.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gjyvc1v7oj"},{"Name":"integrity","Value":"sha256-ZF/w6RPVWQKAofNojBEKe36gNlLuDK3jbT/T09U/3P0="},{"Name":"label","Value":"_content/bnred.Shared/bnred.Shared.bundle.scp.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11853"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ZF/w6RPVWQKAofNojBEKe36gNlLuDK3jbT/T09U/3P0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 03 Mar 2025 13:24:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/css/loading.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\loading.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Or0X8eO62MgHNhhw9lromYgf5C6VNpHBV\u002BUjfA5WEkg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7769"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Or0X8eO62MgHNhhw9lromYgf5C6VNpHBV\u002BUjfA5WEkg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/css/loading.i6lo0fieg4.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\loading.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i6lo0fieg4"},{"Name":"integrity","Value":"sha256-Or0X8eO62MgHNhhw9lromYgf5C6VNpHBV\u002BUjfA5WEkg="},{"Name":"label","Value":"_content/bnred.Shared/css/loading.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7769"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Or0X8eO62MgHNhhw9lromYgf5C6VNpHBV\u002BUjfA5WEkg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/css/site.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UMOD\u002B4CAsqRxoBgQ\u002BbpKrRlsLnQLUUphG5cUFCnblR8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4213"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022UMOD\u002B4CAsqRxoBgQ\u002BbpKrRlsLnQLUUphG5cUFCnblR8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/css/site.ct3866e29z.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ct3866e29z"},{"Name":"integrity","Value":"sha256-UMOD\u002B4CAsqRxoBgQ\u002BbpKrRlsLnQLUUphG5cUFCnblR8="},{"Name":"label","Value":"_content/bnred.Shared/css/site.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4213"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022UMOD\u002B4CAsqRxoBgQ\u002BbpKrRlsLnQLUUphG5cUFCnblR8=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/all.cqsjl4zcgj.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\all.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cqsjl4zcgj"},{"Name":"integrity","Value":"sha256-NA0J0SFBow9T2HDWR/L0upMEdwkzHNRBxD23MBvVLWg="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/all.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"139724"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022NA0J0SFBow9T2HDWR/L0upMEdwkzHNRBxD23MBvVLWg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/all.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\all.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NA0J0SFBow9T2HDWR/L0upMEdwkzHNRBxD23MBvVLWg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"139724"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022NA0J0SFBow9T2HDWR/L0upMEdwkzHNRBxD23MBvVLWg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/all.min.3z9a7it156.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\all.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3z9a7it156"},{"Name":"integrity","Value":"sha256-AbA177XfpSnFEvgpYu1jMygiLabzPCJCRIBtR5jGc0k="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/all.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"101784"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022AbA177XfpSnFEvgpYu1jMygiLabzPCJCRIBtR5jGc0k=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/all.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\all.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AbA177XfpSnFEvgpYu1jMygiLabzPCJCRIBtR5jGc0k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"101784"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022AbA177XfpSnFEvgpYu1jMygiLabzPCJCRIBtR5jGc0k=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/brands.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\brands.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a\u002BcU6KEtZr2ptiakv5JHZIbaZbln5po3kb4mZpF6zik="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23682"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022a\u002BcU6KEtZr2ptiakv5JHZIbaZbln5po3kb4mZpF6zik=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/brands.hlx6dfuua4.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\brands.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hlx6dfuua4"},{"Name":"integrity","Value":"sha256-a\u002BcU6KEtZr2ptiakv5JHZIbaZbln5po3kb4mZpF6zik="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/brands.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23682"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022a\u002BcU6KEtZr2ptiakv5JHZIbaZbln5po3kb4mZpF6zik=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/brands.min.bzjqyogxpb.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\brands.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bzjqyogxpb"},{"Name":"integrity","Value":"sha256-p\u002Bkl9hkqP3kHYh\u002Bd3or8R3UtZx1KeW8s1aU8\u002B8B8IUs="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/brands.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"18594"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022p\u002Bkl9hkqP3kHYh\u002Bd3or8R3UtZx1KeW8s1aU8\u002B8B8IUs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/brands.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\brands.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p\u002Bkl9hkqP3kHYh\u002Bd3or8R3UtZx1KeW8s1aU8\u002B8B8IUs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"18594"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022p\u002Bkl9hkqP3kHYh\u002Bd3or8R3UtZx1KeW8s1aU8\u002B8B8IUs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/fontawesome.3zrrkmhf4p.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\fontawesome.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3zrrkmhf4p"},{"Name":"integrity","Value":"sha256-X0K8shvftBEHtj9CHHOdu37JyyjU4cY23G5L0syjwiM="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/fontawesome.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"113177"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022X0K8shvftBEHtj9CHHOdu37JyyjU4cY23G5L0syjwiM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/fontawesome.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\fontawesome.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-X0K8shvftBEHtj9CHHOdu37JyyjU4cY23G5L0syjwiM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"113177"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022X0K8shvftBEHtj9CHHOdu37JyyjU4cY23G5L0syjwiM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/fontawesome.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\fontawesome.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hjq1Cjn8IDyo9hTO8UxsxwDuZL/qzUFCbc6e\u002BMvZhQk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"80651"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hjq1Cjn8IDyo9hTO8UxsxwDuZL/qzUFCbc6e\u002BMvZhQk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/fontawesome.min.uzncd5ydm8.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\fontawesome.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uzncd5ydm8"},{"Name":"integrity","Value":"sha256-hjq1Cjn8IDyo9hTO8UxsxwDuZL/qzUFCbc6e\u002BMvZhQk="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/fontawesome.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"80651"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hjq1Cjn8IDyo9hTO8UxsxwDuZL/qzUFCbc6e\u002BMvZhQk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/regular.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\regular.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-d1hF6Zf2SffgC/TLwZh87fTT0BnPAenrxisXYP80PC4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"633"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022d1hF6Zf2SffgC/TLwZh87fTT0BnPAenrxisXYP80PC4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/regular.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\regular.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fQbAY/eWwRfEFonJun0jdIopX5FSX7L/0Ke0e9Qopgw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"580"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022fQbAY/eWwRfEFonJun0jdIopX5FSX7L/0Ke0e9Qopgw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/regular.min.fhx8ybnvh6.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\regular.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fhx8ybnvh6"},{"Name":"integrity","Value":"sha256-fQbAY/eWwRfEFonJun0jdIopX5FSX7L/0Ke0e9Qopgw="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/regular.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"580"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022fQbAY/eWwRfEFonJun0jdIopX5FSX7L/0Ke0e9Qopgw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/regular.xq8p3fvtgr.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\regular.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xq8p3fvtgr"},{"Name":"integrity","Value":"sha256-d1hF6Zf2SffgC/TLwZh87fTT0BnPAenrxisXYP80PC4="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/regular.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"633"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022d1hF6Zf2SffgC/TLwZh87fTT0BnPAenrxisXYP80PC4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/solid.46osbeq2uz.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\solid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"46osbeq2uz"},{"Name":"integrity","Value":"sha256-pJNuqWMegUT4By6AZkdeILvY929AOF8osVsGsL868D4="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/solid.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"625"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022pJNuqWMegUT4By6AZkdeILvY929AOF8osVsGsL868D4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/solid.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\solid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pJNuqWMegUT4By6AZkdeILvY929AOF8osVsGsL868D4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"625"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022pJNuqWMegUT4By6AZkdeILvY929AOF8osVsGsL868D4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/solid.min.azllcdazok.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\solid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"azllcdazok"},{"Name":"integrity","Value":"sha256-el0hjI5A3DO5oMJ7ScKl0MlpbqU\u002B5jcYgtNIoxEWrps="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/solid.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"572"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022el0hjI5A3DO5oMJ7ScKl0MlpbqU\u002B5jcYgtNIoxEWrps=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/solid.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\solid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-el0hjI5A3DO5oMJ7ScKl0MlpbqU\u002B5jcYgtNIoxEWrps="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"572"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022el0hjI5A3DO5oMJ7ScKl0MlpbqU\u002B5jcYgtNIoxEWrps=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/svg-with-js.6pex1sdcaj.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\svg-with-js.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6pex1sdcaj"},{"Name":"integrity","Value":"sha256-VtZ/2v8kywPjlkX4gn16zRwk\u002B4r/9GPWMjFaf5csR/c="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/svg-with-js.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21416"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022VtZ/2v8kywPjlkX4gn16zRwk\u002B4r/9GPWMjFaf5csR/c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/svg-with-js.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\svg-with-js.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VtZ/2v8kywPjlkX4gn16zRwk\u002B4r/9GPWMjFaf5csR/c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"21416"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022VtZ/2v8kywPjlkX4gn16zRwk\u002B4r/9GPWMjFaf5csR/c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/svg-with-js.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\svg-with-js.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b5ReHVhq5faSFK/mRgLgLCSopzQYfsTXTSgT1Ugwd/c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16771"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022b5ReHVhq5faSFK/mRgLgLCSopzQYfsTXTSgT1Ugwd/c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/svg-with-js.min.pmajrpr8dw.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\svg-with-js.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pmajrpr8dw"},{"Name":"integrity","Value":"sha256-b5ReHVhq5faSFK/mRgLgLCSopzQYfsTXTSgT1Ugwd/c="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/svg-with-js.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16771"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022b5ReHVhq5faSFK/mRgLgLCSopzQYfsTXTSgT1Ugwd/c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-font-face.amjcgosomw.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-font-face.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"amjcgosomw"},{"Name":"integrity","Value":"sha256-ruEbEFtpO7Sh564p632nm0zJCvJMH9XetFIL2FOQY\u002BM="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/v4-font-face.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1831"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ruEbEFtpO7Sh564p632nm0zJCvJMH9XetFIL2FOQY\u002BM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-font-face.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-font-face.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ruEbEFtpO7Sh564p632nm0zJCvJMH9XetFIL2FOQY\u002BM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1831"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ruEbEFtpO7Sh564p632nm0zJCvJMH9XetFIL2FOQY\u002BM=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-font-face.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-font-face.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Yb0fYtZssjFGBNrMLay89fIO6i0WrmdCdrzn\u002BENzBBY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1736"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Yb0fYtZssjFGBNrMLay89fIO6i0WrmdCdrzn\u002BENzBBY=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-font-face.min.h0dq9cubeq.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-font-face.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h0dq9cubeq"},{"Name":"integrity","Value":"sha256-Yb0fYtZssjFGBNrMLay89fIO6i0WrmdCdrzn\u002BENzBBY="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/v4-font-face.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1736"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Yb0fYtZssjFGBNrMLay89fIO6i0WrmdCdrzn\u002BENzBBY=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-shims.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-shims.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-18fh1PCsJWQcM8GES11ayrRrgZR0toOV\u002ByGO7F6oz6E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"41574"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002218fh1PCsJWQcM8GES11ayrRrgZR0toOV\u002ByGO7F6oz6E=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-shims.fayh04ve0o.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-shims.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fayh04ve0o"},{"Name":"integrity","Value":"sha256-18fh1PCsJWQcM8GES11ayrRrgZR0toOV\u002ByGO7F6oz6E="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/v4-shims.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"41574"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002218fh1PCsJWQcM8GES11ayrRrgZR0toOV\u002ByGO7F6oz6E=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-shims.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-shims.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MCxjgA83JRG1xZgc5l8J3cmSZhmiirfI69I9CxICNqI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"27593"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022MCxjgA83JRG1xZgc5l8J3cmSZhmiirfI69I9CxICNqI=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v4-shims.min.sx5n2to8db.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v4-shims.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sx5n2to8db"},{"Name":"integrity","Value":"sha256-MCxjgA83JRG1xZgc5l8J3cmSZhmiirfI69I9CxICNqI="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/v4-shims.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"27593"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022MCxjgA83JRG1xZgc5l8J3cmSZhmiirfI69I9CxICNqI=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v5-font-face.4h3cykkwl5.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v5-font-face.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4h3cykkwl5"},{"Name":"integrity","Value":"sha256-aPG8E0pZTZGkK6jUXW4Zu\u002BM8lx0DJ56CdV1UeCPIEj4="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/v5-font-face.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"871"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022aPG8E0pZTZGkK6jUXW4Zu\u002BM8lx0DJ56CdV1UeCPIEj4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v5-font-face.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v5-font-face.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aPG8E0pZTZGkK6jUXW4Zu\u002BM8lx0DJ56CdV1UeCPIEj4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"871"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022aPG8E0pZTZGkK6jUXW4Zu\u002BM8lx0DJ56CdV1UeCPIEj4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v5-font-face.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v5-font-face.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5mes899mWORHlgqSkCOWcrrkBXxqYxaMCMyNo/CIxKk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"794"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225mes899mWORHlgqSkCOWcrrkBXxqYxaMCMyNo/CIxKk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/css/v5-font-face.min.qje10ezdu0.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\css\v5-font-face.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qje10ezdu0"},{"Name":"integrity","Value":"sha256-5mes899mWORHlgqSkCOWcrrkBXxqYxaMCMyNo/CIxKk="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/css/v5-font-face.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"794"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225mes899mWORHlgqSkCOWcrrkBXxqYxaMCMyNo/CIxKk=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.9i38cu5pj9.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-brands-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9i38cu5pj9"},{"Name":"integrity","Value":"sha256-P\u002BiQ0Ijs8MybwbkGkgHlKXLbrWI3hlUkCQ4VmC0N5xg="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"107460"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022P\u002BiQ0Ijs8MybwbkGkgHlKXLbrWI3hlUkCQ4VmC0N5xg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-brands-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-x64301uO3TIvXIW49U\u002BMt29ehEZYGuINpefjqb3ukAs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"186112"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022x64301uO3TIvXIW49U\u002BMt29ehEZYGuINpefjqb3ukAs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-brands-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-P\u002BiQ0Ijs8MybwbkGkgHlKXLbrWI3hlUkCQ4VmC0N5xg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"107460"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022P\u002BiQ0Ijs8MybwbkGkgHlKXLbrWI3hlUkCQ4VmC0N5xg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.z96ki82bss.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-brands-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z96ki82bss"},{"Name":"integrity","Value":"sha256-x64301uO3TIvXIW49U\u002BMt29ehEZYGuINpefjqb3ukAs="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-brands-400.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"186112"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022x64301uO3TIvXIW49U\u002BMt29ehEZYGuINpefjqb3ukAs=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.a5sa9hj82d.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-regular-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a5sa9hj82d"},{"Name":"integrity","Value":"sha256-/mnZSEFGLTl/rv8lPuCabceUG\u002Bkx\u002BUKlXmud7487BI0="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25096"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022/mnZSEFGLTl/rv8lPuCabceUG\u002Bkx\u002BUKlXmud7487BI0=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-regular-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/cH3UzAhEX2HaXcV6sJro9Tu\u002BSJcfu27\u002BxsNDP/wq4w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"62048"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022/cH3UzAhEX2HaXcV6sJro9Tu\u002BSJcfu27\u002BxsNDP/wq4w=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.vtd0osvsh9.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-regular-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vtd0osvsh9"},{"Name":"integrity","Value":"sha256-/cH3UzAhEX2HaXcV6sJro9Tu\u002BSJcfu27\u002BxsNDP/wq4w="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"62048"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022/cH3UzAhEX2HaXcV6sJro9Tu\u002BSJcfu27\u002BxsNDP/wq4w=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-regular-400.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-regular-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/mnZSEFGLTl/rv8lPuCabceUG\u002Bkx\u002BUKlXmud7487BI0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25096"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022/mnZSEFGLTl/rv8lPuCabceUG\u002Bkx\u002BUKlXmud7487BI0=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.n70eghwzj2.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-solid-900.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n70eghwzj2"},{"Name":"integrity","Value":"sha256-bVPHBvO3F4zPyCyU0/x5aIKNrNNRcX8nx1emXP2FMUc="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"397728"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022bVPHBvO3F4zPyCyU0/x5aIKNrNNRcX8nx1emXP2FMUc=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-solid-900.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bVPHBvO3F4zPyCyU0/x5aIKNrNNRcX8nx1emXP2FMUc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"397728"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022bVPHBvO3F4zPyCyU0/x5aIKNrNNRcX8nx1emXP2FMUc=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-solid-900.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0nvHUhBcB5\u002BKUW6RQkBqn8Esu0Cfm/hoHy3f4DYLUqY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"150472"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u00220nvHUhBcB5\u002BKUW6RQkBqn8Esu0Cfm/hoHy3f4DYLUqY=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.yr4go0bt7r.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-solid-900.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yr4go0bt7r"},{"Name":"integrity","Value":"sha256-0nvHUhBcB5\u002BKUW6RQkBqn8Esu0Cfm/hoHy3f4DYLUqY="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-solid-900.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"150472"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u00220nvHUhBcB5\u002BKUW6RQkBqn8Esu0Cfm/hoHy3f4DYLUqY=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.bejyua7ldi.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-v4compatibility.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bejyua7ldi"},{"Name":"integrity","Value":"sha256-TXPygFlwFK6JInDO4XdROtMplV3pGtnSpFofV2CjL/o="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10136"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022TXPygFlwFK6JInDO4XdROtMplV3pGtnSpFofV2CjL/o=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.luflx25ujs.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-v4compatibility.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"luflx25ujs"},{"Name":"integrity","Value":"sha256-fRws5fNOGzVnU26NMDnGw0\u002BCV3QpgsX/WltHqC5aZ9c="},{"Name":"label","Value":"_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4584"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022fRws5fNOGzVnU26NMDnGw0\u002BCV3QpgsX/WltHqC5aZ9c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-v4compatibility.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-TXPygFlwFK6JInDO4XdROtMplV3pGtnSpFofV2CjL/o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10136"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022TXPygFlwFK6JInDO4XdROtMplV3pGtnSpFofV2CjL/o=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font-awesome/webfonts/fa-v4compatibility.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font-awesome\webfonts\fa-v4compatibility.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fRws5fNOGzVnU26NMDnGw0\u002BCV3QpgsX/WltHqC5aZ9c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4584"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022fRws5fNOGzVnU26NMDnGw0\u002BCV3QpgsX/WltHqC5aZ9c=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.4nz3gm2iwm.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4nz3gm2iwm"},{"Name":"integrity","Value":"sha256-MJ/NlenzDqZuMQ18ukUcRSPI0smYQk81K6O5SFY7bjU="},{"Name":"label","Value":"_content/bnred.Shared/font/iconfont.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10636"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022MJ/NlenzDqZuMQ18ukUcRSPI0smYQk81K6O5SFY7bjU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.5z964upa0o.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5z964upa0o"},{"Name":"integrity","Value":"sha256-/7vu\u002BwVSf4reckLRXX8CYRYjPzRPFPd8JyHOkm4YNho="},{"Name":"label","Value":"_content/bnred.Shared/font/iconfont.eot"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17796"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022/7vu\u002BwVSf4reckLRXX8CYRYjPzRPFPd8JyHOkm4YNho=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.7kt1gaai4r.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7kt1gaai4r"},{"Name":"integrity","Value":"sha256-GRxT7ZuwCXa9u2E9jpB9ECmuC7ivHlmUoAbaq7ynVNw="},{"Name":"label","Value":"_content/bnred.Shared/font/iconfont.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"60300"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022GRxT7ZuwCXa9u2E9jpB9ECmuC7ivHlmUoAbaq7ynVNw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.8w9x4albli.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8w9x4albli"},{"Name":"integrity","Value":"sha256-WKyOS84CKFwiXYL8GvtqMpwH0FD\u002B8bVD2JOOGUZ8uco="},{"Name":"label","Value":"_content/bnred.Shared/font/iconfont.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8960"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022WKyOS84CKFwiXYL8GvtqMpwH0FD\u002B8bVD2JOOGUZ8uco=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.b8avb4r2wg.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b8avb4r2wg"},{"Name":"integrity","Value":"sha256-i/CtAtSy/rFJOv277RKJP8w65R62HtX\u002B2Ushr2C\u002BHcg="},{"Name":"label","Value":"_content/bnred.Shared/font/iconfont.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17824"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022i/CtAtSy/rFJOv277RKJP8w65R62HtX\u002B2Ushr2C\u002BHcg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nkiYuYvxXxcrsIhJnF4y434nBiz1EZPu4tE\u002BM\u002B1anzg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16067"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022nkiYuYvxXxcrsIhJnF4y434nBiz1EZPu4tE\u002BM\u002B1anzg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/7vu\u002BwVSf4reckLRXX8CYRYjPzRPFPd8JyHOkm4YNho="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"17796"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022/7vu\u002BwVSf4reckLRXX8CYRYjPzRPFPd8JyHOkm4YNho=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GRxT7ZuwCXa9u2E9jpB9ECmuC7ivHlmUoAbaq7ynVNw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"60300"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022GRxT7ZuwCXa9u2E9jpB9ECmuC7ivHlmUoAbaq7ynVNw=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-i/CtAtSy/rFJOv277RKJP8w65R62HtX\u002B2Ushr2C\u002BHcg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"17824"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022i/CtAtSy/rFJOv277RKJP8w65R62HtX\u002B2Ushr2C\u002BHcg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MJ/NlenzDqZuMQ18ukUcRSPI0smYQk81K6O5SFY7bjU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10636"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022MJ/NlenzDqZuMQ18ukUcRSPI0smYQk81K6O5SFY7bjU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WKyOS84CKFwiXYL8GvtqMpwH0FD\u002B8bVD2JOOGUZ8uco="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"8960"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022WKyOS84CKFwiXYL8GvtqMpwH0FD\u002B8bVD2JOOGUZ8uco=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/font/iconfont.yjla5eujtf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\font\iconfont.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yjla5eujtf"},{"Name":"integrity","Value":"sha256-nkiYuYvxXxcrsIhJnF4y434nBiz1EZPu4tE\u002BM\u002B1anzg="},{"Name":"label","Value":"_content/bnred.Shared/font/iconfont.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16067"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022nkiYuYvxXxcrsIhJnF4y434nBiz1EZPu4tE\u002BM\u002B1anzg=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/images/logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-1klqUsG3cqNU\u002BQcVriw7Y9NIlHmSvJRuR6KCibgVjq4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16728"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00221klqUsG3cqNU\u002BQcVriw7Y9NIlHmSvJRuR6KCibgVjq4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/images/logo.u2vmj01w42.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u2vmj01w42"},{"Name":"integrity","Value":"sha256-1klqUsG3cqNU\u002BQcVriw7Y9NIlHmSvJRuR6KCibgVjq4="},{"Name":"label","Value":"_content/bnred.Shared/images/logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16728"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00221klqUsG3cqNU\u002BQcVriw7Y9NIlHmSvJRuR6KCibgVjq4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/js/common.hgf3069b9d.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\common.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hgf3069b9d"},{"Name":"integrity","Value":"sha256-/2asTqMX0qDknWi7/CqjeUJ7Ba3XJlPwKyjhM6ywnD4="},{"Name":"label","Value":"_content/bnred.Shared/js/common.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2762"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/2asTqMX0qDknWi7/CqjeUJ7Ba3XJlPwKyjhM6ywnD4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/bnred.Shared/js/common.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\common.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/2asTqMX0qDknWi7/CqjeUJ7Ba3XJlPwKyjhM6ywnD4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2762"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/2asTqMX0qDknWi7/CqjeUJ7Ba3XJlPwKyjhM6ywnD4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 07 Dec 2024 05:51:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>