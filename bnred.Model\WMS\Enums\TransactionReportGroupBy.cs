using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    public enum TransactionReportGroupBy
    {
        [Display(Name = "仓库")]
        Warehouse = 1,

        [Display(Name = "库位")]
        Location = 2,

        [Display(Name = "物料")]
        Material = 3,

        [Display(Name = "批次")]
        Batch = 4,

        [Display(Name = "日期")]
        Date = 5,

        [Display(Name = "类型")]
        Type = 6,

        [Display(Name = "方向")]
        Direction = 7,

        [Display(Name = "操作人")]
        Operator = 8
    }
}