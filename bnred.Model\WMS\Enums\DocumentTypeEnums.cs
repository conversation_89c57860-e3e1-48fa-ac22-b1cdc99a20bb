using System.ComponentModel.DataAnnotations;

namespace bnred.Model.WMS.Enums
{
    /// <summary>
    /// 文档类型
    /// </summary>
    public enum DocumentType
    {
        /// <summary>
        /// 采购入库
        /// </summary>
        [Display(Name = "采购入库")]
        PurchaseInbound = 0,

        /// <summary>
        /// 生产入库
        /// </summary>
        [Display(Name = "生产入库")]
        ProductionInbound = 1,

        /// <summary>
        /// 退货入库
        /// </summary>
        [Display(Name = "退货入库")]
        ReturnInbound = 2,

        /// <summary>
        /// 调拨入库
        /// </summary>
        [Display(Name = "调拨入库")]
        TransferInbound = 3,

        /// <summary>
        /// 盘盈入库
        /// </summary>
        [Display(Name = "盘盈入库")]
        CountingInbound = 4,

        /// <summary>
        /// 其他入库
        /// </summary>
        [Display(Name = "其他入库")]
        OtherInbound = 5,

        /// <summary>
        /// 销售出库
        /// </summary>
        [Display(Name = "销售出库")]
        SalesOutbound = 10,

        /// <summary>
        /// 生产领料出库
        /// </summary>
        [Display(Name = "生产领料出库")]
        ProductionOutbound = 11,

        /// <summary>
        /// 退货出库
        /// </summary>
        [Display(Name = "退货出库")]
        ReturnOutbound = 12,

        /// <summary>
        /// 调拨出库
        /// </summary>
        [Display(Name = "调拨出库")]
        TransferOutbound = 13,

        /// <summary>
        /// 盘亏出库
        /// </summary>
        [Display(Name = "盘亏出库")]
        CountingOutbound = 14,

        /// <summary>
        /// 其他出库
        /// </summary>
        [Display(Name = "其他出库")]
        OtherOutbound = 15,

        /// <summary>
        /// 库存调整
        /// </summary>
        [Display(Name = "库存调整")]
        Adjustment = 20,

        /// <summary>
        /// 库存移动
        /// </summary>
        [Display(Name = "库存移动")]
        Movement = 21,

        /// <summary>
        /// 库存盘点
        /// </summary>
        [Display(Name = "库存盘点")]
        StockTaking = 22,

        /// <summary>
        /// 质量检验
        /// </summary>
        [Display(Name = "质量检验")]
        QualityCheck = 23,

        /// <summary>
        /// 其他单据
        /// </summary>
        [Display(Name = "其他单据")]
        Other = 99
    }
} 