-- ----------------------------
-- Table structure for WMS_StockTaking
-- ----------------------------
DROP TABLE IF EXISTS [WMS_StockTaking];
CREATE TABLE [WMS_StockTaking] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [Code] nvarchar(50) NOT NULL, -- 盘点单号
  [StockTakingDate] datetime2(7) NOT NULL, -- 盘点日期
  [Status] int NOT NULL, -- 盘点状态 (需要映射到 WMS_EnumDictionary, C#类型: StockTakingStatus)
  [StockTakerId] uniqueidentifier NOT NULL, -- 盘点人ID (外键关联 FrameworkUser)
  [AuditorId] uniqueidentifier NULL, -- 审核人ID (外键关联 FrameworkUser)
  [AuditTime] datetime2(7) NULL, -- 审核时间
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID]),
  UNIQUE NONCLUSTERED ([Code])
)
GO

-- ----------------------------
-- Records of WMS_StockTaking
-- ----------------------------

-- ----------------------------
-- Table structure for WMS_StockTakingDetail
-- ----------------------------
DROP TABLE IF EXISTS [WMS_StockTakingDetail];
CREATE TABLE [WMS_StockTakingDetail] (
  [ID] nvarchar(50) NOT NULL, -- 主键ID
  [StockTakingId] nvarchar(50) NOT NULL, -- 盘点单ID
  [InventoryId] nvarchar(50) NOT NULL, -- 库存ID
  [BookQuantity] decimal(18,4) NOT NULL, -- 账面数量
  [ActualQuantity] decimal(18,4) NOT NULL, -- 实盘数量
  [DifferenceQuantity] decimal(18,4) NOT NULL, -- 差异数量
  [DifferenceReason] nvarchar(500) NULL, -- 差异原因
  [ProcessingMethod] nvarchar(500) NULL, -- 处理方式
  [Remark] nvarchar(500) NULL, -- 备注
  [CreateTime] datetime2(7) NULL,
  [CreateBy] nvarchar(50) NULL,
  [UpdateTime] datetime2(7) NULL,
  [UpdateBy] nvarchar(50) NULL,
  [TenantCode] nvarchar(50) NULL,
  PRIMARY KEY CLUSTERED ([ID])
)
GO

-- ----------------------------
-- Records of WMS_StockTakingDetail
-- ----------------------------

-- ----------------------------
-- Foreign Keys structure for table WMS_StockTaking
-- ----------------------------
-- 注意: StockTakerId 和 AuditorId 外键关联 WMS_FrameworkUser (或其他用户表), 请根据实际用户表名修改。
-- ALTER TABLE [WMS_StockTaking] ADD CONSTRAINT [FK_WMS_StockTaking_StockTaker] FOREIGN KEY ([StockTakerId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO
-- ALTER TABLE [WMS_StockTaking] ADD CONSTRAINT [FK_WMS_StockTaking_Auditor] FOREIGN KEY ([AuditorId]) REFERENCES [WMS_FrameworkUser] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- GO

-- ----------------------------
-- Foreign Keys structure for table WMS_StockTakingDetail
-- ----------------------------
ALTER TABLE [WMS_StockTakingDetail] ADD CONSTRAINT [FK_WMS_StockTakingDetail_StockTaking] FOREIGN KEY ([StockTakingId]) REFERENCES [WMS_StockTaking] ([ID]) ON DELETE CASCADE ON UPDATE NO ACTION;
GO
ALTER TABLE [WMS_StockTakingDetail] ADD CONSTRAINT [FK_WMS_StockTakingDetail_Inventory] FOREIGN KEY ([InventoryId]) REFERENCES [WMS_Inventory] ([ID]) ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- ----------------------------
-- MS_Description for table WMS_StockTaking
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点单号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'Code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'StockTakingDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'Status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点人ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'StockTakerId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'审核人ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'AuditorId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'审核时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'AuditTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTaking', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO

-- ----------------------------
-- MS_Description for table WMS_StockTakingDetail
-- ----------------------------
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点明细' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'主键ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'ID'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'盘点单ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'StockTakingId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'库存ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'InventoryId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'账面数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'BookQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'实盘数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'ActualQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'差异数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'DifferenceQuantity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'差异原因' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'DifferenceReason'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'处理方式' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'ProcessingMethod'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'Remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'CreateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'CreateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'UpdateTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'UpdateBy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WMS_StockTakingDetail', @level2type=N'COLUMN',@level2name=N'TenantCode'
GO 