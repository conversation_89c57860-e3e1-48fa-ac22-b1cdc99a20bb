<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梵素EAP仓库管理系统 - 新增功能模块设计</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .module-intro {
            background-color: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-item {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .footer {
            text-align: center;
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-top: 40px;
            border: 2px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>梵素EAP仓库管理系统</h1>
            <h2>新增功能模块设计方案</h2>
            <p>库存预警 | 库存批次 | 库存快照管理</p>
        </div>

        <section id="inventory-alert">
            <h2>1. 库存预警模块设计</h2>

            <div class="module-intro">
                <h3>1.1 库存预警概述</h3>
                <p>库存预警模块是仓库管理系统的智能监控核心，通过实时监控库存水平、预测需求变化、分析历史数据，为企业提供精准的库存预警服务。支持多维度、多层次的预警机制，确保库存安全，避免缺货和积压风险。</p>

                <div class="feature-grid">
                    <div class="feature-item" style="border-color: #dc3545;">
                        <h4 style="color: #dc3545;">🚨 智能预警引擎</h4>
                        <ul>
                            <li><strong>多级预警阈值</strong>：支持安全库存、最低库存、最高库存等多级预警</li>
                            <li><strong>动态阈值调整</strong>：基于历史数据和季节性因素自动调整预警阈值</li>
                            <li><strong>预测性预警</strong>：基于消耗趋势预测未来库存不足风险</li>
                            <li><strong>异常检测</strong>：识别异常消耗模式和库存波动</li>
                            <li><strong>供应商风险预警</strong>：监控供应商交付风险</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #ffc107;">
                        <h4 style="color: #e67e22;">📊 多维度监控</h4>
                        <ul>
                            <li><strong>物料维度</strong>：按单个物料、物料分类、ABC分类监控</li>
                            <li><strong>地理维度</strong>：按仓库、库区、库位进行区域性监控</li>
                            <li><strong>时间维度</strong>：支持实时、日、周、月等不同时间粒度</li>
                            <li><strong>业务维度</strong>：按项目、客户、订单等业务维度监控</li>
                            <li><strong>质量维度</strong>：监控过期、损坏、质量问题库存</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #17a2b8;">
                        <h4 style="color: #17a2b8;">📱 智能通知系统</h4>
                        <ul>
                            <li><strong>多渠道通知</strong>：支持邮件、短信、微信、系统消息等</li>
                            <li><strong>个性化推送</strong>：根据用户角色和权限推送相关预警</li>
                            <li><strong>升级机制</strong>：预警未处理时自动升级到上级管理者</li>
                            <li><strong>批量通知</strong>：支持批量预警的汇总通知</li>
                            <li><strong>通知历史</strong>：完整记录所有通知历史和处理状态</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #28a745;">
                        <h4 style="color: #28a745;">🎯 预警处理流程</h4>
                        <ul>
                            <li><strong>自动处理</strong>：支持预警触发后的自动处理流程</li>
                            <li><strong>工作流集成</strong>：与采购、调拨等业务流程无缝集成</li>
                            <li><strong>处理跟踪</strong>：完整跟踪预警从产生到解决的全过程</li>
                            <li><strong>效果评估</strong>：评估预警处理的效果和及时性</li>
                            <li><strong>持续优化</strong>：基于历史数据优化预警规则</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h3>1.2 库存预警数据模型</h3>
            <div class="code-block">
// 库存预警规则配置表
public class InventoryAlertRule : BasePoco
{
    [Key]
    [StringLength(50)]
    public new string ID { get; set; } = Guid.CreateVersion7().ToString();

    /// <summary>
    /// 预警规则编号
    /// </summary>
    [Required]
    [StringLength(50)]
    [Display(Name = "规则编号")]
    public string RuleCode { get; set; }

    /// <summary>
    /// 预警规则名称
    /// </summary>
    [Required]
    [StringLength(200)]
    [Display(Name = "规则名称")]
    public string RuleName { get; set; }

    /// <summary>
    /// 预警类型
    /// </summary>
    [Required]
    [Display(Name = "预警类型")]
    public InventoryAlertType AlertType { get; set; }

    /// <summary>
    /// 预警级别
    /// </summary>
    [Required]
    [Display(Name = "预警级别")]
    public AlertLevel AlertLevel { get; set; }

    /// <summary>
    /// 物料ID - 可选，特定物料预警
    /// </summary>
    [StringLength(50)]
    [Display(Name = "物料")]
    public string MaterialId { get; set; }

    /// <summary>
    /// 物料导航属性
    /// </summary>
    public virtual Material Material { get; set; }

    /// <summary>
    /// 物料分类ID - 可选，分类级预警
    /// </summary>
    [StringLength(50)]
    [Display(Name = "物料分类")]
    public string CategoryId { get; set; }

    /// <summary>
    /// 物料分类导航属性
    /// </summary>
    public virtual MaterialCategory Category { get; set; }

    /// <summary>
    /// 仓库ID - 可选，特定仓库预警
    /// </summary>
    [StringLength(50)]
    [Display(Name = "仓库")]
    public string WarehouseId { get; set; }

    /// <summary>
    /// 仓库导航属性
    /// </summary>
    public virtual Warehouse Warehouse { get; set; }

    /// <summary>
    /// 最低库存阈值
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "最低库存")]
    public decimal? MinStockThreshold { get; set; }

    /// <summary>
    /// 安全库存阈值
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "安全库存")]
    public decimal? SafetyStockThreshold { get; set; }

    /// <summary>
    /// 最高库存阈值
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "最高库存")]
    public decimal? MaxStockThreshold { get; set; }

    /// <summary>
    /// 过期预警天数
    /// </summary>
    [Display(Name = "过期预警天数")]
    public int? ExpiryWarningDays { get; set; }

    /// <summary>
    /// 是否启用动态阈值
    /// </summary>
    [Display(Name = "启用动态阈值")]
    public bool EnableDynamicThreshold { get; set; }

    /// <summary>
    /// 检查频率（分钟）
    /// </summary>
    [Display(Name = "检查频率")]
    public int CheckInterval { get; set; }

    /// <summary>
    /// 通知方式
    /// </summary>
    [Required]
    [Display(Name = "通知方式")]
    public NotificationMethod NotificationMethod { get; set; }

    /// <summary>
    /// 通知对象配置（JSON格式）
    /// </summary>
    [Column(TypeName = "nvarchar(max)")]
    [Display(Name = "通知对象")]
    public string NotificationTargets { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [Display(Name = "是否启用")]
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 规则描述
    /// </summary>
    [StringLength(1000)]
    [Display(Name = "规则描述")]
    public string Description { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500)]
    [Display(Name = "备注")]
    public string Remark { get; set; }
}
            </div>

            <h3>1.3 库存预警UI原型设计</h3>

            <!-- 预警管理主界面 -->
            <div style="border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin: 20px 0; background-color: #fff8f8;">
                <h4 style="color: #dc3545; margin-bottom: 15px;">🚨 预警管理主界面</h4>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 15px;">
                    <!-- 顶部统计卡片 -->
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="text-align: center; padding: 15px; background: #f8d7da; border-radius: 8px; border: 1px solid #dc3545;">
                            <div style="font-size: 24px; color: #dc3545; font-weight: bold;">23</div>
                            <div style="font-size: 14px; color: #721c24;">紧急预警</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffc107;">
                            <div style="font-size: 24px; color: #856404; font-weight: bold;">67</div>
                            <div style="font-size: 14px; color: #856404;">严重预警</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #e7f3ff; border-radius: 8px; border: 1px solid #007bff;">
                            <div style="font-size: 24px; color: #007bff; font-weight: bold;">145</div>
                            <div style="font-size: 14px; color: #004085;">一般预警</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #d4edda; border-radius: 8px; border: 1px solid #28a745;">
                            <div style="font-size: 24px; color: #28a745; font-weight: bold;">89</div>
                            <div style="font-size: 14px; color: #155724;">已处理</div>
                        </div>
                    </div>

                    <!-- 操作栏 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <div>
                            <button style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px;">🚨 新建预警规则</button>
                            <button style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px;">✅ 批量处理</button>
                            <button style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px;">📊 预警报告</button>
                        </div>
                        <div>
                            <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                                <option>全部级别</option>
                                <option>紧急</option>
                                <option>严重</option>
                                <option>警告</option>
                                <option>信息</option>
                            </select>
                            <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                                <option>全部状态</option>
                                <option>新建</option>
                                <option>已通知</option>
                                <option>处理中</option>
                                <option>已处理</option>
                            </select>
                            <input type="text" placeholder="搜索物料..." style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>

                    <!-- 预警列表 -->
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">预警编号</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">预警类型</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">物料信息</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">当前库存</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">阈值</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">预警级别</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">预警时间</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">状态</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #fff5f5;">
                                <td style="border: 1px solid #ddd; padding: 10px;">AL202412001</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 12px;">库存不足</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <div style="font-weight: bold;">M001 - 钢材A型</div>
                                    <div style="font-size: 12px; color: #6c757d;">主仓库 - A01区</div>
                                </td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #dc3545; font-weight: bold;">50</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">100</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 12px;">紧急</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">2024-12-31 08:30</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已通知</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">处理</button>
                                    <button style="background: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">忽略</button>
                                </td>
                            </tr>
                            <tr style="background: #fffbf0;">
                                <td style="border: 1px solid #ddd; padding: 10px;">AL202412002</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 12px;">即将过期</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <div style="font-weight: bold;">M002 - 化学试剂B</div>
                                    <div style="font-size: 12px; color: #6c757d;">批次: B20241201</div>
                                </td>
                                <td style="border: 1px solid #ddd; padding: 10px;">200</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">7天</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 12px;">严重</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">2024-12-31 09:15</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #e7f3ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 12px;">处理中</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <button style="background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">完成</button>
                                    <button style="background: #ffc107; color: #212529; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">升级</button>
                                </td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">AL202412003</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #e7f3ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 12px;">库存过量</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <div style="font-weight: bold;">M003 - 塑料原料C</div>
                                    <div style="font-size: 12px; color: #6c757d;">分仓库 - C区</div>
                                </td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #ffc107; font-weight: bold;">5,000</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">3,000</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #e7f3ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 12px;">警告</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">2024-12-31 10:00</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已处理</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">查看</button>
                                    <button style="background: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">关闭</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 预警规则配置界面 -->
            <div style="border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin: 20px 0; background-color: #fffbf0;">
                <h4 style="color: #e67e22; margin-bottom: 15px;">⚙️ 预警规则配置界面</h4>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px;">
                    <h5 style="margin-bottom: 20px; color: #333;">新建预警规则</h5>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">规则名称 *</label>
                            <input type="text" placeholder="请输入规则名称" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">预警类型 *</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>库存不足</option>
                                <option>库存过量</option>
                                <option>即将过期</option>
                                <option>已过期</option>
                                <option>零库存</option>
                                <option>长期无动销</option>
                                <option>质量异常</option>
                                <option>供应商风险</option>
                                <option>成本异常</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">预警级别 *</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>紧急</option>
                                <option>严重</option>
                                <option>警告</option>
                                <option>信息</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">检查频率（分钟）</label>
                            <input type="number" value="30" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">通知方式</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>系统消息</option>
                                <option>邮件</option>
                                <option>短信</option>
                                <option>微信</option>
                                <option>多渠道</option>
                            </select>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">阈值设置</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">最低库存</label>
                                <input type="number" placeholder="100" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">安全库存</label>
                                <input type="number" placeholder="200" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">最高库存</label>
                                <input type="number" placeholder="1000" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">适用范围</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">仓库选择</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option>全部仓库</option>
                                    <option>主仓库</option>
                                    <option>分仓库A</option>
                                    <option>分仓库B</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">物料分类</label>
                                <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option>全部分类</option>
                                    <option>原材料</option>
                                    <option>半成品</option>
                                    <option>成品</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 10px;"><input type="checkbox" checked style="margin-right: 8px;">启用动态阈值</label>
                        <label style="display: block; margin-bottom: 10px;"><input type="checkbox" style="margin-right: 8px;">启用预测性预警</label>
                        <label style="display: block; margin-bottom: 10px;"><input type="checkbox" checked style="margin-right: 8px;">启用规则</label>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px;">取消</button>
                        <button style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px;">保存规则</button>
                    </div>
                </div>
            </div>
        </section>

        <section id="inventory-batch">
            <h2>2. 库存批次模块设计</h2>

            <div class="module-intro">
                <h3>2.1 库存批次概述</h3>
                <p>库存批次模块是对原有批次管理的深度扩展，专注于库存层面的批次精细化管理。通过批次级库存追踪、批次间转换、批次合并分拆等功能，实现更加精确的库存控制和质量管理。</p>

                <div class="feature-grid">
                    <div class="feature-item" style="border-color: #6f42c1;">
                        <h4 style="color: #6f42c1;">🔄 批次库存流转</h4>
                        <ul>
                            <li><strong>批次级库存追踪</strong>：精确到每个批次的库存数量和位置</li>
                            <li><strong>批次间转换</strong>：支持批次重新包装、重新标识</li>
                            <li><strong>批次合并</strong>：相同规格批次的智能合并</li>
                            <li><strong>批次分拆</strong>：大批次按需求分拆为小批次</li>
                            <li><strong>批次调拨</strong>：批次在不同库位间的精确调拨</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #e83e8c;">
                        <h4 style="color: #e83e8c;">📋 批次状态管理</h4>
                        <ul>
                            <li><strong>状态生命周期</strong>：从入库到出库的完整状态管理</li>
                            <li><strong>质量状态跟踪</strong>：待检、合格、不合格等质量状态</li>
                            <li><strong>锁定机制</strong>：支持批次锁定和解锁操作</li>
                            <li><strong>保留策略</strong>：特殊批次的保留和隔离管理</li>
                            <li><strong>状态变更审计</strong>：完整记录状态变更历史</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h3>2.2 库存批次数据模型</h3>
            <div class="code-block">
// 库存批次详细信息表
public class InventoryBatchDetail : BasePoco
{
    [Key]
    [StringLength(50)]
    public new string ID { get; set; } = Guid.CreateVersion7().ToString();

    /// <summary>
    /// 批次ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string BatchId { get; set; }

    /// <summary>
    /// 批次导航属性
    /// </summary>
    public virtual Batch Batch { get; set; }

    /// <summary>
    /// 库位ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string LocationId { get; set; }

    /// <summary>
    /// 库位导航属性
    /// </summary>
    public virtual Location Location { get; set; }

    /// <summary>
    /// 当前库存数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "当前库存")]
    public decimal CurrentQuantity { get; set; }

    /// <summary>
    /// 可用数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "可用数量")]
    public decimal AvailableQuantity { get; set; }

    /// <summary>
    /// 预留数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "预留数量")]
    public decimal ReservedQuantity { get; set; }

    /// <summary>
    /// 冻结数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "冻结数量")]
    public decimal FrozenQuantity { get; set; }

    /// <summary>
    /// 损坏数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "损坏数量")]
    public decimal DamagedQuantity { get; set; }

    /// <summary>
    /// 批次状态
    /// </summary>
    [Required]
    [Display(Name = "批次状态")]
    public BatchInventoryStatus Status { get; set; }

    /// <summary>
    /// 最后盘点时间
    /// </summary>
    [Display(Name = "最后盘点时间")]
    public DateTime? LastStocktakeTime { get; set; }

    /// <summary>
    /// 最后移动时间
    /// </summary>
    [Display(Name = "最后移动时间")]
    public DateTime? LastMovementTime { get; set; }

    /// <summary>
    /// 存储条件要求
    /// </summary>
    [StringLength(200)]
    [Display(Name = "存储条件")]
    public string StorageConditions { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500)]
    [Display(Name = "备注")]
    public string Remark { get; set; }
}

// 批次操作记录表
public class BatchOperation : BasePoco
{
    [Key]
    [StringLength(50)]
    public new string ID { get; set; } = Guid.CreateVersion7().ToString();

    /// <summary>
    /// 批次ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string BatchId { get; set; }

    /// <summary>
    /// 批次导航属性
    /// </summary>
    public virtual Batch Batch { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    [Required]
    [Display(Name = "操作类型")]
    public BatchOperationType OperationType { get; set; }

    /// <summary>
    /// 源批次ID - 用于合并、分拆等操作
    /// </summary>
    [StringLength(50)]
    [Display(Name = "源批次")]
    public string SourceBatchId { get; set; }

    /// <summary>
    /// 目标批次ID - 用于合并、分拆等操作
    /// </summary>
    [StringLength(50)]
    [Display(Name = "目标批次")]
    public string TargetBatchId { get; set; }

    /// <summary>
    /// 操作数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "操作数量")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// 操作前状态
    /// </summary>
    [Display(Name = "操作前状态")]
    public BatchStatus? BeforeStatus { get; set; }

    /// <summary>
    /// 操作后状态
    /// </summary>
    [Display(Name = "操作后状态")]
    public BatchStatus? AfterStatus { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    [Required]
    [StringLength(200)]
    [Display(Name = "操作原因")]
    public string Reason { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    [Required]
    [Display(Name = "操作时间")]
    public DateTime OperationTime { get; set; }

    /// <summary>
    /// 操作人员ID
    /// </summary>
    [Required]
    public Guid OperatorId { get; set; }

    /// <summary>
    /// 操作人员导航属性
    /// </summary>
    public virtual FrameworkUser Operator { get; set; }

    /// <summary>
    /// 审批人员ID
    /// </summary>
    public Guid? ApproverId { get; set; }

    /// <summary>
    /// 审批人员导航属性
    /// </summary>
    public virtual FrameworkUser Approver { get; set; }

    /// <summary>
    /// 审批时间
    /// </summary>
    [Display(Name = "审批时间")]
    public DateTime? ApprovalTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500)]
    [Display(Name = "备注")]
    public string Remark { get; set; }
}

// 批次库存状态枚举
public enum BatchInventoryStatus
{
    [Display(Name = "正常")]
    Normal = 0,

    [Display(Name = "预留")]
    Reserved = 1,

    [Display(Name = "冻结")]
    Frozen = 2,

    [Display(Name = "隔离")]
    Quarantined = 3,

    [Display(Name = "损坏")]
    Damaged = 4,

    [Display(Name = "过期")]
    Expired = 5,

    [Display(Name = "待处理")]
    Pending = 6
}

// 批次操作类型枚举
public enum BatchOperationType
{
    [Display(Name = "创建")]
    Create = 0,

    [Display(Name = "合并")]
    Merge = 1,

    [Display(Name = "分拆")]
    Split = 2,

    [Display(Name = "转换")]
    Convert = 3,

    [Display(Name = "锁定")]
    Lock = 4,

    [Display(Name = "解锁")]
    Unlock = 5,

    [Display(Name = "状态变更")]
    StatusChange = 6,

    [Display(Name = "质量变更")]
    QualityChange = 7,

    [Display(Name = "报废")]
    Scrap = 8
}
            </div>
        </section>

        <section id="inventory-snapshot">
            <h2>3. 库存快照管理模块设计</h2>

            <div class="module-intro">
                <h3>3.1 库存快照概述</h3>
                <p>库存快照管理模块提供库存数据的时点快照功能，支持定期自动快照、手动快照、快照对比分析等功能。通过快照管理，可以追踪库存变化历史、进行时点分析、支持审计要求，为库存管理提供历史数据支撑。</p>

                <div class="feature-grid">
                    <div class="feature-item" style="border-color: #20c997;">
                        <h4 style="color: #20c997;">📸 智能快照生成</h4>
                        <ul>
                            <li><strong>定时自动快照</strong>：支持按日、周、月、季度等周期自动生成</li>
                            <li><strong>事件触发快照</strong>：重要业务事件发生时自动生成快照</li>
                            <li><strong>手动快照</strong>：支持用户手动创建特定时点快照</li>
                            <li><strong>增量快照</strong>：只记录变化数据，节省存储空间</li>
                            <li><strong>压缩存储</strong>：快照数据压缩存储，提高效率</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #fd7e14;">
                        <h4 style="color: #fd7e14;">🔍 快照分析对比</h4>
                        <ul>
                            <li><strong>时点对比</strong>：对比不同时点的库存状态</li>
                            <li><strong>趋势分析</strong>：分析库存变化趋势和规律</li>
                            <li><strong>差异分析</strong>：识别异常变化和差异原因</li>
                            <li><strong>多维度分析</strong>：按物料、仓库、批次等维度分析</li>
                            <li><strong>可视化展示</strong>：图表化展示分析结果</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #6610f2;">
                        <h4 style="color: #6610f2;">📊 快照数据管理</h4>
                        <ul>
                            <li><strong>快照版本管理</strong>：支持快照版本控制和回滚</li>
                            <li><strong>数据完整性</strong>：确保快照数据的完整性和一致性</li>
                            <li><strong>存储优化</strong>：智能存储策略，平衡性能和空间</li>
                            <li><strong>数据归档</strong>：历史快照数据的归档和清理</li>
                            <li><strong>权限控制</strong>：严格的快照访问权限控制</li>
                        </ul>
                    </div>
                    <div class="feature-item" style="border-color: #198754;">
                        <h4 style="color: #198754;">📈 业务应用场景</h4>
                        <ul>
                            <li><strong>审计支持</strong>：为内外部审计提供历史数据支撑</li>
                            <li><strong>合规报告</strong>：满足监管要求的定期报告</li>
                            <li><strong>绩效评估</strong>：基于历史数据进行绩效评估</li>
                            <li><strong>决策支持</strong>：为管理决策提供历史数据参考</li>
                            <li><strong>问题追溯</strong>：快速定位和分析历史问题</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h3>3.2 库存快照数据模型</h3>
            <div class="code-block">
// 库存快照主表
public class InventorySnapshot : BasePoco
{
    [Key]
    [StringLength(50)]
    public new string ID { get; set; } = Guid.CreateVersion7().ToString();

    /// <summary>
    /// 快照编号
    /// </summary>
    [Required]
    [StringLength(50)]
    [Display(Name = "快照编号")]
    public string SnapshotNumber { get; set; }

    /// <summary>
    /// 快照名称
    /// </summary>
    [Required]
    [StringLength(200)]
    [Display(Name = "快照名称")]
    public string SnapshotName { get; set; }

    /// <summary>
    /// 快照类型
    /// </summary>
    [Required]
    [Display(Name = "快照类型")]
    public SnapshotType SnapshotType { get; set; }

    /// <summary>
    /// 快照时间
    /// </summary>
    [Required]
    [Display(Name = "快照时间")]
    public DateTime SnapshotTime { get; set; }

    /// <summary>
    /// 快照范围
    /// </summary>
    [Required]
    [Display(Name = "快照范围")]
    public SnapshotScope Scope { get; set; }

    /// <summary>
    /// 仓库ID - 可选，特定仓库快照
    /// </summary>
    [StringLength(50)]
    [Display(Name = "仓库")]
    public string WarehouseId { get; set; }

    /// <summary>
    /// 仓库导航属性
    /// </summary>
    public virtual Warehouse Warehouse { get; set; }

    /// <summary>
    /// 物料分类ID - 可选，特定分类快照
    /// </summary>
    [StringLength(50)]
    [Display(Name = "物料分类")]
    public string CategoryId { get; set; }

    /// <summary>
    /// 物料分类导航属性
    /// </summary>
    public virtual MaterialCategory Category { get; set; }

    /// <summary>
    /// 快照状态
    /// </summary>
    [Required]
    [Display(Name = "快照状态")]
    public SnapshotStatus Status { get; set; }

    /// <summary>
    /// 总记录数
    /// </summary>
    [Display(Name = "总记录数")]
    public int TotalRecords { get; set; }

    /// <summary>
    /// 总库存数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "总库存数量")]
    public decimal TotalQuantity { get; set; }

    /// <summary>
    /// 总库存价值
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "总库存价值")]
    public decimal TotalValue { get; set; }

    /// <summary>
    /// 快照大小（字节）
    /// </summary>
    [Display(Name = "快照大小")]
    public long SnapshotSize { get; set; }

    /// <summary>
    /// 是否压缩
    /// </summary>
    [Display(Name = "是否压缩")]
    public bool IsCompressed { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Required]
    public Guid CreatedById { get; set; }

    /// <summary>
    /// 创建人导航属性
    /// </summary>
    public virtual FrameworkUser CreatedBy { get; set; }

    /// <summary>
    /// 快照描述
    /// </summary>
    [StringLength(1000)]
    [Display(Name = "快照描述")]
    public string Description { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500)]
    [Display(Name = "备注")]
    public string Remark { get; set; }

    /// <summary>
    /// 快照明细集合
    /// </summary>
    public virtual ICollection<InventorySnapshotDetail> SnapshotDetails { get; set; }
}

// 库存快照明细表
public class InventorySnapshotDetail : BasePoco
{
    [Key]
    [StringLength(50)]
    public new string ID { get; set; } = Guid.CreateVersion7().ToString();

    /// <summary>
    /// 快照主表ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SnapshotId { get; set; }

    /// <summary>
    /// 快照主表导航属性
    /// </summary>
    public virtual InventorySnapshot Snapshot { get; set; }

    /// <summary>
    /// 物料ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string MaterialId { get; set; }

    /// <summary>
    /// 物料导航属性
    /// </summary>
    public virtual Material Material { get; set; }

    /// <summary>
    /// 批次ID - 可选，批次级快照
    /// </summary>
    [StringLength(50)]
    public string BatchId { get; set; }

    /// <summary>
    /// 批次导航属性
    /// </summary>
    public virtual Batch Batch { get; set; }

    /// <summary>
    /// 库位ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string LocationId { get; set; }

    /// <summary>
    /// 库位导航属性
    /// </summary>
    public virtual Location Location { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "库存数量")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// 可用数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "可用数量")]
    public decimal AvailableQuantity { get; set; }

    /// <summary>
    /// 预留数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "预留数量")]
    public decimal ReservedQuantity { get; set; }

    /// <summary>
    /// 冻结数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "冻结数量")]
    public decimal FrozenQuantity { get; set; }

    /// <summary>
    /// 单位成本
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "单位成本")]
    public decimal UnitCost { get; set; }

    /// <summary>
    /// 总价值
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "总价值")]
    public decimal TotalValue { get; set; }

    /// <summary>
    /// 库存状态
    /// </summary>
    [Required]
    [Display(Name = "库存状态")]
    public InventoryStatus Status { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Display(Name = "最后更新时间")]
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500)]
    [Display(Name = "备注")]
    public string Remark { get; set; }
}

// 库存预警记录表
public class InventoryAlertRecord : BasePoco
{
    [Key]
    [StringLength(50)]
    public new string ID { get; set; } = Guid.CreateVersion7().ToString();

    /// <summary>
    /// 预警编号
    /// </summary>
    [Required]
    [StringLength(50)]
    [Display(Name = "预警编号")]
    public string AlertNumber { get; set; }

    /// <summary>
    /// 预警规则ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string RuleId { get; set; }

    /// <summary>
    /// 预警规则导航属性
    /// </summary>
    public virtual InventoryAlertRule Rule { get; set; }

    /// <summary>
    /// 物料ID
    /// </summary>
    [Required]
    [StringLength(50)]
    public string MaterialId { get; set; }

    /// <summary>
    /// 物料导航属性
    /// </summary>
    public virtual Material Material { get; set; }

    /// <summary>
    /// 批次ID - 可选
    /// </summary>
    [StringLength(50)]
    public string BatchId { get; set; }

    /// <summary>
    /// 批次导航属性
    /// </summary>
    public virtual Batch Batch { get; set; }

    /// <summary>
    /// 仓库ID
    /// </summary>
    [StringLength(50)]
    public string WarehouseId { get; set; }

    /// <summary>
    /// 仓库导航属性
    /// </summary>
    public virtual Warehouse Warehouse { get; set; }

    /// <summary>
    /// 预警类型
    /// </summary>
    [Required]
    [Display(Name = "预警类型")]
    public InventoryAlertType AlertType { get; set; }

    /// <summary>
    /// 预警级别
    /// </summary>
    [Required]
    [Display(Name = "预警级别")]
    public AlertLevel AlertLevel { get; set; }

    /// <summary>
    /// 当前库存数量
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "当前库存")]
    public decimal CurrentQuantity { get; set; }

    /// <summary>
    /// 阈值
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    [Display(Name = "阈值")]
    public decimal ThresholdValue { get; set; }

    /// <summary>
    /// 预警消息
    /// </summary>
    [Required]
    [StringLength(1000)]
    [Display(Name = "预警消息")]
    public string AlertMessage { get; set; }

    /// <summary>
    /// 预警时间
    /// </summary>
    [Required]
    [Display(Name = "预警时间")]
    public DateTime AlertTime { get; set; }

    /// <summary>
    /// 预警状态
    /// </summary>
    [Required]
    [Display(Name = "预警状态")]
    public AlertRecordStatus Status { get; set; }

    /// <summary>
    /// 处理人员ID
    /// </summary>
    public Guid? HandlerId { get; set; }

    /// <summary>
    /// 处理人员导航属性
    /// </summary>
    public virtual FrameworkUser Handler { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    [Display(Name = "处理时间")]
    public DateTime? HandleTime { get; set; }

    /// <summary>
    /// 处理结果
    /// </summary>
    [StringLength(1000)]
    [Display(Name = "处理结果")]
    public string HandleResult { get; set; }

    /// <summary>
    /// 是否已通知
    /// </summary>
    [Display(Name = "是否已通知")]
    public bool IsNotified { get; set; }

    /// <summary>
    /// 通知时间
    /// </summary>
    [Display(Name = "通知时间")]
    public DateTime? NotificationTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500)]
    [Display(Name = "备注")]
    public string Remark { get; set; }
}

// 库存预警类型枚举
public enum InventoryAlertType
{
    [Display(Name = "库存不足")]
    LowStock = 0,

    [Display(Name = "库存过量")]
    OverStock = 1,

    [Display(Name = "即将过期")]
    NearExpiry = 2,

    [Display(Name = "已过期")]
    Expired = 3,

    [Display(Name = "零库存")]
    ZeroStock = 4,

    [Display(Name = "长期无动销")]
    SlowMoving = 5,

    [Display(Name = "质量异常")]
    QualityIssue = 6,

    [Display(Name = "供应商风险")]
    SupplierRisk = 7,

    [Display(Name = "成本异常")]
    CostAnomaly = 8
}

// 预警级别枚举
public enum AlertLevel
{
    [Display(Name = "信息")]
    Info = 0,

    [Display(Name = "警告")]
    Warning = 1,

    [Display(Name = "严重")]
    Critical = 2,

    [Display(Name = "紧急")]
    Emergency = 3
}

// 通知方式枚举
public enum NotificationMethod
{
    [Display(Name = "系统消息")]
    SystemMessage = 0,

    [Display(Name = "邮件")]
    Email = 1,

    [Display(Name = "短信")]
    SMS = 2,

    [Display(Name = "微信")]
    WeChat = 3,

    [Display(Name = "钉钉")]
    DingTalk = 4,

    [Display(Name = "多渠道")]
    Multiple = 5
}

// 快照类型枚举
public enum SnapshotType
{
    [Display(Name = "定时快照")]
    Scheduled = 0,

    [Display(Name = "手动快照")]
    Manual = 1,

    [Display(Name = "事件快照")]
    EventTriggered = 2,

    [Display(Name = "增量快照")]
    Incremental = 3,

    [Display(Name = "完整快照")]
    Full = 4
}

// 快照范围枚举
public enum SnapshotScope
{
    [Display(Name = "全部库存")]
    All = 0,

    [Display(Name = "指定仓库")]
    Warehouse = 1,

    [Display(Name = "指定分类")]
    Category = 2,

    [Display(Name = "指定物料")]
    Material = 3,

    [Display(Name = "指定批次")]
    Batch = 4
}

// 快照状态枚举
public enum SnapshotStatus
{
    [Display(Name = "创建中")]
    Creating = 0,

    [Display(Name = "已完成")]
    Completed = 1,

    [Display(Name = "失败")]
    Failed = 2,

    [Display(Name = "已归档")]
    Archived = 3,

    [Display(Name = "已删除")]
    Deleted = 4
}

// 预警记录状态枚举
public enum AlertRecordStatus
{
    [Display(Name = "新建")]
    New = 0,

    [Display(Name = "已通知")]
    Notified = 1,

    [Display(Name = "处理中")]
    Processing = 2,

    [Display(Name = "已处理")]
    Handled = 3,

    [Display(Name = "已忽略")]
    Ignored = 4,

    [Display(Name = "已升级")]
    Escalated = 5
}
            </div>

            <h3>3.3 库存快照UI原型设计</h3>

            <!-- 快照管理主界面 -->
            <div style="border: 2px solid #20c997; border-radius: 8px; padding: 20px; margin: 20px 0; background-color: #f8fffd;">
                <h4 style="color: #20c997; margin-bottom: 15px;">📸 快照管理主界面</h4>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 15px;">
                    <!-- 顶部操作栏 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <div>
                            <button style="background: #20c997; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px;">📸 创建快照</button>
                            <button style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px;">🔍 快照对比</button>
                            <button style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px;">📊 分析报告</button>
                        </div>
                        <div>
                            <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                                <option>全部仓库</option>
                                <option>主仓库</option>
                                <option>分仓库</option>
                            </select>
                            <input type="text" placeholder="搜索快照..." style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>

                    <!-- 快照列表 -->
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">快照编号</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">快照名称</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">快照类型</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">快照时间</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">范围</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">记录数</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">状态</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">SS202412001</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">月末库存快照</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #e7f3ff; color: #007bff; padding: 2px 8px; border-radius: 12px; font-size: 12px;">定时快照</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">2024-12-31 23:59:59</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">全部库存</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">15,234</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已完成</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">查看</button>
                                    <button style="background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">下载</button>
                                    <button style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">SS202412002</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">盘点前快照</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #fff3cd; color: #856404; padding: 2px 8px; border-radius: 12px; font-size: 12px;">手动快照</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">2024-12-30 14:30:00</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">主仓库</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">8,567</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已完成</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">查看</button>
                                    <button style="background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">下载</button>
                                    <button style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">SS202412003</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">系统升级前快照</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 12px;">事件快照</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">2024-12-29 09:00:00</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">全部库存</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">14,892</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #cce5ff; color: #004085; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已归档</span></td>
                                <td style="border: 1px solid #ddd; padding: 10px;">
                                    <button style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">查看</button>
                                    <button style="background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-right: 5px; font-size: 12px;">下载</button>
                                    <button style="background: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">恢复</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <div style="font-size: 14px; color: #6c757d;">共 156 条记录，当前第 1/16 页</div>
                        <div>
                            <button style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; margin-right: 5px;">上一页</button>
                            <button style="background: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; margin-right: 5px;">1</button>
                            <button style="background: #f8f9fa; color: #007bff; border: 1px solid #007bff; padding: 6px 12px; border-radius: 4px; margin-right: 5px;">2</button>
                            <button style="background: #f8f9fa; color: #007bff; border: 1px solid #007bff; padding: 6px 12px; border-radius: 4px; margin-right: 5px;">3</button>
                            <button style="background: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px;">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创建快照对话框 -->
            <div style="border: 2px solid #20c997; border-radius: 8px; padding: 20px; margin: 20px 0; background-color: #f8fffd;">
                <h4 style="color: #20c997; margin-bottom: 15px;">📸 创建快照对话框</h4>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; max-width: 600px;">
                    <h5 style="margin-bottom: 20px; color: #333;">创建库存快照</h5>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">快照名称 *</label>
                        <input type="text" placeholder="请输入快照名称" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">快照类型 *</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>手动快照</option>
                                <option>定时快照</option>
                                <option>事件快照</option>
                                <option>增量快照</option>
                                <option>完整快照</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">快照范围 *</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>全部库存</option>
                                <option>指定仓库</option>
                                <option>指定分类</option>
                                <option>指定物料</option>
                                <option>指定批次</option>
                            </select>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">仓库选择</label>
                        <div style="border: 1px solid #ddd; border-radius: 4px; padding: 10px; max-height: 120px; overflow-y: auto;">
                            <label style="display: block; margin-bottom: 5px;"><input type="checkbox" checked style="margin-right: 8px;">主仓库</label>
                            <label style="display: block; margin-bottom: 5px;"><input type="checkbox" style="margin-right: 8px;">分仓库A</label>
                            <label style="display: block; margin-bottom: 5px;"><input type="checkbox" style="margin-right: 8px;">分仓库B</label>
                            <label style="display: block; margin-bottom: 5px;"><input type="checkbox" style="margin-right: 8px;">临时仓库</label>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">快照描述</label>
                        <textarea placeholder="请输入快照描述..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; height: 80px; resize: vertical;"></textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 10px;"><input type="checkbox" style="margin-right: 8px;">启用数据压缩</label>
                        <label style="display: block; margin-bottom: 10px;"><input type="checkbox" checked style="margin-right: 8px;">包含批次信息</label>
                        <label style="display: block; margin-bottom: 10px;"><input type="checkbox" checked style="margin-right: 8px;">包含成本信息</label>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px;">取消</button>
                        <button style="background: #20c997; color: white; border: none; padding: 10px 20px; border-radius: 4px;">创建快照</button>
                    </div>
                </div>
            </div>

            <!-- 快照对比界面 -->
            <div style="border: 2px solid #fd7e14; border-radius: 8px; padding: 20px; margin: 20px 0; background-color: #fff8f0;">
                <h4 style="color: #fd7e14; margin-bottom: 15px;">🔍 快照对比分析界面</h4>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 15px;">
                    <!-- 对比选择区 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">基准快照</label>
                            <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>SS202412001 - 月末库存快照</option>
                                <option>SS202412002 - 盘点前快照</option>
                                <option>SS202412003 - 系统升级前快照</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">对比快照</label>
                            <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>SS202412002 - 盘点前快照</option>
                                <option>SS202412001 - 月末库存快照</option>
                                <option>SS202412003 - 系统升级前快照</option>
                            </select>
                        </div>
                    </div>

                    <!-- 对比结果概览 -->
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="text-align: center; padding: 15px; background: #e7f3ff; border-radius: 8px;">
                            <div style="font-size: 24px; color: #007bff; font-weight: bold;">+1,234</div>
                            <div style="font-size: 14px; color: #6c757d;">新增记录</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #fff3cd; border-radius: 8px;">
                            <div style="font-size: 24px; color: #856404; font-weight: bold;">2,567</div>
                            <div style="font-size: 14px; color: #6c757d;">变更记录</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #f8d7da; border-radius: 8px;">
                            <div style="font-size: 24px; color: #721c24; font-weight: bold;">-456</div>
                            <div style="font-size: 14px; color: #6c757d;">删除记录</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: #d4edda; border-radius: 8px;">
                            <div style="font-size: 24px; color: #155724; font-weight: bold;">89.2%</div>
                            <div style="font-size: 14px; color: #6c757d;">一致性</div>
                        </div>
                    </div>

                    <!-- 详细对比表格 -->
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">物料编号</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">物料名称</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">基准数量</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">对比数量</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">差异</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">差异率</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">M001</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">钢材A型</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">1,000</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">1,200</td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #28a745;">+200</td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #28a745;">+20%</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 12px;">增加</span></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">M002</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">铝材B型</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">800</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">750</td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #dc3545;">-50</td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #dc3545;">-6.25%</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 12px;">减少</span></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">M003</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">塑料C型</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">500</td>
                                <td style="border: 1px solid #ddd; padding: 10px;">500</td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #6c757d;">0</td>
                                <td style="border: 1px solid #ddd; padding: 10px; color: #6c757d;">0%</td>
                                <td style="border: 1px solid #ddd; padding: 10px;"><span style="background: #e2e3e5; color: #383d41; padding: 2px 8px; border-radius: 12px; font-size: 12px;">无变化</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <section id="integration-summary">
            <h2>4. 模块集成与总结</h2>

            <div class="module-intro">
                <h3>4.1 三大新增模块协同价值</h3>
                <p>库存预警、库存批次和库存快照管理三个模块相互协同，形成了完整的库存智能监控和管理体系。通过预警系统的实时监控、批次管理的精细化控制、快照管理的历史追溯，为企业提供全方位的库存管理解决方案。</p>

                <!-- 模块协同图 -->
                <div style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%); border-radius: 12px; padding: 30px; margin: 30px 0; color: #333;">
                    <h4 style="text-align: center; margin-bottom: 25px; color: #333;">🔄 模块协同关系</h4>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                        <div style="text-align: center; background: rgba(255,255,255,0.8); border-radius: 8px; padding: 20px;">
                            <h5 style="color: #dc3545; margin-bottom: 15px;">🚨 库存预警</h5>
                            <div style="font-size: 14px; line-height: 1.6;">
                                <strong>实时监控</strong><br>
                                • 多维度预警规则<br>
                                • 智能阈值调整<br>
                                • 自动通知处理<br>
                                • 预测性预警
                            </div>
                        </div>
                        <div style="text-align: center; background: rgba(255,255,255,0.8); border-radius: 8px; padding: 20px;">
                            <h5 style="color: #6f42c1; margin-bottom: 15px;">🔄 库存批次</h5>
                            <div style="font-size: 14px; line-height: 1.6;">
                                <strong>精细管控</strong><br>
                                • 批次级库存追踪<br>
                                • 批次状态管理<br>
                                • 批次操作记录<br>
                                • 质量状态跟踪
                            </div>
                        </div>
                        <div style="text-align: center; background: rgba(255,255,255,0.8); border-radius: 8px; padding: 20px;">
                            <h5 style="color: #20c997; margin-bottom: 15px;">📸 库存快照</h5>
                            <div style="font-size: 14px; line-height: 1.6;">
                                <strong>历史追溯</strong><br>
                                • 时点数据快照<br>
                                • 趋势分析对比<br>
                                • 审计数据支撑<br>
                                • 版本管理控制
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心价值展示 -->
                <div style="background-color: #f8f9fa; border: 2px solid #6c757d; border-radius: 12px; padding: 25px; margin: 25px 0;">
                    <h4 style="color: #495057; text-align: center; margin-bottom: 20px;">🎯 新增模块核心价值</h4>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="font-size: 32px; color: #dc3545; margin-bottom: 8px; font-weight: bold;">95%</div>
                            <div style="font-size: 14px; font-weight: bold; color: #495057;">预警准确率</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">智能预警系统</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="font-size: 32px; color: #6f42c1; margin-bottom: 8px; font-weight: bold;">100%</div>
                            <div style="font-size: 14px; font-weight: bold; color: #495057;">批次追溯率</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">精细批次管理</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="font-size: 32px; color: #20c997; margin-bottom: 8px; font-weight: bold;">365天</div>
                            <div style="font-size: 14px; font-weight: bold; color: #495057;">历史数据保存</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">快照管理系统</div>
                        </div>
                    </div>
                </div>

                <!-- 技术特色 -->
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 25px 0;">
                    <div style="border: 2px solid #007bff; border-radius: 8px; padding: 20px; background-color: #f8f9ff;">
                        <h4 style="color: #007bff; margin-bottom: 15px;">🚀 技术创新特色</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>实时预警引擎</strong>：基于规则引擎的智能预警系统</li>
                            <li><strong>批次状态机</strong>：完整的批次状态生命周期管理</li>
                            <li><strong>增量快照</strong>：高效的增量数据快照技术</li>
                            <li><strong>多维分析</strong>：支持多维度数据分析和对比</li>
                            <li><strong>自动化流程</strong>：预警处理和快照生成自动化</li>
                        </ul>
                    </div>
                    <div style="border: 2px solid #28a745; border-radius: 8px; padding: 20px; background-color: #f8fff9;">
                        <h4 style="color: #28a745; margin-bottom: 15px;">💼 业务应用价值</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>风险预防</strong>：提前识别和预防库存风险</li>
                            <li><strong>精细管控</strong>：批次级精确库存管控</li>
                            <li><strong>历史追溯</strong>：完整的库存变化历史记录</li>
                            <li><strong>决策支持</strong>：基于数据的科学决策支持</li>
                            <li><strong>合规审计</strong>：满足审计和合规要求</li>
                        </ul>
                    </div>
                </div>

                <!-- 实施建议 -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; padding: 25px; margin: 25px 0; color: white;">
                    <h4 style="text-align: center; margin-bottom: 20px; color: white;">📋 实施建议</h4>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                        <div style="text-align: center; background: rgba(255,255,255,0.2); border-radius: 8px; padding: 15px;">
                            <div style="font-size: 20px; margin-bottom: 8px;">第一步</div>
                            <div style="font-size: 12px; line-height: 1.4;">
                                部署库存预警模块<br>
                                配置预警规则<br>
                                测试通知机制
                            </div>
                        </div>
                        <div style="text-align: center; background: rgba(255,255,255,0.2); border-radius: 8px; padding: 15px;">
                            <div style="font-size: 20px; margin-bottom: 8px;">第二步</div>
                            <div style="font-size: 12px; line-height: 1.4;">
                                启用库存批次管理<br>
                                批次数据迁移<br>
                                批次操作培训
                            </div>
                        </div>
                        <div style="text-align: center; background: rgba(255,255,255,0.2); border-radius: 8px; padding: 15px;">
                            <div style="font-size: 20px; margin-bottom: 8px;">第三步</div>
                            <div style="font-size: 12px; line-height: 1.4;">
                                配置快照管理<br>
                                设置快照策略<br>
                                建立分析报表
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="footer">
            <p><strong>梵素EAP仓库管理系统 - 新增功能模块设计</strong></p>
            <p>版本：1.0 | 日期：2024年12月 | 设计团队：梵素科技</p>
            <p>本文档包含了库存预警、库存批次、库存快照管理三大新增功能模块的完整设计方案</p>
            <p style="color: #28a745; font-weight: bold;">✅ 库存预警模块 | ✅ 库存批次模块 | ✅ 库存快照管理模块 | ✅ 完整数据模型设计</p>
            <p style="color: #007bff; font-weight: bold; margin-top: 10px;">🚀 基于.NET 9 + Blazor + Bootstrap Blazor V9.4.0 的现代化解决方案</p>

            <!-- 结语 -->
            <div style="text-align: center; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white; margin: 20px 0;">
                <h3 style="color: white; margin-bottom: 15px;">🎉 新增模块设计完成</h3>
                <p style="font-size: 16px; margin-bottom: 15px; line-height: 1.6;">
                    三大新增功能模块将显著提升梵素EAP仓库管理系统的<strong>智能化</strong>、<strong>精细化</strong>和<strong>可追溯性</strong>水平。
                </p>
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                    <div style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 15px; font-weight: bold; font-size: 14px;">
                        🚨 智能预警
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 15px; font-weight: bold; font-size: 14px;">
                        🔄 精细批次
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 15px; font-weight: bold; font-size: 14px;">
                        📸 历史快照
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
