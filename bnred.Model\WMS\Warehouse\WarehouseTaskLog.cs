using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
 
using WalkingTec.Mvvm.Core;
using bnred.Model.WMS.Enums;

namespace bnred.Model.WMS.Warehouse
{
    /// <summary>
    /// 仓库任务日志
    /// 记录任务执行的详细过程
    /// </summary>
    [Table("WMS_WarehouseTaskLogs")]
    public class WarehouseTaskLog : BasePoco
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [StringLength(50)]
        public new string ID { get; set; } = Guid.CreateVersion7().ToString();

        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TaskId { get; set; }

        /// <summary>
        /// 任务
        /// </summary>
        [Display(Name = "任务")]
        public WarehouseTask Task { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
  
        public Guid? OperatorId { get; set; }

 
        public FrameworkUser Operator { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Display(Name = "操作时间")]
        [Required]
        public DateTime OperationTime { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [Display(Name = "操作类型")]
        [Required]
        [StringLength(50)]
        public string OperationType { get; set; }

        /// <summary>
        /// 操作内容
        /// </summary>
        [Display(Name = "操作内容")]
        [Required]
        [StringLength(500)]
        public string OperationContent { get; set; }

        /// <summary>
        /// 操作结果
        /// </summary>
        [Display(Name = "操作结果")]
        [Required]
        [StringLength(50)]
        public string OperationResult { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Display(Name = "备注")]
        [StringLength(500)]
        public string Remark { get; set; }
    }
} 